{"name": "woniu-world-customer", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "test": "vite --mode test", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "build:dev": "vite build --mode development ", "build:prod": "vite build --mode production ", "build:test": "vite build --mode test", "build:daily": "vite build --mode daily", "build:uat": "vite build --mode uat", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"aegis-web-sdk": "1.39.2", "aliyun_numberauthsdk_web": "^2.1.10", "axios": "^1.7.2", "broadcast-channel": "^7.0.0", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "element-plus": "2.8.7", "fingerprintjs2": "^2.1.4", "gsap": "^3.13.0", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qrcode": "^1.5.3", "terser": "^5.39.0", "vue": "^3.4.21", "vue-cropper": "^1.1.1", "vue-router": "^4.3.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/js-cookie": "^3.0.6", "@types/node": "^20.12.5", "@vitejs/plugin-legacy": "^5.4.1", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "npm-run-all2": "^6.1.2", "prettier": "^3.3.2", "rollup-plugin-esbuild": "^6.2.1", "sass": "^1.77.2", "sass-loader": "^14.2.1", "typescript": "~5.4.0", "vite": "^5.2.8", "vite-plugin-compression": "^0.5.1", "vue-tsc": "^2.0.11"}}