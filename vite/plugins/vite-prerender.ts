// https://www.npmjs.com/package/vite-plugin-prerender
import vitePrerender from 'vite-plugin-prerender'
const Renderer = vitePrerender.PuppeteerRenderer
import path from 'path'

export default () => {
  return vitePrerender({
    // 要渲染的路由
    routes: ['/', '/index', '/model/list'],
    // 静态文件目录
    staticDir: path.join(__dirname, 'dist'),
    // 可选 - 使用 html-minifier (https://github.com/kangax/html-minifier)
    // 缩小生成的 HTML。
    // 选项参考：https://github.com/kangax/html-minifier#options-quick-reference
    minify: {
      collapseBooleanAttributes: true,
      collapseWhitespace: true,
      decodeEntities: true,
      keepClosingSlash: true,
      sortAttributes: true,
    },
    // 渲染时是否显示浏览器窗口，值写false可用于调试
    renderer: new Renderer({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'],
    }),
  })
}
