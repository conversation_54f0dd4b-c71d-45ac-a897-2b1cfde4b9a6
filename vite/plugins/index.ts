import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import legacy from '@vitejs/plugin-legacy'

// import createAutoImport from './auto-import'
// import createSvgIcon from './svg-icon'
import createCompression from './compression'
// import createSetupExtend from './setup-extend'
// import vitePrerender from './vite-prerender'
// import viteSeoPrerender from './vite-seo-prerender'

export default function createVitePlugins(viteEnv: Record<string, string>, isBuild = false) {
  const vitePlugins = [
    vue(),
    vueJsx(),
    legacy({
      targets: ['defaults', 'not IE 11'],
    })
  ]
  // vitePlugins.push(createAutoImport())
  // vitePlugins.push(createSetupExtend())
  // vitePlugins.push(createSvgIcon(isBuild))
  // vitePlugins.push(vitePrerender())
  // vitePlugins.push(viteSeoPrerender())
  isBuild && vitePlugins.push(...createCompression(viteEnv))
  return vitePlugins
}
