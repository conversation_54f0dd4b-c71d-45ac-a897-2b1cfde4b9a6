import { ref, toRefs } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { useDictStore } from '@/stores/modules/dict'
import { getDicts } from '@/api/dict'

const sessionStorageStr = 'dict-'
const res: {
  [x:string]: any
} = ref({});
/**
 * 获取字典数据
 */
export function useDict(...args: string[]) {
  return (() => {
    if (!useUserStore().userInfo?.account) {
      return []
    }
    args.forEach((dictType: string, index) => {
      if (!res.value[dictType]) {
        sessionStorage.removeItem(sessionStorageStr + dictType);
        res.value[dictType] = [];
      }
      const dicts = useDictStore().getDict(dictType);
      if (dicts) {
        res.value[dictType] = dicts;
      } else {
        let s = sessionStorage.getItem(sessionStorageStr + dictType);
        if (!s || (s && new Date().getTime() - Number(s) > 1000 * 60)) {
          sessionStorage.setItem(sessionStorageStr + dictType, new Date().getTime() + '');
          getDicts(dictType).then(resp => {
            res.value[dictType] = resp.data.map((p: { dictLabel: any; dictValue: any; listClass: any; cssClass: any; }) => ({ label: p.dictLabel, value: p.dictValue, elTagType: p.listClass, elTagClass: p.cssClass }))
            useDictStore().setDict(dictType, res.value[dictType]);
          }).finally(() => {
            sessionStorage.removeItem(sessionStorageStr + dictType);
          })
        }
      }
    })
    return toRefs(res.value);
  })()
}