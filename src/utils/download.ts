import { fileDownloadUrl } from '@/api/index'

const http = import.meta.env.VITE_APP_FILE_HTTP_PATH
/**
 * 下载图片文件
 * @param url
 * @param fileName
 */
export function downUrlImgFile(url: string, fileName: string) {
  // 创建一个Image对象
  const image = new Image()
  // 设置图片的src属性为要下载的图片地址
  image.src = http + url
  image.setAttribute('crossOrigin', 'anonymous')

  // 加载图片资源
  image.onload = function () {
    // 创建Canvas元素
    let canvas = document.createElement('canvas')
    canvas.width = image.width
    canvas.height = image.height

    // 绘制图片到Canvas
    let ctx = canvas.getContext('2d')
    ctx?.drawImage(image, 0, 0, image.width, image.height)

    let src = canvas.toDataURL('image/png')
    let a = document.createElement('a')
    a.href = src
    a.download = fileName
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }
}

/**
 * 下载文件
 * @param url
 * @param fileName
 */
export async function downUrlFile(objectKey: string, fileName: string) {
  const response = await fileDownloadUrl({ objectKey })
  if (response?.code === 200 && response?.data) {
    getBlob(response.data).then((res: any) => {
      saveAs(res.data, res.name || fileName)
    })
  }
}
function getBlob(url: string) {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open('GET', url, true)
    xhr.responseType = 'blob'
    xhr.onload = () => {
      if (xhr.status === 200) {
        const FILE_NAME = xhr.getResponseHeader('content-disposition')?.split('filename=')[1]
        resolve({
          name: FILE_NAME,
          data: xhr.response
        })
      } else {
        reject()
      }
    }
    xhr.send()
  })
}
function saveAs(blob: any, filename: string) {
  if (window.navigator.msSaveOrOpenBlob) {
    navigator.msSaveBlob(blob, filename)
  } else {
    const link = document.createElement('a')

    link.href = window.URL.createObjectURL(blob)
    link.download = filename
    link.style.display = 'none'
    document.body.appendChild(link)

    link.click()
    document.body.removeChild(link)

    window.URL.revokeObjectURL(link.href)
  }
}
