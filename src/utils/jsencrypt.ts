import { JSEncrypt } from 'jsencrypt';

const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApsrogkhGRItL/3U9q6R3mvZQgnfAagQfl6gZq8qKDfOwkyMyzDxtPdvfwHAyvPecztXRmhcZky1/jBM+79V9cnTDXrRdMx4FsoFs/ajSO4QE0CmXEcorAhk4ONOrmKYC+t0EAypWjAYhi7Dl49IIf6LW/DlpCXH6TVp14ml5wTuVJWeFiAznm32CtZhpHk05MyDcbQLNt17n53eIjWMyN+2si55ifR6mRiG2TkORIgBRmm7QJ2FQPSW8RHJ/vV6oAzzheFZ0bgD5GIqNdPTVZPigad8M7DQnLaxxfthJHG2G6hLWkphNBF2TvGz8aoaxpyK/js1+MlgGI0BgRgRFlwIDAQAB
-----E<PERSON> PUBLIC KEY-----`

const encryptor = new JSEncrypt()
encryptor.setPublicKey(publicKey)

export function encrypt(text: string) {
  return encryptor.encrypt(text)
}

export function decrypt(text: string) {
  return encryptor.decrypt(text)
}