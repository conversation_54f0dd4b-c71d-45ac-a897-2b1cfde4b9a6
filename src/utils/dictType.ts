// 平台
export enum Platform {
  'Amazon' = '0',
  'Tiktok' = '1',
  'Other' = '2',
  'App' = '3'
}
// 模特类型
export enum ModelType {
  '影响者' = '0',
  '素人' = '1',
  '都属于' = '3'
}

export enum AUDIT_STATUS {
  '待审核' = 0,
  '已通过' = 1,
  '已拒绝' = 2
}
/**
 * 申请状态
 * @param status 状态 value/key
 * @returns 状态 key/value
 */
export function auditStatus(status?: AUDIT_STATUS | number): AUDIT_STATUS | string {
  if(status || status === 0) {
    return AUDIT_STATUS[status] || '';
  }
  return ''
}