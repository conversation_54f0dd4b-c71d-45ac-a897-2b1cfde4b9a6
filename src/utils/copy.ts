import { ElMessage, ElMessageBox } from 'element-plus'

/**
 * copy to clipboard
 * @param text
 */
const copyText = (() => {
  if (
    (/iPad|iPhone|iPod/.test(navigator.userAgent) && (!window as any)?.MSStream) ||
    (/Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent))
  ) {
    return iosCopyText
  } else {
    return (text: string) => {
      const el: any = document.createElement('textarea')
      el.value = text
      el.style.position = 'fixed'
      el.style.top = '-500px'
      el.style.right = '-500px'
      document.body.appendChild(el)
      el.select()

      const successful = document.execCommand('copy')
      if (successful) {
        ElMessage.success('已复制到剪贴板!')
      } else {
        alertText(text)
        ElMessage.error('您的浏览器不支持一键复制功能!')
      }
      document.body.removeChild(el)
    }
  }
})()
/**
 * ios copy to clipboard
 * @param text
 */
function iosCopyText(text: string) {
  const el: any = document.createElement('textarea')
  el.value = text
  el.style.position = 'fixed'
  el.style.top = '-500px'
  el.style.right = '-500px'
  document.body.appendChild(el)
  el.select()
  const oldContentEditable = el.contentEditable,
    oldReadOnly = el.readOnly,
    range = document.createRange()

  el.contentEditable = true
  el.readOnly = false
  range.selectNodeContents(el)

  let s: any = window.getSelection()
  s.removeAllRanges()
  s.addRange(range)

  el.setSelectionRange(0, 999999)
  el.contentEditable = oldContentEditable
  el.readOnly = oldReadOnly

  const successful = document.execCommand('copy')
  if (successful) {
    ElMessage.success('已复制到剪贴板.')
  } else {
    alertText(text)
    ElMessage.error('您的浏览器不支持一键复制功能!!')
  }
  document.body.removeChild(el)
}

function makeError() {
  return new DOMException('The request is not allowed', 'NotAllowedError')
}

/**
 * alert copy text
 * @param text
 */
function alertText(text: string) {
  ElMessageBox.alert(
    `<div class="text-n-all template-pre" style="max-height: 600px; overflow-y: auto;font-size: 14px;">${text}</div>`,
    '您的浏览器不支持一键复制功能，请手动复制以下内容',
    {
      customStyle: {
        '--el-messagebox-width': '550px',
        'font-family': 'Arial, PingFang SC, sans-serif',
      },
      cancelButtonText: '关闭',
      showCancelButton: true,
      showConfirmButton: false,
      dangerouslyUseHTMLString: true,
    }
  )
}

/**
 * clipboard copy api
 * @param text
 * @returns
 */
async function clipboardCopyApi(text: string) {
  if (!navigator.clipboard) {
    throw makeError()
  }
  return navigator.clipboard.writeText(text)
}

/**
 * 复制
 * @param text 复制文本
 */
export default async function copy(text: string) {
  try {
    await clipboardCopyApi(text).then(function () {
      ElMessage.success('已复制到剪贴板')
    })
  } catch (err) {
    try {
      copyText(text)
    } catch (err2) {
      throw err2 || err || makeError()
    }
  }
}
