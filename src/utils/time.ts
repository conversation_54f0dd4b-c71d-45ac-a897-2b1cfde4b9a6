/**
 * 距离目标时间还剩几天
 * @param time
 * @returns
 */
export function endTimeToDay(time: string) {
  return Math.ceil((new Date(time).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
}

/**
 * 获取当月的日期范围
 * @param start
 * @param end
 * @returns
 */
export function getMonthRange(start: number, end: number): { startTime: number; endTime: number } {
  if (typeof start !== 'number' || typeof end !== 'number') {
    return {
      startTime: 0,
      endTime: 0,
    }
  }

  if (end < start) {
    ;[start, end] = [end, start]
  }

  // 获取当前年份和月份
  const currentYear = new Date().getFullYear()
  const currentMonth = new Date().getMonth()

  if (start < 1) {
    start = 1
  }
  let maxDay = getCurMonthMaxDay()
  if (end > maxDay) {
    end = maxDay
  }

  // 开始、结束日期
  const startDate = new Date(currentYear, currentMonth, start).getTime()
  const endDate = new Date(currentYear, currentMonth, end, 23, 59, 59).getTime()

  return {
    startTime: startDate,
    endTime: endDate,
  }
}

/**
 * 当前月的最大天数
 * @returns
 */
function getCurMonthMaxDay() {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1

  // 获取下个月的第一天，然后减去一天
  const nextMonth = new Date(year, month, 0)
  const maxDays = nextMonth.getDate()

  return maxDays
}
