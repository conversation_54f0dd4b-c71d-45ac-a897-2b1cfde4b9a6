export enum ORDER_STATUS {
  '需支付' = 1,
  '待审核' = 2,
  '待匹配 ' = 3, // 待确认
  '待匹配' = 4,
  '需发货' = 5,
  '待完成' = 6,
  '需确认' = 7,
  '已完成' = 8,
  '交易关闭' = 9,
}
/**
 * 订单状态
 * @param status 状态 value/key
 * @returns 状态 key/value
 */
export function orderStatus(status?: ORDER_STATUS | number): ORDER_STATUS | string {
  if (status || status === 0) {
    return ORDER_STATUS[status] || ''
  }
  return ''
}
export enum AFTER_SALE_STATUS {
  '售后待审核' = 9,
  '已拒绝' = 10,
  '已取消' = 11,
  '售后中' = 12,
  '售后成功' = 13,
}
/**
 * 售后状态
 * @param status 状态 value/key
 * @returns 状态 key/value
 */
export function afterSaleStatus(status?: AFTER_SALE_STATUS | number): AFTER_SALE_STATUS | string {
  if (status || status === 0) {
    return AFTER_SALE_STATUS[status] || ''
  }
  return ''
}
export enum AUDIT_STATUS {
  '审核通过' = 0,
  '审核退回' = 1,
  '待审核' = 2,
}
/**
 * 审核状态
 * @param status 状态 value/key
 * @returns 状态 key/value
 */
export function auditStatus(status?: AUDIT_STATUS | number): AUDIT_STATUS | string {
  if (status || status === 0) {
    return AUDIT_STATUS[status] || ''
  }
  return ''
}
export enum REFUND_STATUS {
  '退款待审核' = 0,
  '退款中' = 1,
  '已拒绝' = 2,
  '已取消' = 3,
  '退款成功' = 4,
}
/**
 * 退款状态
 * @param status 状态 value/key
 * @returns 状态 key/value
 */
export function refundStatus(status?: REFUND_STATUS | number): REFUND_STATUS | string {
  if (status || status === 0) {
    return REFUND_STATUS[status] || ''
  }
  return ''
}
export enum PAY_TYPE {
  '微信支付' = 1,
  '支付宝支付' = 2,
  '云闪付/银联' = 3,
  '数字人民币' = 4,
  '银行卡转账' = 5,
  '对公转账' = 6,
  '全币种支付' = 7,
  '余额支付' = 10,
}
/**
 * 支付方式
 * @param value value/key
 * @returns key/value
 */
export function payType(value?: PAY_TYPE | number): PAY_TYPE | string {
  if (value) {
    if (value > PAY_TYPE['余额支付']) {
      return PAY_TYPE[value - PAY_TYPE['余额支付']] + '+余额支付'
    }
    return PAY_TYPE[value]
  }
  return ''
}
export enum VIP_ORDER_STATUS {
  '待支付' = 1,
  '待审核' = 2,
  // '交易成功' = 3,
  // '交易关闭' = 4,
  '支付成功' = 3,
  '订单关闭' = 4,
}
/**
 * 订单状态
 * @param status 状态 value/key
 * @returns 状态 key/value
 */
export function vipOrderStatus(status?: VIP_ORDER_STATUS | number): VIP_ORDER_STATUS | string {
  if (status) {
    return VIP_ORDER_STATUS[status] || ''
  }
  return ''
}
/**
 * 订单步骤状态
 */
export enum ORDER_STEPS_STATUS {
  '下单支付' = 1,
  '匹配模特' = 2,
  '商家发货' = 3,
  '完成拍摄' = 4,
  '商家确认' = 5,
  '订单完成' = 6,
  '取消订单' = 10,
}
/**
 * 钱包充值订单状态
 */
export enum WALLET_TOPUP_ORDER_STATUS {
  '待支付' = 1,
  '待审核' = 2,
  '支付成功' = 8,
  '订单关闭' = 9,
}

/**
 * 全币种支付方式
 */
export enum PAY_MONEY_TYPE {
  '万里汇' = 702,
  '其他平台/银行' = 701,
}
/**
 * 全币种支付方式
 * @param status 状态 value/key
 * @returns 状态 key/value
 */
export function payMoneyTypeStatus(status?: PAY_MONEY_TYPE | number): PAY_MONEY_TYPE | string {
  if (status) {
    return PAY_MONEY_TYPE[status] || ''
  }
  return ''
}
