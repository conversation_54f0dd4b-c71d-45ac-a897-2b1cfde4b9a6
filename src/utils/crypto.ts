import CryptoJS from 'crypto-js'

const wnkjKey = CryptoJS.enc.Utf8.parse('w-n-k-j-1111-key')
// const wnkjIv = CryptoJS.enc.Utf8.parse('w-n-k-j-11111-iv')
// AES对称加密
export function encryptionValue(value: string) {
  return CryptoJS.AES.encrypt(value, wnkjKey, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    }).toString();
}
// AES对称解密
export function decryptionValue(value: string) {
  return CryptoJS.AES.decrypt(value, wnkjKey, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7,
    }).toString(CryptoJS.enc.Utf8);
}
