import Fingerprint2 from 'fingerprintjs2'
import { ref } from 'vue'
import { marketingChannelVisit } from '@/api/index'

const fingerprint = ref('')

window.addEventListener('load', () => {
  Fingerprint2.get({}, (components: any[]) => {
    // 参数只有回调函数时，默认浏览器指纹依据所有配置信息进行生成
    const values = components.map(component => component.value) // 配置的值的数组
    fingerprint.value = Fingerprint2.x64hash128(values.join(''), 31) // 生成浏览器指纹
    setMarketingChannelVisit()
  })
})

function setMarketingChannelVisit() {
  if (!fingerprint.value) return
  const temp = new URLSearchParams(window.location.search)
  const dedicatedLinkCode = temp.get('channel') || temp.get('c') || ''
  if (dedicatedLinkCode) {
    marketingChannelVisit({
      dedicatedLinkCode,
      fingerprint: fingerprint.value,
    })
  }
}

function waitFingerprint() {
  return new Promise(async (resolve, reject) => {
    try {
      while (!fingerprint.value) {
        await sleep(100); // 每隔 100ms 检查一次
      }
      resolve(fingerprint.value)
    } catch (error) {
      reject(error)
    }
  })
}

function sleep(ms: number | undefined) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export {
  fingerprint,
  waitFingerprint
}