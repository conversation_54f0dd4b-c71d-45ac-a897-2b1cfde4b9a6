import IconHint from '@/components/icons/IconHint.vue'
import { ElMessageBox } from 'element-plus'
import { markRaw } from 'vue'

/**
 * message box 弹窗
 * @param message 弹窗内容 string | HTML | VNode
 * @param option el-message-box 配置项
 * @returns 
 * @example
 * MessageBox(`XXXXXX消息内容<br/>
      <span>span标签的内容默认小号灰色字体</span>`,
    {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
    }).then(() => {
      console.log('点击确定')
    }).catch(() => {
      console.log('点击取消')
    })
 */
export function MessageBox(message: string, option?: any): Promise<any> {
  return ElMessageBox.confirm(message, {
    showClose: false,
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    icon: markRaw(IconHint),
    dangerouslyUseHTMLString: true,
    ...option,
    customClass: 'custom-message-box' + (option?.title ? ' custom-message-title' : ''),
  })
}
