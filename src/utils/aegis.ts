import Aegis from 'aegis-web-sdk'

const aegis = new Aegis({
  id: 'PJZQ8c1n0w9Y14Jblx', // 上报 id
  uin: '', // 用户唯一 ID（可选）
  reportApiSpeed: true, // 接口测速
  reportAssetSpeed: true, // 静态资源测速
  spa: true, // spa 应用页面跳转的时候开启 pv 计算
  hostUrl: 'https://rumt-zh.com',
  env: import.meta.env.VITE_APP_ENV === 'staging'? 'test' : import.meta.env.VITE_APP_ENV, // 环境
  delay: 2000,
  api: {
    retCodeHandler(data: any) {
      // 注意这里拿到的data是string类型，如果需要对象需要手动parse下
      try {
        data = JSON.parse(data)
      } catch (e) {
      }
      return {
        // isErr 如果是 true 的话，会上报一条 retcode 异常的日志。
        isErr: data.body.code !== 200,
        code: data.body.code
      }
    }
  }
})

export default aegis
