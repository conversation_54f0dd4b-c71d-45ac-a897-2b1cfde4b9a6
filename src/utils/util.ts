// 递归菜单树
export const recursionMenuTree = (ary: any[]) => {
  if (ary.length == 0) {
    return []
  }

  const rootItems = ary.filter(item => item.name.split('-').length === 1)
  const childrenOfItem = new Map()

  for (const item of ary) {
    let i = item.name.lastIndexOf('-')
    // name = 'a-b-c'; parentId ? 'a-' : 'a-b'
    const parentId =
      item.meta?.parentId && item.name.indexOf(item.meta.parentId + '-') > -1
        ? item.meta?.parentId
        : item.name.substring(0, i)
    if (!childrenOfItem.has(parentId)) {
      childrenOfItem.set(parentId, [])
    }
    childrenOfItem.get(parentId).push(item)
  }

  function addChildren(items: any[]) {
    for (const item of items) {
      const childItems = childrenOfItem.get(item.name)
      if (childItems) {
        item.children = addChildren(childItems)
      }
    }
    return items.sort(function (a, b) {
      return (a.menuOrder || 0) - (b.menuOrder || 0)
    })
  }

  return addChildren(rootItems)
}

/**
 * 参数处理
 * @param {*} params  参数
 */
export function tansParams(params: { [x: string]: any }) {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName]
    var part = encodeURIComponent(propName) + '='
    if (value !== null && value !== '' && typeof value !== 'undefined') {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== '' && typeof value[key] !== 'undefined') {
            let params = propName + '[' + key + ']'
            var subPart = encodeURIComponent(params) + '='
            result += subPart + encodeURIComponent(value[key]) + '&'
          }
        }
      } else {
        result += part + encodeURIComponent(value) + '&'
      }
    }
  }
  return result
}
