import { uploadCloudFile } from '@/api/index'
import { ElMessage, ElLoading } from 'element-plus'

/**
 * 深拷贝
 * @param obj 
 * @param hash 
 * @returns 
 */
export function deepClone(obj: any, hash = new WeakMap()) {
  if (obj === null || typeof obj !== 'object') return obj; // 基本类型直接返回
  if (hash.has(obj)) return hash.get(obj); // 解决循环引用
  
  const clone: any = Array.isArray(obj) ? [] : {};
  hash.set(obj, clone);
  
  for (let key in obj) {
    if (obj.hasOwnProperty(key)) {
      clone[key] = deepClone(obj[key], hash);
    }
  }
  return clone;
}

let timer: number | undefined
/**
 * 防抖
 * @param fn 执行函数
 * @param delay 防抖时间
 */
export const debounce = (fn: any, delay: number = 500) => {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(fn, delay)
}
/**
 * 节流
 * @param fn 执行函数
 * @param delay 节流时间
 * @returns {Function}
 */
export const throttle = (fn: any, delay = 500): Function => {
  let flag = false
  return function (this: unknown, ...args: any[]) {
    if (flag) {
      return
    }
    flag = true
    setTimeout(() => {
      fn.apply(this, args)
      flag = false
    }, delay)
  }
}

/**
 * 计算中英文字符串长度
 * @param {string} str
 * @returns {number}
 */
export function stringLength(str: string): number {
  let len = 0
  for (let i = 0; i < str.length; i++) {
    let code = str.charCodeAt(i)
    if (code >= 0 && code <= 127) {
      len += 1
    } else {
      len += 2
    }
  }
  return len
}

export type PasteFileOptions = {
  autoUpload?: boolean
  isBeforeUpload?: boolean
  fileType?: string[]
  size?: number
}
const default_size = 20
const default_fileType = ['png', 'jpg', 'jpeg']
/**
 * 处理粘贴文件上传
 * @param event paste 事件参数
 * @param options.autoUpload 是否自动上传 默认：false
 * @param options.isBeforeUpload 是否对粘贴文件进行校验 默认：true
 * @param options.fileType 校验文件后缀 默认：['png', 'jpg', 'jpeg']
 * @param options.size 校验文件大小 默认：20 (MB)
 * @returns File[] || 自动上传返回接口参数
 */
export const handlePasteFile = (event: ClipboardEvent, options: PasteFileOptions) => {
  // console.log(event);
  return new Promise<File[] | any[]>((resolve, reject) => {
    const items = event.clipboardData?.items || []
    let pasteFiles: File[] = []
    for (let index in items) {
      const item = items[index]
      if (item.kind === 'file') {
        const file = item.getAsFile()
        if (file !== null) {
          pasteFiles.push(file)
        }
      }
    }
    if (!(options?.isBeforeUpload === false) && !beforeUpload(pasteFiles, options)) {
      return reject('error beforeUpload')
    }
    // 是否直接上传
    if (options?.autoUpload) {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在上传中，请稍后',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      let requests: any[] = []
      pasteFiles.forEach(file => {
        const fromData = new FormData()
        fromData.append('file', file)
        requests.push(uploadCloudFile(fromData))
      })
      Promise.all(requests)
        .then(res => {
          ElMessage.success('上传成功')
          let data: any[] | PromiseLike<any[]> = []
          res.forEach(item => {
            data.push(item.data)
          })
          resolve(data)
        })
        .finally(() => el_loading.close())
    } else {
      resolve(pasteFiles)
    }
  })
}
export function beforeUpload(files: any[], options: PasteFileOptions) {
  let check = true
  let fileType = options?.fileType || default_fileType
  for (let i = 0; i < files.length; i++) {
    const fileSuffix = files[i].name.substring(files[i].name.lastIndexOf('.') + 1).toLowerCase()
    if (!fileType.includes(fileSuffix)) {
      ElMessage.warning(`请上传${fileType.join('/')}格式的文件`)
      check = false
      break
    }
    const size = (options?.size || default_size) * 1024 * 1024
    if (files[i].size > size) {
      ElMessage.warning(`要上传的文件不能超过${options?.size || default_size}MB`)
      check = false
      break
    }
  }
  return check
}

/**
 * 返回图片文件分辨率 （宽x高）
 * @param file 文件对象
 * @returns 
 */
export function checkImgResolutionRatio(file: any) {
  return new Promise<{ width: number; height: number } | null>((resolve, reject) => {
    if (file) {
      try {
        const reader = new FileReader()
        reader.onload = function (e: any) {
          const img = new Image()
          img.onload = function () {
            // 获取图片的宽度和高度
            const width = img.width
            const height = img.height
            resolve({ width, height })
          }
          img.src = e.target?.result || ''
        }
        reader.readAsDataURL(file)
      } catch (error) {
        reject(null)
      }
      return
    }
    reject(null)
  })
}

let is_mobile: boolean | null = null
/**
 * 是否是移动端设备
 * @returns
 */
export function isMobileDevice() {
  if (is_mobile === null) {
    is_mobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
  }
  return is_mobile
}

let is_ios: boolean | null = null
/**
 * 是否是ios
 * @returns
 */
export function isIOS() {
  if (is_ios === null) {
    is_ios = /iPad|iPhone|iPod/.test(navigator.userAgent) && (!window as any)?.MSStream
  }
  return is_ios
}

let is_safari: boolean | null = null
/**
 * 是否是Safari浏览器
 * @returns
 */
export function isSafari() {
  if (is_safari === null) {
    is_safari = /Safari/.test(window.navigator.userAgent) && !/Chrome/.test(window.navigator.userAgent)
  }
  return is_safari
}
let is_chrome: boolean | null = null
/**
 * 是否是Chrome浏览器
 * @returns
 */
export function isChrome() {
  if (is_chrome === null) {
    is_chrome = /Chrome/.test(window.navigator.userAgent) && !/OPR/.test(window.navigator.userAgent)
  }
  return is_chrome
}
let is_opera: boolean | null = null
/**
 * 是否是Opera浏览器
 * @returns
 */
export function isOpera() {
  if (is_opera === null) {
    is_opera = /Opera|OPR/.test(window.navigator.userAgent)
  }
  return is_opera
}
let is_firefox: boolean | null = null
/**
 * 是否是Firefox浏览器
 * @returns
 */
export function isFirefox() {
  if (is_firefox === null) {
    is_firefox = /Firefox/.test(window.navigator.userAgent)
  }
  return is_firefox
}
let is_ie: boolean | null = null
/**
 * 是否是IE浏览器
 * @returns
 */
export function isIE() {
  if (is_ie === null) {
    is_ie = /MSIE|Trident/.test(window.navigator.userAgent)
  }
  return is_ie
}
