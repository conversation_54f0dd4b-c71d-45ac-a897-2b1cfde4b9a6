import Cookies from 'js-cookie'

const TokenKey = 'Woniu-Token'

const LocalTokenKey = 'WNKJT'

const ExpiresInKey = 'Woniu-Expires-In'

const ChannelCodeKey = 'ChannelCode'

export function getToken() {
  let token = Cookies.get(TokenKey)
  if(!token) {
    token = ''
  }
  return token
}

const env = import.meta.env.VITE_APP_ENV

export function setToken(token: string) {
  // localStorage.setItem(LocalTokenKey, token)
  if(env === 'development') {
    return Cookies.set(TokenKey, token)
  }
  return Cookies.set(TokenKey, token, { domain: '.woniu.video', expires: 30 })
}

export function removeToken() {
  // localStorage.removeItem(LocalTokenKey)
  Cookies.remove(TokenKey)
  return Cookies.remove(TokenKey, { domain: '.woniu.video', expires: 30 })
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1
}

export function setExpiresIn(time: string) {
  return Cookies.set(ExpiresInKey, time)
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey)
}

export function getChannelCode() {
  const search = new URLSearchParams(window.location.search)
  return search.get('channel') || search.get('c')
}

// export function setChannelCode(code: string) {
//   return sessionStorage.setItem(ChannelCodeKey, code)
// }

// export function removeChannelCode() {
//   return sessionStorage.removeItem(ChannelCodeKey)
// }
