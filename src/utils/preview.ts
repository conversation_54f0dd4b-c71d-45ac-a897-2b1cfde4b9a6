import { ElMessageBox } from 'element-plus'

export function previewImage(url: string, type?: number) {
  if(!url) return
  let className = ''
  if(type === 1) {
    className = 'rectangle-v'
  } else if(type === 2) {
    className = 'rectangle-h'
  } else if(type === 0) {
    className = 'square'
  } else {
    className = 'preview-auto'
  }
  ElMessageBox.alert('', '', {
    customClass: 'preview-message-box',
    dangerouslyUseHTMLString: true,
    message: `<img class="${className}" src="${url}" />`,
    showConfirmButton: false,
    closeOnClickModal: true
  })
}