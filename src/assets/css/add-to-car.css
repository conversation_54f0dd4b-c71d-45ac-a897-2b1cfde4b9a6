.add-to-car {
  position: fixed;
  color: #fff;
  /* font-size: 23rem;
  line-height: 40rem; */
  text-align: center;
  z-index: 99999;
  /* margin-left: -20rem;
  margin-top: -20rem; */
  left: 0;
  top: 0;
  transition: .8s linear;
}

.add-to-car .icon {
  width: 25px;
  height: 25px;
  background: var(--el-color-warning);
  box-shadow: 0 0 10px var(--el-color-warning);
  border-radius: 50%;
  display: block;
  transition: .8s cubic-bezier(0, 1.32, 0.54, 1.54);
  /* transition: 5s cubic-bezier(0.5, -0.5, 1, 1); */
}

.add-to-car-animate {
  animation: car-animate 500ms ease-in-out;
}

@keyframes car-animate {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(0.8);
  }
  50% {
    transform: scale(1.1);
  }
  75% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}
