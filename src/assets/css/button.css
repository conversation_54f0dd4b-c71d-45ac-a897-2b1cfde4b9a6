/* 公共按钮样式 */
.btn-big {
  min-width: 100px;
  height: 36px;
  font-size: 15px;
  padding: 8px 20px !important;
}
/* 按钮a标签样式 */
.link-a {
  color: #2440b3;
  text-decoration-line: underline;
  text-decoration-color: #2440b3;
}
.link-a:visited {
  color: #771caa;
  text-decoration-color: #771caa;
}
/* 圆形按钮 */
.close-round-btn {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 10px;
  height: 10px;
  padding: 4px;
  /* border: 1px solid var(--border-color); */
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  align-content: center;
  z-index: 999;
}
.close-round-btn::before,
.close-round-btn::after {
  content: '';
  background: #929292;
  left: 9px;
  width: 1px;
  height: 20px;
}
.close-round-btn::before {
  transform: rotate(45deg);
  margin-right: 0;
}
.close-round-btn::after {
  transform: rotate(-45deg);
  margin-left: -1px;
}
.close-round-btn:hover {
  /* box-shadow: 0 0 4px var(--modal-bg); */
  transform: scale(1.1);
}