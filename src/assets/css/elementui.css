/* 登录框 */
.login-modal {
  pointer-events: auto;
}

/* 边框问题 */
.el-button:focus {
  outline: none;
}
.el-button:focus-visible {
  outline: none;
}

/* 字体问题 */
.el-select {
  font-family: 'nonexistent-font';
}

/* 不同浏览器颜色问题 */
.el-input__inner::placeholder {
  opacity: 1;
}

/* 不同浏览器文字基线问题 */
.el-button,
.el-menu-item *,
.el-radio-button__inner,
.el-checkbox-button__inner {
  vertical-align: baseline;
}

.video-player-dialog .el-dialog {
  padding: 0;
}
.video-player-dialog .el-dialog .el-dialog__header {
  padding: 0;
}
.video-player-dialog .el-dialog .el-dialog__body {
  line-height: 0;
}

.custom-radius {
  --radius: 6px;
}
.custom-radius.el-button,
.custom-radius .el-select__wrapper,
.custom-radius.el-range-editor,
.custom-radius.el-checkbox-button .el-checkbox-button__inner,
.custom-radius .el-input__wrapper {
  border-radius: var(--radius) !important;
}
.custom-radius.el-input-group--prepend .el-input-group__prepend {
  border-radius: var(--radius) 0 0 var(--radius) !important;
}
.custom-radius.el-input-group--prepend .el-input__wrapper {
  border-radius: 0 var(--radius) var(--radius) 0 !important;
}
.custom-radius.el-input-group--prepend .el-select .el-select__wrapper {
  border-radius: var(--radius) 0 0 var(--radius) !important;
}

/* messagebox字体调整 */
.el-message-box {
  --el-messagebox-font-size: 16px;
  --el-messagebox-content-font-size: 16px;
  --el-messagebox-border-radius: 8px;
}
.message-box-cancel-btn-primary {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
  padding: 10px 20px;
}

/* black message */
.wn-black-message {
  top: 45% !important;
  --el-message-text-color: #fff !important;
  --el-message-bg-color: rgba(0, 0, 0, 0.6) !important;
  --el-message-border-color: transparent !important;
}
.wn-black-message .el-message__icon {
  display: none;
}
.wn-err-msg {
  z-index: 99999 !important;
}

/* 自定义messagebox样式 */
.custom-message-box {
  padding: 12px 0;
}
.custom-message-box.custom-message-title {
  padding: 0 0 12px 0;
}
.custom-message-box.custom-message-title .el-message-box__header {
  height: 45px;
}
.custom-message-box.custom-message-title .el-message-box__title {
  width: 100%;
  height: 50px;
  line-height: 50px;
  box-sizing: border-box;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  background: #f6f6f6;
  padding-left: 20px;
  border-bottom: 1px solid #eee;
}
.custom-message-box.custom-message-title .el-message-box__headerbtn {
  z-index: 3;
  top: 7px;
  right: 3px;
}
.custom-message-box .el-message-box__content {
  box-sizing: border-box;
  padding: 5px 20px;
  min-height: 60px;
}
.custom-message-box .el-message-box__content .el-message-box__container {
  align-items: flex-start;
}
.custom-message-box .el-message-box__content .el-message-box__container .el-message-box__message span {
  color: #999;
  font-size: 14px;
}
.custom-message-box .el-message-box__content .el-icon.el-message-box__status {
  font-size: 20px;
  min-height: var(--el-messagebox-font-line-height);
}
.custom-message-box .el-message-box__btns {
  padding-top: 8px;
  border-top: 1px solid #e4e4e4;
  margin-top: 12px;
}
.custom-message-box .el-message-box__btns .el-button {
  margin: 0 12px 0 0;
  padding: 8px 26px;
}
.custom-message-box .el-message-box__btns .el-button + .el-button {
  margin-left: 0;
}

/* 表格头部字体粗细 */
.el-table thead th {
  font-weight: 400;
}

/* 表格边框-圆角 */
.el-table.custom-table-border .el-table__border-left-patch,
.el-table.custom-table-border.el-table--border:before,
.el-table.custom-table-border.el-table--border:after {
  width: 0;
}
.el-table.custom-table-border.el-table--border .el-table__inner-wrapper:before {
  height: 0;
}
.el-table.custom-table-border.el-table--border .el-table__row:last-child .el-table__cell {
  border-bottom: 1px solid transparent;
}
.el-table.custom-table-border.el-table--border .el-table__cell:last-child {
  border-right: 0;
}
.el-table.custom-table-border {
  border: 1px solid var(--el-table-border-color);
}
.el-table.custom-table-border.radius-box {
  border-radius: var(--table-border-radius);
}
.el-table.custom-table-border.radius-top {
  border-radius: var(--table-border-radius) var(--table-border-radius) 0 0;
}
.el-table.custom-table-border.radius-bottom {
  border-radius: 0 0 var(--table-border-radius) var(--table-border-radius);
}

/* 分页圆角 */
.el-pagination.custom-pagination-radius {
  --el-pagination-border-radius: 6px;
}
.el-pagination.custom-pagination-radius .el-input {
  --el-input-border-radius: 6px;
}

/* 图片预览处理 */
.el-image-viewer__wrapper .el-image-viewer__btn.el-image-viewer__actions {
  display: none;
}

/* tag主题 */
.el-tag {
  --el-color-primary: #559ec5;
  --el-color-primary-light-1: #66a7ca;
  --el-color-primary-light-2: #77b1d0;
  --el-color-primary-light-3: #88bbd6;
  --el-color-primary-light-4: #99c4dc;
  --el-color-primary-light-5: #aacee2;
  --el-color-primary-light-6: #bbd8e7;
  --el-color-primary-light-7: #cce1ed;
  --el-color-primary-light-8: #ddebf3;
  --el-color-primary-light-9: #eef5f9;

  --el-color-warning: #c89668;
  --el-color-warning-light-1: #cda077;
  --el-color-warning-light-2: #d3ab86;
  --el-color-warning-light-3: #d8b595;
  --el-color-warning-light-4: #dec0a4;
  --el-color-warning-light-5: #e3cab3;
  --el-color-warning-light-6: #e9d5c2;
  --el-color-warning-light-7: #eedfd1;
  --el-color-warning-light-8: #f4eae0;
  --el-color-warning-light-9: #f9f4ef;
}
/* el-avatar自定义高比调整高度*/
.el-avatar > img {
  height: auto;
}
/* el-popper 自定义样式 */
.el-popper.is-custom-danger {
  z-index: 100 !important;
  padding: 6px 12px;
  background: var(--el-color-danger-light-9);
  border: 1px solid var(--el-color-danger-light-5);
}
.el-popper.is-custom-danger .el-popper__arrow::before {
  background: #fef0f0;
  border: 1px solid #fab6b6;
  right: 0;
}

/* 根据图片的实际宽高比调整高度 */
@media (orientation: portrait) {
  /* 垂直 */
  .el-image-viewer__wrapper .el-image-viewer__canvas .el-image-viewer__img {
    max-width: 88vmin !important;
    max-height: 88vh !important;
  }
}

@media (orientation: landscape) {
  /* 水平 */
  .el-image-viewer__wrapper .el-image-viewer__canvas .el-image-viewer__img {
    max-width: 88vw !important;
    max-height: 88vmin !important;
  }
}

/* H5端-hover样式 */
@media (min-width: 0px) and (max-width: 768px) {
  .el-button {
    --el-button-hover-bg-color: var(--el-button-bg-color) !important;
    --el-button-hover-text-color: var(--el-button-text-color) !important;
    --el-button-hover-border-color: var(--el-button-border-color) !important;
  }
  .el-checkbox-button__inner:hover {
    color: var(--el-button-text-color, var(--el-text-color-regular));
  }
}
