<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4640925" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">外币</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">帮助中心</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">禁止</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe713;</span>
                <div class="name">提示</div>
                <div class="code-name">&amp;#xe713;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8c7;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe8c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8c6;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe8c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe640;</span>
                <div class="name">标签1</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61c;</span>
                <div class="name">回到顶部</div>
                <div class="code-name">&amp;#xe61c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">英国国旗</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">西班牙</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">法国国旗</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe610;</span>
                <div class="name">美国国旗</div>
                <div class="code-name">&amp;#xe610;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe625;</span>
                <div class="name">德国</div>
                <div class="code-name">&amp;#xe625;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6e3;</span>
                <div class="name">意大利</div>
                <div class="code-name">&amp;#xe6e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe703;</span>
                <div class="name">加拿大</div>
                <div class="code-name">&amp;#xe703;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe611;</span>
                <div class="name">银行卡</div>
                <div class="code-name">&amp;#xe611;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">暂无图片</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">我的订单</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b2;</span>
                <div class="name">模特</div>
                <div class="code-name">&amp;#xe7b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe653;</span>
                <div class="name">个人中心</div>
                <div class="code-name">&amp;#xe653;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe699;</span>
                <div class="name">财务管理</div>
                <div class="code-name">&amp;#xe699;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe711;</span>
                <div class="name">置底</div>
                <div class="code-name">&amp;#xe711;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xee2d;</span>
                <div class="name">更多-write</div>
                <div class="code-name">&amp;#xee2d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">影响者</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe626;</span>
                <div class="name">更多</div>
                <div class="code-name">&amp;#xe626;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xec4d;</span>
                <div class="name">安全</div>
                <div class="code-name">&amp;#xec4d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65e;</span>
                <div class="name">成年人</div>
                <div class="code-name">&amp;#xe65e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe68d;</span>
                <div class="name">更多图片</div>
                <div class="code-name">&amp;#xe68d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe697;</span>
                <div class="name">对公转账</div>
                <div class="code-name">&amp;#xe697;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65f;</span>
                <div class="name">40对公转账</div>
                <div class="code-name">&amp;#xe65f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe65a;</span>
                <div class="name">儿童-06</div>
                <div class="code-name">&amp;#xe65a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">定位</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe676;</span>
                <div class="name">女性</div>
                <div class="code-name">&amp;#xe676;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">首页</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">支付宝支付</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe8d1;</span>
                <div class="name">婴儿票支持</div>
                <div class="code-name">&amp;#xe8d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62a;</span>
                <div class="name">银行卡转账</div>
                <div class="code-name">&amp;#xe62a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe710;</span>
                <div class="name">拍摄</div>
                <div class="code-name">&amp;#xe710;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66f;</span>
                <div class="name">money</div>
                <div class="code-name">&amp;#xe66f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b6;</span>
                <div class="name">收藏</div>
                <div class="code-name">&amp;#xe6b6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6ce;</span>
                <div class="name">老年人</div>
                <div class="code-name">&amp;#xe6ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xee2c;</span>
                <div class="name">男性</div>
                <div class="code-name">&amp;#xee2c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">素人</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xeb7d;</span>
                <div class="name">微信支付</div>
                <div class="code-name">&amp;#xeb7d;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1734516877820') format('woff2'),
       url('iconfont.woff?t=1734516877820') format('woff'),
       url('iconfont.ttf?t=1734516877820') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-Dollar"></span>
            <div class="name">
              外币
            </div>
            <div class="code-name">.icon-Dollar
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bangzhuzhongxin"></span>
            <div class="name">
              帮助中心
            </div>
            <div class="code-name">.icon-bangzhuzhongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jinzhi"></span>
            <div class="name">
              禁止
            </div>
            <div class="code-name">.icon-jinzhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tishi"></span>
            <div class="name">
              提示
            </div>
            <div class="code-name">.icon-tishi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang4"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.icon-shoucang4
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye1"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.icon-shouye1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-tianchongxing-"></span>
            <div class="name">
              标签1
            </div>
            <div class="code-name">.icon-tianchongxing-
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-huidaodingbu"></span>
            <div class="name">
              回到顶部
            </div>
            <div class="code-name">.icon-huidaodingbu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingguoguoqi"></span>
            <div class="name">
              英国国旗
            </div>
            <div class="code-name">.icon-yingguoguoqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xibanya"></span>
            <div class="name">
              西班牙
            </div>
            <div class="code-name">.icon-xibanya
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-faguoguoqi"></span>
            <div class="name">
              法国国旗
            </div>
            <div class="code-name">.icon-faguoguoqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-meiguoguoqi"></span>
            <div class="name">
              美国国旗
            </div>
            <div class="code-name">.icon-meiguoguoqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-DE"></span>
            <div class="name">
              德国
            </div>
            <div class="code-name">.icon-DE
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yidali"></span>
            <div class="name">
              意大利
            </div>
            <div class="code-name">.icon-yidali
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jianada"></span>
            <div class="name">
              加拿大
            </div>
            <div class="code-name">.icon-jianada
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-iconfontjikediancanicon20"></span>
            <div class="name">
              银行卡
            </div>
            <div class="code-name">.icon-iconfontjikediancanicon20
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zanwutupian"></span>
            <div class="name">
              暂无图片
            </div>
            <div class="code-name">.icon-zanwutupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-wodedingdan"></span>
            <div class="name">
              我的订单
            </div>
            <div class="code-name">.icon-wodedingdan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mote1"></span>
            <div class="name">
              模特
            </div>
            <div class="code-name">.icon-mote1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gerenzhongxin"></span>
            <div class="name">
              个人中心
            </div>
            <div class="code-name">.icon-gerenzhongxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-caiwuguanli"></span>
            <div class="name">
              财务管理
            </div>
            <div class="code-name">.icon-caiwuguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhidi"></span>
            <div class="name">
              置底
            </div>
            <div class="code-name">.icon-zhidi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo-copy"></span>
            <div class="name">
              更多-write
            </div>
            <div class="code-name">.icon-gengduo-copy
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingxinagzhe"></span>
            <div class="name">
              影响者
            </div>
            <div class="code-name">.icon-yingxinagzhe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduo"></span>
            <div class="name">
              更多
            </div>
            <div class="code-name">.icon-gengduo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-anquan"></span>
            <div class="name">
              安全
            </div>
            <div class="code-name">.icon-anquan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-chengnianren"></span>
            <div class="name">
              成年人
            </div>
            <div class="code-name">.icon-chengnianren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gengduotupian"></span>
            <div class="name">
              更多图片
            </div>
            <div class="code-name">.icon-gengduotupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-duigongzhuanzhang"></span>
            <div class="name">
              对公转账
            </div>
            <div class="code-name">.icon-duigongzhuanzhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-a-40duigongzhuanzhang"></span>
            <div class="name">
              40对公转账
            </div>
            <div class="code-name">.icon-a-40duigongzhuanzhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ertong-06"></span>
            <div class="name">
              儿童-06
            </div>
            <div class="code-name">.icon-ertong-06
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dingwei"></span>
            <div class="name">
              定位
            </div>
            <div class="code-name">.icon-dingwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nvxing"></span>
            <div class="name">
              女性
            </div>
            <div class="code-name">.icon-nvxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shouye"></span>
            <div class="name">
              首页
            </div>
            <div class="code-name">.icon-shouye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-zhifubaozhifu"></span>
            <div class="name">
              支付宝支付
            </div>
            <div class="code-name">.icon-zhifubaozhifu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yingerpiaozhichi"></span>
            <div class="name">
              婴儿票支持
            </div>
            <div class="code-name">.icon-yingerpiaozhichi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-yinhangqiazhuanzhang"></span>
            <div class="name">
              银行卡转账
            </div>
            <div class="code-name">.icon-yinhangqiazhuanzhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.icon-shoucang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang1"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.icon-shoucang1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-paishe"></span>
            <div class="name">
              拍摄
            </div>
            <div class="code-name">.icon-paishe
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-money"></span>
            <div class="name">
              money
            </div>
            <div class="code-name">.icon-money
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang2"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.icon-shoucang2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-shoucang3"></span>
            <div class="name">
              收藏
            </div>
            <div class="code-name">.icon-shoucang3
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-laonianren"></span>
            <div class="name">
              老年人
            </div>
            <div class="code-name">.icon-laonianren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-nanxing"></span>
            <div class="name">
              男性
            </div>
            <div class="code-name">.icon-nanxing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-suren"></span>
            <div class="name">
              素人
            </div>
            <div class="code-name">.icon-suren
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weixinzhifu"></span>
            <div class="name">
              微信支付
            </div>
            <div class="code-name">.icon-weixinzhifu
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-Dollar"></use>
                </svg>
                <div class="name">外币</div>
                <div class="code-name">#icon-Dollar</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bangzhuzhongxin"></use>
                </svg>
                <div class="name">帮助中心</div>
                <div class="code-name">#icon-bangzhuzhongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jinzhi"></use>
                </svg>
                <div class="name">禁止</div>
                <div class="code-name">#icon-jinzhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tishi"></use>
                </svg>
                <div class="name">提示</div>
                <div class="code-name">#icon-tishi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang4"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#icon-shoucang4</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye1"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#icon-shouye1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tianchongxing-"></use>
                </svg>
                <div class="name">标签1</div>
                <div class="code-name">#icon-tianchongxing-</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-huidaodingbu"></use>
                </svg>
                <div class="name">回到顶部</div>
                <div class="code-name">#icon-huidaodingbu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingguoguoqi"></use>
                </svg>
                <div class="name">英国国旗</div>
                <div class="code-name">#icon-yingguoguoqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xibanya"></use>
                </svg>
                <div class="name">西班牙</div>
                <div class="code-name">#icon-xibanya</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-faguoguoqi"></use>
                </svg>
                <div class="name">法国国旗</div>
                <div class="code-name">#icon-faguoguoqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-meiguoguoqi"></use>
                </svg>
                <div class="name">美国国旗</div>
                <div class="code-name">#icon-meiguoguoqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-DE"></use>
                </svg>
                <div class="name">德国</div>
                <div class="code-name">#icon-DE</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yidali"></use>
                </svg>
                <div class="name">意大利</div>
                <div class="code-name">#icon-yidali</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jianada"></use>
                </svg>
                <div class="name">加拿大</div>
                <div class="code-name">#icon-jianada</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-iconfontjikediancanicon20"></use>
                </svg>
                <div class="name">银行卡</div>
                <div class="code-name">#icon-iconfontjikediancanicon20</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zanwutupian"></use>
                </svg>
                <div class="name">暂无图片</div>
                <div class="code-name">#icon-zanwutupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-wodedingdan"></use>
                </svg>
                <div class="name">我的订单</div>
                <div class="code-name">#icon-wodedingdan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mote1"></use>
                </svg>
                <div class="name">模特</div>
                <div class="code-name">#icon-mote1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gerenzhongxin"></use>
                </svg>
                <div class="name">个人中心</div>
                <div class="code-name">#icon-gerenzhongxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-caiwuguanli"></use>
                </svg>
                <div class="name">财务管理</div>
                <div class="code-name">#icon-caiwuguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhidi"></use>
                </svg>
                <div class="name">置底</div>
                <div class="code-name">#icon-zhidi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo-copy"></use>
                </svg>
                <div class="name">更多-write</div>
                <div class="code-name">#icon-gengduo-copy</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingxinagzhe"></use>
                </svg>
                <div class="name">影响者</div>
                <div class="code-name">#icon-yingxinagzhe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduo"></use>
                </svg>
                <div class="name">更多</div>
                <div class="code-name">#icon-gengduo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-anquan"></use>
                </svg>
                <div class="name">安全</div>
                <div class="code-name">#icon-anquan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-chengnianren"></use>
                </svg>
                <div class="name">成年人</div>
                <div class="code-name">#icon-chengnianren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gengduotupian"></use>
                </svg>
                <div class="name">更多图片</div>
                <div class="code-name">#icon-gengduotupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-duigongzhuanzhang"></use>
                </svg>
                <div class="name">对公转账</div>
                <div class="code-name">#icon-duigongzhuanzhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-a-40duigongzhuanzhang"></use>
                </svg>
                <div class="name">40对公转账</div>
                <div class="code-name">#icon-a-40duigongzhuanzhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ertong-06"></use>
                </svg>
                <div class="name">儿童-06</div>
                <div class="code-name">#icon-ertong-06</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dingwei"></use>
                </svg>
                <div class="name">定位</div>
                <div class="code-name">#icon-dingwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nvxing"></use>
                </svg>
                <div class="name">女性</div>
                <div class="code-name">#icon-nvxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shouye"></use>
                </svg>
                <div class="name">首页</div>
                <div class="code-name">#icon-shouye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-zhifubaozhifu"></use>
                </svg>
                <div class="name">支付宝支付</div>
                <div class="code-name">#icon-zhifubaozhifu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yingerpiaozhichi"></use>
                </svg>
                <div class="name">婴儿票支持</div>
                <div class="code-name">#icon-yingerpiaozhichi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-yinhangqiazhuanzhang"></use>
                </svg>
                <div class="name">银行卡转账</div>
                <div class="code-name">#icon-yinhangqiazhuanzhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#icon-shoucang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang1"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#icon-shoucang1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-paishe"></use>
                </svg>
                <div class="name">拍摄</div>
                <div class="code-name">#icon-paishe</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-money"></use>
                </svg>
                <div class="name">money</div>
                <div class="code-name">#icon-money</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang2"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#icon-shoucang2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-shoucang3"></use>
                </svg>
                <div class="name">收藏</div>
                <div class="code-name">#icon-shoucang3</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-laonianren"></use>
                </svg>
                <div class="name">老年人</div>
                <div class="code-name">#icon-laonianren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-nanxing"></use>
                </svg>
                <div class="name">男性</div>
                <div class="code-name">#icon-nanxing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-suren"></use>
                </svg>
                <div class="name">素人</div>
                <div class="code-name">#icon-suren</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weixinzhifu"></use>
                </svg>
                <div class="name">微信支付</div>
                <div class="code-name">#icon-weixinzhifu</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
