@import '@/assets/fonts/index.css';
@import '@/assets/css/button.css';
@import '@/assets/css/elementui.css';
@import '@/assets/css/add-to-car.css';

#app {
  width: 100%;
  height: 100%;
  font-weight: normal;
}
body { 
  max-width: 2560px;
  width: 100vw !important;
  min-height: 100vh;
  transition:
    color 0.5s,
    background-color 0.5s;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family:
    'wn-font',
    'PingFang SC',
    -apple-system,
    system-ui,
    Segoe UI,
    Roboto,
    Ubuntu,
    Cantarell,
    Noto Sans,
    sans-serif,
    SF UI Text,
    Arial,
    Hiragino Sans GB,
    Microsoft YaHei,
    WenQuanYi Micro Hei;
  font-size: 15px;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0 auto;
  padding: 0;
}

.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.ban-modal {
  position: relative;
}
.ban-modal::after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1888;
}
.move-modal {
  position: relative;
}
.move-modal::after {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1888;
  cursor: move;
}
/* 针对 WebKit 浏览器 */
body::-webkit-scrollbar,
textarea::-webkit-scrollbar,
div::-webkit-scrollbar,
ul::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 8px; /*高宽分别对应横竖滚动条的尺寸*/
  height: 8px;
}
body::-webkit-scrollbar-thumb,
textarea::-webkit-scrollbar-thumb,
div::-webkit-scrollbar-thumb,
ul::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 15px;
  /* box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2); */
  background: #669fc64d;
}
body::-webkit-scrollbar-track,
textarea::-webkit-scrollbar-track,
div::-webkit-scrollbar-track,
ul::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  box-shadow: inset 0 0 5px #00000033;
  background: #ffffffc4;
}
/* Firefox 的滚动条样式 */
body,textarea,div,ul {
  scrollbar-width: thin; /* 可以是 auto, thin 或 none */
  scrollbar-color: #669fc64d #eeeeeec4; /* 第一个颜色是滚动条的颜色，第二个是轨道的颜色 */
}

/*弹性布局*/
.flex-center {
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-between {
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-start {
  display: -webkit-flex;
  display: flex;
  justify-content: start;
  align-items: center;
}
.flex-end {
  display: -webkit-flex;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.flex-around {
  display: -webkit-flex;
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.flex-column {
  display: flex;
  align-items: center;
  flex-direction: column;
}
.gap-5 {
  gap: 5px;
}
.gap-10 {
  gap: 10px;
}

/* padding */
.pd-5 {
  padding: 5px;
}
.pd-8 {
  padding: 8px;
}
.pd-10 {
  padding: 10px;
}
.pd-15 {
  padding: 15px;
}

/*多行超出省略号*/
.more-ell {
  --l: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: var(--l);
  line-clamp: var(--l);
  -webkit-box-orient: vertical;
  text-align: left;
}
/*超出一行省略号*/
.one-ell {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
}

/* 模板字符串中识别空白符换行符等 */
.template-pre {
  white-space: pre-wrap;
}
/* 文本不换行 */
.text-n-no {
  white-space: nowrap;
}
/* 文本自动换行 */
.text-n-auto {
  word-wrap: break-word;
  word-break: normal;
}
/* 文本强制换行 */
.text-n-all {
  word-break: break-all;
}
/* 禁用文本选择 */
.no-select {
  user-select: none; /* 支持大多数主流浏览器 */
  -webkit-user-select: none; /* Safari */
  -moz-user-select: none; /* Firefox */
}
