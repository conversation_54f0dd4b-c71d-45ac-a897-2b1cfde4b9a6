@font-face {
  font-family: "wn-font";
  src: url('https://pstatic.woniu.video/static/assets/fonts/AlibabaPuHuiTi-3-55-Regular.woff2') format('woff2'), /* 优先加载WOFF2 */
       url('https://pstatic.woniu.video/static/assets/fonts/AlibabaPuHuiTi-3-55-Regular.woff') format('woff'),    /* 备选WOFF */
       url('https://pstatic.woniu.video/static/assets/fonts/AlibabaPuHuiTi-3-55-Regular.ttf') format('truetype'); /* 兜底TTF */
}
@font-face {
  font-family: "wn-font-bold";
  src: url('https://pstatic.woniu.video/static/assets/fonts/AlibabaPuHuiTi-3-65-Medium.woff2') format('woff2'),
       url('https://pstatic.woniu.video/static/assets/fonts/AlibabaPuHuiTi-3-65-Medium.woff') format('woff'),
       url('https://pstatic.woniu.video/static/assets/fonts/AlibabaPuHuiTi-3-65-Medium.ttf') format('truetype');
}
@font-face {
  font-family: "D-DIN";
  src: url('https://pstatic.woniu.video/static/assets/fonts/D-DIN.otf') format('opentype');
}
@font-face {
  font-family: "Source-Han-Sans-bold";
  src: url('https://pstatic.woniu.video/static/assets/fonts/SourceHanSansCN-Medium.otf') format('opentype');
}
@font-face {
  font-family: "Source-Han-Sans";
  src: url('https://pstatic.woniu.video/static/assets/fonts/SourceHanSansCN-Regular.otf') format('opentype');
}