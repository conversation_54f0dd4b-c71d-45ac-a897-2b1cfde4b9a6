:deep(.el-upload-list__item) {
  width: 80px;
  height: 80px;
}
:deep(.disabled) {
  .el-upload--picture-card {
    display: none;
  }
}
:deep(.el-upload--picture-card) {
  width: 80px;
  height: 80px;
}
:deep(.el-radio-button) {
  &.is-active {
    box-shadow: none;
  }

  .el-radio-button__inner {
    border-left: var(--el-border);
    border-radius: var(--el-border-radius-base);
  }
}
:deep(.el-radio-group) {
  gap: 6px 10px;

  .el-radio-button.is-active {
    .el-radio-button__inner {
      box-shadow: none;
    }
  }
}
:deep(.el-checkbox-button) {
  &.is-checked {
    box-shadow: none;
  }

  .el-checkbox-button__inner {
    border: var(--el-border);
    border-radius: var(--el-border-radius-base);
  }
}
:deep(.el-checkbox-group) {
  align-items: center;
  display: inline-flex;
  flex-wrap: wrap;
  gap: 6px 10px;

  .el-checkbox-button {
    &.is-checked {
      .el-checkbox-button__inner {
        box-shadow: none;
      }
    }
  }
}
