// transition css

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
    transition: all .5s ease;
}

.fade-transform-enter-form {
    opacity: 0;
    transform: translateX(20px);
}

.fade-transform-leave-to {
    opacity: 0;
    transform: translateX(-20px);
}


/* breadcrumb transition */
.breadcrumb-move,
.breadcrumb-enter-active,
.breadcrumb-leave-active {
    transition: all .5s ease;
}

.breadcrumb-enter-from {
    opacity: 0;
    transform: translateX(30px);
}

.breadcrumb-leave-to {
    opacity: 0;
    transform: translateX(-20px);
}

.breadcrumb-leave-active {
    position: absolute;
}
