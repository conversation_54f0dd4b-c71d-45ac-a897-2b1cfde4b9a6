
.preview-message-box {
  width: fit-content;
  max-width: 100vw;
  max-height: 100vh;
  background: transparent;
  overflow: revert;
  box-shadow: none;

  .el-message-box__header {
    .el-message-box__headerbtn {
      background-color: #606266;
      border-radius: 50%;
      height: 44px;
      width: 44px;
      font-size: 13px;
      right: -25px;
      top: -15px;
      
      .el-message-box__close {
        color: #fff;
        font-size: 24px;
        margin: 2px;
      }
    }
  }

  .el-message-box__container {

    .el-message-box__message {
      p {
        img {
          max-width: 80vw;
          max-height: 80vh;
          object-fit: contain;
        }
        .preview-auto {
          width: auto;
          height: auto;
        }
        .square {
          width: 80vmin;
          height: 80vmin;
        }
        .rectangle-v {
          width: 80vmin;
          height: 107vmin;
        }
        .rectangle-h {
          width: 107vmin;
          height: 80vmin;
        }
      }
    }
  }
  
}