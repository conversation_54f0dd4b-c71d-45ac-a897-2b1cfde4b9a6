@use "sass:map";  // map.get()
@use "sass:list"; // list.nth()
@use "sass:meta"; // meta.type-of()

$breakPoints: (
  'phone': (328px, 768px),
  'pad': (769px, 1199px),
  'notebook': (1200px, 1900px),
  'pc': 1901px
);

@mixin mediaTo($media) {
  @if not map.has-key($breakPoints, $media) {
    @error "Unknown media type: #{$media}. Available types: #{map.keys($breakPoints)}";
  }

  $config: map.get($breakPoints, $media);

  @if meta.type-of($config) == 'list' {
    @media (min-width: list.nth($config, 1)) and (max-width: list.nth($config, 2)) {
      @content;
    }
  } @else {
    @media (min-width: $config) {
      @content;
    }
  }
}