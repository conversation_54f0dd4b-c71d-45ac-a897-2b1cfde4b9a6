import type { RouteRecordRaw } from 'vue-router'

/*
  page.json
  "title": "",
  "icon": "",
  "keywords": "", // seo 关键词
  "description": "", // seo 描述
  "menu": "", // 0：目录，1：菜单，默认""不放入侧边菜单栏
  "menuOpeneds": true, // 目录菜单栏保持展开
  "menuOrder": 0,
  "activeMenu": "",// 子路由激活父级菜单
  "parentId": "",// 将页面指定到父级某个路由的children下
  "redirect": '/index', // 重定向
  "params": '/:id', // 路径传参
  "auth": true, // 是否要权限
  "notSaveNav": true, // 离开当前路由不保存在tabsNav
  "notShowNav": true, // 当前路由不显示在tabsNav
  "win": true, // 打开新窗口
*/
type PageJson = {
  menu?: '' | '0' | '1'
  menuOpeneds?: boolean
  menuOrder?: number
  redirect?: string
  params?: string
  notSaveNav?: string
} & MenuMeta

type AuthType = 'ownerAcc' | 'vip' | 'viped'

type MenuMeta = {
  title: string
  keywords?: string
  description?: string
  icon?: string
  activeMenu?: string
  parentId?: string
  redirect?: string
  auth?: boolean | Array<AuthType>
  notShowNav?: boolean
  win?: boolean
  menuOpeneds?: boolean
}

type MenuData = {
  path: string
  name: string
  menu: string
  menuOrder: number
  meta: MenuMeta
}

type ImportGlob = Record<string, PageJson>

const pages: ImportGlob = import.meta.glob('../views/**/page.json', {
  eager: true,
  import: 'default',
})
// console.log(pages)
const components: ImportGlob = import.meta.glob('../views/**/index.vue', {
  eager: true,
  import: 'default',
})

const menuData: MenuData[] = []

const notSaveNavRouter: string[] = []

const routerData: RouteRecordRaw[] = Object.entries<PageJson>(pages).map(([path, data]): RouteRecordRaw => {
  const compPath = path.replace('page.json', 'index.vue')

  path = path.replace('../views', '').replace('/page.json', '') || '/'

  const name = path.split('/').filter(Boolean).join('-').replace('-index', 'Index')

  let { menu, menuOpeneds, menuOrder, params, notSaveNav, redirect, ...meta } = data

  if (menu) {
    menuData.push({
      path,
      name,
      menu,
      menuOrder: menuOrder || 0,
      meta: {
        ...meta,
        redirect,
        menuOpeneds,
      },
    })
  }
  if (params) {
    path += params
  }
  if (notSaveNav) {
    notSaveNavRouter.push(path)
  }
  return {
    path,
    name,
    component: components[compPath],
    meta,
    redirect,
    children: [],
  }
})
// console.log(routerData)
const routerWhiteList = [
  '/',
  '/index',
  '/404',
  '/error',
  '/access-denied',
  '/wechat/login',
  '/wechat/accredit',
  '/wechat/invite',
  '/another/pay',
  '/agreement',
  '/vip',
  '/workbench/info',
  '/activity/fission'
]

export {
  type PageJson,
  type MenuMeta,
  type AuthType,
  type MenuData,
  routerData,
  menuData,
  notSaveNavRouter,
  routerWhiteList,
}
