import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { getToken } from '@/utils/auth'
import { recursionMenuTree } from '@/utils/util'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { routerData, type AuthType, routerWhiteList } from './routerData'
import { openLogin } from '@/hooks/useLogin'

const routeList = recursionMenuTree(routerData)

NProgress.configure({ showSpinner: false })

const routes: RouteRecordRaw[] = [
  // {
  //   path: '/login',
  //   name: 'login',
  //   component: () => import('@/views/login/index.vue'),
  // },
  {
    path: '/',
    redirect: '/index',
  },
  {
    path: '/404',
    name: '404',
    meta: {
      title: '404',
    },
    component: () => import('@/views/errorPages/404/index.vue'), // 注意这里要带上 文件后缀.vue
  },
  {
    path: '/error',
    name: 'error',
    meta: {
      title: 'error',
    },
    component: () => import('@/views/errorPages/error/index.vue'),
  },
  {
    path: '/access-denied',
    name: 'access-denied',
    meta: {
      title: '无权访问',
    },
    component: () => import('@/views/errorPages/accessDenied/index.vue'),
  },
  {
    path: '/help',
    name: 'help',
    meta: {
      title: '帮助中心',
    },
    component: () => import('@/views/help/index.vue'),
  },
  {
    path: '/vip',
    name: 'vip',
    meta: {
      title: '会员中心',
    },
    component: () => import('@/views/vip/index.vue'),
  },
  {
    path: '/model/details/:modelId',
    name: 'model-details',
    meta: {
      title: '模特详情',
    },
    component: () => import('@/views/model/details/index.vue'),
  },
  {
    path: '/index',
    name: 'index',
    redirect: '/model/list', // 动态路由 重定向
    component: () => import('@/views/index.vue'),
    children: [
      {
        path: '/page',
        name: 'page',
        meta: {
          title: '首页',
        },
        component: () => import('@/views/indexPage/index.vue'),
      },
      ...routeList,
    ],
  },
  {
    path: '/agreement',
    name: 'agreement',
    meta: {
      title: '协议信息',
    },
    component: () => import('@/views/agreement/index.vue'),
  },
  {
    path: '/wechat/login',
    name: 'wechat-login',
    meta: {
      title: '登录/注册',
    },
    component: () => import('@/views/wechatPage/login.vue'),
  },
  {
    path: '/wechat/accredit',
    name: 'wechat-accredit',
    meta: {
      title: '微信认证',
    },
    component: () => import('@/views/wechatPage/accredit.vue'),
  },
  {
    path: '/wechat/invite',
    name: 'wechat-invite',
    meta: {
      title: '邀请你加入',
    },
    component: () => import('@/views/wechatPage/invite.vue'),
  },
  {
    path: '/another/pay',
    name: 'another-pay',
    meta: {
      title: '蜗牛代付',
    },
    component: () => import('@/views/anotherPay/index.vue'),
  },
  {
    path: '/atv',
    name: 'atv',
    meta: {
      title: '活动中心',
    },
    redirect: '/activity/fission',
    component: () => import('@/views/activity/index.vue'),
    children: [
      {
        path: '/activity/fission',
        name: 'activity-fission',
        meta: {
          title: '邀请新商家入驻，领取现金奖励',
        },
        component: () => import('@/views/activity/fission.vue'),
      },
    ],
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
})

// 路由频率限制
let routerCount = 0
let lastRoute = 0

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  const store = useUserStore()
  // console.log(to, from)
  // 路由白名单
  if (routerWhiteList.includes(to.path)) {
    console.log('routerWhiteList')
    if (!getToken()) store.removeInfo()
    NProgress.done()
    next()
    return
  }
  let routeTime = new Date().getTime()
  if (routeTime - lastRoute < 500) {
    routerCount++
  } else {
    routerCount = 0
  }
  lastRoute = routeTime
  if (routerCount > 10) {
    next('/error')
    return
  }
  // 有token
  if (getToken()) {
    const user = store.userInfo
    // console.log('用户', user.account ? user.account : '未登陆')
    // 获取用户信息
    if (!user.account || !from.name || (user.isOwnerAccount === 1 && user.businessVO?.scale == null)) {
      try {
        await store.getUserInfo()
      } catch (e) {
        console.error('router-getUserInfo')
        NProgress.done()
        if (e === 'network') {
          next('/error?err=network')
          return
        }
        next('/error')
        return
      }
    }
    // await store.addRoute()
    // 判断是否要权限
    if (to.meta.auth) {
      if (store.checkAuth(to.meta.auth as boolean | AuthType[])) {
        NProgress.done()
        next()
      } else {
        ElMessage.error('无权限访问！')
        NProgress.done()
        next('/access-denied')
      }
    } else if (!to.name) {
      // 根据to.name来判断是否为动态路由
      if (routerData.findIndex(item => item.path === to.path) !== -1) {
        next({ ...to, replace: true })
      } else {
        next('/404')
      }
    } else if (to.path.length > 1 && to.path.lastIndexOf('/') === to.path.length - 1) {
      // 去掉地址末尾的/
      NProgress.done()
      next(to.path.slice(0, -1))
    } else {
      NProgress.done()
      next()
    }
  } else if (to.path === '/model/list' || to.name === 'model-details') {
    // 模特库
    store.removeInfo()
    NProgress.done()
    next()
  } else {
    //无用户信息
    store.removeInfo()
    NProgress.done()
    ElMessage.error('请先登陆！')
    next('/')
  }
})

router.afterEach(to => {
  const { title, keywords, description } = to.meta
  window.document.title = <string>title || '蜗牛海拍'
  if (keywords) {
    const metaKeywords: HTMLMetaElement | null = document.querySelector('meta[name="keywords"]')
    if (metaKeywords) {
      metaKeywords.content = <string>keywords || ''
    }
  }
  if (description) {
    const metaDescription: HTMLMetaElement | null = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.content = <string>description || ''
    }
  }
  if (
    !routerWhiteList.includes(to.path) &&
    to.path != '/model/list' &&
    to.name != 'model-details' &&
    !getToken()
  ) {
    console.log('router-openLogin')
    openLogin()
  }
})

export default router
