import axios from 'axios'
// 用 InternalAxiosRequestConfig 代替 AxiosRequestConfig
import type { AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import { ElNotification, ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import { getToken, removeToken } from '@/utils/auth'
import { tansParams } from '@/utils/util'
import cache from '@/plugins/cache'
import { fingerprint, waitFingerprint } from '@/utils/fingerprintjs2'
import errorCode from '@/utils/errorCode'
import { useUserStore } from '@/stores/modules/user'
import { routerWhiteList } from '@/router/routerData'

// 处理 类型“AxiosResponse<any，any>”上不存在属性“xxx”
declare module 'axios' {
  interface AxiosInstance {
    (config: AxiosRequestConfig): Promise<any>
  }
}

// 是否显示重新登录
export let isRelogin = { show: false }

const notCacheRequest = [
  '/order/pay/pay-info',
  '/order/another-pay/pay-info'
]

const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 1000 * 30,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
  },
})

// Request interceptors
service.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
    if (getToken() && !isToken) {
      config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      let url = config.url + '?' + tansParams(config.params)
      url = url.slice(0, -1)
      config.params = {}
      config.url = url
    }
    // 自定义特定请求头
    if (!fingerprint.value) {
      await waitFingerprint()
    }
    config.headers['w-n-k-x-fp'] = fingerprint.value
    config.headers['w-n-k-x-version'] = 'wnkx-dev'
    // config.headers['w-n-k-x-version'] = 'wnkx-dwy'
    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
      const requestObj = {
        url: config.url,
        data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
        time: new Date().getTime(),
      }
      const requestSize = Object.keys(JSON.stringify(requestObj)).length // 请求数据大小
      const limitSize = 5 * 1024 * 1024 // 限制存放数据5M
      if (requestSize >= limitSize) {
        console.warn(`[${config.url}]: ` + '请求数据大小超出允许的5M限制，无法进行防重复提交验证。')
        return config
      }
      if (config.url && notCacheRequest.includes(config.url)) {
        return config
      }
      const sessionObj = cache.session.getJSON('sessionObj')
      if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
        cache.session.setJSON('sessionObj', requestObj)
      } else {
        const s_url = sessionObj.url // 请求地址
        const s_data = sessionObj.data // 请求数据
        const s_time = sessionObj.time // 请求时间
        const interval = 200 // 间隔时间(ms)，小于此时间视为重复提交
        if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
          const message = '操作过于频繁！'
          console.warn(`[${s_url}]: ` + message)
          return Promise.reject(new Error(message))
        } else {
          cache.session.setJSON('sessionObj', requestObj)
        }
      }
    }
    return config
  },
  (error: any) => {
    console.log(error)
    Promise.reject(error)
  }
)

// Response interceptors
service.interceptors.response.use(
  async (res: AxiosResponse) => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode['default']
    // 二进制数据则直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
      return res
    }
    if (code === 401) {
      // if (!isRelogin.show && window.location.hash !== '/login') {
      //   isRelogin.show = true
      //   ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
      //     confirmButtonText: '重新登录',
      //     cancelButtonText: '取消',
      //     type: 'warning',
      //   })
      //     .then(() => {
      //       useUserStore().logout()
      //       location.href = '/login'
      //       isRelogin.show = false
      //     })
      //     .catch(() => {
      //       isRelogin.show = false
      //     })
      // }
      useUserStore().removeInfo()
      removeToken()
      // 未登录时处于白名单页面刷新此页面
      let href = routerWhiteList.includes(window.location.pathname) ? window.location.pathname : '/'
      // 防止无限循环
      let err401: any = sessionStorage.getItem('err401')
      if (err401) {
        err401 = JSON.parse(err401)
        if (err401.time - new Date().getTime() < 1000 * 60 && err401.count >= 3) {
          href = '/error'
          sessionStorage.removeItem('err401')
          console.error('---------- error 401 count >= 3 ----------')
        } else {
          err401.count = err401.count + 1
          err401.time = new Date().getTime()
          sessionStorage.setItem('err401', JSON.stringify(err401))
        }
      } else {
        sessionStorage.setItem('err401', JSON.stringify({
          count: 1,
          time: new Date().getTime()
        }))
      }
      location.href = href
      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')
    } else if (code === 500) {
      ElMessage({ message: msg, type: 'error', customClass: 'wn-err-msg' })
      return Promise.reject({ msg: new Error(msg), data: res.data })
    } else if (code === 601) {
      ElMessage({ message: msg, type: 'warning', customClass: 'wn-err-msg' })
      return Promise.reject(new Error(msg))
    } else if (code !== 200) {
      ElNotification.error({ title: msg })
      return Promise.reject('error')
    } else {
      return Promise.resolve(res.data)
    }
  },
  (error: any) => {
    console.log('err' + error)
    let { message } = error
    if (message == 'Network Error') {
      message = '网络请求连接异常'
    } else if (message.includes('timeout')) {
      message = '网络请求超时，请检查您的网络状态'
    } else if (message.includes('Request failed with status code')) {
      message = '网络请求' + message.substr(message.length - 3) + '异常'
    }
    ElMessage({ message: message, type: 'error', duration: 5 * 1000, customClass: 'wn-err-msg' })
    return Promise.reject(error)
  }
)

// 通用下载方法
export async function download(
  url: string,
  params: any,
  filename: any,
  config: AxiosRequestConfig<any> | undefined
) {
  let downloadLoadingInstance = ElLoading.service({
    text: '正在下载数据，请稍候',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  return service
    .post(`${url}?${tansParams(params)}`, null, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob',
      ...config,
    })
    .then(res => {
      let blob: Blob = res.data instanceof Blob ? res.data : new Blob([res.data])
      // const blob = new Blob([res.data])
      const FILE_NAME = res.headers['content-disposition'].split('filename=')[1]
      const link = document.createElement('a')
      link.download = filename || (FILE_NAME && decodeURIComponent(FILE_NAME))
      link.style.display = 'none'
      link.href = window.URL.createObjectURL(blob)
      link.click()
      link.remove()

      downloadLoadingInstance.close()
    })
    .catch(r => {
      console.error(r)
      ElMessage.error('下载文件出现错误，请联系管理员！')
      downloadLoadingInstance.close()
    })
}

export default service
