<template>
  <div id="help-menu">
    <div class="content">
      <div class="content-label">帮助中心</div>
      <div style="height: calc(100% - 56px); overflow-y: auto">
        <div class="content-item" style="margin-bottom: 12px">
          <div class="content-item-label">新手指南</div>
          <div class="content-item-name">
            <template v-if="dataTwo.length > 0" v-for="item in dataTwo" :key="item.id">
              <div
                class="text-value one-ell"
                @click="showHtml(item)"
                :class="{ active: curData.id == item.id }"
              >
                {{ item.name }}
              </div>
            </template>
            <template v-else>
              <div class="text-value" style="pointer-events: none">暂无内容</div>
            </template>
          </div>
        </div>
        <div class="content-item">
          <div class="content-item-label">常见问题</div>
          <div class="content-item-name">
            <template v-if="dataOne.length > 0" v-for="item in dataOne" :key="item.id">
              <div
                class="text-value one-ell"
                :class="{ active: curData.id == item.id }"
                @click="showHtml(item)"
              >
                {{ item.name }}
              </div>
            </template>
            <template v-else>
              <div class="text-value" style="pointer-events: none">暂无内容</div>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="text" v-loading="loading">
      <div class="text-title">
        <div class="text-title-name">{{ curData.name }}</div>
        <div class="text-title-time" v-if="curData.updateTime">更新时间：{{ curData.updateTime }}</div>
      </div>
      <div class="text-html" v-html="curData.content"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, getCurrentInstance, onMounted, watchEffect, watch } from 'vue'
import type { PropType } from 'vue'

const props = defineProps({
  dataOne: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  dataTwo: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
})

watchEffect(() => {
  if (props.dataTwo.length > 0) {
    curData.value = props.dataTwo[0]
  } else if (props.dataOne.length > 0) {
    curData.value = props.dataOne[0]
  }
})

const loading = ref(false)
interface ItemType {
  type: number
  id: number
  name: string
  content: any
  updateTime: string
  sort: number
}
const curData = ref({
  name: '',
  content: '',
  updateTime: '',
  sort: 0,
  id: '',
})
function showHtml(data: any) {
  loading.value = true
  curData.value = data
  setTimeout(() => {
    loading.value = false
  }, 100)
}
</script>

<style scoped lang="scss">
#help-menu {
  display: flex;
  height: 100%;
  padding-right: 8px;
  
  :deep(.el-menu) {
    border-right: 0;
    background-color: transparent;
    font-family: wn-font;
    --el-menu-sub-item-height: 42px;
    --el-menu-item-height: 42px;
    --el-menu-text-color: #6a7484;
    --el-menu-active-color: #09172f;

    .el-sub-menu {
      .el-sub-menu__icon-arrow {
        display: none;
      }
    }
  }
  .header {
    width: 100%;
    text-align: center;
    font-family: wn-font;
    font-size: 24px;
    // font-weight: bold;
    line-height: 37px;
    padding: 10px 0 6px;

    span {
      color: var(--el-color-primary);
    }

    img {
      width: 90px;
      cursor: pointer;
    }
  }
  .content {
    padding-left: 20px;
    border-radius: 5px;
    background: #ffffff6b;
    width: 220px;
    .content-label {
      font-size: 16px;
      text-align: left;
      margin: 15px 0;
    }
    .content-item {
      font-family: wn-font;
      //   margin-bottom: 5px;
      &-label {
        padding: 9px 2px 10px 20px;
        border-radius: 70px;
        border-top-right-radius: 0px;
        border-bottom-right-radius: 0px;
        background: #8fb9d5;
        color: #fff;
        font-size: 16px;
        margin-left: 2px;
      }
      .text-value {
        padding: 8px 0 8px 20px;
        font-size: 14px;
        color: #666;
        cursor: pointer;
      }
      .text-value:hover {
        color: #333;
      }
      .active {
        color: #333;
        font-size: 16px;
        // font-weight: 600;
      }
    }
  }
  .text {
    flex: 1;
    background-color: #ffffff6b;
    padding: 50px 25px 10px 25px;
    margin-left: 15px;
    border-radius: 5px;
    height: 100%;
    overflow: hidden;
    overflow: auto;
    .text-title {
      text-align: center;
      &-name {
        font-size: 18px;
      }
      &-time {
        font-size: 14px;
        color: #999;
      }
    }
    .text-html {
      margin-top: 20px;
    }
  }
}

:deep(.nav-el-menu-vertical:not(.el-menu--collapse)) {
  width: 180px;
  // min-height: 400px;
  padding-bottom: 4px;
}
</style>
