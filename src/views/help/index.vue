<template>
  <div class="index-page">
    <Main isLogo>
      <HelpMenu :dataOne="pageOneData" :dataTwo="pageTwoData" />
    </Main>
  </div>
</template>

<script setup lang="ts">
import Main from '@/components/layout/main.vue'
import HelpMenu from '@/views/help/components/HelpMenu.vue'
import { ref } from 'vue'
import { getHelpList } from '@/api/help'


interface ItemType {
  type: number
  id: number
  name: string
  content: any
  updateTime: string
  sort: number
}
const pageData = ref<any>({})
const pageOneData = ref<ItemType[]>([])
const pageTwoData = ref<ItemType[]>([])

function init() {
  getHelpList().then(res => {
    const tempList = res.data || []
    if (tempList && tempList.length > 0) {
      tempList.forEach((item: any) => {
        item.type == 1 ? (pageOneData.value = item.list) : (pageTwoData.value = item.list)
      })
    }
  })
}

init()
</script>

<style scoped lang="scss">
@use '@/styles/transition.scss';
.index-page {
  display: flex;
  overflow-x: auto;
  height: 100vh;
  background: var(--bg);

  .menu-box {
    position: relative;
    z-index: 9;
    height: 100%;
    // border-right: 1px solid #bababa8a;
    background-color: transparent;
    overflow: hidden;
    transition: 0.3s;
    // min-width: 64px;
    background-image: var(--menu-bg-image);
    background-size: 100% 100%;
    flex-shrink: 0;

    &:hover {
      overflow-y: overlay;
    }
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      /*滚动条里面小方块*/
      border-radius: 15px;
      box-shadow: inset 0 0 5px rgba(0, 220, 255, 0.2);
      background: #95c4fd99;
    }
    &::-webkit-scrollbar-track {
      /*滚动条里面轨道*/
      box-shadow: inset 0 0 5px rgba(0, 220, 255, 0.2);
      background: #ffffff33;
    }
  }

}
</style>
