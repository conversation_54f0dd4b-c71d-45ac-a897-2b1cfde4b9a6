<template>
  <div class="fission-page">
    <PageHeader />

    <div class="fission-bg">
      <div class="content-box">
        <img
          v-if="isMobileDevice()"
          src="@/assets/image/activity/fission/title-1.png"
          alt=""
          class="title-img"
        />
        <img v-else src="@/assets/image/activity/fission/title.png" alt="" class="title-img" />

        <div class="title"><span>活动介绍</span></div>
        <div class="card-box">
          <div :class="[isMobileDevice() ? 'flex-column' : 'flex-between']" class="card">
            <div class="card-content">
              <img class="img1" src="@/assets/image/activity/fission/card-bg-1.png" alt="" />
              <img class="img2" src="@/assets/image/activity/fission/card-icon-1.png" alt="" />
              <div class="flex-column card-text color1">
                <div>你可获得</div>
                <div v-if="fissionData.settleDiscountType == 1">¥{{ fissionData.settleDiscount }}/单</div>
                <div v-else-if="fissionData.settleDiscountType == 2">
                  {{ fissionData.settleDiscount }}%/单
                </div>
                <div v-else>-</div>
                <div>新商家会员开通成功奖励</div>
              </div>
            </div>
            <div class="card-content">
              <img class="img1" src="@/assets/image/activity/fission/card-bg-2.png" alt="" />
              <img class="img2" src="@/assets/image/activity/fission/card-icon-2.png" alt="" />
              <div class="flex-column card-text color2">
                <div>新商家可得</div>
                <div v-if="fissionData.memberDiscountType == 1">
                  会员费立减{{ fissionData.memberDiscount }}元
                </div>
                <div v-else-if="fissionData.memberDiscountType == 2">
                  会员费立享{{ fissionData.memberDiscount }}折
                </div>
                <div v-else>-</div>
                <div>新商家开通会员立享优惠</div>
              </div>
            </div>
          </div>

          <el-button class="act-btn" round type="primary" @click="openEntryDialog">立即参加活动</el-button>
        </div>

        <div class="title"><span>邀请指引</span></div>
        <div class="card-box" v-if="isMobileDevice()">
          <div class="flex-center step">
            <div class="num">01</div>
            <div>分享活动海报&nbsp;邀请好友参与</div>
          </div>
          <div class="step-img">
            <img :src="$picUrl + 'static/assets/customer/fission/step1-m.webp'" alt="" />
          </div>
          <div class="flex-center step">
            <div class="num">02</div>
            <div>好友使用种草码&nbsp;购买会员</div>
          </div>
          <div class="step-img">
            <img :src="$picUrl + 'static/assets/customer/fission/step2-m.webp'" alt="" />
          </div>
          <div class="flex-center step">
            <div class="num">03</div>
            <div>获得收益</div>
          </div>
          <div class="step-img">
            <img :src="$picUrl + 'static/assets/customer/fission/step3-m.webp'" alt="" />
          </div>
        </div>
        <div class="card-box" v-else>
          <div class="flex-center">
            <div class="flex-start step">
              <div class="num">01</div>
              <div>
                <div>分享活动海报</div>
                <div>邀请好友参与</div>
              </div>
            </div>
            <img class="step-arrow" src="@/assets/image/activity/fission/arrow.png" alt="" />
            <div class="flex-start step">
              <div class="num">02</div>
              <div>
                <div>好友使用种草码</div>
                <div>购买会员</div>
              </div>
            </div>
            <img class="step-arrow" src="@/assets/image/activity/fission/arrow.png" alt="" />
            <div class="flex-start step">
              <div class="num">03</div>
              <div>
                <div>获得收益</div>
              </div>
            </div>
          </div>
          <div class="flex-between step-img">
            <img :src="$picUrl + 'static/assets/customer/fission/step1.png'" alt="" />
            <img :src="$picUrl + 'static/assets/customer/fission/step2.png'" alt="" />
            <img :src="$picUrl + 'static/assets/customer/fission/step3.png'" alt="" />
          </div>
        </div>

        <div class="title"><span>活动规则</span></div>
        <div class="card-box">
          <div v-html="agreementContent"></div>
        </div>
      </div>
    </div>

    <!-- <LoginDialog :show-close="true" /> -->
  </div>
</template>

<script setup lang="ts">
// import LoginDialog from '@/views/login/LoginDialog.vue'
import PageHeader from '@/components/layout/header/PageHeader.vue'
import { ElMessage, ElLoading } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { getToken } from '@/utils/auth'
import { loginSuccessAction, openLogin } from '@/hooks/useLogin'
import { getFissionMemberDiscount, getFissionAgreement } from '@/api/activity/fission'
import { useRouter } from 'vue-router'
import { openFissionEntryDialog } from '@/hooks/useSeed'
import { ref } from 'vue'
import { isMobileDevice } from '@/utils/public'

const store = useUserStore()
const router = useRouter()

const fissionData = ref<any>({})
const agreementContent = ref<string>('')

function openEntryDialog() {
  if (!store.userInfo.account) {
    loginSuccessAction.value.fun = () => {
      window.location.reload()
    }
    loginSuccessAction.value.intercept = true
    openLogin()
    return
  }
  let now = new Date().getTime()
  let startTime = new Date(fissionData.value.startTime).getTime()
  let endTime = new Date(fissionData.value.endTime).getTime()
  if (now < startTime || now > endTime) {
    return ElMessage.error('活动暂未开放！')
  }
  openFissionEntryDialog()
}

async function init() {
  if (!store.userInfo.account && getToken()) {
    await store.getUserInfo()
  }

  const el_loading = ElLoading.service({
    lock: true,
    text: '',
    background: 'rgba(255, 255, 255, 0.7)',
  })
  getFissionMemberDiscount()
    .then(res => {
      if (res.code == 200 && res.data) {
        fissionData.value = res.data
        if (fissionData.value.memberDiscountType == 2) {
          fissionData.value.memberDiscount = fissionData.value.memberDiscount / 10
        }
      } else {
        ElMessage.error('暂无活动信息')
        router.replace('/')
      }
    })
    .finally(() => {
      el_loading.close()
    })

  getFissionAgreement().then(res => {
    if (res.data) {
      agreementContent.value = res.data.content || ''
    }
  })
}
init()
</script>

<style scoped lang="scss">
.fission-page {
  position: relative;
  top: 64px;
  width: 100%;
  box-sizing: border-box;
  height: calc(100vh - 64px);
  overflow-y: auto;
  font-family: 'PingFang SC';

  @include mediaTo('phone') {
    height: auto;
    top: 0;
    background-color: rgb(243, 243, 251);
    background-image: url('@/assets/image/activity/fission/bg-1.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .fission-bg {
    background-image: url('@/assets/image/activity/fission/bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding-bottom: 100px;

    @include mediaTo('phone') {
      background-image: none;
      padding-bottom: 50px;
    }
  }

  .content-box {
    width: 1466px;
    margin: 0 auto;

    @include mediaTo('notebook') {
      width: 1200px;
    }
    @include mediaTo('phone') {
      width: 100vw;
      min-width: 375px;
    }

    .title-img {
      width: 858px;
      margin: 0 auto 30px;
      padding-top: 44px;
      display: block;

      @include mediaTo('notebook') {
        width: 720px;
      }
      @include mediaTo('phone') {
        width: 320px;
        padding-top: 95px;
        margin-bottom: 35px;
        transform: translateX(-10px);
      }
    }

    .title {
      width: fit-content;
      margin: 0 auto;
      font-weight: 500;
      font-size: 32px;
      color: #1e2937;
      line-height: 45px;
      margin-bottom: 24px;
      position: relative;

      span {
        position: relative;
        z-index: 2;
      }

      @include mediaTo('notebook') {
        font-size: 28px;
      }
      @include mediaTo('phone') {
        font-size: 25px;
        font-weight: 700;
        margin-bottom: -60px;
      }

      &:before {
        content: '';
        position: absolute;
        bottom: 3px;
        left: -5px;
        width: 12px;
        height: 12px;
        background: #afc3ff;
        border-radius: 50%;

        @include mediaTo('phone') {
          bottom: 8px;
          left: 1px;
        }
      }

      &::after {
        content: '';
        position: absolute;
        bottom: 7px;
        right: -20px;
        width: 27px;
        height: 27px;
        background: #afc3ff;
        border-radius: 50%;

        @include mediaTo('phone') {
          bottom: 16px;
          right: -14px;
        }
      }
    }

    .card-box {
      background: #ffffff;
      box-shadow: 0px 12px 18px 0px rgba(147, 159, 192, 0.2);
      border-radius: 48px;
      border: 1px solid #d8e3ff;
      // border-image: linear-gradient(180deg, rgba(216, 227, 255, 1), rgba(255, 255, 255, 1)) 1 1;
      padding: 50px;
      box-sizing: border-box;
      margin-bottom: 50px;

      @include mediaTo('notebook') {
        padding: 40px;
      }
      @include mediaTo('phone') {
        border-radius: 24px;
        padding: 65px 16px 20px;
        width: calc(100vw - 32px);
        margin: auto auto 58px;
      }
    }
    .card {
      padding: 44px 99px 30px 60px;
      box-sizing: border-box;
      gap: 30px;

      @include mediaTo('phone') {
        padding: 16px 0 0;
        gap: 10px;
      }

      .card-content {
        width: 550px;
        height: auto;
        position: relative;

        @include mediaTo('notebook') {
          width: 450px;
        }
        @include mediaTo('phone') {
          width: 105%;
        }

        .card-text {
          position: absolute;
          top: 0;
          left: 0;
          font-weight: 400;
          font-size: 20px;
          line-height: 28px;
          height: 93%;
          padding: 0 50px;
          align-items: flex-start;
          justify-content: center;
          gap: 10px;

          &.color1 {
            color: #6f82c0;

            & :nth-child(2) {
              color: #1f398f;
            }
          }
          &.color2 {
            color: #b5853d;

            & :nth-child(2) {
              color: #885913;
            }
          }

          @include mediaTo('notebook') {
            font-size: 18px;
            line-height: 24px;
          }
          @include mediaTo('phone') {
            font-size: 14px;
            line-height: 20px;
            padding: 0 34px;
          }

          & :nth-child(2) {
            font-weight: 600;
            font-size: 36px;
            line-height: 50px;

            @include mediaTo('notebook') {
              font-size: 30px;
              line-height: 40px;
            }
            @include mediaTo('phone') {
              font-weight: 800;
              font-size: 24px;
              line-height: 34px;
            }
          }
        }

        .img1 {
          width: 100%;
          height: 100%;
        }
        .img2 {
          position: absolute;
          bottom: 95px;
          right: -25px;
          width: 267px;
          height: 267px;

          @include mediaTo('notebook') {
            bottom: 70px;
            width: 210px;
            height: 210px;
          }
          @include mediaTo('phone') {
            bottom: 45px;
            right: -12px;
            width: 143px;
            height: 143px;
          }
        }
      }
    }
    .act-btn {
      margin-left: 60px;
      width: calc(100% - 60px - 99px);
      height: 80px;
      border-radius: 50px;
      border: none;
      font-weight: 600;
      font-size: 28px;
      background: linear-gradient(91deg, #b9c2ff 0%, #82afff 100%);
      box-shadow: 0px 4px 10px 0px rgba(139, 167, 221, 0.6);

      @include mediaTo('notebook') {
        height: 60px;
        font-size: 22px;
      }
      @include mediaTo('phone') {
        position: fixed;
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 99;
        width: 85%;
        height: 44px;
        font-size: 20px;
        margin: 0;
        background: linear-gradient( 91deg, #AE96FF 0%, #307BFF 100%);
      }
    }

    .step {
      font-weight: 400;
      font-size: 26px;
      line-height: 36px;
      color: var(--text-color);
      width: 300px;
      height: 87px;
      gap: 10px;

      @include mediaTo('notebook') {
        font-size: 22px;
        line-height: 28px;
        width: 240px;
        height: 67px;
      }
      @include mediaTo('phone') {
        font-weight: bold;
        font-size: 18px;
        line-height: 22px;
        width: 100%;
        height: 60px;
        margin-top: 12px;
      }

      .num {
        font-weight: bold;
        font-size: 80px;
        line-height: 87px;
        background-image: linear-gradient(to bottom, #c1ccff, #6ea1ff);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        background-size: 200% auto;

        @include mediaTo('notebook') {
          font-size: 60px;
          line-height: 67px;
        }
        @include mediaTo('phone') {
          font-size: 32px;
          line-height: 35px;
        }
      }
    }
    .step-arrow {
      width: 50px;
      height: 50px;
      margin: 0 60px;
    }
    .step-img {
      gap: 30px;

      img {
        margin-top: 38px;
        width: 428px;

        @include mediaTo('notebook') {
          width: 33%;
          flex: 1;
          min-width: 0;
        }
        @include mediaTo('phone') {
          margin-top: 0;
          width: 100%;
          flex: 1;
          min-width: 0;
        }
      }
    }
  }
}
</style>
