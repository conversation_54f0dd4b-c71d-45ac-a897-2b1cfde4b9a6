<template>
  <el-dialog
    v-model="dialogVisible"
    :width="isMobileDevice() ? '352px' : '400px'"
    title=""
    align-center
    :lock-scroll="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    style="
      border-radius: var(--dialog-radius);
      background-size: 100% 100%;
      /* background: linear-gradient(to bottom, rgb(199, 212, 255), rgb(247, 248, 252) 50%); */
    "
    :style="{
      'background-image': `url(${bgImg})`,
    }"
  >
    <div class="flex-column fission-qrcode-dialog">
      <div class="fission-qrcode-title">微信扫码 {{ store.userInfo.seedId ? '进入后台' : '参加活动' }}</div>
      <div class="fission-qrcode-box" id="wn-fission-qrcode-box" v-loading="qrcodeLoading">
        <img v-if="qrCodeDataUrl" :src="qrCodeDataUrl" alt="" />
      </div>
      <div class="fission-qrcode-tip">温馨提示：请使用当前登录账号绑定的微信扫码参加～</div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import bgImg from '@/assets/image/activity/fission/dialog-bg.png'
import { watchEffect } from 'vue'
import { useSeed } from '@/hooks/useSeed'
import { useUserStore } from '@/stores/modules/user'
import { isMobileDevice } from '@/utils/public'

const store = useUserStore()

const { qrCodeDataUrl, qrcodeLoading, qrcodeError, qrcodeErrorMsg, dialogVisible } = useSeed()

watchEffect(() => {
  if (!store.userInfo.account) {
    qrcodeError.value = true
    qrcodeErrorMsg.value = '请先登录后再参与活动'
  }
})
</script>

<style lang="scss" scoped>
.fission-qrcode-dialog {
  padding: 10px 0px 30px;
  color: var(--text-color);

  .fission-qrcode-title {
    font-weight: 500;
    font-size: 28px;
  }
  .fission-qrcode-box {
    margin: 30px auto;

    img {
      width: 180px;
      height: 180px;
      border-radius: 12px;
    }
  }
  .fission-qrcode-tip {
    font-weight: 400;
    font-size: 14px;

    @include mediaTo('phone') {
      padding: 0 38px;
      text-align: center;
    }
  }
}
</style>
