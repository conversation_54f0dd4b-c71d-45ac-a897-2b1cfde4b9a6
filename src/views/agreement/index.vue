<template>
  <div class="agreement-page" v-loading="loading">
    {{ text }}
  </div>
</template>

<script setup lang="ts">
import { userAgreement, privacyAgreement } from '@/api/user'
import { ref } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const loading = ref(false)
const text = ref('')
const title = ref('')

function init() {
  console.log(route);
  console.log(route.query, route.query.type);
  
  if (route.query.type == 'user') {
    window.document.title = '用户协议'
    userAgreement()
      .then(res => {
        text.value = res.data.content
        title.value = res.data.name
      })
      .finally(() => {
        loading.value = false
      })
  } else if (route.query.type == 'privacy') {
    window.document.title = '隐私政策'
    privacyAgreement()
      .then(res => {
        text.value = res.data.content
        title.value = res.data.name
      })
      .finally(() => {
        loading.value = false
      })
  }
}

init()
</script>

<style scoped lang="scss">
.agreement-page {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  padding: 20px;
  box-sizing: border-box;
  font-size: 1.2em;
}
@media screen and (max-width: 768px) {
  .agreement-page {
    font-size: 6vmin;
  }
}
</style>
