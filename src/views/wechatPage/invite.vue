<template>
  <div class="flex-column wechat-page" v-loading="loading">
    <img class="bg-img" src="@/assets/image/playicon.png" alt="" />
    <div class="flex-center logo" v-if="(status === 6 || status === 7) && needAddWeChat === null">
      <span>蜗牛</span>
      海拍
    </div>
    <div class="index-box" v-if="status === 99">请允许授权，进行下一步</div>
    <div class="flex-column status-box" v-else-if="status === 1">
      <el-icon :size="size" color="#07c160"><SuccessFilled /></el-icon>
      <div class="tips-1">您已有{{ company }}子账号权限</div>
      <div class="tips-2" style="line-break: anywhere">您可打开{{ getHttp() }}，通过微信扫码完成登录</div>
    </div>
    <div class="flex-column status-box" v-else-if="status === 2">
      <el-icon :size="size" color="#fa5151"><WarningFilled /></el-icon>
      <div class="tips-1">您已是{{ oldCompany }}子账号</div>
      <div class="tips-2">
        请与
        <span>{{ oldCompany }}</span>
        解绑后申请加入
        <span>{{ company }}</span>
      </div>
    </div>
    <div class="flex-column status-box" v-else-if="status === 3">
      <el-icon :size="size" color="#fa5151"><WarningFilled /></el-icon>
      <div class="tips-1">您已是平台主账号</div>
      <div class="tips-2">主账号无法加入其他公司</div>
    </div>
    <div class="flex-column status-box" v-else-if="status === 4">
      <el-icon :size="size" color="#fa5151"><WarningFilled /></el-icon>
      <div class="tips-1">企业主账号会员已到期，无法加入</div>
      <div class="tips-2">主账号续费后才可加入该企业</div>
    </div>
    <div class="flex-column status-box" v-else-if="status === 5">
      <el-icon :size="size" color="#07c160"><SuccessFilled /></el-icon>
      <div class="tips-1">您已提交加入申请</div>
      <div class="tips-2">请静待公司审核您的申请</div>
    </div>
    <div
      class="flex-column form-box"
      v-else-if="(status === 6 || status === 7 || status === 9) && needAddWeChat === null"
    >
      <div class="flex-start gap-5 title">
        <img src="@/assets/icon/icon_enterprise.png" alt="" />
        <div>
          <span>{{ company }}</span>
          邀请你加入
        </div>
      </div>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :disabled="disabled"
        label-width="0"
        @submit.prevent
      >
        <el-form-item prop="name">
          <el-input
            v-model="form.name"
            placeholder="请填写你的真实姓名"
            clearable
            @input="handleRedirectUri"
          />
        </el-form-item>
        <template v-if="status === 7 || status === 9">
          <el-form-item prop="phone">
            <el-input
              type="tel"
              v-model.trim="form.phone"
              maxlength="11"
              placeholder="请填写手机号"
              :disabled="checkPhoneLoading"
              @input="handleTel"
            />
          </el-form-item>
          <el-form-item prop="code">
            <div class="code-box">
              <el-input
                v-model.trim="form.code"
                maxlength="4"
                placeholder="请填写验证码"
                @input="handleRedirectUri"
              />
              <el-button
                class="code-btn"
                type="primary"
                link
                :disabled="!checkPhoneBtn || codeTime > 0"
                :loading="codeBtnLoading"
                @click.stop="getPhoneCode"
              >
                <template v-if="codeTime">
                  {{ codeTime + '秒后可再次获取' }}
                </template>
                <template v-else>获取验证码</template>
              </el-button>
            </div>
          </el-form-item>
          <ImageCodeDialog />
        </template>
        <el-form-item>
          <el-button type="primary" round :disabled="checkPhoneLoading" :loading="disabled" @click="next">
            下一步
          </el-button>
        </el-form-item>
      </el-form>
      <div class="flex-column tips">
        <div class="text1">小提示</div>
        <div class="text2">仅需2步，完成加入</div>
        <el-steps :active="1" align-center>
          <el-step title="填写姓名" description="" status="process">
            <template #icon>
              <img src="@/assets/icon/icon_fill.png" alt="" />
            </template>
          </el-step>
          <el-step description="" status="process">
            <template #icon>
              <img src="@/assets/icon/icon_use.png" alt="" />
            </template>
            <template #title>
              <div class="step2">添加蜗牛企业微信</div>
            </template>
          </el-step>
        </el-steps>
      </div>
    </div>
    <div class="flex-column status-box" v-else-if="needAddWeChat === 0">
      <el-icon :size="size" color="#07c160"><SuccessFilled /></el-icon>
      <div class="tips-1">您已成功申请加入{{ company }}</div>
      <div class="tips-2">待主账号通过您的加入申请后，您可打开{{ getHttp() }}，通过微信扫码完成登录</div>
    </div>
    <div class="flex-column img-box" v-else-if="imgUrl">
      <div class="flex-column qrcode-box">
        <div class="qrcode-img">
          <img :src="imgUrl" alt="" class="img" />
          <img src="@/assets/image/wechat/guide.png" alt="" class="guide" />
        </div>
        <p>长按上方二维码识别添加</p>
      </div>
    </div>
    <div class="flex-column status-box" v-else-if="error">
      <!-- <el-icon :size="size" color="#fa5151"><CircleCloseFilled /></el-icon> -->
      <img
        :style="{ width: size + 'px', height: size + 'px' }"
        src="@/assets/image/wechat/tip_icon.png"
        alt=""
      />
      <template v-if="status === 0">
        <div class="tips-1">{{ error }}</div>
        <template v-if="unPayedMemberOrderNums && unPayedMemberOrderNums.length > 0">
          <div class="tips-2">取消后,才可继续申请成为“{{ company }}”的子账号</div>
          <el-button type="primary" round class="btn-2" @click="cancelOrNext">是的，直接取消</el-button>
          <div class="btn-1" @click="handleNoCancel">不取消，我要自己开通会员</div>
        </template>
      </template>
      <template v-if="status === 98 || status === 8">
        <div class="tips-1">提示</div>
        <div class="tips-2">{{ error }}</div>
      </template>
      <template v-if="status === 97">
        <div class="tips-1" style="color: #1769a2">{{ error }}</div>
        <div class="tips-2" style="color: #5695c0">(如有疑问也可联系客服处理)</div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import ImageCodeDialog from '@/views/login/ImageCodeDialog.vue'
import { checkWechat, checkPhone, joinBusiness, checkBusiness, cancelMemberOrder } from '@/api/wechat'
import { onUnmounted, ref, toRefs } from 'vue'
import QRCode from 'qrcode'
import { phone_reg, chinese_Az_reg } from '@/utils/RegExp'
import useImgOrPhoneCode from '@/hooks/useImgOrPhoneCode'
import { useLoop } from './index'

const search = window.location.search
const code = new URLSearchParams(search).get('code')
const state = new URLSearchParams(search).get('state')

const LoopApi = useLoop()

const {
  codeTime,
  codeBtnLoading,
  phoneForm,
  sendPhoneCode, // 发送手机验证码
} = useImgOrPhoneCode()

const size = 60
const loading = ref(true)
const status = ref(0)
const disabled = ref(false)
const formRef = ref()
const unPayedMemberOrderNums = ref([])
const form = ref({
  name: '',
  ...toRefs(phoneForm.value),
})
const rules = {
  name: [{ validator: validateName, trigger: 'change' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: ['change', 'blur'] },
  ],
  code: [{ required: true, message: '请输入验证码', trigger: 'manul' }],
}
const oldCompany = ref('前公司')
const company = ref('当前公司')
const imgUrl = ref('')
const needAddWeChat = ref<number | null>(null)
const error = ref('')

const checkPhoneLoading = ref(false)
const checkPhoneBtn = ref(false)

const SET_DATA_CODE = 'w_a_code'
const SET_DATA = 'w_a_data'
let redirect_uri = ''

function handleRedirectUri() {
  redirect_uri = ''
}
function handleTel(val: any) {
  form.value.phone = val.replace(/\D/g, '')
  redirect_uri = ''
}

function validatePhone(rule: any, value: any, callback: any) {
  checkPhoneBtn.value = false
  if (!phone_reg.test(value)) {
    return callback(new Error('请输入正确的手机号'))
  }
  checkPhoneLoading.value = true
  checkPhone({ phone: value })
    .then((res: any) => {
      if (res.code !== 200) {
        callback(new Error(res.msg))
      } else if (res.data) {
        callback(new Error(res.data))
      } else {
        checkPhoneBtn.value = true
        callback()
      }
    })
    .catch(err => {
      console.log(err)
      callback(new Error(err.data?.msg || '网络错误！请稍后再试'))
    })
    .finally(() => (checkPhoneLoading.value = false))
}

function validateName(rule: any, value: any, callback: any) {
  if (value === '') {
    return callback(new Error('请填写你的真实姓名'))
  }
  if (!chinese_Az_reg.test(value)) {
    return callback(new Error('不可输入数字/不可输入字符'))
  }
  if (value.length > 30) {
    return callback(new Error('超过字数限制'))
  }
  return callback()
}

function handleLoopCheck(params: any) {
  LoopApi.checkFunc = checkBusiness
  LoopApi.callback = res => {
    if (res) {
      if (res.response?.data?.loginStatus === 'LOGIN_SUCCESS') {
        status.value = 0
        needAddWeChat.value = 0
        res.stop()
        return
      }
      if (res.response?.data?.loginStatus === 'EXPIRE') {
        res.stop()
        return
      }
      res.next()
    }
  }
  LoopApi.startLoop(params)
}

function cancelOrNext() {
  if (code) {
    cancelMemberOrder({ orderNums: unPayedMemberOrderNums.value.join(','), code: code, isCancel: true }).then(
      res => {
        if (res.code === 200) {
          status.value = res.data.status
        }
      }
    )
  }
}

function handleNoCancel() {
  if (code) {
    cancelMemberOrder({
      orderNums: unPayedMemberOrderNums.value.join(','),
      code: code,
      isCancel: false,
    }).then(res => {
      if (res.code === 200) {
        if (res.data.status === 3) {
          status.value = res.data.status
        } else {
          status.value = 98
          error.value = '可登录蜗牛后台继续支付订单，成为蜗牛会员'
        }
      }
    })
  }
}

function next() {
  if (redirect_uri) {
    window.location.href = redirect_uri
    return
  }
  if (form.value.name) {
    form.value.name = form.value.name.trim()
  }
  formRef.value.validate((valid: any) => {
    if (valid) {
      disabled.value = true
      let phone, phoneCaptcha
      if (status.value === 7 || status.value === 9) {
        phone = form.value.phone
        phoneCaptcha = form.value.code
      }
      joinBusiness({
        code,
        state,
        name: form.value.name,
        phone,
        phoneCaptcha,
      })
        .then(async res => {
          if (res.data.status === 8) {
            status.value = 97
            error.value = res.data.message || res.message || '未知错误'
            return
          }
          if (res.data.status === 3) {
            status.value = res.data.status
            error.value = res.data.message || res.message || '未知错误'
            return
          }
          needAddWeChat.value = res.data.needAddWeChat

          if (needAddWeChat.value === 1) {
            redirect_uri = ''
            if (res.data.type === 1) {
              redirect_uri = res.data.url
              window.location.href = res.data.url
            } else if (res.data.type === 2) {
              imgUrl.value = res.data.url
              handleLoopCheck({
                code: code,
              })
            } else if (res.data.type == 3) {
              try {
                const dataUrl = await QRCode.toDataURL(res.data.url, {
                  errorCorrectionLevel: 'H',
                  width: '100%',
                })
                imgUrl.value = dataUrl
                error.value = ''
                handleLoopCheck({
                  code: code,
                })
              } catch (err) {
                console.error('Failed to generate QR Code', err)
                error.value = '二维码生成失败'
              }
            }
            localStorage.setItem(
              SET_DATA,
              JSON.stringify({
                status: status.value,
                imgUrl: res.data.type === 2 || res.data.type === 3 ? imgUrl.value : '',
                company: company.value,
                oldCompany: oldCompany.value,
                form: form.value,
                redirect_uri,
              })
            )
          }
        })
        .finally(() => (disabled.value = false))
    }
  })
}

function getPhoneCode() {
  if (codeTime.value) return
  handleRedirectUri()
  formRef.value.validateField('phone', (valid: any) => {
    if (valid) {
      sendPhoneCode({ phoneNum: form.value.phone })
    }
  })
}

function getHttp() {
  return window.location.origin
}

// init

if (import.meta.env.VITE_APP_ENV === 'development') {
  loading.value = false
  status.value = 7
  // error.value = ''
}
function init() {
  if (!code && state) {
    status.value = 99
    loading.value = false
  } else if (code && state) {
    let c = localStorage.getItem(SET_DATA_CODE)
    if (c && c === code) {
      let d: any = localStorage.getItem(SET_DATA)
      if (d) {
        d = JSON.parse(d)
        redirect_uri = d.redirect_uri || ''
        status.value = d.status
        company.value = d.company
        oldCompany.value = d.oldCompany
        imgUrl.value = d.imgUrl || ''
        if (d.form) {
          phoneForm.value.phone = d.form.phone
          phoneForm.value.code = d.form.code
          form.value.name = d.form.name
        }
        error.value = ''
      }
      handleLoopCheck({
        code: code,
      })
      loading.value = false
      return
    }
    checkWechat({
      code,
      state,
    })
      .then(res => {
        if (res.data.status == 0) {
          status.value = 0
          error.value = res.data.message || res.msg || '未知错误'
          company.value = res.data.applyBusinessName
          unPayedMemberOrderNums.value = res.data.unPayedMemberOrderNums
          return
        }
        if (res.data.status == 8) {
          status.value = 0
          error.value = res.data.message || res.message
          return
        }
        localStorage.setItem(SET_DATA_CODE, code)
        status.value = res.data.status
        company.value = res.data.applyBusinessName
        oldCompany.value = res.data.businessName
        error.value = ''
      })
      .catch(err => {
        console.error(err)
        status.value = 0
        error.value = '未知错误'
      })
      .finally(() => (loading.value = false))
  }
}
init()

onUnmounted(() => {
  localStorage.removeItem(SET_DATA_CODE)
  localStorage.removeItem(SET_DATA)
})
</script>

<style scoped lang="scss">
.wechat-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  min-height: 650px;
  background: var(--bg);
  color: #000;
  font-size: 12px;

  .bg-img {
    position: absolute;
    top: 0;
    right: 0;
  }

  .logo {
    position: absolute;
    top: 5vh;
    left: 0;
    width: 100%;
    text-align: center;
    font-family: wn-font;
    font-size: 2.4em;
    font-weight: bold;
    line-height: 37px;
    padding: 10px 0 15px;

    span {
      color: var(--el-color-primary);
    }

    img {
      width: 25px;
      height: 25px;
    }
  }

  .index-box {
    margin: auto;
    font-size: 1.3em;
    font-weight: 400;
    color: #777;
  }

  .form-box {
    width: 100%;
    margin-top: 17vh;

    .title {
      width: 80%;
      margin-bottom: 20px;
      font-size: 1.2em;
      color: var(--text-gray-color);

      span {
        font-size: 1.3em;
        font-weight: bold;
        color: var(--text-color);
      }

      img {
        width: 26px;
        height: 26px;
      }
    }
    .el-input {
      font-size: 1.2em;
      --el-input-height: 38px;
    }
    :deep(.el-input__wrapper) {
      border-radius: 25px;
      padding: 6px 11px 6px 20px;
      box-shadow: 0px 2px 7px 0px rgb(86 149 192 / 22%);
    }

    form {
      width: 80%;
    }
    button {
      font-weight: bold;
      width: 100%;
      margin-top: 20px;
      padding: 18px 15px;
    }

    .code-box {
      width: 100%;
      position: relative;

      .code-btn {
        position: absolute;
        top: 0;
        right: 15px;
        z-index: 3;
        // color: var(--el-color-primary);
        height: 100%;
        width: fit-content;
        margin: 0;
        padding: 0;
        font-size: 1.2em;
        font-weight: 500;
      }
    }

    .tips {
      width: 72%;
      margin-top: 25px;
      background: transparent;
      padding: 10px 10px 15px;
      border-radius: 10px;
      box-shadow:
        0px 0px 6px 0px rgba(92, 159, 227, 0.5),
        inset 0px 1px 2px 0px rgba(255, 255, 255, 0.5);

      .text1 {
        font-size: 1.5em;
        color: var(--text-color);
        font-weight: bold;
      }
      .text2 {
        font-size: 1.2em;
        margin: 2px 0 20px;
        color: var(--text-gray-color);
      }

      .el-steps {
        width: 100%;
        --el-bg-color: transparent;
        --el-color-primary: #3b99fc;
      }
      :deep(.el-steps) {
        .el-step__title {
          font-size: 1.3em;
        }
        .el-step.is-center .el-step__line {
          left: 75%;
          right: -25%;
          height: 0px;
          border-bottom: 2px dashed var(--el-text-color-placeholder);
          background-color: transparent;
        }
      }
      .step2 {
        width: 76px;
        margin: 10px auto 0;
        line-height: 18px;
      }
    }
  }

  .status-box {
    width: 100%;
    height: 100%;
    gap: 20px;
    margin-top: 11vh;
    color: var(--text-color);

    .tips-1 {
      max-width: 80%;
      text-align: center;
      font-size: 1.6em;
      font-weight: 600;
    }
    .tips-2 {
      max-width: 80%;
      text-align: center;
      font-size: 1.3em;
      font-weight: 400;
      color: #919ea7;

      span {
        font-weight: 600;
      }
    }
    .btn-2 {
      padding: 1.93vh 23.5vw;
      margin-top: 11.1vh;
      font-size: 1.3em;
      line-height: 16px;
      font-family:
        'OPPOSans',
        'PingFang SC',
        -apple-system,
        system-ui,
        Segoe UI,
        Roboto,
        Ubuntu,
        Cantarell,
        Noto Sans,
        sans-serif,
        SF UI Text,
        Arial,
        Hiragino Sans GB,
        Microsoft YaHei,
        WenQuanYi Micro Hei;
    }
    .btn-1 {
      color: #5695c0;
      font-weight: 500;
      font-size: 1.2em;
    }
  }

  .img-box {
    width: 100%;
    height: 100%;
    justify-content: center;

    .qrcode-box {
      width: 80%;
      margin: 20px 0;
      gap: 10px;

      .qrcode-img {
        position: relative;
        background: linear-gradient(164deg, #ac99ff 0%, #4b98ff 100%);
        border-radius: 24px;
        width: 75%;
        padding: 10px;

        .img {
          width: 100%;
          border-radius: 24px;
        }

        .guide {
          width: 85px;
          height: 85px;
          position: absolute;
          top: 45%;
          left: calc(100% - 10px);
        }
      }

      p {
        font-weight: 500;
        font-size: 14px;
        color: #004195;
        line-height: 16px;
      }
    }
  }

  :deep(.el-form) {
    .el-form-item__error {
      left: 20px;
      padding-top: 4px;
    }
  }
}
</style>
