import { reactive, ref } from 'vue'

export type LoopApi = {
  delay: number
  checkFunc: (params: any) => Promise<any>
  callback: (res: CallbackData) => void
  startLoop: (params: any) => void
  stopLoop: () => void
}
export type CallbackData = {
  response: any
  params: any
  next: () => void
  stop: (err?: any) => void
}

export function useLoop() {
  const loopApi = reactive<LoopApi>({
    delay: 1000,
    checkFunc: (params: any) => Promise.resolve(true),
    callback: (res: CallbackData) => {},
    startLoop: (params: any) => {
      if (checkSwitch.value) return
      checkSwitch.value = true
      checkLoop(params)
    },
    stopLoop: () => {
      checkSwitch.value = false
    },
  })

  const checkSwitch = ref(false)

  async function checkLoop(params: any) {
    while (checkSwitch) {
      await new Promise((reslove, reject) => {
        setTimeout(() => {
          if (loopApi.checkFunc) {
            loopApi
              .checkFunc(params)
              .then((res: any) => {
                if (!checkSwitch.value) {
                  return reject('stop')
                }
                if (loopApi.callback) {
                  loopApi.callback({
                    response: res,
                    params,
                    next: () => {
                      checkSwitch.value = true
                      return reslove(true)
                    },
                    stop: (err?: any) => {
                      checkSwitch.value = false
                      return reject(err)
                    },
                  })
                } else {
                  checkSwitch.value = false
                  reject('not callback')
                }
              })
              .catch((error: any) => {
                checkSwitch.value = false
                reject(error)
              })
          } else {
            checkSwitch.value = false
            reject('not checkFunc')
          }
        }, loopApi.delay || 1000)
      })
    }
  }

  return loopApi
}
