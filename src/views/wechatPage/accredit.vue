<template>
  <div class="flex-column wechat-page bg" v-loading="loading">
    <div class="flex-column success-box" v-if="success">
      <div class="h5-tips-head" v-if="headTip">关闭当前页面，进入蜗牛海拍系统</div>
      <img src="@/assets/image/wechat/success-filled.png" alt="" />
      <div class="tips-1 text-bg">蜗牛海拍</div>
      <div class="tips-2" v-if="isBind">绑定成功</div>
      <div class="tips-2" v-else>微信授权登录成功</div>
    </div>
    <div class="flex-column tips-box" v-if="beginning">
      <div class="tips-1 text-bg">{{ tip1 }}</div>
      <div class="tips-2 template-pre">{{ tip2 }}</div>
      <div class="arrow">
        <img src="@/assets/image/wechat/arrow-bottom.png" alt="" />
      </div>
    </div>
    <!-- <div class="flex-column error-box" v-if="error">
      <el-icon :size="65" color="#fa5151"><WarningFilled /></el-icon>
      <div class="tips-1 text-bg">{{ tip1 }}</div>
      <div class="tips-2 template-pre">{{ tip2 }}</div>
    </div> -->
    <div class="flex-column img-box" v-if="imgType">
      <h1 class="text-bg">蜗牛海拍</h1>
      <template v-if="imgType === 1 && !imgUrl">
        <div v-if="isBind" class="step-tip">还需一步：添加企微客服，完成绑定</div>
        <div v-else class="step-box flex-center">添加企微客服即可登录</div>
        <!-- <div v-else class="flex-around step-box">
          <div class="step-item">
            <div class="img">
              <img src="@/assets/image/wechat/step1.png" alt="" />
              <div class="line"></div>
            </div>
            <div class="step-text">添加企微客服</div>
          </div>
          <div class="line"></div>
          <div class="step-item">
            <div class="img">
              <img src="@/assets/image/wechat/step2.png" alt="" />
            </div>
            <div class="step-text">绑定手机号</div>
          </div>
        </div> -->
        <div style="height: 300px">
          <div class="add-btn" @click="open">点击继续添加客服</div>
        </div>
      </template>
      <template v-if="imgType === 2 && imgUrl">
        <div v-if="isBind" class="step-tip">还需一步：添加企微客服，完成绑定</div>
        <div v-else class="step-tip" style="color: #004195">扫码添加企微客服即可登录成功</div>
        <!-- <img v-else style="width: 90%" src="@/assets/image/wechat/step.png" alt="" /> -->
        <div class="flex-column qrcode-box">
          <div class="qrcode-img">
            <img :src="imgUrl" alt="" class="img" />
            <!-- <img src="@/assets/image/wechat/guide.png" alt="" class="guide" /> -->
          </div>
          <p>长按上方二维码识别添加</p>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import Cookies from 'js-cookie'
import { wechatAuth, checkMobileQrcode } from '@/api/wechat'
import { onUnmounted, ref } from 'vue'
import QRCode from 'qrcode'
import { useLoop } from './index'

const search = window.location.search
const code = new URLSearchParams(search).get('code')
const state = new URLSearchParams(search).get('state')

const LoopApi = useLoop()

const loading = ref(true)
const success = ref(false)
const headTip = ref(false)
const beginning = ref(false)
const error = ref(false)
const imgUrl = ref('')
const imgType = ref(0)
const tip1 = ref('未知错误')
const tip2 = ref('返回网页刷新后重新扫码')
// 绑定
const isBind = ref(false)

// 移动端提示
const str = Cookies.get('h5-login-tip')
Cookies.remove('h5-login-tip', { domain: '.woniu.video', expires: 0.01 })

function handleLoopCheck(params: any) {
  LoopApi.checkFunc = checkMobileQrcode
  LoopApi.callback = res => {
    if (res) {
      if (
        res.response?.data?.loginStatus === 'LOGIN_SUCCESS' ||
        res.response?.data?.loginStatus === 'LOGIN_NO_PHONE'
      ) {
        success.value = true
        imgType.value = 0
        res.stop()
        return
      }
      if (res.response?.data?.loginStatus === 'EXPIRE') {
        res.stop()
        return
      }
      res.next()
    }
  }
  LoopApi.startLoop(params)
}

const SET_DATA_CODE = 'w_a_code'
const SET_DATA = 'w_a_data'
let add_url = ''

function open() {
  if (add_url) {
    window.location.href = add_url
  }
}

function init() {
  if (window.location.search.indexOf('SubAccountVer') > -1) {
    isBind.value = true
  }
  if (code && state) {
    let c = localStorage.getItem(SET_DATA_CODE)
    if (c && c === code) {
      let d: any = localStorage.getItem(SET_DATA)
      if (d) {
        d = JSON.parse(d)
        imgType.value = d.type || 0
        imgUrl.value = d.imgUrl || ''
        add_url = d.url || ''
      }
      handleLoopCheck({
        ticket: state,
      })
      loading.value = false
      return
    }
    wechatAuth({
      code,
      state,
    })
      .then(async res => {
        if (res.data.loginStatus === 'SNAP_USER') {
          beginning.value = true
          tip1.value = '蜗牛海拍'
          tip2.value = '请点击【使用完整服务】'
        } else if (res.data.loginStatus === 'LOGIN_SUCCESS') {
          success.value = true
          headTip.value = str ? true : false
        } else if (res.data.loginStatus === 'ACCOUNT_DISABLE') {
          error.value = true
          tip1.value = '当前账号已被禁用'
          tip2.value = ''
        } else if (res.data.loginStatus === 'UN_REGISTER') {
          error.value = true
          tip1.value = '错误提示'
          tip2.value = '您还不是平台用户，可直接扫码登录\n（登录即注册）'
        } else if (res.data.type == 1) {
          imgType.value = 1
          localStorage.setItem(
            SET_DATA,
            JSON.stringify({
              url: res.data.url,
              type: 1,
            })
          )
          localStorage.setItem(SET_DATA_CODE, code)
          window.location.href = res.data.url
        } else if (res.data.type == 2) {
          imgType.value = 2
          imgUrl.value = res.data.url
          localStorage.setItem(
            SET_DATA,
            JSON.stringify({
              imgUrl: res.data.url,
              type: 2,
            })
          )
          localStorage.setItem(SET_DATA_CODE, code)
          handleLoopCheck({
            ticket: state,
          })
        } else if (res.data.type == 3) {
          imgType.value = 2
          try {
            const dataUrl = await QRCode.toDataURL(res.data.url, {
              errorCorrectionLevel: 'H',
              width: '100%',
            })
            imgUrl.value = dataUrl
            localStorage.setItem(
              SET_DATA,
              JSON.stringify({
                imgUrl: res.data.url,
                type: 2,
              })
            )
            localStorage.setItem(SET_DATA_CODE, code)
            handleLoopCheck({
              ticket: state,
            })
          } catch (err) {
            console.error('Failed to generate QR Code', err)
            error.value = true
            tip1.value = '错误提示'
            tip2.value = 'Failed to generate QR Code'
          }
        }
      })
      .catch(err => {
        console.error(err)
        error.value = true
        tip1.value = '未知错误'
        tip2.value = '返回网页刷新后重新扫码'
      })
      .finally(() => {
        loading.value = false
      })
  } else {
    error.value = true
    tip1.value = '错误提示'
    tip2.value = '您扫描的二维码有误，请重新扫码或联系客服'
    loading.value = false
  }
}
init()

onUnmounted(() => {
  localStorage.removeItem(SET_DATA_CODE)
  localStorage.removeItem(SET_DATA)
})
</script>

<style scoped lang="scss">
.wechat-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  color: #000;

  &.bg {
    background: linear-gradient(to bottom, #f4f4f9, #e4f0fb);
    // background: var(--bg);
  }

  .text-bg {
    background: linear-gradient(90deg, #ac99ff 0%, #4b98ff 73%);
    -webkit-background-clip: text;
    color: transparent;
    font-weight: 600;
  }

  .success-box {
    width: 100%;
    height: 100%;
    // gap: 20px;
    margin-top: 20vh;
    color: rgba(0, 0, 0, 0.9);
    font-weight: 500;
    text-align: center;
    color: var(--el-color-primary);

    .h5-tips-head {
      position: fixed;
      top: 10px;
      left: 10px;
      color: #fff;
      background-color: rgb(250, 204, 145);
      font-size: 12px;
      font-weight: 500;
      padding: 0px 10px;
      border-radius: 10px;

      &::after {
        content: '';
        display: block;
        width: 0;
        height: 0;
        border-top: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid rgb(250, 204, 145);
        border-left: 5px solid rgb(250, 204, 145);
        position: absolute;
        bottom: 100%;
        left: 10px;
      }
    }

    .tips-1 {
      font-size: 40px;
    }
    .tips-2 {
      font-size: 18px;
      font-weight: 400;
      color: #4b98ff;
    }

    img {
      width: 120px;
      height: 120px;
    }
  }

  .tips-box {
    width: 100%;
    height: 100%;
    // gap: 20px;
    margin-top: 20vh;
    font-weight: 500;
    text-align: center;
    color: var(--el-color-primary);

    .tips-1 {
      font-size: 40px;
    }
    .tips-2 {
      font-size: 18px;
      color: #4b98ff;
    }
    .arrow {
      position: fixed;
      bottom: 15px;
      right: 15px;

      img {
        width: 52px;
        height: 40px;
      }
    }
  }

  .error-box {
    width: 100%;
    height: 100%;
    // gap: 20px;
    margin-top: 120px;
    color: var(--el-color-primary);
    text-align: center;

    .el-icon {
      margin-bottom: 20px;
    }

    .tips-1 {
      font-size: 40px;
      font-weight: 500;
    }
    .tips-2 {
      font-size: 18px;
      font-weight: 400;
      color: #4b98ff;
    }
  }

  .img-box {
    width: 100%;
    height: 100%;
    justify-content: center;

    h1,
    h2 {
      text-align: center;
      font-size: 40px;
      font-weight: 600;
    }

    .qrcode-box {
      width: 80%;
      margin: 20px 0;
      gap: 10px;

      .qrcode-img {
        position: relative;
        background: linear-gradient(164deg, #ac99ff 0%, #4b98ff 100%);
        border-radius: 24px;
        width: 75%;
        padding: 10px;

        .img {
          width: 100%;
          border-radius: 24px;
        }

        .guide {
          width: 85px;
          height: 85px;
          position: absolute;
          top: 45%;
          left: calc(100% - 10px);
        }
      }

      p {
        font-weight: 500;
        font-size: 14px;
        color: #004195;
        line-height: 16px;
      }
    }

    :deep(.el-steps) {
      .is-finish {
        .el-step__line {
          background: transparent;
          border-bottom: 2px dashed var(--el-color-primary);
          height: 0px;
          right: 0;
          top: 12px;

          &::after {
            content: '';
            width: 100%;
            position: absolute;
            top: 0;
            left: calc(100% + 5px);
            border-bottom: 2px dashed var(--el-text-color-placeholder);
          }
        }
        .el-step__icon {
          &.is-text {
            background: var(--el-color-primary);
            color: #fff;
          }
        }
      }
    }
    .step-box {
      width: 90%;
      // height: 115px;
      height: 53px;
      margin: 20px auto 0 auto;
      border-radius: 100px;
      box-shadow: 0px 2px 11px 0px rgba(197, 220, 249, 0.6);
      // box-shadow: 2px 2px 4px 0px #c5dcf9;
      background-color: #fff;
      justify-content: center;
      ont-weight: 500;
      font-size: 16px;
      color: #0e5dc2;

      .line {
        width: 10vw;
        height: 0;
      }

      .step-item {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        .img {
          width: 30vw;
          position: relative;

          .line {
            position: absolute;
            top: calc(50% - 2px);
            left: 100%;
            width: 10vw;
            height: 2px;
            background: linear-gradient(to bottom, #4b98ff 0%, #ac99ff 100%);
          }
          img {
            width: 100%;
          }
        }
        .step-text {
          font-weight: 500;
          font-size: 14px;
          color: #0e5dc2;
        }
      }
    }
    .step-tip {
      width: 90%;
      background: #fff;
      box-shadow: 0px 2px 11px 0px rgba(197, 220, 249, 0.6);
      border-radius: 50px;
      font-weight: 500;
      font-size: 16px;
      color: #0e5dc2;
      line-height: 56px;
      text-align: center;
    }
    .add-btn {
      font-size: 14px;
      color: #ffffff;
      width: 200px;
      height: 44px;
      text-align: center;
      line-height: 44px;
      background: linear-gradient(45deg, #ac99ff 0%, #4b98ff 100%);
      border-radius: 25px;
      margin-top: 55px;
    }
  }
}
</style>
