<template>
  <div
    class="flex-column wechat-page"
    :class="{
      zIndex: error || success || imgUrl,
    }"
    v-loading="loading"
  >
    <div class="flex-column status-box" v-if="success">
      <el-icon :size="55" color="#07c160"><SuccessFilled /></el-icon>
      <div class="tips-1">{{ successText }}</div>
    </div>
    <div class="flex-column status-box" v-if="error">
      <el-icon :size="55" color="#fa5151"><WarningFilled /></el-icon>
      <div class="tips-1">{{ tip1 }}</div>
      <div class="tips-2 template-pre">{{ tip2 }}</div>
      <div v-if="state" class="flex-center" style="width: 80vw; margin: 20px 0">
        <el-button round type="primary" @click="openPhoneLogin" style="width: 100%; padding: 20px 15px">
          验证码登录
        </el-button>
      </div>
    </div>
    <div class="flex-column img-box" v-if="imgUrl">
      <img :src="imgUrl" alt="" />
      <p>请长按识别二维码</p>
      <p style="margin-top: -25px">添加企业微信客服</p>
    </div>

    <div class="phone-login-box" v-if="phoneLogin">
      <div class="flex-column form-box">
        <h2>蜗牛海拍</h2>
        <el-form ref="formRef" :model="form" :rules="rules" :disabled="disabled" label-width="0">
          <el-form-item prop="phone">
            <el-input
              type="tel"
              v-model="form.phone"
              maxlength="11"
              placeholder="请填写手机号"
              :disabled="checkPhoneLoading"
              @input="handleTel"
            />
          </el-form-item>
          <el-form-item prop="code">
            <div class="code-box">
              <el-input v-model="form.code" maxlength="4" placeholder="请填写验证码" />
              <el-button
                class="code-btn"
                type="primary"
                link
                :disabled="!checkPhoneBtn"
                :loading="codeBtnLoading"
                @click.stop="getPhoneCode"
              >
                <template v-if="codeTime">
                  {{ codeTime + '秒后可再次获取' }}
                </template>
                <template v-else>获取验证码</template>
              </el-button>
            </div>
          </el-form-item>
          <ImageCodeDialog />

          <div class="flex-center" style="margin-bottom: 20px">
            <el-button link type="primary" :disabled="checkPhoneLoading" @click="handleTelLogin">
              本机号一键登录
            </el-button>
          </div>

          <el-form-item>
            <el-button
              class="login-btn"
              type="primary"
              round
              :disabled="checkPhoneLoading"
              :loading="disabled"
              @click="next"
            >
              下一步
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import ImageCodeDialog from '@/views/login/ImageCodeDialog.vue'
import { wechatAuth, checkPhone, getAuthToken, getPhoneWithToken, authPhoneLogin } from '@/api/wechat'
import { phone_reg } from '@/utils/RegExp'
import { nextTick, ref, toRefs } from 'vue'
import QRCode from 'qrcode'
import { PhoneNumberServer } from 'aliyun_numberauthsdk_web'
import useImgOrPhoneCode from '@/hooks/useImgOrPhoneCode'
import { ElMessage } from 'element-plus'

const phoneNumberServer = new PhoneNumberServer()

const {
  codeTime,
  codeBtnLoading,
  phoneForm,
  sendPhoneCode, // 发送手机验证码
} = useImgOrPhoneCode()

const search = window.location.search
const code = new URLSearchParams(search).get('code')
const state = ref(new URLSearchParams(search).get('state'))

const loading = ref(true)
const success = ref(false)
const successText = ref('登录成功')
const error = ref(false)
const imgUrl = ref('')
const tip1 = ref('未知错误')
const tip2 = ref('返回网页刷新后重新扫码')

const phoneLogin = ref(false)
const disabled = ref(false)
const checkPhoneLoading = ref(false)
const checkPhoneBtn = ref(false)
const formRef = ref()
const form = ref({
  ...toRefs(phoneForm.value),
})
const rules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'change' },
  ],
  code: [{ required: true, message: '请输入验证码', trigger: 'manul' }],
}

function validatePhone(rule: any, value: any, callback: any) {
  checkPhoneBtn.value = false
  if (!phone_reg.test(value)) {
    return callback(new Error('请输入正确的手机号'))
  }
  checkPhoneLoading.value = true
  checkPhone({ phone: value })
    .then((res: any) => {
      if (res.code !== 200) {
        callback(new Error(res.msg))
      } else if (res.data) {
        callback(new Error(res.data))
      } else {
        checkPhoneBtn.value = true
        callback()
      }
    })
    .catch(err => {
      console.log(err)
      callback(new Error(err.data?.msg || '网络错误！请稍后再试'))
    })
    .finally(() => (checkPhoneLoading.value = false))
}
function handleTel(val: any) {
  form.value.phone = val.replace(/\D/g, '')
}

function getPhoneCode() {
  if (codeTime.value) return
  formRef.value.validateField('phone', (valid: any) => {
    if (valid) {
      sendPhoneCode({ phoneNum: form.value.phone })
    }
  })
}

let accessToken = ''
let jwtToken = ''

if (code && state.value) {
  wechatAuth({
    code,
    state: state.value,
  })
    .then(async res => {
      if (res.data.loginStatus === 'LOGIN_SUCCESS') {
        successCallback()
      } else if (res.data.loginStatus === 'LOGIN_NO_PHONE_PLUS') {
        const netType = phoneNumberServer.getConnection()
        if (netType === 'cellular') {
          getAuth()
        } else {
          error.value = true
          tip1.value = '错误提示'
          tip2.value = '请关闭WiFi或者尝试其他登录方式'
        }
      } else {
        requestCallback(res)
      }
    })
    .catch(err => {
      console.error(err)
      error.value = true
      state.value = ''
      tip1.value = '未知错误'
      tip2.value = '返回网页刷新后重新扫码'
    })
    .finally(() => (loading.value = false))
} else {
  error.value = true
  tip1.value = '错误提示'
  tip2.value = '您扫描的二维码有误，请重新扫码或联系客服'
  loading.value = false
}

function getAuth() {
  getAuthToken()
    .then(res => {
      if (res.code === 200) {
        accessToken = res.data.accessToken
        jwtToken = res.data.jwtToken
        checkLogin()
      }
    })
    .catch(() => (loading.value = false))
}

function checkLogin() {
  phoneNumberServer.checkLoginAvailable({
    accessToken: accessToken,
    jwtToken: jwtToken,
    success: (res: any) => {
      // 身份鉴权成功,调用 getLoginToken 接口
      console.log('身份鉴权', res)
      loading.value = false
      if (res.code === 600000) {
        getToken()
      }
    },
    // 身份鉴权失败,提示用户关闭Wi-Fi或者尝试其他登录方案
    error: function (res: any) {
      console.log('身份鉴权err', res)
      loading.value = false
      error.value = true
      tip1.value = '错误提示'
      tip2.value = '请关闭WiFi或者尝试其他登录方式'
    },
  })
}

const otherChangeBtn = ref()

function getToken() {
  phoneNumberServer.getLoginToken({
    success: (res: any) => {
      // 成功回调，获取spToken
      console.log('getLoginToken', res)
      getPhone(res.spToken)
    },
    // 失败回调
    error: (res: any) => {
      error.value = true
      tip1.value = res.message || res.msg || '未知错误'
      tip2.value = '请关闭WiFi或者尝试其他登录方式'
    },
    // 授权页状态监听函数
    watch: function (status, data) {
      console.log('watch', status, data)
      if (status == 1) {
        nextTick(() => {
          otherChangeBtn.value = document.getElementById('otherChangeBtn')
          console.dir('otherChangeBtn', otherChangeBtn.value)
          if (otherChangeBtn.value) {
            otherChangeBtn.value.addEventListener('click', openPhoneLogin)
          }
        })
      }
    },

    // 页面配置选项
    authPageOption: {
      navText: '登录/注册',
      subtitle: '蜗牛海拍', // 副标题
      isHideLogo: true, // logo显示隐藏
      logoImg: 'XXX',
      btnText: '本机号码一键登录',
      agreeSymbol: '、',
      privacyOne: ['《用户协议》', '/agreement?type=user'],
      privacyTwo: ['《隐私政策》', '/agreement?type=privacy'],
      showCustomView: true,
      customView: otherChangeBtn.value
        ? {}
        : {
            element: '<div class="btn_box other" id="otherChangeBtn">验证码登录</div>',
            style: '.btn_box.other{background: transparent;color: var(--el-color-primary);}',
            js: '',
          },
      privacyBefore: '我已阅读并同意',
      privacyEnd: '自定义结尾',
      vendorPrivacyPrefix: '《',
      vendorPrivacySuffix: '》',
      privacyVenderIndex: 2,
      isDialog: false, // 是否是弹窗样式
      manualClose: true, // 是否手动关闭弹窗/授权页
      isPrePageType: false,
      isShowProtocolDesc: false,
    },
  })
}

async function getPhone(spToken: string) {
  // 向后端发起请求,后端调用 GetPhoneWithToken接口,获取用户手机号, 完成登录
  // console.log('getPhone', spToken)
  let res = await getPhoneWithToken({ spToken })
  if (res && res.code === 200) {
    login(1, res.data.mobile, spToken)
  }
}

function login(isAliyunLogin: number, phone: string, phoneCaptcha: string) {
  authPhoneLogin({
    isAliyunLogin,
    phone,
    phoneCaptcha,
    linkCode: '',
    ticket: state.value,
  })
    .then(res => {
      if (res.code === 200) {
        if (res.data.loginStatus === 'LOGIN_SUCCESS') {
          successCallback()
        } else {
          requestCallback(res)
        }
      }
    })
    .finally(() => {
      disabled.value = false
    })
}

function openPhoneLogin() {
  phoneLogin.value = true
}
function handleTelLogin() {
  if (phoneNumberServer.getConnection() === 'cellular') {
    phoneLogin.value = false
    if (error.value) {
      window.location.reload()
    }
  } else {
    ElMessage({
      customClass: 'wn-black-message',
      message: '请打开移动数据网络',
    })
  }
}

function next() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      disabled.value = true
      login(0, form.value.phone, form.value.code)
    }
  })
}

function successCallback() {
  error.value = false
  success.value = true
  loading.value = false
  successText.value = '登录成功'
}
async function requestCallback(res: any) {
  tip1.value = '错误提示'
  if (res.data.loginStatus === 'ACCOUNT_DISABLE') {
    error.value = true
    tip1.value = '登录失败'
    tip2.value = '您的账号已被禁用'
  } else if (res.data.loginStatus === 'ALIYUN_VERIFY_PHONE_ERROR') {
    // 阿里云校验手机号失败
    ElMessage({
      customClass: 'wn-black-message',
      message: res.data.msg,
    })
  } else if (res.data.loginStatus === 'CAPTCHA_FAULT_ERROR') {
    // 手机验证码校验失败
    ElMessage({
      customClass: 'wn-black-message',
      message: '验证码错误',
    })
  } else if (res.data.loginStatus === 'PHONE_BINDING') {
    error.value = true
    tip2.value = '该手机号已被绑定'
  } else if (res.data.loginStatus === 'WECHAT_BINDING') {
    error.value = true
    tip2.value = '该微信号已被绑定'
  } else if (res.data.loginStatus === 'LOGINING') {
    // 展示企微二维码
    if (res.data.type == 1) {
      window.location.href = res.data.url
    } else if (res.data.type == 2) {
      imgUrl.value = res.data.url
    } else if (res.data.type == 3) {
      try {
        const dataUrl = await QRCode.toDataURL(res.data.url, {
          errorCorrectionLevel: 'H',
          width: '100%',
        })
        imgUrl.value = dataUrl
      } catch (err) {
        console.error('Failed to generate QR Code', err)
        error.value = true
        tip2.value = 'Failed to generate QR Code'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.wechat-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: var(--bg);
  color: #000;

  &.zIndex {
    z-index: 1000;
  }

  .status-box {
    width: 100%;
    height: 100%;
    gap: 20px;
    margin-top: 100px;
    color: rgba(0, 0, 0, 0.9);

    .tips-1 {
      text-align: center;
      font-size: 22px;
      font-weight: 500;
    }
    .tips-2 {
      text-align: center;
      font-size: 16px;
      font-weight: 400;
    }
  }

  .img-box {
    width: 100%;
    height: 100%;
    justify-content: center;

    img {
      width: 90%;
    }

    p {
      text-align: center;
      font-size: 24px;
      font-weight: 400;
    }
  }
}

.phone-login-box {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 999;
  width: 100vw;
  height: 100vh;
  background: var(--bg);

  .form-box {
    width: 100%;
    margin-top: 14vh;

    h2 {
      font-size: 2.2em;
      color: var(--el-color-primary);
    }
    .el-input {
      font-size: 1.2em;
      --el-input-height: 38px;
    }
    :deep(.el-input__wrapper) {
      border-radius: 25px;
      padding: 6px 11px 6px 20px;
      box-shadow: 0px 2px 7px 0px rgb(86 149 192 / 22%);
    }

    form {
      width: 80%;
    }
    :deep(.el-form-item__error) {
      margin-left: 20px;
    }
    .login-btn {
      font-weight: bold;
      width: 100%;
      margin-top: 20px;
      padding: 20px 15px;
    }

    .code-box {
      width: 100%;
      position: relative;

      .code-btn {
        position: absolute;
        top: 0;
        right: 15px;
        z-index: 3;
        // color: var(--el-color-primary);
        height: 100%;
        width: fit-content;
        margin: 0;
        padding: 0;
        font-size: 1.2em;
        font-weight: 500;
      }
    }
  }
}
</style>

<style lang="scss">
#J_pageType.page-type-container {
  overflow-y: hidden;
  background: var(--bg);

  // 此处可修改导航栏整体样式
  .nav {
    opacity: 0;

    .nav-back-icon {
      width: 6vw;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
  // 副标题
  .dialog-subtitle {
    font-size: 8.2vmin;
    font-weight: bold;
    color: var(--el-color-primary);
    font-family: 'wn-font';
    margin-top: 10vmin;
    margin-bottom: 18vmin;
  }
  // 此处修改电话号码掩码的样式
  .number-con input {
    background: transparent;
  }

  // 此处修改协议区域
  .agreement {
    width: 88vw;
    margin: 5vh 6vw 0;
    position: fixed;
    bottom: 10vh;
    left: 0;

    .checke-1 svg g {
      fill: var(--el-color-primary);
    }
  }
  // 此处修改登录按钮的样式
  .submit-btn {
    border-radius: 22px;
    background: var(--el-color-primary);
    border: var(--el-border);
    border-color: var(--el-color-primary);
    color: var(--el-color-white);
    width: 88vw;
    margin: 8.24vh 6vw 0;
  }

  // 此处修改自定义按钮的样式
  .custom-view-box {
    margin: 20px 0 0 0;
    background: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
