<template>
  <div>
    <div class="flex-start another-pay-link-box no-select">
      <div>付款链接：</div>
      <el-icon class="is-loading" v-if="loading"><Loading /></el-icon>
      <div class="one-ell link" v-if="anotherPayLink">{{ anotherPayLink }}</div>
      <CopyButton v-if="anotherPayLink" :copy-content="anotherPayLink" link />
    </div>
    <el-button class="another-pay-btn" round :disabled="disabled" @click="handleCancelAnotherPay">
      取消代付
    </el-button>
    <div class="another-pay-time" v-if="anotherPayEndTime">
      代付中...
      <CountDown
        :end-time="anotherPayEndTime"
        :show-days="false"
        :show-hours="false"
        m="分"
        s="秒"
        @end="handleCountDownEnd"
      />
      后代付失效...
    </div>
  </div>
</template>

<script setup lang="ts">
import CopyButton from '@/components/public/button/CopyButton.vue'
import CountDown from '@/components/public/CountDown.vue'
import { getOrGetPayLink, cancelAnotherPay } from '@/api/anotherPay'
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'
import { ElMessage } from 'element-plus'
import { ElLoading } from 'element-plus'

const props = defineProps({
  orderNum: {
    type: String,
    required: true,
  },
  mergeId: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const loading = ref(false)
const anotherPayLink = ref('')
const anotherPayEndTime = ref<string | number>('')
const uuid = ref('')

function getInfo() {
  loading.value = true
  getOrGetPayLink({
    orderNum: !props.mergeId ? props.orderNum : undefined,
    mergeId: props.mergeId || undefined,
  })
    .then(res => {
      if (res.data?.uuid) {
        uuid.value = res.data.uuid
        anotherPayLink.value = import.meta.env.VITE_APP_ANOTHER_PAY_API + '?code=' + res.data.uuid
        anotherPayEndTime.value = new Date(res.data.createTime).getTime() + 1000 * 60 * 60
      }
    })
    .finally(() => {
      loading.value = false
    })
}

function handleCountDownEnd() {
  // 防止无限刷新
  let t: any = sessionStorage.getItem('anotherPayEndTime')
  let count = 0
  if (t) {
    t = JSON.parse(t)
    if (t[0] === props.orderNum && t[1] - new Date().getTime() < 1000 * 10 && t[2] > 1) {
      ElMessage.error('AnotherPayEndTime Error')
      return
    }
    count = t[2] >= 0 ? t[2] + 1 : count + 1
  } else {
    count++
  }
  sessionStorage.setItem('anotherPayEndTime', JSON.stringify([props.orderNum, new Date().getTime(), count]))
  window.location.reload()
}

function handleCancelAnotherPay() {
  ElMessageBox.confirm('确认取消代付？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(() => {
    const el_loading = ElLoading.service({
      lock: true,
      text: '取消中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    cancelAnotherPay({
      uuid: uuid.value,
    })
      .then(res => {
        if (res.code === 200) {
          ElMessage.success('已取消代付')
          window.location.reload()
        }
      })
      .finally(() => {
        el_loading.close()
      })
  })
}

getInfo()
</script>

<style scoped lang="scss">
.another-pay-link-box {
  margin-top: 15px;
  align-items: baseline;

  .link {
    width: 300px;
  }
}
.another-pay-btn {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
  width: 130px;
  height: 40px;
  font-size: 18px;
  font-weight: bold;
  margin: 15px 0;
}
.another-pay-time {
  color: var(--el-color-primary);
}
</style>
