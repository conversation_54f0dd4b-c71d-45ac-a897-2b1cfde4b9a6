<template>
  <PublicDialog
    ref="DialogRef"
    width="500px"
    title="找人代付"
    :showFooterButton="false"
    custom-close
    align-center
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleClose"
  >
    <div class="flex-column content-box no-select">
      <div>复制链接发送给付款人，付款人使用电脑打开链接，就能帮你完成付款啦~</div>
      <!-- <div class="flex-start link-box">
        <div>付款链接：</div>
        <el-icon class="is-loading" v-if="loading"><Loading /></el-icon>
        <div class="one-ell link" v-if="link">{{ link }}</div>
      </div> -->
      <div class="tip">📢注意：链接1小时后失效，请及时完成支付</div>
    </div>
    <div class="flex-center">
      <el-button round @click="close" style="width: 100px">取消</el-button>
      <el-button round @click="createLink" :loading="loading" type="primary">复制链接找人代付</el-button>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
// import CopyButton from '@/components/public/button/CopyButton.vue'
import { createOrGetPayLink } from '@/api/anotherPay'
import copy from '@/utils/copy'
import { ref } from 'vue'

const DialogRef = ref()
const link = ref('')
const loading = ref(false)
const orderNum = ref('')
const mergeOrderNum = ref('')

const emits = defineEmits(['submit'])

function open(ON: string, mergeId?: string) {
  orderNum.value = ON
  mergeOrderNum.value = mergeId || ''
  DialogRef.value?.open()
}

function close() {
  DialogRef.value?.close()
}

function createLink() {
  loading.value = true
  createOrGetPayLink({
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
  })
    .then(res => {
      if (res.code === 200 && res.data) {
        emits('submit', res.data)
        link.value = res.data
        copy(import.meta.env.VITE_APP_ANOTHER_PAY_API + '?code=' + res.data)
      }
      close()
    })
    .catch(close)
    .finally(() => (loading.value = false))
}

function handleClose() {
  link.value = ''
}

defineExpose({
  open,
  close,
})
</script>

<style scoped lang="scss">
.content-box {
  font-size: 14px;
  color: var(--text-color);
  align-items: flex-start;
  gap: 20px;
  padding: 10px 0 30px;

  .link-box {
    align-items: baseline;

    .link {
      width: 330px;
    }
  }

  .tip {
    color: var(--el-color-warning);
  }
}
</style>
