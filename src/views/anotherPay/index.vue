<template>
  <div class="pages another-pay-page">
    <div class="header">
      <img
        style="cursor: pointer"
        :src="$picUrl + 'static/assets/img_logo_wnkx.webp'"
        alt=""
        @click.stop="toPage('/')"
      />
    </div>
    <div class="flex-column gap-10 empty-box primary" v-if="anotherPayStatus === 'loading'">
      <el-icon class="is-loading" :size="50"><Loading /></el-icon>
      <span class="empty-text">加载中</span>
    </div>
    <div class="flex-column gap-10 empty-box danger" v-if="anotherPayStatus === 'error'">
      <el-icon :size="50"><WarningFilled /></el-icon>
      <span class="empty-text size-20">加载失败，请稍后再试</span>
      <el-button type="primary" size="small" @click="reload">刷新</el-button>
    </div>
    <div class="flex-column gap-10 empty-box danger" v-if="anotherPayStatus === 'invalid'">
      <el-icon :size="50"><CircleCloseFilled /></el-icon>
      <span class="empty-text size-20">代付链接已失效</span>
      <span class="tip-text" v-if="orderNum">订单号：{{ orderNum }}</span>
    </div>
    <div class="flex-column gap-10 empty-box success" v-if="anotherPayStatus === 'payed'">
      <el-icon :size="50"><SuccessFilled /></el-icon>
      <span class="empty-text size-20">已完成代付</span>
      <span class="tip-text" v-if="orderNum">订单号：{{ orderNum }}</span>
      <span class="tip-text" v-if="returnCountDown">{{ returnCountDown }}s后返回首页</span>
      <span class="tip-text" v-else>正在跳转中···</span>
    </div>
    <div class="head-box" v-if="anotherPayStatus === 'paying'">
      <div class="head-content">
        <div class="head-icon">
          <img style="width: 80px; height: 100%" src="@/assets/icon/icon_order_succ.png" alt="" />
        </div>
        <div class="head-text">
          <div class="flex-between">
            <div class="head-text__title">
              [{{ anotherPayData.createBy || anotherPayData.createNickBy }}]请您为他代付订单
            </div>
          </div>
          <div class="head-text__order" v-if="orderNum">
            订单号：{{ orderNum }}
            <CopyButton :copy-content="orderNum" link>
              <el-icon size="14"><CopyDocument /></el-icon>
            </CopyButton>
          </div>
          <div class="head-text__info flex-start" v-if="mergeOrderNum && tableData.length">
            <div>共合并提交{{ tableData.length }}笔订单，{{ handleVideoCount(tableData) }}个视频</div>
            <el-button
              style="font-size: 16px; margin-left: 14px"
              v-if="mergeOrderNum && tableData.length"
              link
              type="primary"
              @click="handleScollToTop"
            >
              查看详情
              <el-icon size="14"><ArrowDown /></el-icon>
            </el-button>
            <!-- <PayOrderList :table-data="tableData" :loading="loading" :error-disabled="errorDisabled" /> -->
          </div>
          <div class="head-text__time" v-if="anotherPayData.createTime">
            <CountDown
              :end-time="new Date(anotherPayData.createTime).getTime() + 1000 * 60 * 60"
              :show-days="false"
              :show-hours="false"
              m="分"
              s="秒"
              @end="handleCountDownEnd"
            />
            <span>后代付失效...</span>
          </div>
        </div>
      </div>
      <div style="padding: 0 45px">
        <el-divider style="margin: 16px 0" />
      </div>
      <div style="margin: 0 0 45px 148px; padding-right: 45px; position: relative" v-loading="loading">
        <div
          class="total-box"
          v-if="
            orderInfo.orderPromotionAmountSum &&
            !mergeOrderNum &&
            orderInfo.orderDiscountDetailVOS &&
            orderInfo.orderDiscountDetailVOS.length > 0
          "
        >
          <div class="box-title">
            <div class="total-box-title">订单合计</div>
            <span v-if="errorDisabled" style="font-size: 18px">-</span>
            <span v-else>
              {{
                payWay === PAY_TYPE['全币种支付']
                  ? toFixed2(orderInfo.oldNeedPayAmountDollar)
                  : toFixed2(orderInfo.oldNeedPayAmount)
              }}
            </span>
            <div class="total-box-icon">{{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}</div>
          </div>
          <!-- <div>
            <div class="total-box-title">订单合计</div>
            <span v-if="errorDisabled" style="font-size: 20px">-</span>
            <span v-else>
              {{
                payWay === PAY_TYPE['全币种支付']
                  ? toFixed2(orderInfo.oldNeedPayAmountDollar)
                  : toFixed2(orderInfo.oldNeedPayAmount)
              }}
            </span>
            {{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}
          </div> -->
          <!-- <div class="tips" v-if="!errorDisabled">（已换算成人民币）</div> -->
        </div>
        <div
          class="total-box"
          v-if="orderInfo.orderDiscountDetailVOS && orderInfo.orderDiscountDetailVOS.length > 0"
        >
          <!-- <div>
            优惠金额：
            <span style="color: var(--member-color)">{{ orderInfo.orderPromotionAmountSum }}</span>
            CNY
          </div> -->
          <div class="box-title" v-if="handleShowDiscount(orderInfo.orderDiscountDetailVOS, '1')">
            <div class="total-box-title">限时满减活动</div>
            <span style="color: var(--member-color)">
              -{{ hanldeDiscount(orderInfo.orderDiscountDetailVOS, '1') }}
            </span>
            <div class="total-box-icon">CNY</div>
          </div>
          <div
            class="box-title"
            style="margin-top: 15px"
            v-if="handleShowDiscount(orderInfo.orderDiscountDetailVOS, '4')"
          >
            <div class="total-box-title">每月首单返还会员费</div>
            <span style="color: var(--member-color)">
              -{{ hanldeDiscount(orderInfo.orderDiscountDetailVOS, '4') }}
            </span>
            <div class="total-box-icon">USD</div>
          </div>
        </div>
        <div class="total-box" v-if="anotherPayData.orderType == 5">
          <div>
            充值金额：
            <span>{{ toFixed2(orderInfo.amount) }}</span>
            CNY
            <span class="gray-tip" v-if="orderInfo.containPresentedAmount">
              (含赠送金额{{ toFixed2(orderInfo.containPresentedAmount) }}CNY)
            </span>
          </div>
          <div v-if="payWay === PAY_TYPE['全币种支付']">
            实付金额：
            <span style="color: var(--el-color-warning)">{{ toFixed2(orderInfo.payAmountDollar) }}</span>
            USD
          </div>
          <div v-else>
            实付金额：
            <span style="color: var(--el-color-warning)">{{ toFixed2(orderInfo.payAmount) }}</span>
            CNY
          </div>
        </div>
        <div class="total-box" v-else>
          <div class="box-title" v-if="payWay === PAY_TYPE['全币种支付']">
            <div class="total-box-title">应付合计</div>
            <span style="font-size: 20px; font-weight: bold" v-if="orderNum && orderNum.startsWith('HYWN')">
              {{ toFixed2(orderInfo.oldNeedPayAmountDollar) }}
            </span>
            <span style="font-size: 20px; font-weight: bold" v-else>
              {{ toFixed2(orderInfo.afterPromotionAmountDollar) }}
            </span>
            <div class="total-box-icon">USD</div>
          </div>
          <div class="box-title" v-else>
            <div class="total-box-title">应付合计</div>
            <span style="font-size: 20px; font-weight: bold" v-if="orderNum && orderNum.startsWith('HYWN')">
              {{ toFixed2(orderInfo.oldNeedPayAmount) }}
            </span>
            <span style="font-size: 20px; font-weight: bold" v-else>
              {{ toFixed2(orderInfo.afterPromotionAmount) }}
            </span>
            <div class="total-box-icon">CNY</div>
          </div>
        </div>
        <el-divider
          v-if="orderInfo.orderDiscountDetailVOS && orderInfo.orderDiscountDetailVOS.length > 0"
          style="margin: 20px 0 0 0"
        />
        <div class="pay-type flex-start">
          <div class="pay-type-title">请选择支付方式</div>
          <div class="select-pay-box">
            <el-radio-group size="large" v-model="payWay" :disabled="disabled" @change="payWayChange">
              <el-radio-button :value="PAY_TYPE['微信支付']" :label="PAY_TYPE['微信支付']">
                <!-- <i
                  class="iconfont icon-weixinzhifu"
                  :class="{ 'wx-color': payWay !== PAY_TYPE['微信支付'] }"
                ></i> -->
                <img
                  v-show="payWay !== PAY_TYPE['微信支付']"
                  src="@/assets/image/pay_wechat_icon.png"
                  alt=""
                  class="pay-icon"
                />
                <img
                  v-show="payWay === PAY_TYPE['微信支付']"
                  src="@/assets/image/pay_wechat_icon2.png"
                  alt=""
                  class="pay-icon"
                />
                微信支付
              </el-radio-button>
              <el-tooltip :content="payDollarTips" raw-content placement="top" effect="dark">
                <el-radio-button
                  :value="PAY_TYPE['全币种支付']"
                  :label="PAY_TYPE['全币种支付']"
                  :disabled="disabled || minAmount"
                  style="position: relative; overflow: hidden"
                >
                  <div class="btn-corner-mark"></div>
                  <!-- <i class="iconfont icon-Dollar"></i> -->
                  <img
                    v-show="payWay !== PAY_TYPE['全币种支付']"
                    src="@/assets/image/pay_all_icon.png"
                    alt=""
                    class="pay-icon"
                  />
                  <img
                    v-show="payWay === PAY_TYPE['全币种支付']"
                    src="@/assets/image/pay_all_icon2.png"
                    alt=""
                    class="pay-icon"
                  />
                  全币种支付
                </el-radio-button>
              </el-tooltip>
              <el-radio-button :value="PAY_TYPE['支付宝支付']" :label="PAY_TYPE['支付宝支付']">
                <!-- <i
                  class="iconfont icon-zhifubaozhifu"
                  :class="{ 'zfb-color': payWay !== PAY_TYPE['支付宝支付'] }"
                ></i> -->
                <img
                  v-show="payWay !== PAY_TYPE['支付宝支付']"
                  src="@/assets/image/pay_ali_icon.png"
                  alt=""
                  class="pay-icon"
                />
                <img
                  v-show="payWay === PAY_TYPE['支付宝支付']"
                  src="@/assets/image/pay_ali_icon2.png"
                  alt=""
                  class="pay-icon"
                />
                支付宝支付
              </el-radio-button>
              <!-- <el-radio-button :value="PAY_TYPE['银行卡转账']" :label="PAY_TYPE['银行卡转账']">
              <i v-if="payWay === PAY_TYPE['银行卡转账']" class="iconfont icon-iconfontjikediancanicon20"></i>
              <svg v-else class="icon" aria-hidden="true" style="width: 20px; height: 20px">
                <use xlink:href="#icon-yinhangqiazhuanzhang"></use>
              </svg>
              银行转账
            </el-radio-button> -->
              <!-- <el-tooltip content="对公转账，需要额外支付5%的开票服务费" placement="top" effect="light"> -->
              <el-radio-button :value="PAY_TYPE['对公转账']" :label="PAY_TYPE['对公转账']">
                <!-- <i class="iconfont icon-duigongzhuanzhang"></i> -->
                <img
                  v-show="payWay !== PAY_TYPE['对公转账']"
                  src="@/assets/image/pay_public_icon.png"
                  alt=""
                  class="pay-icon"
                />
                <img
                  v-show="payWay === PAY_TYPE['对公转账']"
                  src="@/assets/image/pay_public_icon2.png"
                  alt=""
                  class="pay-icon"
                />
                对公转账
              </el-radio-button>
              <!-- </el-tooltip> -->
            </el-radio-group>
          </div>
        </div>
        <!-- <div class="mt-20" style="font-size: 16px">请选择支付方式</div> -->
        <div class="pay-info flex-between">
          <div class="flex-start">
            <div class="pay-info-title">还需支付金额</div>
            <span v-if="errorDisabled" style="font-size: 24px">-</span>
            <div v-else>
              <span>
                {{
                  payWay === PAY_TYPE['全币种支付']
                    ? toFixed2(orderInfo.payAmountDollar)
                    : toFixed2(orderInfo.payAmount)
                }}
              </span>
              <span style="font-size: 14px; margin-left: 6px; font-weight: 400">
                {{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}
              </span>
            </div>

            <div v-if="payWay === PAY_TYPE['全币种支付']">
              <span style="font-weight: normal; margin: 0 8px">/</span>
              <span>{{ toFixed2(orderInfo.payAmount) }}</span>
              <span style="font-weight: normal; font-size: 16px">
                <span style="font-size: 14px; margin-left: 6px; font-weight: 400">CNY</span>
              </span>
            </div>
            <!-- <span v-else>{{ orderInfo.payAmount }}</span> -->
          </div>
          <div class="btn-box">
            <el-button
              type="primary"
              round
              style="width: 180px; height: 40px; font-size: 18px; font-weight: bold"
              :disabled="disabled || errorDisabled"
              @click="tryLock"
            >
              立即支付
            </el-button>
          </div>
        </div>
        <!-- <div class="total-box">
          <div>
            还需支付：
            <span v-if="errorDisabled" style="font-size: 20px">-</span>
            <span v-else>
              {{
                payWay === PAY_TYPE['全币种支付']
                  ? toFixed2(orderInfo.payAmountDollar)
                  : toFixed2(orderInfo.payAmount)
              }}
            </span>
            {{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}
            <span v-if="payWay === PAY_TYPE['全币种支付']">
              <span style="font-weight: normal; margin: 0 8px">/</span>
              <span>{{ toFixed2(orderInfo.payAmount) }}</span>
              <span style="font-weight: normal; font-size: 16px">&nbsp;CNY</span>
              <span
                class="total-exchange-rate"
                v-if="anotherPayData.orderType == 5 && orderInfo.currentExchangeRate"
              >
                实时百度汇率：{{ orderInfo.currentExchangeRate }}
              </span>
            </span>
          </div>
        </div> -->
      </div>
    </div>

    <div
      class="card-two"
      v-if="anotherPayStatus === 'paying' && (anotherPayData.orderType == 0 || anotherPayData.orderType == 4)"
    >
      <div class="flex-start gap-10" style="margin-top: 20px">
        <div class="card-two__title">订单费用明细</div>
        <DownloadButton
          type="primary"
          link
          icon="Download"
          text="下载费用清单"
          message="确认下载费用清单"
          loadingText="下载中"
          url="/order/another-pay/download-pay-info"
          :disabled="disabled || errorDisabled"
          :params="() => ({ code: searchCode, mergeId: mergeOrderNum })"
        />
      </div>
      <div style="width: 100%" v-for="(item, i) in tableData" :key="i">
        <div class="flex-between card-two__order-head">
          <span>订单号：{{ item.orderNum }}</span>
          <div>
            <span>合计(CNY)：{{ item.amount }}</span>
            <span v-if="item.orderPromotionAmount" style="margin-left: 10px">
              优惠(CNY)：{{ item.orderPromotionAmount }}
            </span>
          </div>
        </div>
        <el-table
          ref="tableRef"
          :data="item.orderVideoPayInfos"
          style="width: 100%"
          border
          header-cell-class-name="table-head-primary"
          v-loading="loading"
        >
          <template #empty>
            <Empty image-type="order" description="暂无数据" :image-size="100" />
          </template>
          <el-table-column prop="productChinese" label="产品名称" minWidth="260" align="center">
            <template v-slot="{ row }">
              <div v-if="row.productChinese">
                <span v-if="row.productChinese.length <= 16">
                  {{ row.productChinese }}
                </span>
                <el-tooltip
                  v-else
                  effect="dark"
                  :content="`<div style='max-width: 600px;'>${row.productChinese}</div>`"
                  raw-content
                  placement="top"
                >
                  <span>{{ row.productChinese.substring(0, 16) }}...</span>
                </el-tooltip>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column prop="videoPrice" label="视频佣金" width="140" align="center">
            <!-- <template #header>
              <div class="flex-center gap-5">
                <span>视频佣金</span>
                <el-tooltip
                  :content="getTipContent('order-pay-video-commission')"
                  placement="top"
                  effect="dark"
                  raw-content
                >
                  <el-icon
                    color="rgb(155, 166, 186)"
                    style="margin: 1px 0 0 -2px"
                  >
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template> -->
            <template v-slot="{ row }">
              {{ row.videoPrice ? `$${row.videoPrice}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="picPrice" label="照片佣金" width="140" align="center">
            <!-- <template #header>
              <div class="flex-center gap-5">
                <span>照片佣金</span>
                <el-tooltip
                  :content="getTipContent('order-pay-picCount')"
                  placement="top"
                  effect="dark"
                  raw-content
                >
                  <el-icon
                    color="rgb(155, 166, 186)"
                    style="margin: 1px 0 0 -2px"
                  >
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template> -->
            <template v-slot="{ row }">
              {{ row.picPrice ? `$${row.picPrice}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="commissionPaysTaxes" label="佣金代缴税费" width="140" align="center">
            <template #header>
              <div class="flex-center gap-5">
                <span>佣金代缴税费</span>
                <el-tooltip
                  :content="getTipContent('order-pay-taxes')"
                  placement="top"
                  effect="dark"
                  raw-content
                >
                  <el-icon color="rgb(155, 166, 186)" style="margin: 1px 0 0 -2px">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template v-slot="{ row }">
              {{ row.commissionPaysTaxes ? `$${row.commissionPaysTaxes.toFixed(2)}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="exchangePrice" label="PayPal代付手续费" width="160" align="center">
            <template #header>
              <div class="flex-center gap-5">
                <span>PayPal代付手续费</span>
                <el-tooltip
                  :content="getTipContent('order-pay-PayPal')"
                  placement="top"
                  effect="dark"
                  raw-content
                >
                  <el-icon color="rgb(155, 166, 186)" style="margin: 1px 0 0 -2px">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template v-slot="{ row }">
              {{ row.exchangePrice ? `$${row.exchangePrice.toFixed(2)}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="servicePrice" label="服务费" width="140" align="center">
            <template #header>
              <div class="flex-center gap-5">
                <span>服务费</span>
                <el-tooltip
                  :content="getTipContent('order-pay-service')"
                  placement="top"
                  effect="dark"
                  raw-content
                >
                  <el-icon color="rgb(155, 166, 186)" style="margin: 1px 0 0 -2px">
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <template v-slot="{ row }">
              {{ row.servicePrice ? `$${row.servicePrice}` : '0' }}
            </template>
          </el-table-column>
          <el-table-column prop="currentExchangeRate" label="实时百度汇率" width="140" align="center">
            <!-- <template #header>
              <div class="flex-center gap-5">
                <span>实时百度汇率</span>
                <el-tooltip
                  :content="getTipContent('order-pay-rate')"
                  placement="top"
                  effect="dark"
                  raw-content
                >
                  <el-icon
                    color="rgb(155, 166, 186)"
                    style="margin: 1px 0 0 -2px"
                  >
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template> -->
            <template v-slot="{ row }">
              {{
                !errorDisabled && row.currentExchangeRate >= 6.5 && row.currentExchangeRate <= 8
                  ? row.currentExchangeRate
                  : '-'
              }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="小计（USD）" width="140" align="center">
            <template v-slot="{ row }">
              {{ row.amountDollar ? `$${row.amountDollar.toFixed(2)}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="小计（CNY）" width="140" align="center">
            <template v-slot="{ row }">
              {{ !errorDisabled && row.amount ? `￥${row.amount.toFixed(2)}` : '-' }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <div class="card-two__total">
        <div class="flex-start" v-if="payWay === PAY_TYPE['对公转账'] && !isAnotherPay">
          <p>开票服务费：</p>
          <span v-if="orderInfo.taxPointCost && !errorDisabled">
            ￥{{ orderInfo.taxPointCost }}（{{ orderInfo.taxPoint }}%）
          </span>
          <span v-else>-</span>
        </div>
        <div class="flex-start">
          <p>订单合计：</p>
          <span v-if="orderInfo.oldNeedPayAmount && !errorDisabled">￥{{ orderInfo.oldNeedPayAmount }}</span>
          <span v-else>-</span>
        </div>
      </div> -->
    </div>
    <template v-if="payWay === PAY_TYPE['全币种支付']">
      <TransferMoneyPaySelect
        :payeeId="orderInfo.payeeId"
        :pay-amount="orderInfo.payAmountDollar"
        :cny-amount="orderInfo.payAmount"
        :orderNum="orderNum"
        :mergeId="mergeOrderNum"
        ref="TransferMoneyPaySelectRef"
        is-another-pay
        @submit="onSubmit"
      />
    </template>
    <TransferMoneyPay
      v-if="anotherPayStatus === 'paying'"
      ref="TransferMoneyPayRef"
      :title="dialogTitle"
      :pay-way="payWay"
      :pay-amount="payWay === PAY_TYPE['全币种支付'] ? orderInfo.payAmountDollar : orderInfo.payAmount"
      :cny-amount="orderInfo.payAmount"
      :tax-point-cost="orderInfo.taxPointCost"
      :orderNum="orderNum"
      :mergeId="mergeOrderNum"
      is-another-pay
      @submit="onSubmit"
      @close="handleClose"
    />
    <QrCodeDialog
      v-if="anotherPayStatus === 'paying'"
      ref="QrCodeDialogRef"
      width="450px"
      :title="dialogTitle"
      :http-code="getPayCode"
      :check-code="getPayCheck"
      :auto-check-code="false"
      :is-zfb-pay="payWay === PAY_TYPE['支付宝支付']"
      :loading="QrCodeDialogLoading"
      @success="payCodeSuccess"
      @close="handleClose"
    >
      <!-- @success="payCodeSuccess" @change="handleQrCodePayStatus" -->
      <div class="qrcode-pay-box">
        <span class="flex-start gap-5 qrcode-tips">
          <img v-if="payWay === PAY_TYPE['微信支付']" src="@/assets/icon/icon_weixinzhifu.png" alt="" />
          <!-- <img v-if="payWay === PAY_TYPE['支付宝支付']" src="@/assets/icon/icon_zhifubaozhifu.png" alt="" /> -->
          <svg v-if="payWay === PAY_TYPE['支付宝支付']" class="icon" aria-hidden="true">
            <use xlink:href="#icon-zhifubaozhifu"></use>
          </svg>
          手机
          {{ payWay === PAY_TYPE['微信支付'] ? '微信' : '支付宝' }}
          扫码支付
        </span>
        <div class="pay-total-box">
          <!-- <div>支付金额</div> -->
          <div class="pay-total">
            <span v-if="payWay === PAY_TYPE['全币种支付']">{{ toFixed2(orderInfo.payAmountDollar) }}</span>
            <span v-else>{{ toFixed2(orderInfo.payAmount) }}</span>
            {{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}
          </div>
        </div>
      </div>
    </QrCodeDialog>

    <!-- <Viewer /> -->
    <PayTipDialog :table-data="tableData" />
  </div>
</template>

<script setup lang="ts">
import { computed, onUnmounted, reactive, ref } from 'vue'
import { PAY_TYPE, payType } from '@/utils/order'
import PayOrderList from '@/views/order/pay/components/PayOrderList.vue'
// import Viewer from '@/components/public/viewer/index.vue'
import CopyButton from '@/components/public/button/CopyButton.vue'
import DownloadButton from '@/components/public/button/DownloadButton.vue'
import CountDown from '@/components/public/CountDown.vue'
import TransferMoneyPay from '@/views/order/components/dialog/TransferMoneyPay.vue'
import TransferMoneyPaySelect from '@/views/order/components/dialog/TransferMoneyPaySelect.vue'
import QrCodeDialog from '@/components/public/dialog/QrCodeDialog.vue'
import PayTipDialog from '@/views/order/components/dialog/PayTipDialog.vue'
import { getChannelCode } from '@/utils/auth'
import {
  getOrGetPayLinkByCode,
  anotherPayInfo,
  anotherPayMemberInfo,
  anotherPayPrepayInfo,
  anotherPayCode,
  checkAnotherPay,
  submitAnotherPayCredentials,
  submitAnotherPayPrepayCredentials,
  payLockAnotherPay,
} from '@/api/anotherPay'
import { ElMessage, ElMessageBox } from 'element-plus'
// import useQrCodeVerify from '@/hooks/useQrCodeVerify'
import { useAnotherPay, unUseOrderPay } from '@/hooks/usePay'
import { useTooltips } from '@/hooks/useTooltips'
import { useRouter } from 'vue-router'
import { getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance() as any

const { getTipContent } = useTooltips()
const router = useRouter()
// const { handleQrCodePayStatus } = useQrCodeVerify()

onUnmounted(() => {
  unUseOrderPay()
})

function toFixed2(val: any) {
  if (val) {
    return val.toFixed(2)
  }
  return '0'
}

const anotherPayStatus = ref<'loading' | 'error' | 'invalid' | 'paying' | 'payed'>('paying')
const returnCountDown = ref(5)

const orderNum = ref('')
const mergeOrderNum = ref('')
const orderInfo = ref<{
  amount: number
  isBalanceLock: number
  validBalance: number
  videoPrice: number
  picPrice: number
  exchangePrice: number
  servicePrice: number
  discountsAmount: number
  orderOriginAmount: number
  orderAmountResult: number
  currentExchangeRate: number
  orderAmount: number
  oldNeedPayAmount: number
  oldNeedPayAmountDollar: number
  orderPromotionAmountSum: number
  afterPromotionAmount: number
  afterPromotionAmountDollar: number
  balance: number
  taxPoint: number
  taxPointCost: number
  payAmount: number
  payAmountDollar: number
  payType: PAY_TYPE | null
  orderBalance: number
  payeeId: number
  accountId: number
  containPresentedAmount: number
  orderDiscountDetailVOS?: any[]
}>({
  amount: 0,
  videoPrice: 0,
  picPrice: 0,
  exchangePrice: 0,
  servicePrice: 0,
  currentExchangeRate: 0,
  orderAmount: 0,
  oldNeedPayAmount: 0,
  balance: 0,
  taxPoint: 0,
  taxPointCost: 0,
  payType: null,
  orderBalance: 0,
  payAmount: 0,
  isBalanceLock: 0,
  validBalance: 0,
  discountsAmount: 0,
  orderAmountResult: 0,
  orderOriginAmount: 0,
  oldNeedPayAmountDollar: 0,
  orderPromotionAmountSum: 0,
  payAmountDollar: 0,
  payeeId: 0,
  accountId: 0,
  afterPromotionAmount: 0,
  afterPromotionAmountDollar: 0,
  containPresentedAmount: 0,
  orderDiscountDetailVOS: [],
})
const tableData = ref<any[]>([
  [
    {
      productChinese: '-',
      videoPrice: 0,
      picPrice: 0,
      exchangePrice: 0,
      servicePrice: 0,
      currentExchangeRate: 0,
      amount: 0,
    },
  ],
])

const payWay = ref<PAY_TYPE>(PAY_TYPE['微信支付'])

const TransferMoneyPayRef = ref<InstanceType<typeof TransferMoneyPay>>()
const QrCodeDialogRef = ref<InstanceType<typeof QrCodeDialog>>()
const TransferMoneyPaySelectRef = ref<InstanceType<typeof TransferMoneyPaySelect>>()
const QrCodeDialogLoading = ref(false)

const dialogTitle = computed(() => {
  return payType(payWay.value) as string
})

const disabled = ref(true)
const loading = ref(false)
const errorDisabled = ref(false)

const payDollarTips = ref(`<div style="text-align: center">
    跨境卖家的最优选择<br/>
    收款支持币种:  离岸人民币,美元,澳元,加币,欧元,英镑,港币,日元,新西兰元,新加坡元
  </div>`)
const minAmount = computed(() => {
  if (orderInfo.value.payAmountDollar < 50) {
    payDollarTips.value = '<div>当前交易金额低于 50 USD，不支持使用全币种支付。</div>'
    return true
  }
  payDollarTips.value = `<div style="text-align: center">
    跨境卖家的最优选择<br/>
    收款支持币种:  离岸人民币,美元,澳元,加币,欧元,英镑,港币,日元,新西兰元,新加坡元
  </div>`
  return false
})

function toPage(path?: string) {
  if (path) {
    let search = getChannelCode()
    if (path === '/') {
      window.location.href = proxy.$officialWebsiteUrl + (search ? `?c=${search}` : '')
    } else if (path === '/vip') {
      if (router.currentRoute.value.path === '/vip') return
      router.push('/vip' + (search ? `?c=${search}` : ''))
    } else {
      router.push(path + (search ? `?c=${search}` : ''))
    }
  }
}

let returnTimer: any = null
function handleReturnCountDown() {
  if (returnTimer) return
  returnCountDown.value = 5
  returnTimer = setInterval(() => {
    returnCountDown.value--
    if (returnCountDown.value === 0) {
      clearInterval(returnTimer)
      toPage('/')
    }
  }, 1000)
}

const anotherPayData = ref<any>({})
let AnotherPayHook = reactive<any>({})
const searchCode = ref('')

async function init() {
  const search = window.location.search
  const code = new URLSearchParams(search).get('code')
  if (!code) {
    anotherPayStatus.value = 'invalid'
    return
  }
  searchCode.value = code
  let res = await getOrGetPayLinkByCode({ code })
  if (!res || !res.data) {
    anotherPayStatus.value = 'error'
    return
  }
  orderNum.value = res.data.orderNum
  mergeOrderNum.value = res.data.mergeId || ''
  // 完成支付
  if (res.data.isPay == 1) {
    anotherPayStatus.value = 'payed'
    handleReturnCountDown()
    return
  }
  // 失效链接
  if (res.data.status == 0) {
    anotherPayStatus.value = 'invalid'
    return
  }
  anotherPayData.value = res.data

  let infoRes: any
  try {
    if (res.data.orderType == 0 || res.data.orderType == 4) {
      infoRes = await anotherPayInfo({
        orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
        mergeId: mergeOrderNum.value || undefined,
        isPublic: 0,
      })
    } else if (res.data.orderType == 1) {
      infoRes = await anotherPayMemberInfo({ orderNum: orderNum.value, isPublic: 0 })
    } else if (res.data.orderType == 5) {
      infoRes = await anotherPayPrepayInfo({ orderNum: orderNum.value })
    }
    if (infoRes?.code === 200 && infoRes.data) {
      // 记录支付方式
      if (infoRes.data.payType != null) {
        if (infoRes.data.payType < PAY_TYPE['余额支付']) {
          payWay.value = infoRes.data.payType
        } else if (infoRes.data.payType === PAY_TYPE['余额支付']) {
          payWay.value = PAY_TYPE['余额支付']
        } else {
          payWay.value = infoRes.data.payType - PAY_TYPE['余额支付']
        }
      }
      getPayInfo()
    }
    anotherPayStatus.value = 'paying'
  } catch (e) {
    console.error(e)
    errorDisabled.value = true
    return
  }
  AnotherPayHook = reactive(
    useAnotherPay(orderNum.value, mergeOrderNum.value, (status: number) => {
      // console.log('anotherPayStatusChange', status);
      if (status === 1 || status === 9) {
        anotherPayStatus.value = 'payed'
        handleReturnCountDown()
      } else {
        anotherPayStatus.value = 'invalid'
      }
    })
  )
}
function handleVideoCount(data: Array<any>) {
  return data.reduce((pre: any, cur: any) => {
    return pre + (cur.orderVideoPayInfos?.length || 0)
  }, 0)
}
const hanldeDiscount = (list: any, type: any) => {
  return list.find((item: any) => item.type == type).amount
}
const handleShowDiscount = (list: any, type: any) => {
  return list && list.length > 0 ? list.some((item: any) => item.type == type) : false
}

function reload() {
  window.location.reload()
}

// 订单支付信息
function orderPayInfo(params: any) {
  if (anotherPayData.value.orderType == 0 || anotherPayData.value.orderType == 4) {
    return anotherPayInfo(params)
  } else if (anotherPayData.value.orderType == 1) {
    return anotherPayMemberInfo(params)
  } else if (anotherPayData.value.orderType == 5) {
    return anotherPayPrepayInfo({
      orderNum: orderNum.value,
    })
  }
  return Promise.reject()
}

// 倒计时结束
function handleCountDownEnd() {
  anotherPayStatus.value = 'invalid'
  disabled.value = true
  ElMessage.error('代付链接已失效')
}

const platform = ref(0)
// 支付二维码
function getPayCode() {
  let params = {
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
    payType: payWay.value,
  }
  return anotherPayCode(params)
}
// 二维码状态
function getPayCheck() {
  let params = {
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
    platform: platform.value,
  }
  return checkAnotherPay(params)
}

// 扫码成功
function payCodeSuccess() {
  ElMessage.success('支付成功')
  anotherPayStatus.value = 'payed'
  handleReturnCountDown()
  QrCodeDialogRef.value?.close()
}

// 支付方式选择
function payWayChange() {
  getPayInfo()
}

// 打开支付窗口
function openPayBox() {
  if (payWay.value === PAY_TYPE['全币种支付']) {
    TransferMoneyPaySelectRef.value?.open(orderInfo.value)
  }
  if (payWay.value === PAY_TYPE['银行卡转账'] || payWay.value === PAY_TYPE['对公转账']) {
    if (anotherPayData.value.orderType == 5) {
      TransferMoneyPayRef.value?.open({ detailId: orderInfo.value.accountId })
    } else {
      TransferMoneyPayRef.value?.open({ payeeId: orderInfo.value.payeeId })
    }
  } else if (payWay.value === PAY_TYPE['微信支付']) {
    platform.value = 1
    handleOpenQrcodeDelay()
  } else if (payWay.value === PAY_TYPE['支付宝支付']) {
    platform.value = 2
    handleOpenQrcodeDelay()
  }
}
// 打开 微信支付/支付宝支付 延迟check接口
function handleOpenQrcodeDelay() {
  // QrCodeDialogLoading.value = true
  QrCodeDialogRef.value?.open()
  setTimeout(() => {
    AnotherPayHook.isOpenPayDialog = true
    // QrCodeDialogLoading.value = false
  }, 5000)
}

function tryLock() {
  if (!payWay.value || payWay.value >= PAY_TYPE['余额支付']) {
    ElMessage.warning('请选择支付方式')
    return
  }
  loading.value = true
  payLockAnotherPay({
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
    payType: payWay.value,
    payAmount: orderInfo.value.payAmount,
    taxPointCost: orderInfo.value.taxPointCost,
    useBalance: 0,
  })
    .then(() => {
      getPayInfo(true)
    })
    .catch(() => {
      loading.value = false
    })
}
// 获取订单信息
function getPayInfo(first: boolean = false) {
  loading.value = true
  disabled.value = true
  orderPayInfo({
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
    isPublic: payWay.value === PAY_TYPE['对公转账'] ? 0 : 1,
    // useBalance: use_balance.value || undefined,
  })
    .then(res => {
      orderInfo.value = res.data
      if (res.data.orderPayInfoDetailVOS?.length) {
        tableData.value = res.data.orderPayInfoDetailVOS
      }
      if (
        (orderNum.value?.startsWith('DDWN') && !res.data.orderPayInfoDetailVOS?.length) ||
        res.data.defaultExchangeRate
      ) {
        errorDisabled.value = true
        if (res.data.defaultExchangeRate) {
          ElMessageBox.alert(
            `<div>
              <p style="text-align: center;margin: 10px 0;font-size: 20px">百度汇率自动获取失败</p>
              <p style="text-align: center;margin: 10px 0;font-size: 15px">请联系您的对接客服为您处理~</p>
            </div>`,
            '',
            {
              customStyle: {
                '--el-messagebox-width': '360px',
                '--el-messagebox-border-radius': '10px',
              },
              showClose: false,
              center: true,
              roundButton: true,
              showCancelButton: true,
              showConfirmButton: false,
              cancelButtonText: '知道了',
              cancelButtonClass: 'message-box-cancel-btn-primary',
              dangerouslyUseHTMLString: true,
            }
          )
        }
        return
      }
      errorDisabled.value = false
      // 记录支付方式
      if (orderInfo.value.payType == null) {
        orderInfo.value.payType = payWay.value
      }
      // 防止在锁定全币种支付时金额变更低于50 USD
      if (payWay.value === PAY_TYPE['全币种支付'] && orderInfo.value.payAmountDollar < 50) {
        ElMessage.warning('当前交易金额低于50 USD，不支持使用全币种支付。')
        payWay.value = PAY_TYPE['对公转账']
        getPayInfo()
        return
      }
      // 打开支付窗口
      if (first) {
        getPayInfo()
        openPayBox()
      }
    })
    .catch(() => {
      errorDisabled.value = true
    })
    .finally(() => {
      loading.value = false
      disabled.value = false
    })
}
// 银行/对公 转账
function onSubmit(params: any, close: () => void) {
  // params.useBalance = use_balance.value
  params.payAmount = orderInfo.value.payAmount
  params.payAmountDollar = orderInfo.value.payAmountDollar

  if (anotherPayData.value.orderType == 5) {
    // 钱包充值
    params.resourceIds = params.objectKeys
    params.objectKeys = undefined
    submitAnotherPayPrepayCredentials(params)
      .then(() => {
        ElMessage.success('提交成功！')
        anotherPayStatus.value = 'payed'
        handleReturnCountDown()
        close()
        TransferMoneyPayRef.value?.close()
        TransferMoneyPaySelectRef.value?.close()
      })
      .finally(() => close())
    return
  }
  submitAnotherPayCredentials(params)
    .then(() => {
      ElMessage.success('提交成功！')
      anotherPayStatus.value = 'payed'
      handleReturnCountDown()
      close()
      TransferMoneyPayRef.value?.close()
      TransferMoneyPaySelectRef.value?.close()
    })
    .finally(() => close())
}

function handleClose() {
  // if (orderInfo.value.payType != null) {
  //   router.replace('/order/list')
  // }
  AnotherPayHook.isOpenPayDialog = false
}

// 滚动到订单费用清单
function handleScollToTop() {
  const scrollBox = document.querySelector('.pages')
  if (scrollBox?.children?.length) {
    for (const el of scrollBox.children) {
      if (el.classList.contains('card-two')) {
        el.scrollIntoView({ behavior: 'smooth' })
        break
      }
    }
  }
}

onUnmounted(() => {
  if (returnTimer) {
    clearInterval(returnTimer)
  }
})

init()
</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';

.header {
  width: 100%;
  font-family: OPPOSans, OPPOSans;
  font-size: 24px;
  font-weight: bold;
  line-height: 37px;

  span {
    color: var(--el-color-primary);
  }

  img {
    width: 90px;
    // cursor: pointer;
  }
}

.another-pay-page {
  background: var(--bg);
  height: 100vh;
  overflow: auto;
  padding: 50px;
  padding-top: 10px;

  .empty-box {
    width: 100%;
    height: 100%;
    justify-content: center;

    &.primary {
      color: var(--el-color-primary);

      .empty-text {
        color: var(--el-color-primary);
      }
    }
    &.danger {
      color: #fa5151;
    }
    &.success {
      color: #07c160;
    }

    .empty-text {
      font-size: 15px;
      color: var(--text-color);

      &.size-20 {
        font-size: 20px;
      }
    }
    .tip-text {
      font-size: 12px;
      color: var(--text-color);
    }
  }
}
.head-box {
  min-width: 1000px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.24) 0%, #ffffff 100%);
  border: 1px solid #fff;
  padding: 13px;
  border-radius: 19px;
  gap: 10px;
  font-size: 16px;
  box-shadow: 3px 3px 4px 0px var(--shadow-gray);

  &__title {
    font-size: 14px;
    .head-title {
      display: flex;
      align-items: center;
    }
  }
  &__title::after {
    content: '';
    display: block;
    height: 1px;
    background-color: #e4e8eb;
    margin-top: 8px;
  }

  .mt-20 {
    margin-top: 20px;
  }
  .btn-box {
    // position: absolute;
    // bottom: -25px;
    // right: 45px;
  }
}
.head-content {
  margin: 35px 45px 0px;
  display: flex;
  .head-icon {
    height: 80px;
    width: 80px;
  }
  .head-text {
    // margin: 10px 0 0 22px;
    margin-left: 24px;
    flex: 1;
    &__title {
      font-size: 24px;
      font-weight: 600;
    }
    &__order {
      display: flex;
      font-size: 14px;
      align-items: center;
    }
    &__time {
      font-size: 14px;
      color: var(--el-color-warning);
    }
    &__info {
      margin-top: 12px;
      color: #777777;
      font-size: 16px;
    }
  }
}
.pay-type {
  margin-top: 19px;
  &-title {
    font-size: 14px;
    color: #777777;
    text-align: left;
    width: 140px;
    margin-right: 14px;
  }
}
.pay-info {
  margin-top: 40px;
  &-title {
    font-size: 16px;
    color: #333333;
    text-align: left;
    width: 140px;
    margin-right: 14px;
  }
  span {
    font-size: 24px;
    font-weight: 600;
    color: #09172f;
  }
}
.title {
  font-size: 16px;
  font-weight: 700;
  margin: 4px 0 10px;

  &.primary {
    font-size: 25px;
    font-weight: 600;
    color: var(--el-color-primary);
  }
}
.card-one {
  width: 66vw;
  min-width: 653px;
  // display: flex;
  // flex-wrap: wrap;
  background: #f1f4fb;

  border-radius: 14px;
  // background-color: var(--bg);
  padding: 14px;
}
.card-two {
  min-width: 918px;
  padding: 5px 55px 25px;
  margin-top: 27px;
  background: #fff;
  border-radius: 19px;
  font-weight: 500;
  color: var(--text-color);

  &__title {
    font-size: 16px;
    // margin: 20px 0;
  }
  &__total {
    width: fit-content;
    margin: 20px 0 0 auto;

    p {
      width: 100px;
      text-align: right;
      margin: 0;
    }
  }
  &__order-head {
    font-size: 14px;
    margin: 10px 0;
    // color: var(--text-gray-color);
    color: #777777;
  }
}
.down-btn {
  position: absolute;
  top: 10px;
  left: 10px;
}
.info-box {
  font-size: 16px;
  .el-col {
    align-items: baseline;
  }

  .el-col-8 {
    display: flex;

    .label {
      flex-shrink: 0;
      width: 80px;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 95px);
      word-break: break-all;
    }
  }
  .el-col-12 {
    display: flex;
    .label {
      flex-shrink: 0;
      width: 80px;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 95px);
      word-break: break-all;
    }
  }

  .flex-start {
    align-items: flex-start;
  }

  .label {
    width: 150px;
    text-align: right;
    margin: 0 5px 3px 0;
    color: #7f7f7f;
  }
  .money {
    font-size: 16px;
    font-weight: 500;

    span {
      font-size: 12px;
      font-weight: 200;
      color: var(--el-text-color-placeholder);
    }

    del {
      color: var(--el-text-color-placeholder);
    }
  }
  .money-checkbox {
    :deep(.el-checkbox) {
      .el-checkbox__label {
        font-size: 16px;
      }
    }
    span {
      font-size: 20px;
      color: var(--el-text-color-regular);
    }
  }
}
.total-box {
  margin-top: 10px;
  text-align: left;
  font-size: 16px;
  width: fit-content;
  position: relative;
  .box-title {
    display: flex;
    align-items: baseline;
  }
  &-icon {
    margin-left: 6px;
  }
  span {
    font-size: 18px;
    font-weight: 400;
    color: #09172f;
  }
  &-title {
    font-size: 14px;
    color: #777777;
    text-align: left;
    width: 140px;
    margin-right: 14px;
  }
  .tips {
    position: absolute;
    bottom: 6px;
    left: calc(100% + 10px);
    width: max-content;
    font-size: 13px;
    color: #9ba6ba;
    // color: var(--text-gray-color);
  }
  .gray-tip {
    font-size: 12px;
    color: #777;
    font-weight: 500;
  }
  .total-exchange-rate {
    position: absolute;
    left: 80px;
    bottom: -12px;
    font-size: 12px;
    color: #777;
    font-weight: 500;
  }
}
.select-pay-label {
  :deep(.el-divider) {
    max-width: 350px;
    margin: 40px auto;

    .el-divider__text {
      font-size: 18px;
      color: var(--text-gray-color);
    }
  }
}
.pay-icon {
  width: 24px;
  height: 24px;
}
.select-pay-box {
  margin: 0;

  :deep(.el-radio-group) {
    margin: 5px 20px 5px 0px;
    font-size: 16px;

    .el-radio-button__inner {
      border-radius: 6px;
      display: flex;
      align-items: center;
      gap: 5px;
      padding: 9px 19px;
    }
  }
  .wx-color {
    color: rgb(25, 213, 108);
  }
  .zfb-color {
    color: rgb(0, 159, 232);
  }

  .iconfont {
    // background: linear-gradient(to right, #fff, #fff) no-repeat right;
    // background-size: 18px 11px;
    font-size: 20px;
  }

  .btn-corner-mark {
    position: absolute;
    top: -1px;
    left: -1px;
    width: 32px;
    height: 32px;
    background-image: url('@/assets/image/recommend_corner_mark.png');
    background-size: 100%;

    // position: absolute;
    // top: -1px;
    // left: -1px;
    // width: 35px;
    // height: 35px;
    // overflow: hidden;

    // &::after {
    //   content: '推荐';
    //   display: block;
    //   position: absolute;
    //   top: -3px;
    //   left: -41px;
    //   width: 100px;
    //   height: 24px;
    //   line-height: 33px;
    //   font-size: 12px;
    //   text-align: center;
    //   background-color: rgb(255, 85, 82);
    //   color: #fff;
    //   transform: rotate(-45deg);
    // }
  }
}
// .balance-checkbox {
// margin: 0 0 70px;
// }
.qrcode-pay-box {
  margin-left: 20px;
  line-height: 40px;

  .pay-total-box {
    color: var(--text-gray-color);
    font-size: 14px;
    margin-top: 10px;

    .pay-total {
      font-family: PingFangSC;
      color: var(--el-color-warning);

      span {
        font-size: 26px;
        font-weight: 600;
      }
    }
  }

  .qrcode-tips {
    font-size: 16px;
    font-weight: bold;
    color: #1f2122;

    img {
      width: 16px;
      height: 16px;
    }

    .icon {
      width: 16px;
      height: 16px;
    }
  }
}
:deep(.el-table) {
  width: 100%;

  .table-head-primary.el-table__cell {
    padding: 13px 0;
    text-align: center;
    color: var(--text-color);
    background-color: var(--table-head-bg);
  }
}
</style>
