<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    :width="isMobileDevice() ? '352px' : '500px'"
    :close-on-click-modal="false"
    align-center
    @close="handleClose"
    style="--el-dialog-padding-primary: 16px 0px 16px 16px;"
  >
    <div v-if="loading" class="flex-column loading-box">
      <el-icon class="is-loading" size="20">
        <Loading />
      </el-icon>
    </div>
    <div v-show="!loading" class="agreement-content" v-html="text"></div>
    <template #footer>
      <div class="footer-btn">
        <el-button type="primary" :disabled="!text || time > 0" @click="handleConfirm">
          {{ time ? `(${time}) ` : '' }}我已阅读并同意
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { Loading } from '@element-plus/icons-vue'
import { ElDialog, ElButton, ElIcon } from 'element-plus'
import { userAgreement, privacyAgreement } from '@/api/user'
import { ref } from 'vue'
import { type AgreementType } from './index'
import { isMobileDevice } from '@/utils/public'

interface Props {
  visible: boolean
  type: AgreementType
  close?: () => void
  confirm?: () => void
}

const props = defineProps<Props>()

const emit = defineEmits(['update:visible'])

const loading = ref(true)
const title = ref('')
const text = ref('')
const time = ref(0)

const handleConfirm = async () => {
  props.confirm?.()
  handleClose()
}

const handleClose = () => {
  emit('update:visible', false)
  props.close?.()
}

const countdown = () => {
  if (time.value > 0) {
    setTimeout(() => {
      time.value -= 1
      countdown()
    }, 1000)
  }
}

const init = () => {
  loading.value = true
  if (props.type === 'privacy') {
    privacyAgreement()
      .then(res => {
        text.value = res.data.content
        title.value = res.data.name
        time.value = 5
        countdown()
      })
      .finally(() => {
        loading.value = false
      })
  }
  if (props.type === 'user') {
    userAgreement()
      .then(res => {
        text.value = res.data.content
        title.value = res.data.name
        time.value = 5
        countdown()
      })
      .finally(() => {
        loading.value = false
      })
  }
}

init()
</script>

<style scoped lang="scss">
.loading-box {
  height: 300px;
  justify-content: center;
}
.agreement-content {
  min-height: 300px;
  max-height: 500px;
  padding-right: 16px;
  overflow-y: overlay;
}
.footer-btn {
  margin: 16px 16px 0 0;
}
</style>

<style lang="scss">
.agreement-content {
  * {
    box-sizing: border-box;
    margin: 0;
    outline: none;
    padding: 0;
  }
  span {
    text-indent: 0;
  }
  p {
    margin: 15px 0;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 20px 0;
  }
  pre>code {
    word-wrap: normal;
    font-family: Consolas,Monaco,Andale Mono,Ubuntu Mono,monospace;
    -webkit-hyphens: none;
    hyphens: none;
    line-height: 1.5;
    margin: .5em 0;
    overflow: auto;
    padding: 1em;
    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;
    text-align: left;
    text-shadow: 0 1px #fff;
    white-space: pre;
    word-break: normal;
    word-spacing: normal;
  }
}
</style>
