import { createApp, type App } from 'vue'
import AgreementDialog from './agreement.vue'

const div = document.createElement('div')
div.setAttribute('class', 'wn-dialog-container')
document.body.appendChild(div)

export type AgreementType = 'user' | 'privacy'

/**
 * 协议
 * @returns 
 */
export async function showAgreementDialog(type: AgreementType): Promise<any> {
  return new Promise((resolve, reject) => {
    let dialogApp: App<Element> | undefined = createApp(AgreementDialog, {
      visible: true,
      type,
      close: () => {
        if (dialogApp) {
          dialogApp.unmount()
          dialogApp = undefined
        }
      },
      confirm: () => {
        resolve('confirm')
        dialogApp?.unmount()
        dialogApp = undefined
      },
    })
    dialogApp.mount(div)
  })
}
