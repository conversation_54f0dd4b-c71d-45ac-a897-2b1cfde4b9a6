<template>
  <div class="pages">
    <div class="w-bg">
      <div class="flex-between">
        <Title>我的会员订单</Title>
        <el-button link icon="ArrowLeft" style="margin-right: 18px" @click="goback">返回</el-button>
      </div>

      <Empty v-if="!listData.length" image-type="order" description="暂无数据" :image-size="120" />

      <template v-for="item in listData" :key="item.id">
        <OrderItemV1 :data="item" @action="handleAction" @countDownEnd="getList" />
      </template>

      <div class="pagination-box" style="margin-bottom: 20px" v-if="listData.length">
        <el-pagination
          class="custom-pagination-radius"
          background
          @size-change="pageChange({ pageNum: 1, pageSize: $event })"
          @current-change="pageChange({ pageNum: $event, pageSize })"
          :current-page="pageNum"
          :page-size="pageSize"
          :page-sizes="pageSizes"
          layout="total, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>

    <InvoiceDialog ref="invoiceDialogRef" :url="invoiceDialogUrl" />
    <!-- <Voucher ref="VoucherRef" /> -->
  </div>
</template>

<script setup lang="ts">
import Title from '@/components/public/Title.vue'
import OrderItem from '@/views/center/tradingRecord/components/orderItem.vue'
import OrderItemV1 from '@/views/center/tradingRecord/components/orderItemV1.vue'
import InvoiceDialog from '@/components/public/dialog/InvoiceDialog.vue'
// import Voucher from '@/views/order/components/dialog/Voucher.vue'
import { memberOrderList, cancelMemberOrder, viewInvoice } from '@/api/vip'
import { getCurrentInstance, ref } from 'vue'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()

const { proxy } = getCurrentInstance() as any

// const VoucherRef = ref<InstanceType<typeof Voucher>>()

const router = useRouter()

const listData = ref([])
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const pageSizes = [10, 20, 30, 50]

const invoiceDialogRef = ref<InstanceType<typeof InvoiceDialog>>()
const invoiceDialogUrl = ref('')

function goback() {
  router.replace('/vip')
}

function getList() {
  loading.value = true
  memberOrderList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  })
    .then(res => {
      listData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => (loading.value = false))
}

function pageChange(page: { pageNum: number; pageSize: number }) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  getList()
}

function handleAction(btn: string, row: any) {
  if (btn === '取消订单') {
    ElMessageBox.confirm('确认取消订单？', '温馨提示', {
      confirmButtonText: '确定',
      cancelButtonText: '再想想',
    })
      .then(() => {
        const el_loading = ElLoading.service({
          lock: true,
          text: '正在取消中，请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        cancelMemberOrder(row.orderNum)
          .then(res => {
            ElMessage.success('取消成功')
            getList()
          })
          .finally(() => el_loading.close())
      })
      .catch(() => {})
  } else if (btn === '查看发票') {
    viewInvoiceImage(row.orderInvoice?.id)
    // invoiceDialogUrl.value = '/public/1.pdf'
    // invoiceDialogRef.value.open()
  } else if (btn === '下载发票') {
    downloadInvoice(row.orderInvoice?.id)
  }
}
// 查看发票
function viewInvoiceImage(id: any) {
  if (!id) {
    return
  }
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在加载中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  viewInvoice(id)
    .then(res => {
      if (typeof res.data === 'string') {
        const fileSuffix = res.data.substring(res.data.lastIndexOf('.') + 1).toLowerCase()
        if (fileSuffix === 'pdf') {
          invoiceDialogUrl.value = proxy.$picUrl + res.data
          invoiceDialogRef.value.open()
        } else if (fileSuffix === 'ofd' || fileSuffix === 'xml') {
          ElMessageBox.alert(`暂不支持预览${fileSuffix}文件`, '温馨提示', {
            confirmButtonText: '知道了',
          })
        } else if (fileSuffix === 'jpg' || fileSuffix === 'png' || fileSuffix === 'jpeg') {
          showViewer([res.data])
        }
      } else if (Array.isArray(res.data)) {
        showViewer(res.data.map((item: any) => item.objectKey))
      }
    })
    .finally(() => el_loading.close())
}
function downloadInvoice(id: any) {
  if (!id) {
    return
  }
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在下载中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  viewInvoice(id)
    .then(res => {
      if (typeof res.data === 'string') {
        // const fileSuffix = res.data.substring(res.data.lastIndexOf('.') + 1).toLowerCase()
        // if (fileSuffix === 'pdf') {
        //   invoiceDialogUrl.value = proxy.$picUrl + res.data
        //   invoiceDialogRef.value.open()
        // } else if(fileSuffix === 'ofd' || fileSuffix === 'xml') {
        //   ElMessageBox.alert(`暂不支持预览${fileSuffix}文件`, '温馨提示', {
        //     confirmButtonText: '知道了',
        //   })
        // } else if(fileSuffix === 'jpg' || fileSuffix === 'png' || fileSuffix === 'jpeg') {
        //   showViewer([res.data])
        // }
        fetch(proxy.$picUrl + res.data).then(async response => {
          const blob = await response.blob()
          const a = document.createElement('a')
          a.href = URL.createObjectURL(blob)
          a.download = res.data.substring(res.data.lastIndexOf('/') + 1)
          a.click()
        })
      }
    })
    .finally(() => el_loading.close())
}

getList()
</script>

<style scoped lang="scss"></style>
