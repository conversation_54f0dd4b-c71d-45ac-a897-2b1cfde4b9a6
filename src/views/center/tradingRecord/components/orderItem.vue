<template>
  <div class="order-item-box">
    <div class="flex-between head-box">
      <div class="flex-start title">
        <img src="@/assets/icon/icon_vip.png" />
        <span>{{ findSetMealLabel(data.packageType) }}</span>
      </div>
      <div class="order-status" v-if="VIP_ORDER_STATUS['待支付'] === data.status">
        <div>{{ VIP_ORDER_STATUS[data.status] }}</div>
        <div class="order-time" v-if="data.orderTime">
          订单将在
          <CountDown :endTime="handleOrderTime(data.orderTime)" showDays @end="handleTimeEnd" />
          后关闭
        </div>
      </div>
      <div class="order-status" v-else>{{ VIP_ORDER_STATUS[data.status] }}</div>
    </div>
    <el-form label-width="110px">
      <el-form-item label="权益时间">
        <div class="flex-between" style="width: 100%; padding-right: 20px">
          <div class="flex-start gap-10">
            <div>
              {{
                data.memberEndTime
                  ? (data.memberStartTime || '-') + ' 至 ' + (data.memberEndTime || '-')
                  : '-'
              }}
            </div>
            <el-tag v-if="data.presentedTime && data.status != VIP_ORDER_STATUS['订单关闭']" type="warning" round size="small">
              <span>加赠</span>
              <span>{{ data.presentedTime }}</span>
              <span v-if="data.presentedTimeType === 1">天</span>
              <span v-else-if="data.presentedTimeType === 2">个月</span>
              <span v-else-if="data.presentedTimeType === 3">年</span>
            </el-tag>
          </div>
          <div class="flex-end">
            <span style="color: var(--el-text-color-regular); padding-right: 12px">还需支付</span>
            <span style="color: var(--text-red-color)" v-if="data.isDefaultExchangeRate">-</span>
            <span
              style="color: var(--text-red-color)"
              v-else-if="
                data.payType == PAY_TYPE['全币种支付'] ||
                data.payType == PAY_TYPE['全币种支付'] + PAY_TYPE['余额支付']
              "
            >
              {{ data.surplusAmountDollar ? data.surplusAmountDollar.toFixed(2) : 0 }} USD
            </span>
            <span style="color: var(--text-red-color)" v-else>
              {{ data.surplusAmount ? data.surplusAmount.toFixed(2) : 0 }} CNY
            </span>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="支付方式">
        <span v-if="VIP_ORDER_STATUS['订单关闭'] === data.status">-</span>
        <span v-else>{{ data.payType ? payType(data.payType) : '-' }}</span>
      </el-form-item>
      <el-form-item label="支付时间">
        {{ data.payTime || '-' }}
      </el-form-item>
      <el-form-item label="订单编号">
        {{ data.orderNum }}
      </el-form-item>
      <el-form-item label="套餐金额">{{ data.packageAmount }} USD</el-form-item>
      <!-- <el-form-item label="服务费">{{ data.taxPointCost || 0 }} CNY</el-form-item> -->
      <el-form-item label="钱包支付">{{ data.useBalance ? data.useBalance.toFixed(2) : 0 }} CNY</el-form-item>
      <el-form-item label="百度汇率">
        {{ data.isDefaultExchangeRate ? '-' : data.currentExchangeRate || '-' }}
      </el-form-item>
      <el-form-item label="种草码优惠" v-if="data.seedCodeDiscount">
        {{
          data.isDefaultExchangeRate ? '-' : data.seedCodeDiscount ? data.seedCodeDiscount.toFixed(2) : 0
        }}
        CNY
      </el-form-item>
      <!-- <el-form-item label="还需支付">
        <span v-if="data.isDefaultExchangeRate">-</span>
        <span v-else-if="data.payType == PAY_TYPE['全币种支付']">{{ data.payAmountDollar ? data.payAmountDollar.toFixed(2) : 0 }} USD</span>
        <span v-else>{{ data.payAmount ? data.payAmount.toFixed(2) : 0 }} CNY</span>
      </el-form-item> -->
    </el-form>

    <div class="flex-end btn" v-if="data.status === VIP_ORDER_STATUS['待支付']">
      <el-button round plain :disabled="disabled" @click="handleAction('取消订单')">取消订单</el-button>
      <!-- <el-button round plain @click="handleAction('凭证单')">凭证单</el-button> -->
      <!-- <DownloadButton
        v-if="data.payType"
        round
        plain
        text="下载凭证单据"
        loadingText="下载中"
        :beforeConfirm="false"
        fileName="订单凭据"
        url="/order/order/get-document-pdf"
        :params="{
          orderNum: data.orderNum
        }"
      /> -->
      <el-button v-if="!data.isDefaultExchangeRate" round type="primary" :disabled="disabled" @click="toBuy">
        去支付
      </el-button>
      <el-tooltip v-else :visible="true" placement="top" effect="custom-danger" append-to=".pages">
        <template #content>
          <div class="flex-start gap-5" style="color: red">
            <el-icon><WarnTriangleFilled /></el-icon>
            汇率获取失败，请联系客服
          </div>
        </template>
        <el-button type="primary" round :disabled="disabled" @click="toBuy">去支付</el-button>
      </el-tooltip>
    </div>
    <!-- <div class="flex-end btn" v-else-if="data.status === VIP_ORDER_STATUS['支付成功']">
      <template v-if="data.orderInvoice?.status == 3">
        <el-text style="margin-right: 10px">已开票</el-text>
        <el-button round plain @click="handleAction('下载发票')">下载发票</el-button>
        <el-button round plain @click="handleAction('查看发票')">查看发票</el-button>
      </template>
      <el-text v-else-if="data.payType == PAY_TYPE['对公转账']">开票中</el-text>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import CountDown from '@/components/public/CountDown.vue'
// import DownloadButton from '@/components/public/button/DownloadButton.vue'
import { PAY_TYPE, payType, VIP_ORDER_STATUS } from '@/utils/order'
import { vipSetMealList } from '@/views/center/data'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { ref } from 'vue'

const router = useRouter()
const store = useUserStore()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const disabled = ref(false)

const emits = defineEmits<{
  action: [btn: string, row: any]
  countDownEnd: []
}>()

function findSetMealLabel(val: number) {
  let t = vipSetMealList.find(item => item.type == val)
  return t ? t.label : ''
}

function handleOrderTime(time: string) {
  let date = time.split(' ')[0] + ' 00:00:00'
  return new Date(date).getTime() + 1000 * 60 * 60 * 24 * 31 - 1000 // 加30天 到最后一天 23:59:59
}

function handleTimeEnd() {
  disabled.value = true
}

// 按钮操作
function handleAction(btn: string) {
  emits('action', btn, props.data)
}

// 去支付
function toBuy() {
  store.realTimeCheckVip().then(() => {
    if (store.isOwnerAcc()) {
      router.push({
        name: 'center-pay',
        state: { orderNum: props.data.orderNum, type: props.data.packageType, payType: props.data.payType },
      })
    }
  })
}
</script>

<style scoped lang="scss">
.order-item-box {
  position: relative;
  border-radius: 10px;
  margin: 20px 0 20px;
  padding: 0 0 10px;
  background-color: var(--bg);
  border: 1px solid var(--el-color-primary-light-7);
  overflow: hidden;

  .head-box {
    height: 50px;
    padding: 0 20px;
    background-color: var(--el-color-primary-light-7);
    margin-bottom: 10px;

    .title {
      gap: 10px;
    }

    img {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
    }
    span {
      font-size: 15px;
      font-weight: 600;
    }
  }
  .order-status {
    text-align: right;
    font-size: 15px;

    .order-time {
      font-weight: 400;
      font-size: 12px;
      color: var(--text-gray-color);

      span {
        font-weight: 400;
        font-size: 12px;
        color: var(--text-gray-color);
      }
    }
  }
  .btn {
    position: absolute;
    bottom: 10px;
    right: 10px;
  }
  .btn2 {
    position: absolute;
    top: 0;
    right: 20px;
    height: 100%;
    justify-content: center;
    gap: 10px;
  }

  :deep(.el-form-item) {
    margin-bottom: 5px;
  }
}
</style>
