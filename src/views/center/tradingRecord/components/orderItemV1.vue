<template>
  <div class="order-item-box">
    <div class="flex-between head-box">
      <div class="flex-start title">
        <img src="@/assets/icon/icon_vip.png" />
        <span>{{ findSetMealLabel(data.packageType) }}</span>
      </div>
      <div class="order-status" v-if="VIP_ORDER_STATUS['待支付'] === data.status">
        <div class="flex-end gap-5">
          <img class="icon-img" src="@/assets/icon/clock_icon.png" />
          <span>{{ VIP_ORDER_STATUS[data.status] }}</span>
        </div>
        <div class="order-time" v-if="data.orderTime">
          订单将在
          <CountDown :endTime="handleOrderTime(data.orderTime)" showDays @end="handleTimeEnd" />
          后关闭
        </div>
      </div>
      <div class="order-status flex-end gap-5" v-else>
        <img
          class="icon-img"
          v-if="data.status === VIP_ORDER_STATUS['待审核']"
          src="@/assets/icon/clock_icon.png"
        />
        <el-icon v-else-if="data.status === VIP_ORDER_STATUS['支付成功']" color="#2ea571" :size="16">
          <SuccessFilled />
        </el-icon>
        <el-icon v-else-if="data.status === VIP_ORDER_STATUS['订单关闭']" color="#d54941" :size="16">
          <CircleCloseFilled />
        </el-icon>
        <span>{{ VIP_ORDER_STATUS[data.status] }}</span>
      </div>
    </div>
    <el-form label-width="110px">
      <template
        v-if="data.status === VIP_ORDER_STATUS['待支付'] || data.status === VIP_ORDER_STATUS['订单关闭']"
      >
        <el-row>
          <el-col :span="7">
            <el-form-item label="订单编号">
              {{ data.orderNum }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="下单人">
              {{ data.orderUserNickName || '-' }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="会员类型">
              {{ findSetMealTitle(data.packageType) }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="百度汇率">
              {{ data.isDefaultExchangeRate ? '-' : data.currentExchangeRate || '-' }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="套餐金额">{{ data.packageAmount }} USD</el-form-item>
          </el-col>
          <el-col :span="6" v-if="data.status === VIP_ORDER_STATUS['待支付']">
            <el-form-item label="钱包支付">
              {{ data.useBalance ? data.useBalance.toFixed(2) : 0 }} CNY
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="优惠金额">
          <template v-if="data.orderDiscountDetailVOS && data.orderDiscountDetailVOS.length > 0">
            {{ handleDiscount(data.orderDiscountDetailVOS, 'discount') }} CNY
            <el-tag style="margin-left: 20px" type="danger" round>
              {{ handleDiscount(data.orderDiscountDetailVOS, 'type') }}
            </el-tag>
          </template>
          <span v-else>-</span>
        </el-form-item>

        <div class="flex-end amount-box" v-if="data.status === VIP_ORDER_STATUS['待支付']">
          <span style="color: var(--el-text-color-regular); padding-right: 12px">还需支付</span>
          <span class="amount" v-if="data.isDefaultExchangeRate">-</span>
          <span
            class="amount"
            v-else-if="
              data.payType == PAY_TYPE['全币种支付'] ||
              data.payType == PAY_TYPE['全币种支付'] + PAY_TYPE['余额支付']
            "
          >
            {{ data.surplusAmountDollar ? data.surplusAmountDollar.toFixed(2) : 0 }} USD
          </span>
          <span class="amount" v-else>{{ data.surplusAmount ? data.surplusAmount.toFixed(2) : 0 }} CNY</span>
        </div>
      </template>
      <template v-if="data.status === VIP_ORDER_STATUS['待审核']">
        <el-row>
          <el-col :span="7">
            <el-form-item label="订单编号">
              {{ data.orderNum }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="下单人">
              {{ data.orderUserNickName || '-' }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="支付方式">
              <span v-if="VIP_ORDER_STATUS['订单关闭'] === data.status">-</span>
              <span v-else>{{ data.payType ? payType(data.payType) : '-' }}</span>
              {{ data?.payType == 7 ? '- ' + payMoneyTypeStatus(data?.payTypeDetail) : '' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="百度汇率">
              {{ data.isDefaultExchangeRate ? '-' : data.currentExchangeRate || '-' }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="会员类型">
              {{ findSetMealTitle(data.packageType) }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="钱包支付">
              {{ data.useBalance ? data.useBalance.toFixed(2) : 0 }} CNY
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="套餐金额">{{ data.packageAmount }} USD</el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优惠金额">
              <template v-if="data.orderDiscountDetailVOS && data.orderDiscountDetailVOS.length > 0">
                {{ handleDiscount(data.orderDiscountDetailVOS, 'discount') }} CNY
                <el-tag style="margin-left: 20px" type="danger" round>
                  {{ handleDiscount(data.orderDiscountDetailVOS, 'type') }}
                </el-tag>
              </template>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
        </el-row>
        <div class="flex-end amount-box">
          <span class="amount" v-if="data.isDefaultExchangeRate">-</span>
          <span
            class="amount"
            v-else-if="
              data.payType == PAY_TYPE['全币种支付'] ||
              data.payType == PAY_TYPE['全币种支付'] + PAY_TYPE['余额支付']
            "
          >
            {{ data.surplusAmountDollar ? data.surplusAmountDollar.toFixed(2) : 0 }} USD
            <span style="margin: 0 5px">/</span>
          </span>
          <span class="amount">{{ data.surplusAmount ? data.surplusAmount.toFixed(2) : 0 }} CNY</span>
        </div>
      </template>
      <template v-if="data.status === VIP_ORDER_STATUS['支付成功']">
        <el-row>
          <el-col :span="7">
            <el-form-item label="订单编号">
              {{ data.orderNum }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="会员类型">
              {{ findSetMealTitle(data.packageType) }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="下单人">
              {{ data.orderUserNickName || '-' }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="权益时间">
              <div class="flex-start gap-10">
                <div>
                  {{
                    data.memberEndTime
                      ? (data.memberStartTime || '-') + ' 至 ' + (data.memberEndTime || '-')
                      : '-'
                  }}
                </div>
                <el-tag
                  v-if="data.presentedTime && data.status != VIP_ORDER_STATUS['订单关闭']"
                  type="warning"
                  round
                  size="small"
                >
                  <span>加赠</span>
                  <span>{{ data.presentedTime }}</span>
                  <span v-if="data.presentedTimeType === 1">天</span>
                  <span v-else-if="data.presentedTimeType === 2">个月</span>
                  <span v-else-if="data.presentedTimeType === 3">年</span>
                </el-tag>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="套餐金额">{{ data.packageAmount }} USD</el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="实付金额">{{ data.realPayAmount }} CNY</el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="支付方式">
              <span>{{ data.payType ? payType(data.payType) : '-' }}</span>
              {{ data?.payType == 7 ? '- ' + payMoneyTypeStatus(data?.payTypeDetail) : '' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优惠金额">
              <template v-if="data.orderDiscountDetailVOS && data.orderDiscountDetailVOS.length > 0">
                {{ handleDiscount(data.orderDiscountDetailVOS, 'discount') }} CNY
                <el-tag style="margin-left: 20px" type="danger" round>
                  {{ handleDiscount(data.orderDiscountDetailVOS, 'type') }}
                </el-tag>
              </template>
              <span v-else>-</span>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="钱包支付">
              {{ data.useBalance ? data.useBalance.toFixed(2) : 0 }} CNY
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7">
            <el-form-item label="支付时间">
              {{ data.payTime || '-' }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="百度汇率">
              {{ data.isDefaultExchangeRate ? '-' : data.currentExchangeRate || '-' }}
            </el-form-item>
          </el-col>
        </el-row>
        <div class="flex-end amount-box">
          <span style="color: var(--el-text-color-regular); padding-right: 12px">已支付</span>
          <span class="amount" v-if="data.isDefaultExchangeRate">-</span>
          <span
            class="amount"
            v-else-if="
              data.payType == PAY_TYPE['全币种支付'] ||
              data.payType == PAY_TYPE['全币种支付'] + PAY_TYPE['余额支付']
            "
          >
            {{ data.payAmountDollar ? data.payAmountDollar.toFixed(2) : 0 }} USD
            <span style="margin: 0 5px">/</span>
          </span>
          <span class="amount">{{ data.payAmount ? data.payAmount.toFixed(2) : 0 }} CNY</span>
        </div>
      </template>
    </el-form>

    <div class="flex-end btn" v-if="data.status === VIP_ORDER_STATUS['待支付']">
      <el-button round plain :disabled="disabled" @click="handleAction('取消订单')">取消订单</el-button>

      <el-button v-if="!data.isDefaultExchangeRate" round type="primary" :disabled="disabled" @click="toBuy">
        去支付
      </el-button>
      <el-tooltip v-else :visible="true" placement="top" effect="custom-danger" append-to=".pages">
        <template #content>
          <div class="flex-start gap-5" style="color: red">
            <el-icon><WarnTriangleFilled /></el-icon>
            汇率获取失败，请联系客服
          </div>
        </template>
        <el-button type="primary" round :disabled="disabled" @click="toBuy">去支付</el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import CountDown from '@/components/public/CountDown.vue'
import { PAY_TYPE, payType, VIP_ORDER_STATUS, payMoneyTypeStatus } from '@/utils/order'
import { vipSetMealList, specialOfferList } from '@/views/center/data'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { ref } from 'vue'

const router = useRouter()
const store = useUserStore()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const disabled = ref(false)

const emits = defineEmits<{
  action: [btn: string, row: any]
  countDownEnd: []
}>()

function findSetMealLabel(val: number) {
  let t = vipSetMealList.find(item => item.type == val)
  return t ? t.label : ''
}
function findSetMealTitle(val: number) {
  let t = vipSetMealList.find(item => item.type == val)
  return t ? t.title : ''
}

function handleDiscount(list: any[], type?: string) {
  let t = list.find(item => item.type == 2 || item.type == 3 || item.type == 5)
  if (type == 'discount') {
    return t ? t.discountAmount : 0
  } else if (type == 'type') {
    return t ? specialOfferList.find(item => item.value == t.type)?.label : ''
  }
}

function handleOrderTime(time: string) {
  let date = time.split(' ')[0] + ' 00:00:00'
  return new Date(date).getTime() + 1000 * 60 * 60 * 24 * 31 - 1000 // 加30天 到最后一天 23:59:59
}

function handleTimeEnd() {
  disabled.value = true
}

// 按钮操作
function handleAction(btn: string) {
  emits('action', btn, props.data)
}

// 去支付
function toBuy() {
  store.realTimeCheckVip().then(() => {
    if (store.isOwnerAcc() || store.isVip()) {
      router.push({
        name: 'center-pay',
        state: { orderNum: props.data.orderNum, type: props.data.packageType, payType: props.data.payType },
      })
    }
  })
}
</script>

<style scoped lang="scss">
.order-item-box {
  position: relative;
  border-radius: 10px;
  margin: 20px 0 20px;
  padding: 0 0 10px;
  background-color: var(--bg);
  border: 1px solid var(--el-color-primary-light-7);
  overflow: hidden;

  .head-box {
    height: 55px;
    padding: 0 20px;
    background-color: var(--el-color-primary-light-7);
    margin-bottom: 10px;

    .title {
      gap: 10px;
    }

    img {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
    }
    span {
      font-size: 15px;
      font-weight: 600;
    }

    .icon-img {
      width: 16px;
      height: 16px;
    }
  }
  .order-status {
    text-align: right;
    font-size: 15px;
    line-height: 16px;

    .order-time {
      font-weight: 400;
      font-size: 12px;
      color: #a0816c;
      background: rgb(255, 245, 231);
      padding: 0 10px;
      border-radius: 10px;
      margin-top: 5px;

      span {
        font-weight: 400;
        font-size: 12px;
        color: #a0816c;
      }
    }
  }
  .amount-box {
    position: absolute;
    top: 65px;
    right: 20px;
    font-size: 14px;
    color: var(--text-color);
    line-height: 32px;

    .amount {
      font-weight: bold;
      font-size: 16px;
      color: #ff5722;
    }
  }
  .btn {
    position: absolute;
    bottom: 20px;
    right: 20px;

    .el-button {
      width: 100px;
    }
  }
  .btn2 {
    position: absolute;
    top: 0;
    right: 20px;
    height: 100%;
    justify-content: center;
    gap: 10px;
  }

  :deep(.el-form-item) {
    margin-bottom: 5px;
  }
}
</style>
