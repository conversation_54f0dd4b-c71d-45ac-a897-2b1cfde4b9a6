<template>
  <div class="pages">
    <div class="flex-start tabs">
      <!-- <div v-if="store.isOwnerAcc()" class="tab" @click="router.replace('/vip')">我的会员</div> -->
      <div class="tab border-left" @click="router.replace('/center/info')">个人资料</div>
      <div v-if="store.isOwnerAcc() && store.isViped()" class="tab border-left" @click="router.replace('/center/acc')">
        子账号管理
      </div>
      <div class="tab active">黑名单</div>
    </div>
    <div class="w-bg center-body">
      <div>
        <el-tabs v-model="curTab">
          <el-tab-pane label="黑名单列表" name="1">
            <div class="table-box" v-if="!modelList.length">
              <Empty description="暂无数据" :image-size="120" />
            </div>
            <div class="table-box" v-else>
              <div class="flex-between model-list-item" v-for="(row, index) in modelList">
                <div class="flex-start gap-10">
                  <el-avatar class="model-avatar" icon="UserFilled" :src="$picUrl + row.modelPic + '!3x4'" />
                  <div class="name">{{ row.name }}</div>
                  <NationTag :type="row.nation" isIcon color="var(--text-gray-color)" />
                  <ModelTypeTag :type="row.type" isIcon color="var(--text-gray-color)" />
                </div>
                <div>
                  <el-button type="primary" link @click="handleBlackList(row.modelId)">取消拉黑</el-button>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import NationTag from '@/components/public/tag/NationTag.vue'
import ModelTypeTag from '@/components/public/tag/ModelTypeTag.vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { blackModelList, cancelBlackModel } from '@/api/model'
import { ref } from 'vue'
import { MessageBox } from '@/utils/message'
import { ElMessage } from 'element-plus'

const router = useRouter()
const store = useUserStore()
store.getUserInfo()

const modelList = ref<any[]>([])
const loading = ref(false)
const curTab = ref('1')

function getList() {
  loading.value = true
  blackModelList({})
    .then(res => {
      modelList.value = res.data?.rows || []
    })
    .finally(() => {
      loading.value = false
    })
}

function handleBlackList(modelId: any) {
  MessageBox(`<p>确认取消拉黑？</p>`, {
    showClose: true,
    title: '提示',
    customStyle: {
      '--el-messagebox-width': '420px',
    },
    cancelButtonText: '取消',
    confirmButtonText: '确定',
  })
    .then(() => {
      cancelBlackModel(modelId).then(res => {
        if (res.code == 200) {
          getList()
          ElMessage.success('操作成功')
        }
      })
    })
    .catch(() => {})
}

getList()
</script>

<style scoped lang="scss">
@use '@/views/center/tabs.scss';
.center-body {
  position: relative;
  border-radius: 8px;
  padding-top: 0 !important;
  padding-right: 0 !important;
  min-width: 1040px;

  .table-box {
    min-height: 500px;
    height: calc(100vh - 220px);
    padding-right: 20px;
    overflow-y: overlay;

    .model-list-item {
      border-bottom: 1px solid var(--border-gray-color);
      padding: 15px 0;

      .name {
        margin-right: 20px;
      }
    }
  }

  :deep(.el-tabs) {
    .el-tabs__header {
      padding-right: 20px;
    }
  }
}
</style>
