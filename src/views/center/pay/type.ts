/**
 * 会员支付订单详情
 */
export type VipOrderInfo = {
  auditStatus: number,
  businessAccountDetailVO: {
    account: string,
    balance: number,
    businessId: number,
    businessName: string,
    businessStatus: number,
    customerType: number,
    externalUserId: string,
    id: number,
    invoiceContent: string,
    invoiceDutyParagraph: string,
    invoiceTitle: string,
    invoiceTitleType: number,
    isBalanceLock: number,
    isOwnerAccount: number,
    isProxy: number,
    lastLoginTime: string,
    memberCode: string,
    memberFirstTime: string,
    memberFirstType: number,
    memberLastTime: string,
    memberPackageType: number,
    memberStatus: number,
    memberType: number,
    memberValidity: string,
    name: string,
    nickName: string,
    ownerAccount: string,
    pic: string,
    status: number,
    unionid: string,
    waiterId: number
  },
  id: number,
  orderAmount: number,
  orderId: number,
  orderNoteVo: {
    createTime: string,
    dutyParagraph: string,
    id: number,
    orderNum: string,
    title: string,
    updateTime: string
  },
  orderNum: string,
  orderRemark: string,
  orderTime: string,
  orderUserId: number,
  packageType: number,
  payAccount: string,
  payAmount: number,
  payTime: string,
  payType: number,
  payUserId: number,
  realPayAmount: number,
  status: number,
  useBalance: number
}