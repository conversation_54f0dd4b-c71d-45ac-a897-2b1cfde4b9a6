<template>
  <div class="pages">
    <div class="head-box">
      <!-- <div class="head-box__title">
        <div class="head-title">
          <el-icon color="#5695c0"><CaretRight /></el-icon>
          <span style="margin-left: 5px">我的订单</span>
          <span style="color: #e4e8eb; margin: 0 10px">/</span>
          <span>创建订单</span>
          <span style="color: #e4e8eb; margin: 0 10px">/</span>
          <span style="font-weight: 800">支付订单</span>
        </div>
      </div> -->
      <div class="head-content">
        <div class="head-icon">
          <img style="width: 80px; height: 100%" src="@/assets/icon/icon_order_succ.png" alt="" />
        </div>
        <div class="head-text">
          <div class="head-text__title">订单提交成功，请尽快付款！</div>
          <div class="head-text__order">
            订单号：{{ orderNum }}
            <CopyButton v-if="orderNum" :copy-content="orderNum" link>
              <el-icon size="14"><CopyDocument /></el-icon>
            </CopyButton>
          </div>
          <el-divider />
          <div class="title">订单结算</div>
          <div class="info-box card-one" v-loading="loading">
            <el-row>
              <template v-for="item in vipSetMealList">
                <template v-if="vipSetMealType === item.type">
                  <el-col :span="8">
                    <div class="flex-start">
                      <div class="label">会员类型：</div>
                      <div class="money">
                        {{ item.title }}
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="8">
                    <div class="flex-start">
                      <div class="label">套餐金额：</div>
                      <div class="money">{{ item.$ }} USD</div>
                    </div>
                  </el-col>
                </template>
              </template>
              <el-col :span="8">
                <div class="flex-start">
                  <div class="label">实时百度汇率：</div>
                  <div class="money" v-if="errorDisabled">-</div>
                  <div class="money" v-else>{{ orderInfo.currentExchangeRate }}</div>
                </div>
              </el-col>
            </el-row>
            <el-row v-if="isSeedCode">
              <el-col :span="12">
                <el-form
                  class="seed-code-form"
                  ref="formRef"
                  :model="form"
                  :rules="rules"
                  status-icon
                  @submit.prevent
                >
                  <el-form-item prop="code" label="">
                    <div class="flex-start" style="margin-top: 18px; align-items: center">
                      <div class="form-label">种草码：</div>
                      <el-input
                        placeholder="如有邀请码可在此填写"
                        v-model="form.code"
                        maxlength="10"
                        style="width: 170px"
                        :disabled="checkLoading || isAnotherPay"
                      ></el-input>
                      <el-button
                        type="primary"
                        native-type="submit"
                        @click="checkCode"
                        :disabled="!form.code || isAnotherPay"
                        :loading="checkLoading"
                        style="margin-left: 10px; height: 30px"
                      >
                        验证
                      </el-button>
                    </div>
                    <template #error="{ error }">
                      <div class="seed-code-error">{{ error }}</div>
                    </template>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
            <el-row :style="{ 'margin-top': isSeedCode ? '' : '18px' }">
              <el-col :span="8">
                <div class="flex-start" style="align-items: center">
                  <div class="label">优惠金额：</div>
                  <template v-if="isSeedCode">
                    <div class="money">
                      {{ orderInfo.seedCodeDiscount ? orderInfo.seedCodeDiscount + ' CNY' : '-' }}
                    </div>
                    <div class="tag" v-if="orderInfo.seedCodeDiscount">种草码优惠</div>
                  </template>
                  <template v-else>
                    <div class="money">
                      {{ handleDiscountAmount('discount') }}
                    </div>
                    <div class="tag" v-if="halfPriceDiscount">{{ handleDiscountAmount('tag') }}</div>
                  </template>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="flex-start">
                  <div class="label">订单金额：</div>
                  <div class="money" v-if="errorDisabled">-</div>
                  <div class="money" v-else>
                    <template v-if="payWay === PAY_TYPE['全币种支付']">
                      <div
                        :class="{ tips: orderInfo.afterSeedCodeDiscount }"
                        :style="{ textDecoration: orderInfo.afterSeedCodeDiscount ? 'line-through' : 'none' }"
                      >
                        {{ orderInfo.oldNeedPayAmountDollar }} USD
                      </div>
                      <div v-if="orderInfo.afterSeedCodeDiscount">
                        {{ orderInfo.afterSeedCodeDiscountDollar }} USD /
                        {{ orderInfo.afterSeedCodeDiscount }} CNY
                      </div>
                    </template>
                    <template v-else>
                      <div
                        :class="{ tips: orderInfo.afterSeedCodeDiscount }"
                        :style="{ textDecoration: orderInfo.afterSeedCodeDiscount ? 'line-through' : 'none' }"
                      >
                        <span>{{ orderInfo.oldNeedPayAmountDollar }} USD /</span>
                        {{ orderInfo.oldNeedPayAmount }} CNY
                      </div>
                      <div v-if="orderInfo.afterSeedCodeDiscount">
                        {{ orderInfo.afterSeedCodeDiscountDollar }} USD /
                        {{ orderInfo.afterSeedCodeDiscount }} CNY
                      </div>
                    </template>
                    <!-- <span class="tips">（已换算成人民币）</span> -->
                  </div>
                </div>
              </el-col>
            </el-row>
            <!-- <template v-for="(item, i) in vipSetMealList">
              <div class="flex-start" :key="i" v-if="vipSetMealType === item.type">
                <div class="label">{{ item.title }}套餐：</div>
                <div class="money">￥{{ item.price }}</div>
              </div>
            </template> -->
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="title primary">订单提交成功，请尽快付款！</div> -->

    <!-- <div class="head-box card flex-start">
      订单号：{{ orderNum }}
      <CopyButton v-if="orderNum" :copy-content="orderNum" link>
        <el-icon><CopyDocument /></el-icon>
      </CopyButton>
    </div> -->

    <!-- <div class="title">订单结算</div> -->
    <div class="card-two" v-loading="loading">
      <div style="font-size: 22px" v-if="!isAllBalancePay">请选择支付方式</div>
      <!-- <div class="select-pay-label" v-if="!isAllBalancePay">
        <el-divider>请选择支付方式</el-divider>
      </div> -->
      <AnotherPayInfo v-if="isAnotherPay" :orderNum="orderNum" :disabled="disabled || errorDisabled" />
      <template v-else>
        <div class="select-pay-box" v-if="!isAllBalancePay">
          <el-radio-group size="large" v-model="payWay" :disabled="disabled" @change="payWayChange">
            <el-radio-button :value="PAY_TYPE['微信支付']" :label="PAY_TYPE['微信支付']">
              <i
                class="iconfont icon-weixinzhifu"
                :class="{ 'wx-color': payWay !== PAY_TYPE['微信支付'] }"
              ></i>
              微信支付
            </el-radio-button>
            <el-tooltip :content="payDollarTips" raw-content placement="top" effect="dark">
              <el-radio-button
                :value="PAY_TYPE['全币种支付']"
                :label="PAY_TYPE['全币种支付']"
                :disabled="disabled || minAmount"
                style="position: relative; overflow: hidden"
              >
                <div class="btn-corner-mark"></div>
                <i class="iconfont icon-Dollar"></i>
                全币种支付
              </el-radio-button>
            </el-tooltip>
            <el-radio-button :value="PAY_TYPE['支付宝支付']" :label="PAY_TYPE['支付宝支付']">
              <i
                class="iconfont icon-zhifubaozhifu"
                :class="{ 'zfb-color': payWay !== PAY_TYPE['支付宝支付'] }"
              ></i>
              支付宝支付
            </el-radio-button>
            <!-- <el-radio-button :value="PAY_TYPE['银行卡转账']" :label="PAY_TYPE['银行卡转账']">
              <i v-if="payWay === PAY_TYPE['银行卡转账']" class="iconfont icon-iconfontjikediancanicon20"></i>
              <svg v-else class="icon" aria-hidden="true" style="width: 20px; height: 20px">
                <use xlink:href="#icon-yinhangqiazhuanzhang"></use>
              </svg>
              银行转账
            </el-radio-button> -->
            <!-- <el-tooltip content="对公转账，需要额外支付5%的开票服务费" placement="top" effect="light"> -->
            <el-radio-button :value="PAY_TYPE['对公转账']" :label="PAY_TYPE['对公转账']">
              <i class="iconfont icon-duigongzhuanzhang"></i>
              对公转账
            </el-radio-button>
            <!-- </el-tooltip>-->
          </el-radio-group>
        </div>
        <div class="balance-checkbox" style="display: flex; align-items: center">
          <div style="height: 40px; line-height: 38px">使用余额：</div>
          <div class="money-checkbox">
            <template v-if="orderInfo.orderBalance || orderInfo.validBalance">
              <!-- :label="occupying ? '余额已被其他订单占用无法使用' : '使用账户余额抵扣支付 '" -->
              <el-checkbox
                v-model="checked"
                :disabled="disabled || occupying || errorDisabled"
                label="使用钱包支付"
                size="large"
                @change="checkedChange"
              />
              <!-- <template v-if="!occupying"> -->
              &nbsp;
              <span v-if="errorDisabled">-</span>
              <span v-else-if="orderInfo.orderBalance">
                {{ toFixed2(orderInfo.orderBalance, 0) }}&nbsp;CNY
              </span>
              <span v-else-if="orderInfo.validBalance > orderInfo.oldNeedPayAmount">
                {{ toFixed2(orderInfo.oldNeedPayAmount, 0) }}&nbsp;CNY
              </span>
              <span v-else>{{ toFixed2(orderInfo.validBalance, 0) }}&nbsp;CNY</span>
              <!-- </template> -->
            </template>
            <div v-else style="height: 40px; line-height: 38px">暂无可用余额</div>
          </div>
        </div>
        <div class="total-box">
          <div>
            还需支付：
            <span v-if="errorDisabled" style="font-size: 20px">-</span>
            <span v-else>
              {{
                payWay === PAY_TYPE['全币种支付']
                  ? toFixed2(orderInfo.payAmountDollar)
                  : toFixed2(orderInfo.payAmount)
              }}
            </span>
            {{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}
            <span v-if="payWay === PAY_TYPE['全币种支付']">
              <span style="font-weight: normal; margin: 0 8px">/</span>
              <span>{{ toFixed2(orderInfo.payAmount) }}</span>
              <span style="font-weight: normal; font-size: 16px">&nbsp;CNY</span>
            </span>
          </div>
          <!-- <div v-if="payWay === PAY_TYPE['对公转账'] && !isAllBalancePay && !errorDisabled" class="tips">
            已包含服务费：{{ orderInfo.taxPointCost }}元（{{ orderInfo.taxPoint }}%）
          </div> -->
        </div>
        <div class="flex-end" style="margin: 30px 0">
          <el-button
            v-if="isAnotherPayButton"
            class="another-pay-btn"
            round
            :disabled="disabled || errorDisabled"
            @click="handleAnotherPay"
          >
            找人代付
            <span class="tip">*可以分享给财务或其他人帮你付款</span>
          </el-button>
          <el-button
            v-if="isAllBalancePay"
            type="primary"
            round
            style="width: 180px; height: 40px; font-size: 15px; font-weight: bold"
            :disabled="disabled || errorDisabled"
            @click="balancePay"
          >
            余额支付
          </el-button>
          <el-button
            v-else
            type="primary"
            round
            style="width: 180px; height: 40px; font-size: 15px; font-weight: bold"
            :disabled="disabled || errorDisabled"
            @click="tryLock"
          >
            立即支付
          </el-button>
        </div>
      </template>
    </div>

    <template v-if="payWay === PAY_TYPE['全币种支付']">
      <TransferMoneyPaySelect
        :payeeId="orderInfo.payeeId"
        :pay-amount="orderInfo.payAmountDollar"
        :cny-amount="orderInfo.payAmount"
        :orderNum="orderNum"
        ref="TransferMoneyPaySelectRef"
        @submit="onSubmit"
      />
    </template>

    <TransferMoneyPay
      v-if="underWay"
      ref="TransferMoneyPayRef"
      :title="dialogTitle"
      :pay-way="payWay"
      :pay-amount="payWay === PAY_TYPE['全币种支付'] ? orderInfo.payAmountDollar : orderInfo.payAmount"
      :cny-amount="orderInfo.payAmount"
      :tax-point-cost="orderInfo.taxPointCost"
      :orderNum="orderNum"
      :seedCode="seedCode"
      @submit="onSubmit"
      @close="handleClose"
    />

    <QrCodeDialog
      v-if="underWay"
      ref="QrCodeDialogRef"
      width="450px"
      :title="dialogTitle"
      :http-code="getPayCode"
      :check-code="getPayCheck"
      :auto-check-code="false"
      :is-zfb-pay="payWay === PAY_TYPE['支付宝支付']"
      :loading="QrCodeDialogLoading"
      @close="handleClose"
    >
      <!-- @success="payCodeSuccess" @change="handleQrCodePayStatus" -->
      <!-- <DownloadButton
        class="link-a down-btn"
        link
        text="下载凭证单据"
        loadingText="下载中"
        message="确认下载凭证单"
        fileName="订单凭据"
        url="/order/order/get-document-pdf"
        :params="{
          orderNum,
        }"
      /> -->
      <div class="qrcode-pay-box">
        <span class="flex-start gap-5 qrcode-tips">
          <img v-if="payWay === PAY_TYPE['微信支付']" src="@/assets/icon/icon_weixinzhifu.png" alt="" />
          <!-- <img v-if="payWay === PAY_TYPE['支付宝支付']" src="@/assets/icon/icon_zhifubaozhifu.png" alt="" /> -->
          <svg v-if="payWay === PAY_TYPE['支付宝支付']" class="icon" aria-hidden="true">
            <use xlink:href="#icon-zhifubaozhifu"></use>
          </svg>
          手机
          {{ payWay === PAY_TYPE['微信支付'] ? '微信' : '支付宝' }}
          扫码支付
        </span>
        <div class="pay-total-box">
          <!-- <div>支付金额</div> -->
          <div class="pay-total">
            <span v-if="payWay === PAY_TYPE['全币种支付']">{{ orderInfo.payAmountDollar }}</span>
            <span v-else>{{ orderInfo.payAmount }}</span>
            {{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}
          </div>
        </div>
      </div>
    </QrCodeDialog>

    <AnotherPayLink ref="AnotherPayLinkRef" @submit="showAnotherPay" />
    <PayTipDialog />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { PAY_TYPE, payType } from '@/utils/order'
import CopyButton from '@/components/public/button/CopyButton.vue'
// import DownloadButton from '@/components/public/button/DownloadButton.vue'
import TransferMoneyPay from '@/views/order/components/dialog/TransferMoneyPay.vue'
import TransferMoneyPaySelect from '@/views/order/components/dialog/TransferMoneyPaySelect.vue'
import QrCodeDialog from '@/components/public/dialog/QrCodeDialog.vue'
import AnotherPayLink from '@/views/anotherPay/dialog/Link.vue'
import AnotherPayInfo from '@/views/anotherPay/info.vue'
import PayTipDialog from '@/views/order/components/dialog/PayTipDialog.vue'
import {
  orderPayCode,
  orderPayCheck,
  orderUploadCredentials,
  orderBalancePay,
  orderMemberPayLock,
} from '@/api/order'
import { memberOrderPayInfo, checkSeedCode } from '@/api/vip'
import { useRouter } from 'vue-router'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { vipSetMealList, specialOfferList } from '@/views/center/data'
import useQrCodeVerify from '@/hooks/useQrCodeVerify'
import { useUserStore } from '@/stores/modules/user'
import { aZ_d_reg } from '@/utils/RegExp'
import { nextTick } from 'vue'
import { usePay } from '@/hooks/usePay'

const { handleQrCodePayStatus } = useQrCodeVerify()
const {
  underWay,
  isOpenPayDialog,
  routerPathUrl,
  checkPlatform,
  isAnotherPay,
  payTipDialogVisible,
  payTipType,
  checkPayStatus,
} = usePay(history.state.orderNum)

const router = useRouter()
const store = useUserStore()
store.getUserInfo()

function toFixed2(val: any, df?: any) {
  if (val) {
    return val.toFixed(2)
  }
  if (df !== undefined) {
    return df
  }
  return '0'
}

const orderNum = ref('')
const vipSetMealType = ref(0)
const orderInfo = ref<{
  isBalanceLock: number
  validBalance: number
  videoPrice: number
  picPrice: number
  exchangePrice: number
  servicePrice: number
  currentExchangeRate: number
  orderAmount: number
  oldNeedPayAmount: number
  oldNeedPayAmountDollar: number
  orderDiscountDetailVOS: any[]
  afterSeedCodeDiscount: number
  afterSeedCodeDiscountDollar: number
  balance: number
  taxPoint: number
  taxPointCost: number
  payAmount: number
  payAmountDollar: number
  payType: PAY_TYPE | null
  orderBalance: number
  payeeId: number
  seedCodeDiscount: number
}>({
  videoPrice: 0,
  picPrice: 0,
  exchangePrice: 0,
  servicePrice: 0,
  currentExchangeRate: 0,
  orderAmount: 0,
  oldNeedPayAmount: 0,
  afterSeedCodeDiscount: 0,
  balance: 0,
  taxPoint: 0,
  taxPointCost: 0,
  payType: null,
  orderDiscountDetailVOS: [],
  orderBalance: 0,
  payAmount: 0,
  isBalanceLock: 0,
  validBalance: 0,
  oldNeedPayAmountDollar: 0,
  payAmountDollar: 0,
  afterSeedCodeDiscountDollar: 0,
  payeeId: 0,
  seedCodeDiscount: 0,
})
const formRef = ref()
const isSeedCode = ref(false)
const form = ref({
  code: '',
  success: '',
})
const checkLoading = ref(false)
const seedCode = computed(() => {
  if (isSeedCode.value && form.value.success) {
    return form.value.success
  }
  return ''
})
const checked = ref(false)
const rules = {
  code: [
    { required: true, message: '请输入验证码', trigger: 'manul' },
    { validator: checkCodeValidator, trigger: 'manul' },
  ],
}
const occupying = ref(false)
const payWay = ref<PAY_TYPE>(PAY_TYPE['微信支付'])
const platform = ref(0)

const TransferMoneyPayRef = ref<InstanceType<typeof TransferMoneyPay>>()
const TransferMoneyPaySelectRef = ref<InstanceType<typeof TransferMoneyPaySelect>>()
const QrCodeDialogRef = ref<InstanceType<typeof QrCodeDialog>>()
const AnotherPayLinkRef = ref<InstanceType<typeof AnotherPayLink>>()

const QrCodeDialogLoading = ref(false)

const dialogTitle = computed(() => {
  return payType(payWay.value) as string
})

const disabled = ref(true)
const loading = ref(true)
const errorDisabled = ref(false)

const payDollarTips = ref(`<div style="text-align: center">
    跨境卖家的最优选择<br/>
    收款支持币种:  离岸人民币,美元,澳元,加币,欧元,英镑,港币,日元,新西兰元,新加坡元
  </div>`)
const minAmount = computed(() => {
  if (orderInfo.value.payAmountDollar < 50) {
    payDollarTips.value = '<div>当前交易金额低于 50 USD，不支持使用全币种支付。</div>'
    return true
  }
  payDollarTips.value = `<div style="text-align: center">
    跨境卖家的最优选择<br/>
    收款支持币种:  离岸人民币,美元,澳元,加币,欧元,英镑,港币,日元,新西兰元,新加坡元
  </div>`
  return false
})

// 是否余额支付
const isAllBalancePay = computed(() => {
  return checked.value && orderInfo.value.payAmount === 0
})
const use_balance = computed(() => {
  if (!checked.value) {
    return 0
  }
  return orderInfo.value.validBalance > orderInfo.value.oldNeedPayAmount
    ? orderInfo.value.oldNeedPayAmount
    : orderInfo.value.validBalance
})

// 找人代付按钮
const isAnotherPayButton = computed(() => {
  return (
    (!checked.value || !(orderInfo.value.orderBalance || orderInfo.value.validBalance)) &&
    (payWay.value === PAY_TYPE['全币种支付'] ? orderInfo.value.payAmountDollar : orderInfo.value.payAmount) >
      0 &&
    !form.value.success
  )
})

async function init() {
  // console.log(history.state);
  if (!history.state.orderNum) {
    window.location.href = '/center/tradingRecord'
    return
  }
  routerPathUrl.value = '/center/tradingRecord'
  orderNum.value = history.state.orderNum
  vipSetMealType.value = history.state.type
  let p = +history.state.payType
  if (p) {
    if (p < PAY_TYPE['余额支付']) {
      payWay.value = p
    } else {
      payWay.value = p - PAY_TYPE['余额支付']
    }
  }
  await memberOrderPayInfo({
    orderNum: orderNum.value,
    isPublic: payWay.value === PAY_TYPE['对公转账'] ? 0 : 1,
    useBalance: use_balance.value || undefined,
  })
    .then(res => {
      // 记录支付方式
      if (res.data.payType != null) {
        history.state.payType = res.data.payType
        if (res.data.payType < PAY_TYPE['余额支付']) {
          payWay.value = res.data.payType
        } else if (res.data.payType === PAY_TYPE['余额支付']) {
          payWay.value = PAY_TYPE['余额支付']
        } else {
          payWay.value = res.data.payType - PAY_TYPE['余额支付']
        }
      }
      getPayInfo(true)
    })
    .catch(() => {
      errorDisabled.value = true
    })
}

function checkCodeValidator(rule: any, value: string, callback: any) {
  if (!value || checkLoading.value) {
    return callback()
  }
  form.value.success = ''
  if (!aZ_d_reg.test(value)) {
    nextTick(() => getPayInfo())
    return callback(new Error('邀请码错误'))
  }
  checkLoading.value = true
  checkSeedCode({
    seedCode: value,
    orderNum: orderNum.value,
  })
    .then((res: any) => {
      if (res.data) {
        if (res.data.result) {
          form.value.success = value
          callback()
        } else {
          callback(new Error(res.data.errorMessage || 'ERROR'))
        }
      } else {
        callback(new Error(res.msg || 'ERROR'))
      }
      nextTick(() => getPayInfo())
    })
    .catch(err => {
      callback(new Error(err.data?.msg || '网络错误，请稍后再试'))
    })
    .finally(() => {
      checkLoading.value = false
    })
}
function checkCode() {
  formRef.value.validate()
}

// 支付二维码
function getPayCode() {
  let params: any = {
    orderNum: orderNum.value,
    payType: payWay.value,
  }
  if (seedCode.value) {
    params.seedCode = seedCode.value
  }
  return orderPayCode(params)
}
// 二维码状态
function getPayCheck() {
  let params = {
    orderNum: orderNum.value,
    platform: platform.value,
  }
  return orderPayCheck(params)
}

// 扫码成功
function payCodeSuccess() {
  routerPathUrl.value = '/vip'
  underWay.value = false
  // goback()
  ElMessage.success('支付成功')
}

// 支付方式选择
function payWayChange() {
  getPayInfo()
}
// 使用余额选择
function checkedChange() {
  getPayInfo()
}
// 找人代付
function handleAnotherPay() {
  AnotherPayLinkRef.value?.open(orderNum.value)
}
function showAnotherPay() {
  if (orderInfo.value.payType == null) {
    payWay.value = PAY_TYPE['微信支付']
  }
  getPayInfo()
  isAnotherPay.value = true
}

// 打开支付窗口
function openPayBox() {
  checkPlatform.value = payWay.value
  if (payWay.value === PAY_TYPE['全币种支付']) {
    TransferMoneyPaySelectRef.value?.open(orderInfo.value)
  }
  if (payWay.value === PAY_TYPE['银行卡转账'] || payWay.value === PAY_TYPE['对公转账']) {
    TransferMoneyPayRef.value?.open({ payeeId: orderInfo.value.payeeId })
  } else if (payWay.value === PAY_TYPE['微信支付']) {
    platform.value = 1
    handleOpenQrcodeDelay()
  } else if (payWay.value === PAY_TYPE['支付宝支付']) {
    platform.value = 2
    handleOpenQrcodeDelay()
  }
}
let openTimer: any
// 打开 微信支付/支付宝支付 延迟check接口
function handleOpenQrcodeDelay() {
  // QrCodeDialogLoading.value = true
  QrCodeDialogRef.value?.open()
  openTimer = setTimeout(() => {
    isOpenPayDialog.value = true
    // QrCodeDialogLoading.value = false
  }, 5000)
}

function tryLock() {
  if (!payWay.value || payWay.value >= PAY_TYPE['余额支付']) {
    ElMessage.warning('请选择支付方式')
    return
  }
  loading.value = true
  orderMemberPayLock({
    orderNum: orderNum.value,
    payType: payWay.value,
    payAmount: orderInfo.value.payAmount,
    taxPointCost: orderInfo.value.taxPointCost,
    useBalance: use_balance.value,
  })
    .then(() => {
      let t = payWay.value
      if (checked.value) {
        t += PAY_TYPE['余额支付']
      }
      orderInfo.value.payType = t
      // openPayBox()
      getPayInfo(true)
    })
    .catch(() => {
      loading.value = false
    })
}

function handleDiscountAmount(type: string) {
  let t = orderInfo.value.orderDiscountDetailVOS.find(
    item => item.type == 2 || item.type == 3 || item.type == 5
  )
  if (type == 'discount') {
    return t ? t.discountAmount + ' CNY' : '-'
  } else if (type == 'tag') {
    return t ? specialOfferList.find(item => item.value == t.type)?.label : ''
  }
}

const halfPriceDiscount = ref(false)
// 获取订单信息
function getPayInfo(first: boolean = false) {
  loading.value = true
  disabled.value = true
  memberOrderPayInfo({
    orderNum: orderNum.value,
    isPublic: payWay.value === PAY_TYPE['对公转账'] ? 0 : 1,
    useBalance: use_balance.value || undefined,
    seedCode: seedCode.value || undefined,
  })
    .then(res => {
      orderInfo.value = res.data
      if (!res.data.orderNum || res.data.defaultExchangeRate) {
        errorDisabled.value = true
        if (res.data.defaultExchangeRate) {
          ElMessageBox.alert(
            `<div>
              <p style="text-align: center;margin: 10px 0;font-size: 20px">百度汇率自动获取失败</p>
              <p style="text-align: center;margin: 10px 0;font-size: 15px">请联系您的对接客服为您处理~</p>
            </div>`,
            '',
            {
              customStyle: {
                '--el-messagebox-width': '360px',
                '--el-messagebox-border-radius': '10px',
              },
              showClose: false,
              center: true,
              roundButton: true,
              showCancelButton: true,
              showConfirmButton: false,
              cancelButtonText: '知道了',
              cancelButtonClass: 'message-box-cancel-btn-primary',
              dangerouslyUseHTMLString: true,
              callback: () => {
                window.location.href = routerPathUrl.value
              },
            }
          )
        }
        return
      }
      if (res.data.orderDiscountDetailVOS && res.data.orderDiscountDetailVOS.length > 0) {
        halfPriceDiscount.value = res.data.orderDiscountDetailVOS.some((item: any) => item.type == 2)
        if (!halfPriceDiscount.value) {
          halfPriceDiscount.value = res.data.orderDiscountDetailVOS.some((item: any) => item.type == 3)
        }
      }
      errorDisabled.value = false
      // 是否可输入种草码
      isSeedCode.value = res.data.canInputSeedCode ? true : false
      // 是否锁定余额
      if (orderInfo.value.orderBalance > 0) {
        checked.value = true
        occupying.value = true
      }
      // 记录支付方式
      if (orderInfo.value.payType != null) {
        history.state.payType = orderInfo.value.payType
      }
      // 防止在锁定全币种支付时金额变更低于50 USD
      if (payWay.value === PAY_TYPE['全币种支付'] && orderInfo.value.payAmountDollar < 50) {
        ElMessage.warning('当前交易金额低于50 USD，不支持使用全币种支付。')
        payWay.value = PAY_TYPE['微信支付']
        getPayInfo()
        return
      }
      // 支付方式
      if (orderInfo.value.payType != null && first) {
        if (orderInfo.value.payType < PAY_TYPE['余额支付']) {
          payWay.value = orderInfo.value.payType
        } else if (orderInfo.value.payType === PAY_TYPE['余额支付']) {
          payWay.value = PAY_TYPE['余额支付']
        } else {
          payWay.value = orderInfo.value.payType - PAY_TYPE['余额支付']
        }
        // 防止多页面支付切换对公转账时，税点未更新
        if (payWay.value === PAY_TYPE['对公转账'] && !orderInfo.value.taxPointCost) {
          getPayInfo()
        }

        openPayBox()
      } else if (first) {
        checked.value = true
        getPayInfo()
      }
    })
    .catch(() => {
      errorDisabled.value = true
    })
    .finally(() => {
      loading.value = false
      disabled.value = false
    })
}

// 银行/对公 转账
function onSubmit(params: any, close: () => void) {
  params.useBalance = use_balance.value
  params.payAmount = orderInfo.value.payAmount
  params.payAmountDollar = orderInfo.value.payAmountDollar
  params.content = '现代服务推广费'
  if (seedCode.value) {
    params.seedCode = seedCode.value
  }
  orderUploadCredentials(params)
    .then(() => {
      ElMessage.success('提交成功！')
      routerPathUrl.value = '/center/tradingRecord'
      underWay.value = false
      payTipDialogVisible.value = true
      payTipType.value = 1
      // router.replace('/center/tradingRecord')
    })
    .finally(() => close())
}
// 余额支付
function balancePay() {
  ElMessageBox.confirm('确认支付？', '温馨提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在支付中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      disabled.value = true
      let params: any = {
        orderNum: orderNum.value,
      }
      if (seedCode.value) {
        params.seedCode = seedCode.value
      }
      orderBalancePay(params)
        .then(res => {
          payCodeSuccess()
          payTipDialogVisible.value = true
          payTipType.value = 2
        })
        .finally(() => {
          el_loading.close()
          disabled.value = false
        })
    })
    .catch(() => {})
}

function handleClose() {
  // if (orderInfo.value.payType != null) {
  //   router.replace('/center/tradingRecord')
  // }
  if (openTimer) clearTimeout(openTimer)
  isOpenPayDialog.value = false
}
function goback() {
  router.go(-1)
}

init()
</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';
.pages {
  padding-top: 0;
}
.head-box {
  min-width: 1000px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.24) 0%, #ffffff 100%);
  padding: 13px;
  border-radius: 19px;
  gap: 10px;
  font-size: 16px;
  box-shadow: 3px 3px 4px 0px var(--shadow-gray);
  &__title {
    font-size: 14px;
    .head-title {
      display: flex;
      align-items: center;
    }
  }
  &__title::after {
    content: '';
    display: block;
    height: 1px;
    background-color: #e4e8eb;
    margin-top: 8px;
  }
}
.head-content {
  margin: 35px 45px;
  display: flex;
  .head-icon {
    height: 80px;
    width: 80px;
  }
  .head-text {
    margin: 10px 0 0 22px;
    // margin-left: 22px;
    flex: 1;
    &__title {
      font-size: 24px;
      font-weight: 600;
    }
    &__order {
      display: flex;
      font-size: 14px;
      align-items: center;
    }
  }
}
.title {
  font-size: 16px;
  font-weight: 700;
  margin: 4px 0 10px;

  &.primary {
    font-size: 25px;
    font-weight: 600;
    color: var(--el-color-primary);
  }
}
.card-one {
  max-width: 50vw;
  // display: flex;
  // flex-wrap: wrap;
  background: #f1f4fb;

  border-radius: 14px;
  // background-color: var(--bg);
  padding: 14px;
}
.card-two {
  min-width: 700px;
  padding: 41px 45px 41px 160px;
  margin-top: 27px;
  background: #fff;
  border-radius: 19px;
  font-weight: 500;
  color: #09172f;
}
.another-pay-btn {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
  width: 180px;
  height: 40px;
  font-size: 15px;
  font-weight: bold;
  position: relative;

  .tip {
    position: absolute;
    bottom: -20px;
    left: -13px;
    font-size: 12px;
    color: #aaa;
    font-weight: 500;
    transform: scale(0.8);
    letter-spacing: 1px;
  }
}
.down-btn {
  position: absolute;
  top: 10px;
  left: 10px;
}
:deep(.el-form) {
  .is-success {
    .el-input__suffix-inner {
      color: var(--el-color-success);
    }
  }
  &.seed-code-form {
    .is-error {
      .el-input {
        .el-input__wrapper {
          padding-right: 30px;

          &::after {
            content: '无效';
            color: var(--el-color-danger);
            font-size: 12px;
            font-weight: 300;
            line-height: 32px;
            position: absolute;
            top: 0;
            right: 5px;
          }
        }
      }
    }
    .seed-code-error {
      color: var(--el-color-danger);
      position: absolute;
      bottom: 0;
      left: 360px;
      width: 500px;
      margin-left: 10px;
      line-height: 32px;
      text-align: left;
    }
  }
}
.info-box {
  font-size: 16px;
  min-width: 900px;

  .flex-start {
    align-items: flex-start;
  }

  .el-col {
    align-items: baseline;

    .label {
      flex-shrink: 0;
      width: 120px;
    }
  }

  .form-label {
    font-size: 16px;
    width: 120px;
    flex-shrink: 0;
    text-align: right;
    color: #7f7f7f;
  }
  .label {
    // width: 120px;
    text-align: right;
    margin: 0 5px 3px 0;
    color: #7f7f7f;
  }
  .money {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
    flex-shrink: 0;

    .tips {
      font-size: 12px;
      font-weight: 200;
      color: var(--el-text-color-placeholder);
    }

    del {
      color: var(--text-gray-color);
    }
  }
  .tag {
    color: #f48c2b;
    background: #fff0e3;
    font-size: 14px;
    border-radius: 20px;
    padding: 0 10px;
    margin-left: 10px;
    flex-shrink: 0;
  }
  .money-checkbox {
    :deep(.el-checkbox) {
      .el-checkbox__label {
        font-size: 16px;
      }
    }
    span {
      font-size: 20px;
      color: var(--el-text-color-regular);
    }
  }
}
.total-box {
  margin-top: 10px;
  text-align: left;
  font-size: 18px;

  span {
    font-size: 30px;
    font-weight: 600;
    color: #09172f;
  }
  .tips {
    font-size: 13px;
    color: #9ba6ba;
    // color: var(--text-gray-color);
  }
}
.select-pay-label {
  :deep(.el-divider) {
    max-width: 350px;
    margin: 40px auto;

    .el-divider__text {
      font-size: 18px;
      color: var(--text-gray-color);
    }
  }
}
.select-pay-box {
  margin: 0;

  :deep(.el-radio-group) {
    margin: 5px 20px 5px 5px;
    font-size: 16px;

    .el-radio-button__inner {
      display: flex;
      align-items: center;
      gap: 5px;
    }
  }
  .wx-color {
    color: rgb(25, 213, 108);
  }
  .zfb-color {
    color: rgb(0, 159, 232);
  }

  .iconfont {
    // background: linear-gradient(to right, #fff, #fff) no-repeat right;
    // background-size: 18px 11px;
    font-size: 20px;
  }

  .btn-corner-mark {
    position: absolute;
    top: -1px;
    left: -1px;
    width: 32px;
    height: 32px;
    background-image: url('@/assets/image/recommend_corner_mark.png');
    background-size: 100%;

    // position: absolute;
    // top: -1px;
    // left: -1px;
    // width: 35px;
    // height: 35px;
    // overflow: hidden;

    // &::after {
    //   content: '推荐';
    //   display: block;
    //   position: absolute;
    //   top: -3px;
    //   left: -41px;
    //   width: 100px;
    //   height: 24px;
    //   line-height: 33px;
    //   font-size: 12px;
    //   text-align: center;
    //   background-color: rgb(255, 85, 82);
    //   color: #fff;
    //   transform: rotate(-45deg);
    // }
  }
}
// .balance-checkbox {
// margin: 0 0 70px;
// }
.qrcode-pay-box {
  margin-left: 20px;
  line-height: 40px;

  .pay-total-box {
    color: var(--text-gray-color);
    font-size: 14px;
    margin-top: 10px;

    .pay-total {
      font-family: PingFangSC;
      color: var(--el-color-warning);

      span {
        font-size: 26px;
        font-weight: 600;
      }
    }
  }

  .qrcode-tips {
    font-size: 16px;
    font-weight: bold;
    color: #1f2122;

    img {
      width: 16px;
      height: 16px;
    }

    .icon {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
