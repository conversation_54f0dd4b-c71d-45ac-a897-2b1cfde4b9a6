<template>
  <div>
    <div
      class="flex-column vip-set-meal-box"
      :style="{ '--box-color': data.bgColor, '--box-text-color': data.textColor }"
    >
      <div style="width: 100%;">
        <div class="top-box">
          <span v-if="data.activityMonth">
            限时加赠{{ data.activityMonth }}个月
            (<CountDown :endTime="data.activityTime" showDays @end="$emit('countDownEnd')" />)
          </span>
          <span v-else-if="data.hot">强烈推荐</span>
        </div>
        <!-- <div class="activity" v-if="data.activityMonth">
          <img src="@/assets/image/icon/gift.png" alt="" />
          <span class="activity-text">限时加赠{{ data.activityMonth }}个月</span>
        </div> -->
      </div>
      <div class="head" :class="{ 'hot-text': data.hot }">
        <span>{{ data.title }}</span>
      </div>
      <div class="flex-column money-box" :class="{ 'hot-text': data.hot }">
        <div class="money">
          <span>${{ handlePrice(data.$, data.month) }}</span>/月
        </div>
        <div>
          <!-- <span
            :class="{
              'un-text': data.activityMonth,
            }"
          >
            每月约￥{{ handlePrice(data.price, data.month) }}
          </span> -->
          <span>
            {{ data.month }}个月合计<strong>${{ data.$ }}</strong>
          </span>
        </div>
      </div>
      <el-button class="buy-btn" type="primary" v-if="purchased" @click="toBuy">立即续费</el-button>
      <el-button class="buy-btn" type="primary" v-else @click="toBuy">立即开通</el-button>
      <!-- <div v-if="data.activityMonth" class="activity-time">活动仅剩{{ data.activityTime }}天</div> -->
    </div>
  </div>
</template>

<script setup lang="ts">
import CountDown from '@/components/public/CountDown.vue'
import { useUserStore } from '@/stores/modules/user'
import { computed } from 'vue'

const store = useUserStore()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['action', 'countDownEnd'])

const purchased = computed(() => store.isVip())

function handlePrice(price: number, month: number) {
  return +(price / month).toFixed(2)
}

function toBuy() {
  emits('action')
}
</script>

<style scoped lang="scss">
.vip-set-meal-box {
  position: relative;
  width: 240px;
  padding-bottom: 30px;
  border-radius: 10px;
  box-shadow: 0px 2px 8px 0px rgba(23, 51, 70, 0.38);
  gap: 16px;
  background: #fff;
  --box-color: #eee1bb;
  --box-text-color: #c0bd00;

  .top-box {
    position: relative;
    text-align: center;
    width: 100%;
    min-height: 20px;
    height: fit-content;
    border-radius: 10px 10px 0 0;
    background: var(--box-color);
    color: var(--box-text-color);
    font-size: 13px;
    padding: 5px 0;
  }

  .head {
    width: 70%;
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    border-bottom: 1px solid #e4e8eb;
    padding-bottom: 10px;
    display: flex;
    justify-content: center;
    gap: 6px;
    font-family: "PingFang SC";

    // &.hot-text {
    //   font-size: 20px;
    // }
  }

  .money-box {
    color: #9ba6ba;
    font-size: 12px;
    margin: 0;
    gap: 6px;

    .money {
      font-size: 14px;
      font-weight: bold;
      color: var(--text-color);

      span {
        font-family: "PingFang SC";
        font-size: 28px;
      }
    }

    strong {
      color: var(--text-color);
    }
    
    .un-text {
      text-decoration-line: line-through;
    }

    // &.hot-text {
    //   .money {
    //     span {
    //       font-size: 30px;
    //     }
    //   }
    // }
  }
  .buy-btn {
    width: 80%;
    color: var(--box-text-color);
    background: var(--box-color);
    border: none;
  }
  .activity {
    width: fit-content;
    font-size: 12px;
    background: linear-gradient( 90deg, #ff8152 0%, #f8625c 100%);
    padding: 0 5px 0 10px;
    border-radius: 2px 0 0 22px;
    margin-left: auto;

    .activity-text {
      color: #fff;
    }
  }
  .activity-time {
    color: #5D421D;
    font-size: 12px;
    padding: 0 5px;
  }
}
</style>
