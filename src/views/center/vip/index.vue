<template>
  <div class="pages">
    <div class="flex-start tabs">
      <div class="tab active">我的会员</div>
      <div class="tab" @click="router.replace('/center/info')">个人资料</div>
      <div v-if="store.isOwnerAcc() && store.isViped()" class="tab border-left" @click="router.replace('/center/acc')">
        子账号管理
      </div>
      <div class="tab border-left" @click="router.replace('/center/blackList')">黑名单</div>
    </div>
    <div class="w-bg center-body">
      <div class="bg-box">
        <!-- <img class="vip-bg" src="@/assets/image/vip_bg.png" alt=""> -->
        <div class="flex-end head-btn">
          <el-button
            type="primary"
            plain
            round
            icon="DocumentCopy"
            @click="routerNewWindow('center-tradingRecord')"
          >
            开通记录
          </el-button>

          <div class="flex-start un-pay-number" v-if="unPayNum">
            <el-text>您有{{ unPayNum }}笔未支付订单，</el-text>
            <el-button type="primary" link @click="toView">立即查看</el-button>
          </div>
        </div>
        <div class="head-title">
          <img src="@/assets/image/snailvip.png" alt="" />
          <h2>开箱视频行业第一</h2>
          <h3>加入会员，享跨境拍摄服务最低价</h3>
        </div>
        <div class="tips" v-html="tipsText"></div>
        <div class="flex-center">
          <div class="flex-center" style="gap: 60px; margin: 60px 0 30px; transform: scale(1.3);">
            <template v-for="(item, i) in setMealList" :key="i">
              <VipSetMealBox
                v-if="item.type > 0"
                :data="item"
                @action="toBuy(item.type)"
                @countDownEnd="getVipActivity"
              />
            </template>
          </div>
        </div>
        <div style="padding: 10px 0px; margin-top: 60px">
          <VipTips />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import VipSetMealBox from '@/views/center/vip/components/VipSetMealBox.vue'
import VipTips from '@/views/center/vip/components/VipTips.vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { vipSetMealList } from '@/views/center/data'
import { createVipOrder, getMemberConfig, getMemberUnPay, latestUnPayMemberOrder } from '@/api/vip'
import { ElLoading } from 'element-plus'
import { computed, ref } from 'vue'
import { MessageBox } from '@/utils/message'
// import { endTimeToDay } from '@/utils/time'

const router = useRouter()
const store = useUserStore()
store.getUserInfo()

const setMealList = ref(vipSetMealList)
const unPayNum = ref(0)
const unPayOrderNums = ref<any[]>([])

const tipsText = computed(() => {
  if (store.isVip()) {
    if (store.userInfo.businessVO?.memberStatus === 2) {
      return `会员还有<span style="color: var(--text-color);">${handleMemberValidity(store.userInfo.businessVO?.memberValidity)}</span>天到期，请及时续费`
    }
    return '会员有效期至' + store.userInfo.businessVO?.memberValidity
  }
  if (store.userInfo.businessVO?.memberStatus === 3) {
    return `会员已于${store.userInfo.businessVO?.memberValidity}到期`
  }
  return ''
  // return '为您的企业选择最佳会员方案'
})

function handleMemberValidity(time: string) {
  return Math.ceil((new Date(time).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
}

function routerNewWindow(name: string) {
  const { href } = router.resolve({ name })
  window.open(href, '_blank')
}
function toView() {
  if (unPayNum.value === 1) {
    // 只有一条未支付订单-去支付
    store.realTimeCheckVip().then(() => {
      if (store.isOwnerAcc()) {
        router.push({
          name: 'center-pay',
          state: {
            orderNum: unPayOrderNums.value[0].orderNum,
            type: unPayOrderNums.value[0].packageType,
            payType: unPayOrderNums.value[0].payType,
          },
        })
      }
    })
  } else if (unPayNum.value > 1) {
    // 多条未支付订单-开通记录列表
    routerNewWindow('center-tradingRecord')
  }
}

let el_loading: any

async function toBuy(type: number) {
  el_loading = ElLoading.service({
    lock: true,
    text: '正在加载中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  await getMemberOrderNum()
  if (unPayNum.value > 0) {
    // 存在未支付订单
    el_loading.close()
    MessageBox(`您有待支付会员订单<br/><span>您可以选择继续去支付订单，或者取消未支付订单重新下单</span>`, {
      confirmButtonText: '去支付',
      cancelButtonText: '去订单列表',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      showClose: true,
      distinguishCancelAndClose: true,
    })
      .then(() => {
        el_loading = ElLoading.service({
          lock: true,
          text: '正在打开中',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        store.realTimeCheckVip().then(() => {
          if (store.isOwnerAcc()) {
            router.push({
              name: 'center-pay',
              state: {
                orderNum: unPayOrderNums.value[0].orderNum,
                type: unPayOrderNums.value[0].packageType,
                payType: unPayOrderNums.value[0].payType,
              },
            })
            el_loading.close()
          }
        }).catch(() => el_loading.close())
      })
      .catch((action) => {
        if (action === 'cancel') {
          el_loading = ElLoading.service({
            lock: true,
            text: '正在下单中',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          routerNewWindow('center-tradingRecord')
        }
      })
  } else {
    createVipOrderApi(type)
  }
}

function createVipOrderApi(type: number) {
  createVipOrder({
    packageType: type,
  })
    .then(res => {
      if (res.data?.orderNum) {
        router.push({ name: 'center-pay', state: { orderNum: res.data.orderNum, type } })
      }
    })
    .finally(() => el_loading.close())
}

function getVipActivity() {
  getMemberConfig().then(res => {
    if (res.data.status == 1) {
      let startTime = new Date(res.data.presentedStartDate).getTime()
      if (startTime > new Date().getTime()) return
      setMealList.value[0].activityMonth = res.data.presentedTime
      setMealList.value[0].activityTime = res.data.presentedEndDate
    }
  })
}

async function getMemberOrderNum() {
  return getMemberUnPay().then(res => {
    if (res.data) {
      unPayNum.value = res.data.unPayNum
      unPayOrderNums.value = res.data.unPayOrderList
    }
  })
}

getVipActivity()
getMemberOrderNum()
</script>

<style scoped lang="scss">
@use '@/views/center/tabs.scss';
.center-body {
  position: relative;
  border-radius: 8px;
  padding-top: 0 !important;
  min-width: 1040px;

  .bg-box {
    padding-top: 20px;
    background-image: url('@/assets/image/vip_bg.png');
    background-repeat: no-repeat;
    background-size: 100% auto;
    background-position: top;
  }

  .vip-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }

  .head-btn {
    position: relative;
    padding: 10px 10px 0;

    .un-pay-number {
      position: absolute;
      top: 115%;
      right: 10px;
      font-size: 14px;
      line-height: 14px;
      align-items: baseline;
    }
  }

  .head-title {
    width: fit-content;
    // background: #f2f2f2;
    text-align: center;
    padding: 10px 160px;
    margin: 0 auto 10px;
    border-radius: 20px;

    img {
      height: 60px;
    }

    h2 {
      font-size: 33px;
      font-weight: bold;
      margin-top: -60px;
      margin-bottom: 14px;
    }
    h3 {
      color: #77746b;
      font-size: 16px;
      margin: 0;
    }
  }
  .tips {
    position: absolute;
    top: 36px;
    right: 165px;
    color: var(--el-color-primary);
    font-size: 14px;
    font-weight: 600;
  }
}
</style>
