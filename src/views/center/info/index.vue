<template>
  <div class="pages">
    <div class="flex-start tabs">
      <!-- <div v-if="store.isOwnerAcc()" class="tab" @click="router.replace('/vip')">我的会员</div> -->
      <div class="tab active">个人资料</div>
      <div v-if="store.isOwnerAcc() && store.isViped()" class="tab" @click="router.replace('/center/acc')">
        子账号管理
      </div>
      <!--      <div class="tab border-left" @click="router.replace('/center/blackList')">黑名单</div>-->
    </div>
    <div class="w-bg center-body">
      <div class="width-box">
        <div class="flex-start head-box">
          <MemberCard />
        </div>
        <div class="content-box">
          <div class="title">基本信息</div>
          <!-- <div class="flex-start">
            <div class="label">会员编码</div>
            <div class="info">{{ store.userInfo.businessVO?.memberCode }}</div>
          </div> -->
          <!-- <el-divider /> -->
          <!-- <div class="flex-between">
            <div class="flex-start">
              <div class="label">蜗牛账号</div>
              <div class="info">{{ store.userInfo.account }}</div>
            </div>
            <div class="btn">
              <el-button link size="large" icon="Edit" @click="resetPwd">重置密码</el-button>
            </div>
          </div> -->
          <div class="flex-between">
            <div class="flex-start">
              <div class="label">手机号码</div>
              <div class="info">{{ phone || '-' }}</div>
            </div>
            <div class="btn">
              <el-button link type="primary" size="large" @click="openUpdatePhone" v-if="phone">
                <template #icon>
                  <img src="@/assets/icon/icon_phone.png" />
                </template>
                变更手机号
              </el-button>
              <el-button v-else link type="primary" size="large" @click="openBindPhone">
                立即绑定手机号
              </el-button>
            </div>
          </div>
          <el-divider />
          <div class="flex-between">
            <div class="flex-start">
              <div class="label">微信绑定</div>
              <div class="info">{{ store.userInfo.nickName }}</div>
            </div>
            <div class="btn">
              <el-button link type="primary" size="large" @click="openWechatQrcodeAuth('wechat-bind-auth')">
                <template #icon>
                  <img src="@/assets/icon/icon_switch.png" />
                </template>
                更换绑定
              </el-button>
            </div>
          </div>
          <el-divider />
          <div class="flex-between">
            <div class="flex-start">
              <div class="label">姓&emsp;&emsp;名</div>
              <div class="info">{{ store.userInfo.name }}</div>
            </div>
            <div class="btn">
              <el-button link type="primary" size="large" @click="openEditName()">
                <template #icon>
                  <img src="@/assets/icon/icon_edit.png" />
                </template>
                编辑
              </el-button>
            </div>
          </div>
          <el-divider />

          <div class="title" style="margin-top: 30px">企业信息</div>
          <div class="flex-between">
            <div class="flex-start">
              <div class="label">公司名称</div>
              <div class="info">{{ store.userInfo.businessVO?.name }}</div>
            </div>
            <div class="btn">
              <el-button
                v-if="store.isOwnerAcc() && store.isVip()"
                link
                type="primary"
                size="large"
                @click="openWechatQrcodeAuth('wechat-edit-auth')"
              >
                <template #icon>
                  <img src="@/assets/icon/icon_edit.png" />
                </template>
                编辑
              </el-button>
            </div>
          </div>
          <el-divider />
          <div class="flex-between">
            <div class="flex-start">
              <div class="label">
                <div>公司规模</div>
                <div style="transform: translateX(-14px)">（员工人数）</div>
              </div>
              <div class="info">{{ scale }}</div>
            </div>
            <div class="btn">
              <el-button
                v-if="store.isOwnerAcc() && store.isVip()"
                link
                type="primary"
                size="large"
                @click="openWechatQrcodeAuth('wechat-edit-auth-scale')"
              >
                <template #icon>
                  <img src="@/assets/icon/icon_edit.png" />
                </template>
                编辑
              </el-button>
            </div>
          </div>
          <el-divider />
          <div class="flex-between" style="align-items: baseline">
            <div class="flex-start" style="align-items: baseline">
              <div class="label">发票管理</div>
              <div class="info">
                <div>{{ store.userInfo.businessVO?.invoiceTitle }}</div>
                <div>{{ store.userInfo.businessVO?.invoiceDutyParagraph }}</div>
              </div>
            </div>
            <div class="btn">
              <el-button
                v-if="store.isOwnerAcc()"
                link
                type="primary"
                size="large"
                @click="openInvoiceManage"
              >
                <template #icon>
                  <img src="@/assets/icon/icon_tools.png" />
                </template>
                管理
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <UpdateCompanyName ref="UpdateCompanyNameRef" @success="getInfo" />
    <UpdateCompanyScale ref="UpdateCompanyScaleRef" @success="getInfo" />
    <UpdateName ref="UpdateNameRef" @success="getInfo" />
    <UpdatePhone ref="UpdatePhoneRef" @success="getInfo" />
    <UpdateWechat ref="UpdateWechatRef" />
    <ResetPassword ref="ResetPasswordRef" is-check-identity />
    <InvoiceManage ref="InvoiceManageRef" @success="getInfo" />
    <BindingPhone ref="BindingPhoneRef" @success="getInfo" />
  </div>
</template>

<script setup lang="ts">
import MemberCard from '@/views/center/components/MemberCard.vue'
import UpdateCompanyName from '@/views/center/components/UpdateCompanyName.vue'
import UpdateCompanyScale from '@/views/center/components/UpdateCompanyScale.vue'
import UpdateName from '@/views/center/components/UpdateName.vue'
import UpdatePhone from '@/views/center/components/UpdatePhone.vue'
import UpdateWechat from '@/views/center/components/UpdateWechat.vue'
import ResetPassword from '@/views/center/components/ResetPassword.vue'
import InvoiceManage from '@/views/center/components/InvoiceManage.vue'
import BindingPhone from '@/views/center/components/BindingPhone.vue'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'
import { businessScale } from '@/hooks/useWelcome'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { handelRenewDialog } = useShowRenewDialog()

const router = useRouter()

const store = useUserStore()

const UpdateCompanyNameRef = ref<InstanceType<typeof UpdateCompanyName>>()
const UpdateCompanyScaleRef = ref<InstanceType<typeof UpdateCompanyScale>>()
const UpdateNameRef = ref<InstanceType<typeof UpdateName>>()
const UpdatePhoneRef = ref<InstanceType<typeof UpdatePhone>>()
const UpdateWechatRef = ref<InstanceType<typeof UpdateWechat>>()
const ResetPasswordRef = ref<InstanceType<typeof ResetPassword>>()
const InvoiceManageRef = ref<InstanceType<typeof InvoiceManage>>()

const phone = computed(() => {
  if (store.userInfo.phone) {
    return store.userInfo.phone.substring(0, 3) + '****' + store.userInfo.phone.substring(7)
  }
  return ''
})

const scale = computed(() => {
  if (store.userInfo.businessVO?.scale) {
    return businessScale.find(item => item.value === store.userInfo.businessVO?.scale)?.label || ''
  }
  return ''
})

// 重置密码
async function resetPwd() {
  let acc = store.userInfo.businessVO?.ownerAccount
  if (!acc) {
    try {
      await store.getUserInfo()
      acc = store.userInfo.businessVO?.ownerAccount
    } catch (e) {
      return
    }
  }
  if (acc) {
    ResetPasswordRef.value?.open(acc)
  } else {
    ElMessage.error('acc未知错误！请联系管理员')
  }
}
// 变更手机号
function openUpdatePhone() {
  UpdatePhoneRef.value?.open()
}

//立即绑定
const BindingPhoneRef = ref()
function openBindPhone() {
  BindingPhoneRef.value?.open()
}
// 编辑姓名
function openEditName() {
  UpdateNameRef.value?.open()
}
// 编辑发票抬头
function openInvoiceManage() {
  InvoiceManageRef.value?.open()
}
// 身份认证
function openWechatQrcodeAuth(type: string) {
  // 编辑公司名称
  if (type === 'wechat-edit-auth') {
    UpdateCompanyNameRef.value?.open(store.userInfo.businessVO?.name || '', '')
    return
  }
  // 编辑公司规模
  if (type === 'wechat-edit-auth-scale') {
    UpdateCompanyScaleRef.value?.open(store.userInfo.businessVO?.scale)
    return
  }
  // 微信绑定
  if (type === 'wechat-bind-auth') {
    UpdateWechatRef.value?.open()
    return
  }
}

async function getInfo() {
  handelRenewDialog()
  try {
    await store.getUserInfo()
  } catch (e) {
    return
  }
}

getInfo()
</script>

<style scoped lang="scss">
@use '@/views/center/tabs.scss';
.center-body {
  min-width: 1080px;

  .width-box {
    width: 100%;
  }

  .head-box {
    padding: 20px;
    gap: 20px;
  }
  .content-box {
    padding: 20px;
    margin: 0 20px;
    border: 1px solid var(--border-gray-color);
    border-radius: 10px;

    // .flex-start {
    //   align-items: flex-start;
    // }
    // .flex-between {
    //   align-items: flex-start;
    // }

    .title {
      font-size: 14px;
      width: fit-content;
      padding: 5px 12px;
      border: 1px solid var(--border-gray-color);
      margin: 0 0 20px;
    }

    .el-divider--horizontal {
      --el-border-color: #dcdfe661;
      margin: 10px 0;
    }

    .label {
      width: 130px;
      font-size: 15px;
      font-weight: 400;
      margin: 10px;
      color: var(--text-color);
    }
    .info {
      font-size: 15px;
      color: #9ba6ba;
      margin: 10px 0;
    }
    .btn {
      width: 100px;
      font-size: 18px;
      text-align: left;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }
}
.tips {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-primary);
}
</style>
