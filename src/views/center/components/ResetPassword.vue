<template>
  <PublicDialog
    ref="DialogRef"
    :title="title"
    width="380px"
    v-bind="$attrs"
    custom-close
    align-center
    :showFooterButton="false"
    destroy-on-close
    @close="handleClose"
  >
    <template v-if="isCheckIdentity && !isCheck">
      <div class="flex-column box">
        <QrCode
          size="large"
          :code-type="2"
          :auto-check-code="false"
          @init="QrCodeError = false"
          @change="handleChange"
          @success="handleSuccess"
        />
        <div class="flex-column" v-if="QrCodeError && isLoginReset">
          <div class="login-tips">
            <div>您还不是平台用户，可直接扫码登录</div>
            <div>（登录即注册）</div>
          </div>
          <el-button
            type="primary"
            native-type="submit"
            round
            @click="toLoginBtn()"
            style="width: 100%;"
          >
            去扫码登录＞
          </el-button>
        </div>
        <div class="qrcode-tips" v-else>
          打开微信扫一扫
          <br />
          确认您的身份
        </div>
      </div>
    </template>
    <template v-else>
      <div class="flex-column box">
        <div class="pwd-tips">
          请输入新密码，您的新密码必须与以前不同，密码需要包含数字、字母等超过8个字符
        </div>
        <el-form
          class="form-box"
          ref="formRef"
          :model="form"
          label-position="top"
          :rules="rules"
          @submit.prevent
        >
          <el-form-item prop="pwd">
            <div class="input-box">
              <el-input
                placeholder="输入新密码"
                prefix-icon="Lock"
                v-model="form.pwd"
                type="password"
                show-password
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item prop="pwd2">
            <div class="input-box">
              <el-input
                placeholder="确认新密码"
                prefix-icon="Lock"
                v-model="form.pwd2"
                type="password"
                show-password
              ></el-input>
            </div>
          </el-form-item>

          <el-button type="primary" native-type="submit" round @click="resetPwd" class="form-btn">
            确认
          </el-button>
        </el-form>
      </div>
    </template>
  </PublicDialog>
</template>

<script setup lang="ts">
import QrCode from '@/components/public/qrcode/QrCode.vue'
import type { CODE_TYPE, QRCODE_STATUS } from '@/components/public/qrcode/type'
import { ref } from 'vue'
import { password_reg } from '@/utils/RegExp'
import { resetPassword, updatePassword } from '@/api/center'
import { ElLoading, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import useQrCodeVerify from '@/hooks/useQrCodeVerify'

const { handleQrCodeVerifyAcc } = useQrCodeVerify()

const store = useUserStore()

const emits = defineEmits(['checkSuccess', 'toLogin'])

const props = defineProps({
  title: {
    type: String,
    default: '重置密码'
  },
  // 修改前是否验证身份
  isCheckIdentity: {
    type: Boolean,
    default: false,
  },
  // 登录时忘记密码
  isLoginReset: {
    type: Boolean,
    default: false,
  },
})

defineExpose({
  open,
  close,
})

const DialogRef = ref()
const formRef = ref()
// 身份是否已验证
const isCheck = ref(false)
const QrCodeError = ref(false)
const form = ref<{
  acc: number | string
  pwd: string
  pwd2: string
}>({
  acc: '',
  pwd: '',
  pwd2: '',
})
const ticket = ref('')

const rules = {
  pwd: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { pattern: password_reg, message: '请输入8-18位英文和数字的组合密码', trigger: 'change' },
  ],
  pwd2: [{ required: true, validator: checkPwd, trigger: 'blur' }],
}
function checkPwd(rule: any, value: any, callback: any) {
  if (form.value.pwd !== value) {
    return callback(new Error('确认密码不一致！！！'))
  }
  return callback()
}

function open(acc?: number | string) {
  form.value.acc = acc || ''
  DialogRef.value?.open()
}

function close() {
  DialogRef.value?.close()
}

function handleClose() {
  form.value = {
    acc: '',
    pwd: '',
    pwd2: '',
  }
  ticket.value = ''
  isCheck.value = false
  QrCodeError.value = false
}

function handleChange(code: {
  res: any
  ticket: string
  type: CODE_TYPE
  update: (status: QRCODE_STATUS | string, data: any) => void
  next: () => void
  stop: (err?: any) => void
}) {
  if(props.isLoginReset) {
    if(code.res.data.loginStatus === 'UN_REGISTER') {
      QrCodeError.value = true
    }
    code.update(code.res.data.loginStatus, { ticket: code.ticket, ...code.res.data })
    code.next()
    return
  }
  return handleQrCodeVerifyAcc(code)
}

function handleSuccess(data: any) {
  ticket.value = data.ticket
  isCheck.value = true
  emits('checkSuccess')
}

function toLoginBtn() {
  emits('toLogin')
  close()
}

function resetPwd() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在提交中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      if(props.isLoginReset) {
        updatePassword({
          password: form.value.pwd,
          ticket: ticket.value,
        })
          .then(() => {
            ElMessage.success('新密码设置成功')
            close()
          })
          .finally(() => el_loading.close())
      } else {
        resetPassword({
          account: form.value.acc,
          password: form.value.pwd,
          ticket: ticket.value,
        })
          .then(() => {
            ElMessage.success('修改成功')
            close()
            try {
              store.getUserInfo()
            } catch (err) {
              console.error('getUserInfo')
            }
          })
          .finally(() => el_loading.close())
      }
    }
  })
}
</script>

<style scoped lang="scss">
.box {
  width: 350px;
  margin: 10px auto;
  padding: 18px 0;

  .login-tips {
    text-align: center;
    color: #888;
    margin: 10px 0 15px;
  }
  .pwd-tips {
    color: var(--text-gray-color);
  }
  .qrcode-tips {
    text-align: center;
    font-size: 25px;
    font-weight: bold;
    color: var(--el-color-success);
  }
  .form-box {
    width: 300px;
    margin: 25px 0;

    .input-box {
      width: 100%;
    }
    .form-btn {
      width: 100%;
      margin-top: 20px;
    }
  }
}
</style>
