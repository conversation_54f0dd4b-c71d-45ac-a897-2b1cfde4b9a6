<template>
  <div>
    <el-dialog
      align-center
      append-to-body
      title=""
      v-model="isShow"
      width="450px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="flex-column login-box login-form">
        <h2 style="font-weight: 500">绑定手机号</h2>
        <el-form ref="formRef" :model="form" label-position="top" :rules="rules" status-icon @submit.prevent>
          <el-form-item prop="phone" label="">
            <div class="input-box">
              <el-input
                placeholder="请输入手机号"
                prefix-icon="User"
                maxlength="11"
                v-model="form.phone"
                @input="handleTel"
              ></el-input>
            </div>
            <template #error="{ error }">
              <div class="form-error-box">
                <el-icon><CircleCloseFilled /></el-icon>
                {{ error }}
              </div>
            </template>
          </el-form-item>
          <el-form-item prop="code" label="">
            <div class="input-box code-box">
              <el-input
                placeholder="请输入验证码"
                prefix-icon="Lock"
                maxlength="4"
                v-model="form.code"
              ></el-input>
              <el-button
                class="code-btn"
                link
                :loading="codeBtnLoading"
                :disabled="!checkPhoneBtn"
                @click.stop="getPhoneCode"
              >
                <template v-if="codeTime">
                  {{ codeTime + ' 秒后可再次获取' }}
                </template>
                <template v-else>获取验证码</template>
              </el-button>
            </div>
            <template #error="{ error }">
              <div class="form-error-box">
                <el-icon><CircleCloseFilled /></el-icon>
                {{ error }}
              </div>
            </template>
          </el-form-item>
          <el-button
            type="primary"
            native-type="submit"
            round
            @click="confirm()"
            :loading="loginLoading"
            class="login-btn"
          >
            确认
          </el-button>
        </el-form>
      </div>
      <ImageCodeDialog />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import ImageCodeDialog from '@/views/login/ImageCodeDialog.vue'
import { bizUserUpdatePhone } from '@/api/center'
import { ref, toRefs } from 'vue'
import { phone_reg } from '@/utils/RegExp'
import useImgOrPhoneCode from '@/hooks/useImgOrPhoneCode'
import { ElLoading, ElMessage } from 'element-plus'
import { checkPhone } from '@/api/wechat'
const isShow = ref(false)

const formRef = ref()
const loginLoading = ref(false)
const checkPhoneBtn = ref(false)

const {
  codeTime,
  codeBtnLoading,
  phoneForm,
  sendPhoneCode, // 发送手机验证码
} = useImgOrPhoneCode()

const form = ref({
  ...toRefs(phoneForm.value),
})

const bindFormRef = ref()
const bindDialogVisible = ref(false)
const bindDialogTicket = ref('')

const rules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'change' },
  ],
  code: [{ required: true, message: '请输入验证码', trigger: 'manul' }],
}

const emits = defineEmits(['success'])
const open = () => {
  isShow.value = true
}

const close = () => {
  formRef.value.resetFields()
  isShow.value = false
}

function validatePhone(rule: any, value: any, callback: any) {
  checkPhoneBtn.value = false
  if (!phone_reg.test(value)) {
    return callback(new Error('请输入正确的手机号'))
  }
  checkPhone({ phone: value })
    .then((res: any) => {
      if (res.code !== 200) {
        callback(new Error(res.msg))
      } else if (res.data) {
        callback(new Error(res.data))
      } else {
        checkPhoneBtn.value = true
        callback()
      }
    })
    .catch(err => {
      console.log(err)
      callback(new Error(err.data?.msg || '网络错误！请稍后再试'))
    })
    .finally(() => {})
}
function handleTel(val: any) {
  form.value.phone = val.replace(/\D/g, '')
}

// 获取手机验证码
function getPhoneCode() {
  if (codeTime.value) return
  if (bindDialogVisible.value) {
    bindFormRef.value.validateField('phone', (valid: any) => {
      if (valid) {
        sendPhoneCode({ phoneNum: form.value.phone, ticket: bindDialogTicket.value, isRegister: true })
      }
    })
  } else {
    formRef.value.validateField('phone', (valid: any) => {
      if (valid) {
        sendPhoneCode({ phoneNum: form.value.phone })
      }
    })
  }
}

function confirm() {
  formValidate()
}
function formValidate() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      onConfirm()
    } else {
      // ElMessage.warning('请输入手机号和验证码！')
    }
  })
}
function onConfirm() {
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在保存中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  bizUserUpdatePhone({
    phone: form.value.phone,
    phoneCaptcha: form.value.code,
  })
    .then(res => {
      ElMessage.success('绑定成功')
      close()
      emits('success', res)
    })
    .finally(() => el_loading.close())
}

defineExpose({ open, close })
</script>

<style lang="scss" scoped>
:deep(.el-input__wrapper) {
  border-radius: 20px;
}
:deep(.el-form) {
  .is-success {
    .el-input__suffix-inner {
      color: var(--el-color-primary);
    }
  }
}
.login-box {
  margin-top: -25px;
  width: 100%;
  form {
    width: 80%;
  }

  :deep(.el-form) {
    .el-form-item__label {
      color: var(--el-color-info);

      &::before {
        content: '';
      }
    }
  }

  h2 {
    margin-bottom: 15px;
    color: var(--text-color);
  }
  .input-box {
    width: 100%;
    margin-top: 5px;

    :deep(.el-input) {
      --el-input-height: 40px;
    }
  }
  .code-box {
    position: relative;
    :deep(.el-input) {
      .el-input__suffix {
        display: none;
      }
    }

    .code-btn {
      --el-font-size-base: 13px;
      position: absolute;
      top: 0;
      right: 0;
      color: #169bd5;
      width: 125px;
      height: 40px;
      padding: 0 5px;

      &::before {
        content: '';
        display: inline-block;
        height: 14px;
        border-left: 1px solid #e8ebf0;
        position: absolute;
        left: 0;
        top: calc(50% - 7px);
      }
    }
  }
  .login-btn {
    width: 100%;
    margin: 8px 0 20px;
    padding: 18px 15px;
  }
}
.form-error-box {
  color: var(--el-color-danger);
  font-size: 12px;
  line-height: 20px;
  position: absolute;
  top: 100%;
  left: 15px;
  display: flex;
  align-items: center;
  gap: 5px;
}
</style>
