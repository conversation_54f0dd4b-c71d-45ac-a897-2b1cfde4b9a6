<template>
  <PublicDialog
    ref="DialogRef"
    title="发票管理"
    width="420px"
    custom-close
    align-center
    :showFooterButton="false"
    @close="handleClose"
  >
    <div class="flex-column box">
      <div class="title-tips">增值税普通发票</div>
      <el-form class="form-box" ref="formRef" :model="form" :rules="rules" label-width="90px" @submit.prevent>
        <el-form-item label="抬头类型" prop="invoiceTitleType">
          <el-radio-group v-model="form.invoiceTitleType">
            <el-radio border :value="0">企业单位</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="发票抬头" prop="invoiceTitle">
          <el-input
            v-model="form.invoiceTitle"
            placeholder="请填写发票抬头"
            clearable
            maxlength="32"
          ></el-input>
        </el-form-item>
        <el-form-item label="税号" prop="invoiceDutyParagraph">
          <el-input
            v-model="form.invoiceDutyParagraph"
            placeholder="请填写纳税人识别号"
            clearable
            maxlength="32"
          ></el-input>
        </el-form-item>
        <el-form-item label="发票内容" prop="invoiceContent">
          <div class="invoice-content">{{ form.invoiceContent }}</div>
        </el-form-item>

        <el-button type="primary" native-type="submit" round @click="onConfirm" class="form-btn">
          保存
        </el-button>
      </el-form>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { type PropType, ref } from 'vue'
import { updateInvoice } from '@/api/center'
import { ElLoading } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'

const store = useUserStore()

defineExpose({
  open,
  close,
})

type Form = {
  invoiceTitleType: number | string
  invoiceTitle: string
  invoiceDutyParagraph: string
  invoiceContent: string
}

type CustomRequest = (form: Form) => void

const props = defineProps({
  request: {
    type: Function as PropType<CustomRequest>,
  },
})

const emits = defineEmits(['success'])

const DialogRef = ref()
const formRef = ref()

const form = ref<Form>({
  invoiceTitleType: 0,
  invoiceTitle: '',
  invoiceDutyParagraph: '',
  invoiceContent: '现代服务推广费',
})
const rules = {
  invoiceTitleType: [{ required: true, message: '请选择抬头类型', trigger: 'blur' }],
  invoiceTitle: [{ required: true, message: '请填写发票抬头', trigger: 'blur' }],
  invoiceDutyParagraph: [{ required: true, message: '请填写纳税人识别号', trigger: 'blur' }],
}

function open(invoiceTitle?: string, invoiceDutyParagraph?: string) {
  form.value.invoiceTitle = invoiceTitle || store.userInfo.businessVO?.invoiceTitle || ''
  form.value.invoiceDutyParagraph =
    invoiceDutyParagraph || store.userInfo.businessVO?.invoiceDutyParagraph || ''
  DialogRef.value?.open()
}

function close() {
  DialogRef.value?.close()
}

function handleClose() {
  form.value = {
    invoiceTitleType: 0,
    invoiceTitle: '',
    invoiceDutyParagraph: '',
    invoiceContent: '现代服务推广费',
  }
}

function onConfirm() {
  if (form.value.invoiceTitle) {
    form.value.invoiceTitle = form.value.invoiceTitle.trim()
  }
  if (form.value.invoiceDutyParagraph) {
    form.value.invoiceDutyParagraph = form.value.invoiceDutyParagraph.trim()
  }
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      if (props.request) {
        props.request(form.value)
        return
      }
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在保存中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      let acc = store.userInfo.businessVO?.ownerAccount
      if (!acc) {
        try {
          await store.getUserInfo()
          acc = store.userInfo.businessVO?.ownerAccount
        } catch (e) {
          el_loading.close()
          return
        }
      }
      updateInvoice({
        ownerAccount: acc,
        ...form.value,
      })
        .then(res => {
          emits('success', res)
          close()
        })
        .finally(() => el_loading.close())
    }
  })
}
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  padding: 0 10px;
  box-sizing: border-box;
  margin: 10px auto;

  :deep(.el-radio) {
    .el-radio__input {
      .el-radio__inner {
        display: none;
      }
    }
  }

  .title-tips {
    font-size: 17px;
    text-align: left;
    color: var(--text-color);
    width: 100%;
    padding-left: 45px;
  }

  .form-box {
    width: 100%;
    margin: 25px 0;

    .invoice-content {
      font-size: 13px;
      color: var(--el-color-primary);
      // border: 1px solid var(--el-input-border-color);
      // box-shadow: 0 0 0 1px var(--el-input-border-color,var(--el-border-color)) inset;
      width: 100%;
      // padding: 0 11px;
      border-radius: 4px;
    }

    .form-btn {
      width: 80%;
      margin: 20px 10% 0;
    }
  }
}
</style>
