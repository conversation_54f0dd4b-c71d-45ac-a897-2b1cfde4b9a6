<template>
  <PublicDialog
    ref="DialogRef"
    title="修改公司名称"
    width="400px"
    custom-close
    align-center
    :showFooterButton="false"
    @close="handleClose"
  >
    <div class="flex-column box">
      <el-form class="form-box" ref="formRef" :model="form" :rules="rules" @submit.prevent>
        <el-form-item prop="name">
          <div class="input-box">
            <el-input v-model="form.name" placeholder="请输入公司名称" clearable maxlength="50"></el-input>
          </div>
        </el-form-item>

        <el-button type="primary" native-type="submit" round @click="onConfirm" class="form-btn">
          确认
        </el-button>
      </el-form>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { resetBusinessName } from '@/api/center'
import { ElLoading, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'

const store = useUserStore()

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const DialogRef = ref()
const formRef = ref()

const form = ref<{
  name: string
  ticket: string
}>({
  name: '',
  ticket: '',
})
const rules = {
  name: [{ required: true, message: '请输入公司名称', trigger: 'blur' }],
}

function open(name: string, ticket: string) {
  form.value.name = name
  form.value.ticket = ticket
  DialogRef.value?.open()
}

function close() {
  DialogRef.value?.close()
}

function handleClose() {
  form.value = {
    name: '',
    ticket: '',
  }
  formRef.value.clearValidate()
}

function onConfirm() {
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      let name = form.value.name.trim()
      if (!name) {
        ElMessage.error('公司名称不能为空')
        return
      }
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在保存中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      let acc = store.userInfo.businessVO?.ownerAccount
      if (!acc) {
        try {
          await store.getUserInfo()
          acc = store.userInfo.businessVO?.ownerAccount
        } catch (e) {
          el_loading.close()
          return
        }
      }
      resetBusinessName({
        ownerAccount: acc,
        ...form.value,
        name,
      })
        .then(res => {
          emits('success', res)
          close()
        })
        .finally(() => el_loading.close())
    }
  })
}
</script>

<style scoped lang="scss">
.box {
  width: 350px;
  margin: 10px auto;

  .form-box {
    width: 300px;
    margin: 25px 0;

    .input-box {
      width: 100%;
    }
    .form-btn {
      width: 100%;
      margin-top: 20px;
    }
  }
}
</style>
