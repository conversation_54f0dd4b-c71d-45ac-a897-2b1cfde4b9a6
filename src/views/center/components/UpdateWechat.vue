<template>
  <el-dialog
    v-model="dialogVisible"
    title="更换微信"
    width="700px"
    center
    align-center
    destroy-on-close
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    style="border-radius: var(--dialog-radius);"
    @close="handleClose"
  >
    <div class="flex-column box">
      <AnimationSteps
        :active="step"
        :steps="[{ title: '身份验证' }, { title: '绑定新微信' }, { title: '换绑成功' }]"
      />
      <TransitionGroup name="uw-transform" tag="div" class="flex-column content-box">
        <div v-if="step === 0" :key="0" class="uw-list-item">
          <QrCode
            ref="QrCodeRef"
            size="large"
            :auto-check-code="false"
            :code-type="codeType"
            :is-zfb-pay="false"
            successText="验证成功"
            @change="handleQrCodeVerifyAcc"
            @success="checkAuthSuccess"
          />
          <div class="tips">请使用【{{ store.userInfo.nickName }}】的微信扫码，进行身份验证</div>
        </div>
        <div v-if="step === 1" :key="1" class="uw-list-item">
          <QrCode
            ref="QrCodeRef"
            size="large"
            :auto-check-code="false"
            :code-type="codeType"
            :is-zfb-pay="false"
            @change="handleQrCodeVerifyAcc"
            @success="checkSuccess"
          />
          <div class="tips">请使用要绑定的新微信号扫码</div>
        </div>
        <div v-if="step === 2" class="uw-list-item success-box">
          <div>换绑成功</div>
          <div>微信已更换为：【{{ store.userInfo.nickName }}】</div>
        </div>
      </TransitionGroup>
    </div>
    <template #footer>
      <div class="flex-center footer-btn-box">
        <el-button v-if="step != 2" @click="close">取消</el-button>
        <el-button v-else type="primary" @click="close">关闭</el-button>
        <!-- <el-button type="primary" @click="step++">next</el-button>
        <el-button type="primary" @click="step = 0">next 0</el-button> -->
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import AnimationSteps from '@/components/public/steps/AnimationSteps.vue'
import QrCode from '@/components/public/qrcode/QrCode.vue'
import type { CODE_TYPE, QRCODE_STATUS } from '@/components/public/qrcode/type'
import { computed, ref } from 'vue'
import useQrCodeVerify from '@/hooks/useQrCodeVerify'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage, ElLoading } from 'element-plus'
import { rebind } from '@/api/center'
import { ElMessageBox } from 'element-plus'

const { handleQrCodeVerifyAcc } = useQrCodeVerify()

const router = useRouter()
const store = useUserStore()

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const dialogVisible = ref(false)
const step = ref(0)
const nickName = ref('')
const authTicket = ref('')

const codeType = computed<CODE_TYPE>(() => {
  if (step.value === 0) {
    return 2
  }
  return 1
})

function open() {
  dialogVisible.value = true
  console.log('open', step.value)
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  nickName.value = ''
  authTicket.value = ''
  step.value = 0
}

function checkAuthSuccess(data: any) {
  ElMessage.success('认证成功')
  authTicket.value = data.ticket
  step.value = 1
}
function checkSuccess(data: any) {
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在绑定中，请稍后',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  rebind({
    account: store.userInfo.account,
    accountTicket: authTicket.value,
    ticket: data.ticket,
  })
    .then(async res => {
      ElMessage.success('绑定成功')
      nickName.value = res.data
      try {
        await store.getUserInfo()
        step.value = 2
        emits('success')
      } catch (e) {
        ElMessageBox.confirm('获取最新用户信息失败！请刷新页面或重新登录', '系统提示', {
          cancelButtonText: '刷新',
          confirmButtonText: '退出登录',
        })
          .then(() => {
            store.logout()
            router.replace('/login')
          })
          .catch(() => {
            store.removeInfo()
            router.go(0)
          })
      }
      el_loading.close()
    })
    .catch(() => el_loading.close())
}
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  margin: 10px auto;

  .content-box {
    position: relative;
    width: 100%;
    height: 250px;
    justify-content: center;

    
    .uw-list-item {
      position: absolute;
      top: 10px;
      left: 50%;
      transform: translateX(-50%);
    }

    .tips {
      color: var(--el-color-primary);
      font-size: 15px;
      font-weight: 500;
      margin: 10px 0;
      text-align: center;
    }

    .success-box {
      color: var(--el-color-primary);
      font-size: 15px;
      font-weight: 500;
      line-height: 28px;
      text-align: center;
      top: calc(50% - 28px);
    }

  }
}
.footer-btn-box {
  .el-button {
    width: 100px;
  }
}

.uw-transform-leave-active {
  transition: all 1s linear;
}

.uw-transform-enter-active {
  animation: bounce-in 1.5s ease-out;
}

@keyframes bounce-in {
  // 淡入淡出效果
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
  // 滑动效果
  // from {
  //   opacity: 0;
  //   transform: translateX(20%);
  // }
  // to {
  //   opacity: 1;
  //   transform: translateX(-50%);
  // }
}
.uw-transform-leave-from {
  opacity: 1;
  // transform: translateX(-50%) !important;
}
.uw-transform-leave-to {
  opacity: 0;
  // transform: translateX(-120%) !important;
}
</style>
