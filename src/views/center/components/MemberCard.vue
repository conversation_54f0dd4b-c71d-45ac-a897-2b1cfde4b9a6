<template>
  <div class="flex-between member-card-box">
    <img class="member-img" src="@/assets/image/member.png" />
    <div class="flex-start gap member-info">
      <img class="member-img-text" src="@/assets/image/MEMBER BENEFITS.png" />
      <img class="member-icon" src="@/assets/icon/icon_vip.png" />
      <div style="z-index: 3">
        <div class="name">
          {{ name }}
          <OwnerTag />
        </div>
        <div class="flex-between time">
          {{ endTime }}
        </div>
      </div>
    </div>
    <div style="z-index: 3">
      <template v-if="store.isOwnerAcc()">
        <el-button type="primary" style="padding: 5px 25px" round @click="toMember">
          <div v-if="isFiveRenew">半价续费</div>
          <span v-else>{{ btnText }}</span>
        </el-button>
      </template>
      <template v-if="!store.isOwnerAcc() && (store.isVip() || store.isViped())">
        <el-button type="primary" style="padding: 5px 25px" round @click="toMember">
          <div v-if="isFiveRenew">半价续费</div>
          <span v-else>去续费</span>
        </el-button>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import OwnerTag from '@/components/public/tag/OwnerTag.vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessageBox } from 'element-plus'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'

const { isFiveRenew } = useShowRenewDialog()

const store = useUserStore()
const router = useRouter()

const btnText = ref('去续费')
const name = computed(() => {
  let str = '您还不是会员'
  if (store.userInfo.businessVO?.memberStatus) {
    if (store.userInfo.businessVO.memberStatus == 3) {
      str = '蜗牛会员（已到期）'
    } else {
      str = '蜗牛会员'
    }
  }
  return str
})
const endTime = computed(() => {
  let str = '开通会员，享受超多权益'
  btnText.value = '马上开通'
  if (store.userInfo.businessVO?.memberStatus == 1 || store.userInfo.businessVO?.memberStatus == 2) {
    let time = store.userInfo.businessVO?.memberValidity || ''
    if (time) {
      time = time.replace('T', ' ').replace('.000+08:00', '')
    }
    str = checkTime(time)
    btnText.value = '去续费'
  }
  return str
})

function checkTime(time: string) {
  if (time) {
    let end = new Date(time).getTime()
    if (end - new Date().getTime() <= 30 * 24 * 60 * 60 * 1000) {
      return '即将到期：' + time + '到期'
    }
  }
  return '会员有效期：' + time + '到期'
}

function toMember() {
  if (store.isOwnerAcc() || store.isVip()) {
    router.replace('/vip')
    return
  }
  let div = ''
  if (store.isVip()) {
    div = `<p>仅主账号可续费会员</p><p style="font-size: 14px;color: var(--text-gray-color)">如果会员快要到期，请联系主账号进行续费~</p>`
  } else {
    div = `<p>仅主账号可开通会员</p><p style="font-size: 14px;color: var(--text-gray-color)">如果要开通会员，请联系主账号进行开通~</p>`
  }
  ElMessageBox.alert(div, '温馨提醒', {
    confirmButtonText: '确定',
    type: 'warning',
    dangerouslyUseHTMLString: true,
  })
}
</script>

<style scoped lang="scss">
.member-card-box {
  position: relative;
  border-radius: 10px;
  flex-shrink: 0;
  width: 100%;
  height: 130px;
  min-width: 330px;
  background: var(--bg);
  padding: 0 40px;
  box-sizing: border-box;

  .member-img {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 85px;
    z-index: 1;
  }

  .gap {
    gap: 10px;
  }
  .member-info {
    position: relative;

    .member-img-text {
      width: 210px;
      height: 66px;
      position: absolute;
      top: -20px;
      left: -15px;
      z-index: 1;
    }
    .member-icon {
      width: 50px;
      height: 50px;
      flex-shrink: 0;
      z-index: 3;
    }
    .name {
      font-size: 20px;
      font-weight: bold;
      color: #32688d;
      position: relative;
      width: fit-content;
    }
    .time {
      font-size: 13px;
      color: var(--el-color-primary);

      span {
        margin-left: 10px;
        color: #4b7902;
        cursor: pointer;
      }
    }
  }
}
</style>
