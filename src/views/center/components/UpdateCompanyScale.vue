<template>
  <PublicDialog
    ref="DialogRef"
    title="修改公司规模"
    width="400px"
    custom-close
    align-center
    :showFooterButton="false"
    @close="handleClose"
  >
    <div class="flex-column box">
      <el-form class="form-box" ref="formRef" :model="form" :rules="rules" @submit.prevent>
        <el-form-item prop="scale">
          <ul class="scale-list">
            <li
              v-for="(item, i) in businessScale"
              :key="i"
              :class="{ active: item.value == form.scale }"
              @click="form.scale = item.value"
            >
              {{ item.label }}
              <el-icon v-if="item.value == form.scale" class="active-icon" color="var(--el-color-primary)"><Check /></el-icon>
            </li>
          </ul>
        </el-form-item>

        <el-button type="primary" native-type="submit" round @click="onConfirm" class="form-btn">
          确认
        </el-button>
      </el-form>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { resetBusinessScale } from '@/api/center'
import { ElLoading } from 'element-plus'
import { businessScale } from '@/hooks/useWelcome'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const DialogRef = ref()
const formRef = ref()

const form = ref<{
  scale?: number
  ticket?: string
}>({
  scale: undefined,
  ticket: undefined,
})
const rules = {
  scale: [{ required: true, message: '请选择规模', trigger: 'change' }],
}

function open(scale?: number | null, ticket?: string) {
  form.value.scale = scale || void 0
  form.value.ticket = ticket
  DialogRef.value?.open()
}

function close() {
  DialogRef.value?.close()
}

function handleClose() {
  form.value = {
    scale: void 0,
    ticket: void 0,
  }
  formRef.value.clearValidate()
}

function onConfirm() {
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在保存中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      resetBusinessScale(form.value)
        .then(res => {
          emits('success', res)
          close()
        })
        .finally(() => el_loading.close())
    }
  })
}
</script>

<style scoped lang="scss">
.box {
  width: 350px;
  margin: 10px auto;

  .form-box {
    width: 300px;
    margin: 25px 0;

    .scale-list {
      width: 100%;
      margin: 0;
      padding: 0;
      list-style-type: none;

      li {
        position: relative;
        width: 100%;
        text-align: center;
        line-height: 24px;
        border: 1px solid #e4e4e4;
        color: var(--el-text-color);
        border-radius: 20px;
        padding: 3px;
        margin: 5px 0 10px;
        cursor: pointer;

        &:hover {
          color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }
        &.active {
          color: var(--el-color-primary);
          border-color: var(--el-color-primary);
        }

        .active-icon {
          position: absolute;
          top: 50%;
          right: 10px;
          transform: translateY(-50%);
        }
      }
    }
    .form-btn {
      width: 100%;
      margin-top: 20px;
    }
  }
}
</style>
