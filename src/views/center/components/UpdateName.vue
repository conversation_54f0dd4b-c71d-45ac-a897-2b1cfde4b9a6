<template>
  <PublicDialog
    ref="DialogRef"
    title="修改姓名"
    width="400px"
    custom-close
    align-center
    :showFooterButton="false"
    @close="handleClose"
  >
    <div class="flex-column box">
      <el-form class="form-box" ref="formRef" :model="form" :rules="rules" @submit.prevent>
        <el-form-item prop="name">
          <div class="input-box">
            <el-input v-model="form.name" placeholder="请输入姓名" clearable maxlength="32"></el-input>
          </div>
        </el-form-item>

        <el-button type="primary" native-type="submit" round @click="onConfirm" class="form-btn">
          保存
        </el-button>
      </el-form>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { bizUserUpdateName } from '@/api/center'
import { ElLoading, ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'

const store = useUserStore()

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const DialogRef = ref()
const formRef = ref()

const form = ref<{
  name: string
}>({
  name: '',
})
const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
}

function open() {
  form.value.name = store.userInfo.name
  DialogRef.value?.open()
}

function close() {
  DialogRef.value?.close()
}

function handleClose() {
  form.value = {
    name: '',
  }
  formRef.value.clearValidate()
}

function onConfirm() {
  formRef.value.validate(async (valid: any) => {
    if (valid) {
      let name = form.value.name.trim()
      if (!name) {
        ElMessage.error('姓名不能为空')
        return
      }
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在保存中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      bizUserUpdateName({
        name,
      })
        .then(res => {
          emits('success', res)
          close()
        })
        .finally(() => el_loading.close())
    }
  })
}
</script>

<style scoped lang="scss">
.box {
  width: 350px;
  margin: 10px auto;

  .form-box {
    width: 300px;
    margin: 25px 0;

    .input-box {
      width: 100%;
    }
    .form-btn {
      width: 100%;
      margin-top: 20px;
    }
  }
}
</style>
