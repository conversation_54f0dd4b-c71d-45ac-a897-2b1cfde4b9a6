<template>
  <el-dialog
    v-model="dialogVisible"
    title="变更手机号"
    width="700px"
    center
    align-center
    :show-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    style="border-radius: var(--dialog-radius);"
    @close="handleClose"
  >
    <div class="flex-column box">
      <el-steps
        style="width: 100%"
        :active="step - 1"
        align-center
        process-status="finish"
        finish-status="success"
      >
        <el-step title="验证原绑定手机号" />
        <el-step title="绑定新手机号" />
        <el-step title="换绑成功" />
      </el-steps>
      <el-form
        class="flex-column form-box"
        ref="formRef"
        :model="form"
        :rules="rules"
        inline
        label-width="100px"
        @submit.prevent
      >
        <el-form-item label="原绑定手机号" prop="curPhone" label-width="110px" v-if="step === 1">
          <div class="input-box">
            <el-input v-model="form.curPhone" maxlength="11" placeholder="请输入已绑定的原手机号" clearable></el-input>
          </div>
        </el-form-item>
        <template v-if="step === 2">
          <el-form-item prop="phone" label="新手机号" label-width="80px">
            <div class="input-box">
              <el-input
                placeholder="请输入要更换的新手机号"
                prefix-icon="User"
                maxlength="11"
                clearable
                :disabled="checkBindLoading"
                v-model="form.phone"
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item prop="code" label="验证码" label-width="80px">
            <div class="input-box code-box">
              <el-input
                placeholder="请输入验证码"
                prefix-icon="Lock"
                maxlength="4"
                v-model="form.code"
              ></el-input>
              <el-button
                class="code-btn"
                link
                :disabled="!checkBindBtn"
                :loading="codeBtnLoading"
                @click.stop="getPhoneCode"
              >
                <template v-if="codeTime">
                  {{ codeTime + '秒后可再次获取' }}
                </template>
                <template v-else>获取验证码</template>
              </el-button>
            </div>
          </el-form-item>
        </template>
        <div v-if="step === 3" class="success-box">
          <div>换绑成功</div>
          <div>手机号已更换为：{{ form.phone }}</div>
        </div>
      </el-form>
    </div>
    <ImageCodeDialog />
    <template #footer>
      <div class="flex-center footer-btn-box">
        <el-button v-if="step === 1" @click="close">取消</el-button>
        <el-button v-if="step === 2" @click="handlePrev">上一步</el-button>
        <el-button v-if="step === 1" type="primary" :loading="nextBtn" @click="handleNext">下一步</el-button>
        <el-button v-else-if="step === 2" type="primary" :disabled="checkBindLoading" @click="onConfirm">
          下一步
        </el-button>
        <el-button v-else-if="step === 3" type="primary" @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import ImageCodeDialog from '@/views/login/ImageCodeDialog.vue'
import { ref, toRefs } from 'vue'
import { checkPhone, checkBindingPhone, bizUserUpdatePhone } from '@/api/center'
import { ElLoading } from 'element-plus'
import { phone_reg } from '@/utils/RegExp'
import useImgOrPhoneCode from '@/hooks/useImgOrPhoneCode'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const {
  codeTime,
  codeBtnLoading,
  phoneForm,
  sendPhoneCode, // 发送手机验证码
} = useImgOrPhoneCode()

const dialogVisible = ref(false)
const formRef = ref()
const step = ref(1)
const form = ref({
  curPhone: '',
  ...toRefs(phoneForm.value),
})
const rules = {
  curPhone: [
    { required: true, message: '请先输入手机号', trigger: 'blur' },
    { pattern: phone_reg, message: '手机号错误，请重新填写~', trigger: 'change' },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: checkBindPhone, trigger: 'change' },
    // { pattern: phone_reg, message: '请输入正确的手机号', trigger: 'blur' },
  ],
  code: [{ required: true, message: '请输入验证码', trigger: 'manul' }],
}
const nextBtn = ref(false)

const checkBindLoading = ref(false)
const checkBindBtn = ref(false)
function checkBindPhone(rule: any, value: any, callback: any) {
  checkBindBtn.value = false
  if (!phone_reg.test(value)) {
    return callback(new Error('请输入正确的手机号'))
  }
  checkBindLoading.value = true
  checkBindingPhone({ phone: value })
    .then((res: any) => {
      if (res.code !== 200) {
        callback(new Error(res.msg))
      } else {
        checkBindBtn.value = true
        callback()
      }
    })
    .catch(err => {
      callback(new Error(err.data?.msg || '网络错误！请稍后再试'))
    })
    .finally(() => (checkBindLoading.value = false))
}

function open() {
  step.value = 1
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
}

function handleClose() {
  form.value.curPhone = ''
  form.value.phone = ''
  form.value.code = ''
  formRef.value?.clearValidate()
}

function handlePrev() {
  step.value = 1
  handleClose()
}

function handleNext() {
  formRef.value.validateField('curPhone', (valid: any) => {
    if (valid) {
      nextBtn.value = true
      checkPhone({ phone: form.value.curPhone })
        .then(res => {
          if (res.code === 200) {
            step.value = 2
          }
        })
        .finally(() => (nextBtn.value = false))
    }
  })
}

// 获取手机验证码
function getPhoneCode() {
  if (codeTime.value) return
  formRef.value.validateField('phone', (valid: any) => {
    if (valid) {
      sendPhoneCode({ phoneNum: form.value.phone })
    }
  })
}

function onConfirm() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在保存中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      bizUserUpdatePhone({
        phone: form.value.phone,
        phoneCaptcha: form.value.code,
      })
        .then(res => {
          emits('success', res)
          step.value = 3
        })
        .finally(() => el_loading.close())
    }
  })
}
</script>

<style scoped lang="scss">
.box {
  width: 100%;
  margin: 10px auto;

  :deep(.el-steps) {
    .is-finish {
      .el-step__icon {
        background-color: var(--el-color-primary);
        color: #fff;
      }
    }
    .el-step {
      .el-step__icon {
        text-align: center;

        .el-step__icon-inner {
          font-family: 'PingFang SC';
        }
        div {
          line-height: 24px;
        }
      }
    }
    .el-step__head.is-success {
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);

      .el-step__line {
        .el-step__line-inner {
          border-width: 1px !important;
          width: 100% !important;
        }
      }
    }
    .el-step__main {
      .is-success {
        &.el-step__title {
          color: var(--el-color-primary);
        }
        &.el-step__description {
          color: var(--el-color-primary);
        }
      }
    }
  }

  .form-box {
    // width: 400px;
    height: 230px;
    justify-content: center;

    :deep(.el-form-item) {
      .el-form-item__label {
        height: 40px;
        line-height: 40px;
        justify-content: flex-start;
      }
    }

    .title {
      margin: 10px 0;
    }

    .input-box {
      width: 300px;

      :deep(.el-input) {
        --el-input-height: 40px;
      }
    }
    .code-box {
      position: relative;
      :deep(.el-input) {
        .el-input__suffix {
          display: none;
        }
      }

      .code-btn {
        position: absolute;
        top: 0;
        right: 8px;
        color: #169bd5;
        height: 40px;
      }
    }

    .success-box {
      color: var(--el-color-primary);
      font-size: 15px;
      font-weight: 500;
      line-height: 28px;
      text-align: center;
    }
  }
}
.footer-btn-box {
  .el-button {
    width: 100px;
  }
}
</style>
