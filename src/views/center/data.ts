interface VipSetMeal {
  type: number
  title: string
  label: string
  price: number
  $: number
  month: number
  hot?: boolean
  activityMonth?: number
  activityTime?: number | string
  bgColor?: string
  textColor?: string
  activityPresentedTime?: string
}
/**
 * 套餐类型
 */
export const vipSetMealList: VipSetMeal[] = [
  {
    type: 1,
    title: '1年VIP',
    label: '年度会员',
    price: 1980,
    $: 238.8,
    month: 12,
    bgColor: 'linear-gradient( 143deg, #FFFCF8 0%, #FFEACF 99%)',
    textColor: '#254974',
    hot: true,
  },
  {
    type: 2,
    title: '3年VIP',
    label: '三年会员',
    price: 4980,
    $: 644.4,
    month: 36,
    bgColor: 'linear-gradient( 143deg, #FDFEFF 0%, #DDEAF4 100%)',
    textColor: '#252323',
  },
  {
    type: 0,
    title: '季度VIP',
    label: '季度会员',
    price: 980,
    $: 119.7,
    month: 3,
    bgColor: 'linear-gradient( 143deg, #FFFCF8 0%, #FFEACF 99%)',
    textColor: '#252323',
  },
]
export const specialOfferList = [
  {
    label: '满5单减100',
    value: 1
  },
  {
    label: '半价续费',
    value: 2
  },
  {
    label: '种草码优惠',
    value: 3
  },
  {
    label: '种草码优惠',
    value: 5
  }
]