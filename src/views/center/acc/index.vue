<template>
  <div class="pages">
    <div class="flex-start tabs">
      <!-- <div v-if="store.isOwnerAcc()" class="tab" @click="router.replace('/vip')">我的会员</div> -->
      <div class="tab border-left" @click="router.replace('/center/info')">个人资料</div>
      <div class="tab active">子账号管理</div>
      <!--      <div class="tab" @click="router.replace('/center/blackList')">黑名单</div>-->
    </div>
    <div class="w-bg center-body">
      <div class="acc-box">
        <div class="head-tips">
          <div class="title flex-start gap-5">
            <el-icon color="var(--el-color-primary)" :size="15"><InfoFilled /></el-icon>
            账号说明
          </div>
          <div class="flex-start gap-5" style="align-items: baseline">
            <div>
              <div class="tips">1. 子账号和主账号共享套餐额度和查询记录，同时也共享有效期；</div>
              <div class="tips">2. 主账号有删除子账号的权限，删除后，子账号将不能查看公司任何信息；</div>
              <div class="tips">3. 子账号也支持续费会员，续费成功后，会员有效期同步更新；</div>
            </div>
            <div>
              <div class="tips">
                4.
                每个子账号只能加入一个会员主账户进行协作，如果这个子账号需要加入另外一个会员账号，请先解绑；
              </div>
              <div class="tips">5. 若需变更手机号，可登录后变更或联系客服修改。</div>
            </div>
          </div>

          <!-- <div class="tips">5. 请勿进行违规行为，否则可能会导致主账号和子账号一同封禁，对您造成损失。</div> -->
        </div>

        <div style="position: relative; margin-top: 20px">
          <div class="btn-box">
            <el-button class="custom-radius" v-if="store.isVip()" type="primary" icon="Plus" @click="add">
              添加子账号
            </el-button>
          </div>

          <el-tabs v-model="curTab" @tab-change="handleTabChange">
            <el-tab-pane label="账号列表" :name="'one'">
              <el-table
                :data="tableData"
                class="custom-table-border radius-box"
                style="width: 100%"
                height="500"
                border
                header-row-class-name="table-primary-header-row"
                :row-class-name="tableRowClassName"
                v-loading="tableLoading"
              >
                <template #empty>
                  <Empty description="暂无子账号数据" :image-size="120" />
                </template>
                <el-table-column type="index" label="序号" align="center" width="55" :index="indexMethod" />
                <!-- <el-table-column prop="account" label="子账号" align="center" min-width="190" /> -->
                <el-table-column prop="phone" label="手机号" align="center" min-width="190" />
                <el-table-column prop="name" label="员工姓名" align="center" min-width="190" />
                <el-table-column prop="nickName" label="绑定微信" align="center" min-width="190">
                  <template v-slot="{ row }">
                    {{ row.nickName ? row.nickName : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="lastLoginTime" label="最后登录时间" align="center" width="190">
                  <template v-slot="{ row }">
                    {{
                      row.lastLoginTime ? row.lastLoginTime.replace('T', '').replace('.000+08:00', '') : '-'
                    }}
                  </template>
                </el-table-column>
                <!-- <el-table-column prop="status" label="账号状态" align="center" width="140">
                  <template v-slot="{ row }">
                    <el-switch
                      :model-value="row.status"
                      :active-value="0"
                      :inactive-value="1"
                      @click="handleStatusChange(row)"
                    />
                  </template>
                </el-table-column> -->
                <el-table-column label="操作" fixed="right" align="center" width="170">
                  <template v-slot="{ row }">
                    <el-button
                      v-if="row.bizUserId && store.isViped()"
                      link
                      type="primary"
                      icon="Switch"
                      @click="untieBind(row)"
                    >
                      解绑
                    </el-button>
                    <el-button
                      v-else-if="store.isVip()"
                      link
                      type="primary"
                      icon="Switch"
                      @click="accBind(row)"
                    >
                      绑定
                    </el-button>
                    <!-- <el-button link type="primary" @click="againBind(row)">重绑</el-button> -->
                    <!-- <el-button link type="primary" @click="resetPwd(row)">重置密码</el-button> -->
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
            <el-tab-pane label="申请记录" :name="'two'">
              <template #label>
                <div class="apply-List">
                  <el-badge :max="999" :value="applyNumber" :show-zero="false" :offset="[4, 0]">
                    <span class="apply-title">申请列表</span>
                  </el-badge>
                  <!-- <span class="apply-title">申请列表</span> -->
                  <!-- <el-badge class="mark" :value="3" /></div> -->
                  <!-- <span class="apply-number">99</span> -->
                </div>
              </template>
              <ApplicationList ref="applicationListRef" @updateNumber="getApplyNum" />
            </el-tab-pane>
          </el-tabs>
        </div>

        <!-- <div class="table-page-box">
          <el-pagination
            class="custom-pagination-radius"
            background
            @size-change="pageSizeChange"
            @current-change="curPageChange"
            :current-page="pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, prev, pager, next, jumper"
            :total="total"
          />
        </div> -->
      </div>
    </div>

    <AddAccount ref="AddAccountRef" @success="handleQuery()" />
    <UntieBind ref="UntieBindRef" @success="handleQuery()" />
    <AccBind ref="AccBindRef" @success="handleQuery()" />
    <ResetPassword ref="ResetPasswordRef" />
    <QrCodeDialog
      ref="QrCodeDialogRef"
      title="绑定微信"
      qr-code-tips=""
      :code-type="1"
      @success="qrcodeSuccess"
    >
      <div></div>
      <template #footer>
        <div class="qrcode-tips template-pre">
          {{ '使用新的微信扫一扫\n绑定新人员' }}
        </div>
      </template>
    </QrCodeDialog>
    <el-dialog
      align-center
      append-to-body
      title=""
      v-model="isShow"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeDialog"
    >
      <div class="dialog-content">
        <div>
          该子账号下存在{{
            dialogType == '3' ? '待支付和待审核' : dialogType == '2' ? '待审核' : '待支付'
          }}的会员开通订单
        </div>
        <div>是否取消相关订单？</div>
        <div style="font-weight: 400; font-size: 12px">（取消后,才能解绑）</div>
      </div>
      <template #footer>
        <span class="dialog-footer flex-center" style="padding-bottom: 15px">
          <el-button round @click="closeDialog" style="padding: 8px 36px">暂不取消</el-button>
          <el-button round type="primary" @click="cancelOrder">好的，直接取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UntieBind from '@/views/center/acc/components/UntieBind.vue'
import AccBind from '@/views/center/acc/components/AccBind.vue'
import AddAccount from '@/views/center/acc/components/AddAccount.vue'
import ResetPassword from '@/views/center/components/ResetPassword.vue'
import QrCodeDialog from '@/components/public/dialog/QrCodeDialog.vue'
import ApplicationList from '@/views/center/acc/components/ApplicationList.vue'
import { useUserStore, type businessAccountVOS } from '@/stores/modules/user'
import { useRouter } from 'vue-router'
import { rebind, updateAccStatus, getBusinessAccountApplyNum, getValidOrderList } from '@/api/center'
import { cancelMemberOrder, cancelSonMemberOrder } from '@/api/vip'
import { unbindLoginUser } from '@/api/user'
import { ElLoading, ElMessage } from 'element-plus'
import { ElMessageBox } from 'element-plus'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { handelRenewDialog } = useShowRenewDialog()

const router = useRouter()

const store = useUserStore()

const AddAccountRef = ref<InstanceType<typeof AddAccount>>()
const UntieBindRef = ref<InstanceType<typeof UntieBind>>()
const AccBindRef = ref<InstanceType<typeof AccBind>>()
const ResetPasswordRef = ref<InstanceType<typeof ResetPassword>>()
const QrCodeDialogRef = ref<InstanceType<typeof QrCodeDialog>>()
const tableData = ref<businessAccountVOS>([])
// const pageNum = ref(1)
// const pageSize = ref(20)
// const total = ref(0)
const tableLoading = ref(false)
const bindAcc = ref('')

const curTab = ref('one') //当前tab
const applyNumber = ref(0) //申请记录数量
const applicationListRef = ref<InstanceType<typeof ApplicationList>>()
const isShow = ref(false)

function handleTabChange() {
  if (curTab.value === 'one') {
    handleQuery()
    getApplyNum()
  } else {
    getApplyNum()
    applicationListRef.value?.getAccountAppltyList()
  }
}

// 行颜色
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 !== 0) {
    return 'primary-row'
  }
  return ''
}
// 序号
const indexMethod = (index: number) => {
  return index + 1
}

function getApplyNum() {
  getBusinessAccountApplyNum().then(res => {
    applyNumber.value = res.data
  })
}

function doQueryAll() {
  getApplyNum()
  handleQuery()
}

async function handleQuery() {
  tableLoading.value = true
  handelRenewDialog()
  try {
    await store.getUserInfo()
    tableData.value = store.userInfo.businessAccountVOS
  } catch (e) {
    tableData.value = []
  }
  tableLoading.value = false
}
// 分页
// function pageSizeChange(val: number) {
//   pageNum.value = 1
//   pageSize.value = val
//   handleQuery()
// }
// function curPageChange(val: number) {
//   pageNum.value = val
//   handleQuery()
// }

function add() {
  if (!store.isVip()) {
    ElMessageBox.confirm(
      `
      <div style="text-align: center;margin: 10px 0 10px;">
        <div>您当前非蜗牛会员</div>
        <div>开通会员后可添加子账号</div>
      </div>
    `,
      '',
      {
        center: true,
        confirmButtonText: '立即开通',
        dangerouslyUseHTMLString: true,
      }
    ).then(() => {
      router.push('/vip')
    })
    return
  }
  AddAccountRef.value?.open()
}

const dialogType = ref('')
const dialogNums = ref<string[]>([])

// 解绑
function untieBind(row: any) {
  handleisOrder(row)
}

const curUnBindData = ref<{
  id: string
  name: string
  nickName: string
  phone: string
}>({
  id: '',
  name: '',
  nickName: '',
  phone: '',
})

function handleisOrder(row: any) {
  getValidOrderList({ bizUserId: row.bizUserId }).then(res => {
    if (res.data && res.data.length > 0) {
      const hasStatus1 = res.data.filter((data: any) => data.status === 1)
      const hasStatus2 = res.data.filter((data: any) => data.status === 2)
      if (hasStatus1 && hasStatus1.length > 0 && hasStatus2 && hasStatus2.length > 0) {
        dialogType.value = '3'
        dialogNums.value.push(hasStatus1[0].orderNum)
        hasStatus2.forEach((item: any) => {
          dialogNums.value.push(item.orderNum)
        })
      } else if (hasStatus1 && hasStatus1.length > 0) {
        dialogType.value = '1'
        dialogNums.value.push(hasStatus1[0].orderNum)
      } else if (hasStatus2 && hasStatus2.length > 0) {
        dialogType.value = '2'
        hasStatus2.forEach((item: any) => {
          dialogNums.value.push(item.orderNum)
        })
      } else {
        dialogType.value = ''
      }
    }
    if (dialogNums.value.length > 0) {
      curUnBindData.value = row
      isShow.value = true
    } else {
      UntieBindRef.value?.open(row)
    }
  })
}

function cancelOrder() {
  cancelSonMemberOrder({ orderNums: dialogNums.value.join(',') }).then(() => {
    isShow.value = false
    dialogNums.value = []
    dialogType.value = ''
    unbindLoginUser({ businessAccountId: curUnBindData.value.id }).then(() => {
      curUnBindData.value = {
        id: '',
        name: '',
        nickName: '',
        phone: '',
      }
      ElMessage.success('解绑成功')
      handleQuery()
    })
  })
}
function closeDialog() {
  isShow.value = false
  dialogNums.value = []
  dialogType.value = ''
}
// 绑定
function accBind(row: any) {
  AccBindRef.value?.open(row.id)
}
// 重绑
function againBind(row: any) {
  bindAcc.value = row.account
  QrCodeDialogRef.value?.open()
}
// 重置密码
function resetPwd(row: any) {
  ResetPasswordRef.value?.open(row.account)
}
// 扫码成功
function qrcodeSuccess(data: any) {
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在绑定中，请稍后',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  tableLoading.value = true
  rebind({
    account: bindAcc.value,
    ticket: data.ticket,
  })
    .then(() => {
      QrCodeDialogRef.value?.close()
      ElMessage.success('绑定成功')
      handleQuery()
    })
    .catch(() => (tableLoading.value = false))
    .finally(() => el_loading.close())
}
// 账号状态变更
function handleStatusChange(row: any) {
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在更新中，请稍后',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  updateAccStatus({
    account: row.account,
    status: row.status === 0 ? 1 : 0,
  })
    .then(() => {
      handleQuery()
    })
    .finally(() => el_loading.close())
}
doQueryAll()
// handleQuery()
</script>

<style scoped lang="scss">
@use '@/views/center/tabs.scss';
.apply-List {
  .apply-title {
    position: relative;
  }
  .apply-number {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    min-width: 20px;
    min-height: 15px;
    font-size: 12px;
    top: 4px;
    // right: -8px;
    transform: scale(0.8);
    left: 70px;
    background-color: #e3465ae6;
    border-radius: 50%;
    color: #fff;
  }
}
.center-body {
  background-color: var(--bg);
  border-radius: 8px;

  .acc-box {
    .head-tips {
      padding: 20px 20px 10px;
      font-size: 12px;
      color: var(--text-gray-color);
      border-radius: 12px;
      // border: 1px solid var(--el-color-primary-light-5);
      background-color: #f6f6f6;

      .title {
        color: var(--el-color-primary);
      }
      .tips {
        margin: 2px 0 2px 20px;
      }
    }

    .btn-box {
      position: absolute;
      right: 0;
      top: 0;
      z-index: 5;
    }

    :deep(.el-table) {
      .table-primary-header-row {
        --el-table-header-bg-color: #f6f6f6;

        .el-table__cell {
          padding: 15px 0;
          color: var(--text-color);
        }
      }
      .primary-row {
        --el-table-tr-bg-color: var(--el-color-primary-light-9);
      }

      .el-table__row:last-child .el-table__cell {
        border-bottom: var(--el-table-border);
      }
    }
  }
}
.qrcode-tips {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: var(--el-color-primary);
}
.dialog-content {
  padding: 15px 0;
  text-align: center;
  color: #333;
  font-size: 16px;
  font-weight: bold;
}
</style>
