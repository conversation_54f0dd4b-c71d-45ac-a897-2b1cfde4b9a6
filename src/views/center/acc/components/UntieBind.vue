<template>
  <PublicDialog
    ref="DialogRef"
    width="550px"
    title="解绑"
    :showFooterButton="true"
    custom-close
    align-center
    destroy-on-close
    :close-on-click-modal="false"
  >
    <div class="content-box">
      <div class="info">
        <div class="flex-start">
          <div class="label">姓名</div>
          <div class="value">{{ info.name }}</div>
        </div>
        <div class="flex-start">
          <div class="label">微信名</div>
          <div class="value">{{ info.nickName || '-' }}</div>
        </div>
        <div class="flex-start">
          <div class="label">手机号</div>
          <div class="value">{{ info.phone }}</div>
        </div>
      </div>
      <div class="tips">
        <div>是否确认解除该账号与本企业的绑定？</div>
        <div>解除后该账号无法继续查看公司任何信息，后续可重新绑定其它账号继续该账号的工作</div>
      </div>
    </div>
    <template #footerButton>
      <div class="flex-center footer-btn-box">
        <el-button round @click="close">取消</el-button>
        <el-button round type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { unbindLoginUser } from '@/api/user'

const DialogRef = ref()
const info = ref<{
  id: string
  name: string
  nickName: string
  phone: string
}>({
  id: '',
  name: '',
  nickName: '',
  phone: ''
})

const emit = defineEmits(['success'])

defineExpose({
  open,
  close,
})

function open(row: any) {
  info.value = row
  DialogRef.value.open()
}
function close() {
  DialogRef.value.close()
}

function handleConfirm() {
  unbindLoginUser({ businessAccountId: info.value.id }).then(() => {
    ElMessage.success('解绑成功')
    emit('success')
    close()
  })
}
</script>

<style scoped lang="scss">
.content-box {
  .info {
    padding: 15px;
    background-color: #F6F6F6;
    color: var(--text-gray-color);
    font-size: 13px;
    line-height: 28px;
    border-radius: 15px;

    .label {
      color: #9BA6BA;
      width: 50px;
      text-align: right;
      margin-right: 6px;
    }

    .value {
      color: #000;
    }
  }
  .tips {
    margin: 20px 0 15px;
    font-size: 12px;
    color: var(--text-gray-color);

    div:first-child {
      color: var(--text-color);
      font-size: 15px;
    }
  }
}
.footer-btn-box {
  .el-button {
    width: 100px;
  }
}
</style>
