<template>
  <PublicDialog
    ref="DialogRef"
    width="1058px"
    title="如何邀请子账号"
    :showFooterButton="false"
    custom-close
    align-center
    :title-Center="false"
    destroy-on-close
    style="padding: 0 0 20px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <template #header>
      <div class="flex-between addAcc-dialog-title">
        <span>如何邀请子账号</span>
        <div class="close-round-btn" @click="close"></div>
      </div>
    </template>

    <div class="flex-between" style="gap: 16px">
      <div class="flex-column content-box">
        <div class="step-box">
          <img class="step-bg" src="@/assets/image/center/step1.png" alt="" />
          <div class="step-tip-box" style="width: 100%">
            <div class="step-tip-title">发送二维码给企业员工</div>
            <div class="step-tip-desc">请将右边的二维码保存，发送给企业员工，使用微信扫码加入;</div>
          </div>
        </div>
        <div class="step-box">
          <img class="step-bg" src="@/assets/image/center/step2.png" alt="" />
          <img class="step-tip-img" src="@/assets/image/center/step2-img.png" alt="" />
          <div class="step-tip-box">
            <div class="step-tip-title">员工扫码填写信息</div>
            <div class="step-tip-desc">
              企业员工使用微信扫一扫进入信息填写页，填写个人信息并添加蜗牛企业微信即可完成加入申请;
            </div>
          </div>
        </div>
        <div class="step-box">
          <img class="step-bg" src="@/assets/image/center/step3.png" alt="" />
          <img class="step-tip-img" src="@/assets/image/center/step3-img.png" alt="" />
          <div class="step-tip-box">
            <div class="step-tip-title">审核员工信息</div>
            <div class="step-tip-desc">
              需在申请记录中完成员工账号审核，审核通过后，企业员工即可通过微信扫码登录蜗牛平台。
            </div>
          </div>
        </div>
      </div>
      <div class="right-box">
        <div class="flex-column right-box-content" id="wn-invite-qrcode-box">
          <!-- <img
            class="qrcode-bg-img"
            src="@/assets/image/center/addAccQrcode.png"
            alt=""
            @load="handleBgImgLoad"
          /> -->
          <div class="flex-column qrcode-bg" v-loading="qrcodeLoading">
            <div class="title-box">用微信扫描二维码,加入</div>
            <div class="name-box">
              {{ store.userInfo.businessVO?.name }}({{ store.userInfo.businessVO?.memberCode }})
            </div>
            <div class="flex-column qrcode-img-box">
              <img class="qrcode-img" v-if="qrCodeDataUrl" :src="qrCodeDataUrl" alt="" />
            </div>
            </div>
        </div>
        <div class="flex-center btn-box">
          <el-button type="primary" round v-if="qrCodeDataUrl" @click="saveQrCode">保存二维码</el-button>
          <el-button type="primary" round v-else @click="getCode" :loading="qrcodeLoading">
            刷新二维码
          </el-button>
        </div>
      </div>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import QRCode from 'qrcode'
import html2canvas from 'html2canvas'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
// import { wechatQrcode } from '@/api/wechat'
import { useViewer } from '@/hooks/useViewer'
import { stringLength } from '@/utils/public'

const store = useUserStore()

const { showViewer } = useViewer()

const DialogRef = ref()

const imgs = [
  'https://pstatic.woniu.video/static/approve_2.jpg',
  'https://pstatic.woniu.video/static/approve_3.jpg',
]

const marginTop = computed(() => {
  if (store.userInfo.businessVO?.name) {
    return stringLength(`${store.userInfo.businessVO.name}(${store.userInfo.businessVO.memberCode})`) > 23
  }
  return false
})

const qrCodeDataUrl = ref('')
const qrcodeLoading = ref(false)
const saveLoading = ref(false)

defineExpose({
  open,
  close,
})

function open() {
  getCode()
  DialogRef.value.open()
}
function close() {
  DialogRef.value.close()
}

function openViewer(index: number) {
  showViewer(imgs, { index, scale: 'fullSize' })
}

function handleClose() {
  qrCodeDataUrl.value = ''
}

function handleBgImgLoad() {
  console.log('img load')
}

async function getCode() {
  qrcodeLoading.value = true
  let vipcode = store.userInfo.businessVO?.memberCode
  if (vipcode) {
    const http = encodeURIComponent(import.meta.env.VITE_APP_INVITE_API + '/wechat/invite')
    const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx552e48004afb1d3e&redirect_uri=${http}&response_type=code&scope=snsapi_userinfo&state=SubAccountVer${vipcode}`
    try {
      const dataUrl = await QRCode.toDataURL(url, {
        errorCorrectionLevel: 'H',
        width: '100%',
      })
      qrCodeDataUrl.value = dataUrl
      qrcodeLoading.value = false
    } catch (error: any) {
      console.error('Failed to generate QR Code', error)
      qrcodeLoading.value = false
    }
  }
}
// 保存二维码截图
async function saveQrCode() {
  saveLoading.value = true
  try {
    const dom = document.getElementById('wn-invite-qrcode-box') as HTMLElement
    if (!dom) {
      saveLoading.value = false
      ElMessage.error('保存出错了！')
      console.error('Element null')
      return
    }
    const canvas = await html2canvas(dom, {
      useCORS: true,
      scale: 2,
      allowTaint: true,
      backgroundColor: '#fff',
      // onclone: cb => {
      //   const d = cb.getElementById('wn-invite-qrcode-box')
      //   if(d) {
      //     d.style.display = 'flex'
      //   }
      // }
    })
    const img = canvas.toDataURL('image/png')
    const a = document.createElement('a')
    a.href = img
    a.download = (store.userInfo.businessVO?.name || '') + '邀请码.png'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    saveLoading.value = false
  } catch (error) {
    saveLoading.value = false
    console.error('Error capturing element:', error)
    ElMessage.error('保存出错了！')
  }
}
</script>

<style scoped lang="scss">
.addAcc-dialog-title {
  font-size: 20px;
  font-weight: 500;
  color: var(--text-color);
  // background: var(--bg);
  padding: 14px 20px 0;
  border-radius: 12px 12px 0 0;
}
.content-box {
  gap: 16px;
  width: 594px;

  .step-box {
    position: relative;
    width: 100%;

    .step-bg {
      width: 100%;
      display: block;
    }
    .step-tip-img {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;
      width: 270px;
    }
    .step-tip-box {
      position: absolute;
      top: 60px;
      left: 0;
      width: 320px;
      padding: 0 35px 0 20px;
      box-sizing: border-box;

      .step-tip-title {
        font-weight: 400;
        font-size: 20px;
        color: #333;
        line-height: 30px;
      }
      .step-tip-desc {
        font-weight: 400;
        font-size: 14px;
        color: #777;
        line-height: 21px;
      }
    }
  }
}
.right-box {
  background: rgba(246, 250, 253, 1);
  width: 408px;
  height: 636px;
  border-radius: 14px;
  padding: 30px 16px;
  box-sizing: border-box;
  flex-shrink: 0;
  background: linear-gradient(180deg, #f4f4f9 0%, #eef7ff 100%);

  #wn-invite-qrcode-box {
    width: 375px;
    min-height: 481px;
    // background: linear-gradient(0deg, #f4f4f9 0%, #eef7ff 100%);
    background-image: url('@/assets/image/center/addAccQrcode.png');
    background-size: 100% 481px;
    background-repeat: no-repeat;
    background-color: #eef7ff;
    color: var(--text-color);
    font-weight: 400;
    font-family: 'PingFang SC';
    position: relative;
  }

  .qrcode-bg-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .qrcode-bg {
    // position: absolute;
    // top: 0;
    // left: 21px;
    // z-index: 2;
    width: 332px;
    // background: linear-gradient(174deg, #d4e4ff 0%, #ffffff 41%);
    padding: 42px 0 38px;
    border-radius: 14px;
    margin-top: 94px;
  }
  .title-box {
    font-size: 20px;
    line-height: 30px;
  }
  .name-box {
    font-size: 22px;
    line-height: 33px;
    text-align: center;
    max-width: 90%;
    word-break: break-all;
  }
  .btn-box {
    margin-top: 10px;

    .el-button {
      width: 212px;
      height: 42px;
      border-color: transparent;
      background: linear-gradient(90deg, #4db8ff 0%, #8eb4ff 41%);
    }
  }
  .qrcode-img-box {
    margin: 30px 0 0;
    width: 100%;
    padding-bottom: 30px;
    background-color: #fff;
    border-radius: 12px;

    .qrcode-img {
      width: 166px;
      height: 166px;
  
      &.margin-top-15 {
        margin-top: 15px;
      }
    }
  }
}
</style>
