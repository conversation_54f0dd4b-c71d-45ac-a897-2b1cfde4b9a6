<template>
  <div>
    <el-table
      :data="tableData"
      class="custom-table-border radius-box"
      style="width: 100%"
      height="500"
      header-row-class-name="table-primary-header-row"
      :row-class-name="tableRowClassName"
      v-loading="tableLoading"
    >
      <el-table-column type="index" label="序号" align="center" width="55" />
      <el-table-column label="员工姓名" prop="name" align="center" min-width="190"></el-table-column>
      <el-table-column label="绑定微信" prop="nickName" align="center" min-width="190"></el-table-column>
      <el-table-column label="申请时间" prop="createTime" align="center" width="190"></el-table-column>
      <el-table-column label="审核时间" prop="auditTime" align="center" width="190"></el-table-column>
      <el-table-column label="状态" prop="auditStatus" align="center" width="140">
        <template v-slot="{ row }">
          {{ auditStatus(row.auditStatus) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" align="center" width="170">
        <template v-slot="{ row }">
          <div v-if="store.isVip() && row.auditStatus === 0">
            <el-button link type="primary" @click="passTheAudit(row.id, 'pass')">通过</el-button>
            <el-button link type="danger" @click="passTheAudit(row.id, 'reject')">拒绝</el-button>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <Empty image-type="order" description="暂无申请记录" :image-size="120" />
      </template>
    </el-table>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { auditStatus } from '@/utils/dictType'
import { getAppltyList, accountAudit } from '@/api/center'
import { useUserStore } from '@/stores/modules/user'

const store = useUserStore()

const tableLoading = ref(false)

const tableData = ref([])

const emits = defineEmits(['updateNumber'])
defineExpose({ getAccountAppltyList })

//获取列表
function getAccountAppltyList() {
  tableLoading.value = true
  getAppltyList()
    .then(res => {
      if (res.code === 200) {
        tableData.value = res.data.rows
      }
    })
    .finally(() => {
      tableLoading.value = false
    })
}

// 行颜色
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 !== 0) {
    return 'primary-row'
  }
  return ''
}

//通过
function passTheAudit(id: string, type: string) {
  ElMessageBox.confirm(`确认${type == 'pass' ? '通过申请' : '拒绝申请'}吗？`, '提示', {})
    .then(() => {
      const data = {
        auditStatus: type == 'pass' ? 1 : 2,
        id,
      }
      accountAudit(data).then(() => {
        getAccountAppltyList()
        emits('updateNumber')
        ElMessage.success(`${type == 'pass' ? '申请通过！' : '已拒绝！'}`)
      })
    })
    .catch(() => {})
}
</script>

<style scoped lang="scss"></style>
