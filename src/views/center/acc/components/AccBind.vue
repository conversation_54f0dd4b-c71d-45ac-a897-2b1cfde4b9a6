<template>
  <PublicDialog
    ref="DialogRef"
    width="480px"
    title="绑定"
    :showFooterButton="true"
    custom-close
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="content-box">
      <div class="tips" v-if="!showQrcode">
        <div>填写账号信息后绑定微信即可完成绑定</div>
        <div>绑定后即可查看该子账号中已有订单数据，并可共享套餐额度、查询记录</div>
      </div>
      <el-form
        v-show="!showQrcode"
        class="form-box"
        ref="formRef"
        :model="form"
        label-width="88px"
        status-icon
        :rules="rules"
        @submit.prevent
      >
        <el-form-item prop="name" label="员工姓名">
          <div class="input-box">
            <el-input placeholder="请输入员工姓名" v-model="form.name" clearable></el-input>
          </div>
        </el-form-item>
        <el-form-item prop="phone" label="手机号">
          <div class="input-box">
            <el-input
              placeholder="请输入手机号"
              v-model.trim="form.phone"
              :disabled="loading"
              maxlength="11"
              @input="checkPhoneSuccess = false"
            ></el-input>
          </div>
        </el-form-item>
      </el-form>
      <div v-if="showQrcode" class="gap-10">
        <div style="text-align: center; padding: 0 55px">
          请先使用微信扫码验证账号状态，未注册用户将直接进行注册流程，已注册用户可填写信息后直接绑定
        </div>
        <QrCode ref="QrCodeRef" :key="qrcodekey" :code-type="5" size="large" @success="checkSuccess" />
        <!-- :http-code="handleBindRequest" -->
        <!-- :load="load" -->
        <!-- <div class="tips">
          <h4>绑定微信后即可完成绑定</h4>
          <div>绑定后即可查看该子账号中已有订单数据，并可共享套餐额度、查询记录</div>
        </div> -->
        <div style="text-align: center; color: #8c8c8c; font-size: 12px">二维码有效期为10分钟</div>
      </div>
    </div>
    <template #footerButton>
      <div class="flex-center footer-btn-box" v-if="!showQrcode">
        <el-button round @click="close">取消</el-button>
        <el-button v-if="showQrcode" round type="primary" :disabled="bindBtn" @click="handleBind">
          完成绑定
        </el-button>
        <el-button v-else round type="primary" :loading="loading" @click="handleConfirm">确认</el-button>
      </div>
      <div v-else></div>
    </template>
  </PublicDialog>
</template>

<script setup lang="ts">
import QrCode from '@/components/public/qrcode/QrCode.vue'
import { ref } from 'vue'
import { getLoginUserByTicket } from '@/api/wechat'
import { ElLoading, ElMessage } from 'element-plus'
import { phone_reg } from '@/utils/RegExp'
import { checkPhoneStatus } from '@/api/center'
import { bindingLoginUser } from '@/api/user'

const DialogRef = ref()

const emit = defineEmits(['success'])

defineExpose({
  open,
  close,
})

const formRef = ref()
const accId = ref('')
const form = ref({
  name: '',
  phone: '',
})
const loading = ref(false)
const showQrcode = ref(true)
const bindBtn = ref(true)
const qrCodeUrl = ref('')
const qrCodeTicket = ref('')
const qrcodekey = ref(0)

const rules = {
  name: [
    { required: true, message: '请输入员工姓名', trigger: 'blur' },
    { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' },
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: checkPhone, trigger: 'change' },
  ],
}
let checkPhoneSuccess = false
function checkPhone(rule: any, value: any, callback: any) {
  if (!phone_reg.test(value)) {
    return callback(new Error('请输入正确的手机号'))
  }
  if (checkPhoneSuccess) return callback()
  loading.value = true
  checkPhoneStatus({ phone: value, ticket: qrCodeTicket.value })
    .then((res: any) => {
      if (res.data.phoneStatusEnum === 'NEW_ACCOUNT') {
        // qrCodeTicket.value = res.data.ticket
        // qrCodeUrl.value = res.data.qrcode
        checkPhoneSuccess = true
        callback()
      } else {
        // qrCodeTicket.value = ''
        // qrCodeUrl.value = ''
        callback(new Error('此手机号已绑定其它账号，如需绑定请解绑后重新绑定'))
      }
    })
    .catch(() => {
      // qrCodeTicket.value = ''
      // qrCodeUrl.value = ''
      callback(new Error('网络错误！请稍后再试'))
    })
    .finally(() => (loading.value = false))
}
function open(id: any) {
  accId.value = id
  DialogRef.value.open()
}
function close() {
  DialogRef.value.close()
}
function handleClose() {
  accId.value = ''
  form.value.name = ''
  form.value.phone = ''
  showQrcode.value = true
  qrCodeTicket.value = ''
  qrCodeUrl.value = ''
  bindBtn.value = true
}

function load() {
  checkPhoneStatus({ phone: form.value.phone }).then((res: any) => {
    if (res.data.phoneStatusEnum === 'NEW_ACCOUNT') {
      qrCodeTicket.value = res.data.ticket
      qrCodeUrl.value = res.data.qrcode
      qrcodekey.value++
    }
  })
}
function handleConfirm() {
  if (loading.value) return
  if (form.value.name) {
    form.value.name = form.value.name.trim()
  }
  formRef.value.validate((valid: any) => {
    if (valid) {
      // bindBtn.value = true
      loading.value = false
      handleBind()

      // showQrcode.value = true
      qrcodekey.value++
    } else {
      loading.value = false
    }
  })
}

function handleBindRequest() {
  return new Promise((resolve, reject) => {
    if (qrCodeUrl.value && qrCodeTicket.value) {
      resolve({
        data: {
          qrcode: qrCodeUrl.value,
          ticket: qrCodeTicket.value,
        },
      })
    } else {
      reject('error')
    }
  })
}

function checkSuccess(data: any) {
  ElMessage.success('扫码成功！')
  qrCodeTicket.value = data.ticket
  getLoginUserByTicket({ ticket: data.ticket }).then((res: any) => {
    if (res.code === 200) {
      showQrcode.value = false
      bindBtn.value = false
      form.value.phone = res.data.phone || ''
      form.value.name = res.data.name || ''
    }
  })
}

function handleBind() {
  if (bindBtn.value) return
  const el_loading = ElLoading.service({
    lock: true,
    text: '绑定中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  bindingLoginUser({
    businessAccountId: accId.value,
    name: form.value.name ? form.value.name.trim() : '',
    phone: form.value.phone,
    ticket: qrCodeTicket.value,
  })
    .then(res => {
      emit('success')
      close()
      ElMessage.success('绑定成功！')
    })
    .finally(() => el_loading.close())
}
</script>

<style scoped lang="scss">
.content-box {
  .form-box {
    margin-bottom: 50px;

    .input-box {
      width: 300px;
    }
  }
  .tips {
    margin: 0 0 20px;
    font-size: 12px;
    color: var(--text-gray-color);

    div:first-child {
      color: var(--text-color);
      font-size: 15px;
    }
    h4 {
      color: var(--text-color);
      font-size: 18px;
      margin: 10px 0;
    }
  }
}
.footer-btn-box {
  .el-button {
    width: 100px;
  }
}
</style>
