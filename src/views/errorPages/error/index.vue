<template>
  <div class="page">
    <div class="box">
      <img src="@/assets/image/404.jpg" alt="" />
      <div class="hint" v-if="networkError">
        <div>网络异常</div>
        <div>Network Error！</div>
        <div>网络连接出现异常。可以点击下方的按钮，重新加载。</div>
        <el-button round plain @click="load(0)">重新加载</el-button>
      </div>
      <div class="hint" v-else>
        <div>访问异常</div>
        <div>Unknown Error！</div>
        <div>您所访问的页面出错了。可以点击下方的按钮，重新加载。</div>
        <el-button round plain @click="load(1)">重新加载</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { removeToken } from '@/utils/auth'
import { ref } from 'vue'
const router = useRouter()

const networkError = ref(false)

networkError.value = new URLSearchParams(window.location.search).get('err') === 'network'

function load(i: number) {
  if(i) {
    removeToken()
  }
  router.push('/')
}
</script>

<style lang="scss" scoped>
.page {
  display: flex;
  align-items: center;
  justify-content: center;

  .box {
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
    height: 450px;
    margin-top: 10vh;

    img {
      width: 450px;
    }

    .hint {
      div {
        margin-bottom: 8px;
      }
      & div:nth-child(1) {
        font-size: 30px;
      }
      & div:nth-child(2) {
        font-size: 26px;
      }
      & div:nth-child(3) {
        font-size: 14px;
        margin-bottom: 14px;
      }
    }
  }
}
</style>
