<template>
  <div class="page">
    <div class="box">
      <img src="@/assets/image/404.jpg" alt="" />
      <div class="hint">
        <div>404</div>
        <div>页面丢失！！！</div>
        <div>您所寻找的页面不存在。可以点击下方的按钮，返回主页。</div>
        <el-button round plain @click="router.push('/')">返回首页</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
const router = useRouter()
</script>

<style lang="scss" scoped>
.page {
  display: flex;
  align-items: center;
  justify-content: center;

  .box {
    display: flex;
    justify-content: center;
    align-items: center;
    align-content: center;
    margin-top: 10vh;

    img {
      width: 450px;
    }

    .hint {
      div {
        margin-bottom: 8px;
      }
      & div:nth-child(1) {
        font-size: 80px;
      }
      & div:nth-child(2) {
        font-size: 30px;
      }
      & div:nth-child(3) {
        font-size: 14px;
        margin-bottom: 14px;
      }
    }
  }
}
</style>
