<template>
  <div class="vip-page" ref="VipPageRef">
    <img src="@/assets/image/vip_banner1.png" alt="" class="bg-img" />

    <PageHeader />

    <div class="vip-content main-width" ref="VipContentRef">
      <div class="flex-column" v-if="winWidth768">
        <h1>买家秀视频</h1>
        <h1>行业市占率第一</h1>
        <h4 style="margin-top: 20px">加入蜗牛会员</h4>
        <h4>享29.9美金一口价视频拍摄服务</h4>
      </div>
      <template v-else>
        <h1>买家秀视频 行业市占率第一</h1>
        <h4>加入蜗牛会员，享29.9美金一口价视频拍摄服务</h4>
      </template>

      <div class="flex-between set-meal-box" style="gap: 30px">
        <!--        <img src="@/assets/image/vip_hand_bg.png" alt="" class="img" />-->
        <img
          src="https://pstatic.woniu.video/static/assets/customer/vip/vip_hand_bg.png"
          alt=""
          class="img"
        />
        <template v-if="winWidth768">
          <VipSetMealPhoneBox
            v-for="(item, index) in setMealList"
            :key="index"
            :data="item"
            @action="toBuy(item.type)"
            @countDownEnd="getVipActivity"
          />
        </template>
        <template v-else>
          <VipSetMealBox
            v-for="(item, index) in setMealList"
            :key="index"
            :data="item"
            @action="toBuy(item.type)"
            @countDownEnd="getVipActivity"
          />
        </template>
      </div>

      <div class="flex-between validity-box">
        <div class="flex-start validity">
          <img v-if="tipsText" src="@/assets/image/center/VIP_icon_yxq.png" alt="" />
          <div v-html="tipsText"></div>
        </div>
        <div class="flex-start un-pay-number" v-if="store.userInfo.account">
          <div v-if="unPayNum">您有{{ unPayNum }}笔未支付订单，</div>
          <el-button v-if="unPayNum" class="btn" type="primary" style="color: #65adeb" link @click="toView">
            立即查看
          </el-button>
          <el-button
            type="primary"
            round
            icon="Document"
            class="records-btn"
            @click="routerNewWindow('center-tradingRecord')"
          >
            开通记录
          </el-button>
        </div>
      </div>

      <h3 class="icon-position-3">为什么要选择蜗牛</h3>

      <VipExplain />

      <h3 class="icon-position-1">会员权益</h3>

      <VipEquity />

      <div class="screen-advantage" ref="ScreenAdvantageRef">
        <h3 class="icon-position-2">平台优势</h3>

        <VipAdvantage class="advantage-box" />
      </div>

      <h3 class="icon-position-2">常见问题</h3>

      <VipProblem />
    </div>

    <VipFooter />

    <KeFu />

    <!-- <LoginDialog /> -->
    <!-- <WelcomeDialog v-if="store.userInfo.account" /> -->
    <!-- <Viewer /> -->
    <!-- <MobileDeviceDialog /> -->
  </div>
</template>

<script setup lang="ts">
import PageHeader from '@/components/layout/header/PageHeader.vue'
import VipIcon1 from '@/assets/image/vip_icon1.png'
import VipIcon2 from '@/assets/image/vip_icon2.png'
import RefundIcon1 from '@/assets/image/refund_icon1.png'
import RefundIcon2 from '@/assets/image/refund_icon2.png'
import VipSetMealBox from '@/views/vip/components/VipSetMealBox.vue'
import VipSetMealPhoneBox from '@/views/vip/components/VipSetMealPhoneBox.vue'
import VipExplain from '@/views/vip/components/VipExplain.vue'
import VipEquity from '@/views/vip/components/VipEquity.vue'
import VipAdvantage from '@/views/vip/components/VipAdvantage.vue'
import VipProblem from '@/views/vip/components/VipProblem.vue'
import VipFooter from '@/views/vip/components/VipFooter.vue'
import KeFu from '@/views/vip/components/KeFu.vue'

// import LoginDialog from '@/views/login/LoginDialog.vue'
// import WelcomeDialog from '@/components/public/dialog/WelcomeDialog.vue'
// import Viewer from '@/components/public/viewer/index.vue'
// import MobileDeviceDialog from '@/components/public/dialog/MobileDeviceDialog.vue'

import { vipSetMealList } from '@/views/center/data'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { computed, getCurrentInstance, onMounted, onUnmounted, ref } from 'vue'
import { ElLoading } from 'element-plus'
import { MessageBox } from '@/utils/message'
import { getMonthRange } from '@/utils/time'
import { openLogin } from '@/hooks/useLogin'
import {
  createVipOrder,
  getMemberConfig,
  getMemberUnPay,
  latestUnPayMemberOrder,
  memberActivityList,
} from '@/api/vip'
import { getToken } from '@/utils/auth'
import aegis from '@/utils/aegis'
import Aegis from 'aegis-web-sdk'
import { getValidPromotionActivityList } from '@/api/order'

import { useMobileDeviceAction } from '@/hooks/useMobileDeviceAction'
const { handleMDA } = useMobileDeviceAction()

import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { twoOrderInfo } = useShowRenewDialog()

import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

const { proxy } = getCurrentInstance() as any

const router = useRouter()
const store = useUserStore()

const winWidth768 = ref(false)

const setMealList = ref(
  vipSetMealList
    .filter(item => item.type != 0)
    .map(item => {
      return {
        ...item,
        vipIcon: item.type === 1 ? VipIcon2 : VipIcon1,
        refundIcon: item.type === 1 ? RefundIcon2 : RefundIcon1,
      }
    })
)

const unPayNum = ref(0)
const unPayOrderNums = ref<any[]>([])
const tipsText = computed(() => {
  if (store.isVip()) {
    if (store.userInfo.businessVO?.memberStatus === 2) {
      return `会员还有<span style="color: var(--text-color);">${handleMemberValidity(store.userInfo.businessVO?.memberValidity)}</span>天到期，请及时续费`
    }
    return '会员有效期至' + store.userInfo.businessVO?.memberValidity
  }
  if (store.userInfo.businessVO?.memberStatus === 3) {
    return `会员已于${store.userInfo.businessVO?.memberValidity}到期`
  }
  return ''
  // return '为您的企业选择最佳会员方案'
})

function handleMemberValidity(time: string) {
  return Math.ceil((new Date(time).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
}

function toPage(path?: string) {
  if (path) {
    if (path === '/') {
      window.location.href = proxy.$officialWebsiteUrl
    } else {
      router.push(path)
    }
  }
}
function toView() {
  if (unPayNum.value === 1) {
    // 只有一条未支付订单-去支付
    store.realTimeCheckVip().then(() => {
      // if (store.isOwnerAcc()) {
      router.push({
        name: 'center-pay',
        state: {
          orderNum: unPayOrderNums.value[0].orderNum,
          type: unPayOrderNums.value[0].packageType,
          payType: unPayOrderNums.value[0].payType,
        },
      })
      // }
    })
  } else if (unPayNum.value > 1) {
    // 多条未支付订单-开通记录列表
    routerNewWindow('center-tradingRecord')
  }
}
function routerNewWindow(name: string) {
  const { href } = router.resolve({ name })
  window.open(href, '_blank')
}

let el_loading: any

async function toBuy(type: number) {
  try {
    aegis?.report({
      msg: '登录用户点击购买会员',
      level: Aegis.logType.REPORT,
      ext1: type.toString(),
      trace: 'trace',
    })
  } catch (error) {
    console.log('日志上报失败')
  }

  // 移动端设备处理
  let mda = await handleMDA()
  if (mda) {
    return
  }

  if (!store.userInfo.account) {
    openLogin()
    return
  }

  el_loading = ElLoading.service({
    lock: true,
    text: '正在加载中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  await getMemberOrderNum()
  if (unPayNum.value > 0) {
    // 存在未支付订单
    el_loading.close()
    MessageBox(`您有待支付会员订单<br/><span>您可以选择继续去支付订单，或者取消未支付订单重新下单</span>`, {
      confirmButtonText: '去支付',
      cancelButtonText: '重新下单',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      showClose: true,
      distinguishCancelAndClose: true,
    })
      .then(() => {
        el_loading = ElLoading.service({
          lock: true,
          text: '正在打开中',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        store
          .realTimeCheckVip()
          .then(() => {
            // if (store.isOwnerAcc()) {
            router.push({
              name: 'center-pay',
              state: {
                orderNum: unPayOrderNums.value[0].orderNum,
                type: unPayOrderNums.value[0].packageType,
                payType: unPayOrderNums.value[0].payType,
              },
            })
            el_loading.close()
            // }
          })
          .catch(() => el_loading.close())
      })
      .catch(action => {
        if (action === 'cancel') {
          el_loading = ElLoading.service({
            lock: true,
            text: '正在下单中',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          latestUnPayMemberOrder()
            .then(() => {
              createVipOrderApi(type)
            })
            .catch(() => el_loading.close())
        }
      })
      .finally(() => el_loading.close())
  } else {
    createVipOrderApi(type)
  }
}

function createVipOrderApi(type: number) {
  createVipOrder({
    packageType: type,
  })
    .then(res => {
      if (res.data?.orderNum) {
        router.push({ name: 'center-pay', state: { orderNum: res.data.orderNum, type } })
      }
    })
    .finally(() => el_loading.close())
}

async function getMemberOrderNum() {
  return getMemberUnPay()
    .then(res => {
      if (res.data) {
        unPayNum.value = res.data.unPayNum
        unPayOrderNums.value = res.data.unPayOrderList
      }
    })
    .finally(() => {})
}

function getVipActivity() {
  setMealList.value.forEach(item => {
    delete item.activityMonth
    delete item.activityPresentedTime
  })
  memberActivityList().then(res => {
    if (res.code === 200 && res.data) {
      res.data?.forEach((item: any) => {
        if (item.status === 1) {
          let { startTime, endTime } = getMonthRange(item.startTime, item.endTime)
          let now = new Date().getTime()
          if (startTime > now || endTime < now) return

          let typeStr = ''
          if (item.presentedTimeType == 1) {
            typeStr = '天'
          } else if (item.presentedTimeType == 2) {
            typeStr = '个月'
          } else if (item.presentedTimeType == 3) {
            typeStr = '年'
          }
          if (item.memberPackageType == 1) {
            setMealList.value[0].activityPresentedTime = item.presentedTime + typeStr
            setMealList.value[0].activityTime = endTime
          } else if (item.memberPackageType == 2) {
            setMealList.value[1].activityPresentedTime = item.presentedTime + typeStr
            setMealList.value[1].activityTime = endTime
          }
        }
      })
    }
  })
  // getMemberConfig().then(res => {
  //   if (res.data?.status == 1) {
  //     let startTime = new Date(res.data.presentedStartDate).getTime()
  //     let endTime = new Date(res.data.presentedEndDate).getTime()
  //     let now = new Date().getTime()
  //     if (startTime > now || endTime < now) return
  //     setMealList.value[0].activityMonth = res.data.presentedTime
  //     setMealList.value[0].activityTime = res.data.presentedEndDate
  //   }
  // })
}

function getInfo() {
  getValidPromotionActivityList().then(res => {
    if (res.data && res.data.length > 0) {
      twoOrderInfo.value = res.data.find((item: any) => item.type === 4) || {}
    }
  })
}

function init() {
  getVipActivity()
  getInfo()
  if (store.userInfo.account || getToken()) {
    store.getUserInfo().then(() => {
      if (store.isOwnerAcc() || store.isVip()) {
        getMemberOrderNum()
      }
    })
  }
}
init()

const VipPageRef = ref()
const VipContentRef = ref()
const ScreenAdvantageRef = ref()

let timer: any
let timerResize: any
const adScrollTrigger = ref()

function handleResize() {
  if (timerResize) clearTimeout(timerResize)
  timerResize = setTimeout(() => {
    if (adScrollTrigger.value) {
      adScrollTrigger.value.refresh()
    }
  }, 200)
}

function createScrollTrigger() {
  // 平台优势
  adScrollTrigger.value = ScrollTrigger.create({
    trigger: ScreenAdvantageRef.value,
    start: 'top 64px',
    end: '+=6900 bottom',
    markers: false,
    scrub: true,
    pin: true,
    animation: gsap.timeline().fromTo('.advantage-box', { x: 0, opacity: 1 }, { x: -3450, opacity: 1 }),
  })
  timer = setTimeout(() => {
    if (adScrollTrigger.value) {
      adScrollTrigger.value.refresh()
    }
  }, 500)

  window.addEventListener('resize', handleResize)
}

onMounted(() => {
  gsap.registerPlugin(ScrollTrigger)

  setTimeout(() => {
    if (window.innerWidth > 768) {
      createScrollTrigger()
      window.addEventListener('resize', handleResize)
    } else {
      winWidth768.value = true
    }
  })
})
onUnmounted(() => {
  if (timerResize) clearTimeout(timerResize)
  if (timer) clearTimeout(timer)
  if (adScrollTrigger.value) adScrollTrigger.value.kill()
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.vip-page {
  position: relative;
  top: 0;
  // top: 64px;
  padding-top: 64px;
  width: 100%;
  // box-sizing: border-box;
  background: linear-gradient(153deg, #fffefd 0%, #f9fafa 30%, #f0f3f6 100%);
  // height: 100vh;
  // height: calc(100vh - 64px);
  overflow-x: hidden;
  overflow-y: hidden;

  @include mediaTo('phone') {
    background: #fff;
  }

  .screen-advantage {
    height: 700px;

    @include mediaTo('phone') {
      height: auto;
    }
  }

  .bg-img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    min-width: 1100px;
  }

  .vip-header-box {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
    width: 100%;
    min-width: 800px;
    overflow-x: auto;
    font-family:
      PingFangSC,
      PingFang SC;

    .vip-header {
      width: 100%;
      // max-width: 1400px;
      min-width: 800px;
      height: 64px;
      box-sizing: border-box;
      padding: 0 20px;
      background-color: #fff;

      .logo {
        width: 90px;
        margin-right: 30px;
        margin-left: 15px;
        cursor: pointer;
      }

      .head-menu {
        gap: 15px;
        align-content: baseline;

        .item {
          cursor: pointer;
          width: 70px;
          height: 26px;
          text-align: center;
          color: #1e2937;
          font-size: 15px;
          padding: 1px 0;
          // margin-right: 20px;

          img {
            width: 18px;
            padding: 0 3px;
            transform: translate(-2px, 1px);
            -moz-transform: translate(-2px, 0px);
          }

          &:hover {
            color: var(--el-color-primary);
          }

          &.active {
            width: 80px;
            cursor: auto;
            background-color: #14110e;
            border-radius: 25px;

            span {
              background: linear-gradient(180deg, #fff9f3 0%, #ffbb88 100%);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              font-weight: 500;
            }
          }
        }
      }
    }
  }

  .vip-content {
    min-height: calc(100vh - 64px);
    width: 1100px;
    box-sizing: border-box;
    position: relative;
    z-index: 1;
    font-family:
      PingFangSC,
      PingFang SC;

    @include mediaTo('phone') {
      width: 100%;
    }

    h1 {
      margin-top: 150px;
      margin-bottom: 16px;
      width: 750px;
      height: 78px;
      font-weight: 600;
      font-size: 56px;
      color: #ffffff;
      line-height: 78px;
      text-shadow: 0px 6px 10px rgb(156 174 185 / 30%);
      text-align: left;
      font-style: normal;
      background: linear-gradient(180deg, #1e2937 0%, #2a3a50 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      @include mediaTo('phone') {
        width: 100%;
        margin-top: 0;
        margin-bottom: 0;
        font-size: 32px;
        height: 47px;
        line-height: 47px;
        text-align: center;

        &:nth-child(1) {
          margin-top: 39px;
        }
      }
    }

    h3 {
      width: fit-content;
      text-align: center;
      font-weight: 600;
      font-size: 32px;
      color: #1e2937;
      margin: 0 auto 40px;
      padding-top: 60px;
      position: relative;

      &::before {
        content: '';
        display: inline-block;
        width: 80px;
        height: 8px;
        background: linear-gradient(270deg, #fff0 0%, #e1efff 46%, #a7c3e3 84%);
        border-radius: 4px;
        position: absolute;
        bottom: 9px;
        z-index: -1;
      }

      &.icon-position-1 {
        &::before {
          left: 3px;
        }
      }
      &.icon-position-2 {
        &::before {
          right: -18px;
        }
      }
      &.icon-position-3 {
        &::before {
          right: -15px;
        }
      }

      @include mediaTo('phone') {
        margin-bottom: 30px;

        &::before {
          display: none;
        }
      }
    }

    h4 {
      margin: 0;
      width: 550px;
      height: 33px;
      font-weight: 500;
      font-size: 24px;
      color: #7992af;
      line-height: 33px;
      text-align: left;
      font-style: normal;
      background: linear-gradient(90deg, #6489b5 0%, #6282a9 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;

      @include mediaTo('phone') {
        width: 100%;
        font-size: 20px;
        height: 30px;
        line-height: 30px;
        text-align: center;

        &:nth-child(1) {
          margin-top: 20px;
        }
      }
    }

    .set-meal-box {
      gap: 30px;
      margin: 100px 0 0;
      position: relative;

      @include mediaTo('phone') {
        display: block;
        margin-top: -40px;
      }

      .img {
        position: absolute;
        top: 0;
        right: 0;
        width: 750px;
        transform: translate(140px, -390px);

        @include mediaTo('phone') {
          position: relative;
          top: 55px;
          width: 100%;
          transform: translate(-7%, 0);
        }
      }
    }

    .validity-box {
      margin-top: 35px;
      font-size: 16px;
      color: #171e1a;

      @include mediaTo('phone') {
        display: none;
      }

      .validity {
        gap: 10px;
        line-height: 26px;

        img {
          width: 26px;
          height: 26px;
        }
      }

      .un-pay-number {
        align-items: baseline;

        .el-button.btn {
          font-size: 16px;
          vertical-align: baseline;
        }
        .records-btn {
          width: 120px;
          --el-button-bg-color: #65adeb;
          --el-button-border-color: #65adeb;
        }
      }
    }
  }

  .main-width {
    margin: 0 auto;
  }
}
@media screen and (max-width: 1100px) {
  .vip-page {
    .main-width {
      margin: 0;
    }
  }
}
</style>
