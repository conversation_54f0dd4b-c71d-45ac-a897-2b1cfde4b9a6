<template>
  <div
    class="flex-column vip-setMeal-box"
    :style="{ '--bgColor': data.bgColor, '--textColor': data.textColor }"
  >
    <div class="activity-box" v-if="data.activityPresentedTime">
      <div>活动限时加赠{{ data.activityPresentedTime }}</div>
      <div>
        <span>仅剩:&nbsp;</span>
        <CountDown :endTime="data.activityTime" showDays h="时" m="分" s="秒" @end="$emit('countDownEnd')" />
      </div>
    </div>
    <div class="refund-icon">
      <el-popover
        placement="top"
        popper-style="box-shadow: 0px 6px 10px 0px rgba(223,228,237,0.5);border-radius: 8px;border: none;padding: 20px"
        title=""
        :width="310"
        trigger="hover"
      >
        <template #reference>
          <!-- <img :src="data.refundIcon" alt="" /> -->
          <div class="refund-icon-tip">
            <div :class="[`bg-${data.type}`]">七天无理由</div>
          </div>
        </template>
        <!-- <div class="popover-tip-1" v-if="data.type === 1">89%的新用户选择此会员</div>
        <div class="popover-tip-2" v-if="data.type === 1" style="margin-bottom: 18px">
          建议新用户可以优先选择"年度会员"尝试。
        </div> -->
        <div class="popover-tip-1">支持七天无理由退会员费</div>
        <div class="popover-tip-2">
          购买会员后若不满意，7天内可申请退还会员费，无需任何理由，当天申请当天退款。
        </div>
      </el-popover>
    </div>
    <img :src="data.vipIcon" alt="" class="icon" />
    <div class="flex-center title">
      <span>{{ data.label }}</span>
      <img v-if="data.type == 1" src="@/assets/image/recommend_icon.png" alt="" />
    </div>
    <template v-if="data.type === 2 || !twoOrderInfo.type || twoOrderInfo.type != 4">
      <!-- <div class="money">
        <span class="dicount-large" v-if="isFiveRenew && memberRsidueTime < 30">{{ data.$ }}</span>
        <span class="large">{{ isFiveRenew && memberRsidueTime < 30 ? data.$ / 2 : data.$ }}</span>
        &nbsp;USD
      </div> -->
      <!-- <span class="money">
        <span class="dicount-large" v-if="isFiveRenew && memberRsidueTime < 30">{{ data.$ }}</span>
        <span class="large">折合{{ isFiveRenew && memberRsidueTime < 30 ? data.$ / 2 : data.$ }}</span>
        &nbsp;USD
      </span> -->
      <div class="money">
        <template v-if="!isFiveRenew">折合</template>
        <span
          class="dicount-large"
          v-if="(isFiveRenew && memberRsidueTime < 30 && !twoOrderInfo.type) || twoOrderInfo.type != 4"
        >
          {{ data.$ }}
        </span>
        <span
          class="dicount-large"
          v-if="isFiveRenew && memberRsidueTime < 30 && twoOrderInfo.type && twoOrderInfo.type == 4"
        >
          ${{ handlePrice(data.$, data.month, false) }}
        </span>
        <span class="small">${{ handlePrice(data.$, data.month, isFiveRenew) }}</span>
        /月
      </div>
      <div class="money">
        <img
          class="img"
          v-if="data.type === 1 && !isFiveRenew"
          src="@/assets/image/vip_tip_icon.png"
          alt=""
        />
        会员期内,每月首单
        <span class="discount">{{ isFiveRenew ? '还减' : '立减' }}$19.9</span>
      </div>
      <!-- <div class="money">
        折合每月
        <span class="discount">${{ handlePrice(data.$, data.month) }}</span>
        USD
      </div> -->
    </template>
    <template v-else>
      <div class="money">
        <template v-if="!isFiveRenew">折合</template>
        <span
          class="dicount-large"
          v-if="(isFiveRenew && memberRsidueTime < 30 && !twoOrderInfo.type) || twoOrderInfo.type != 4"
        >
          {{ data.$ }}
        </span>
        <span
          class="dicount-large"
          v-if="isFiveRenew && memberRsidueTime < 30 && twoOrderInfo.type && twoOrderInfo.type == 4"
        >
          ${{ (data.$ / 12).toFixed(1) }}
        </span>
        <span class="small">${{ handlePrice(data.$, data.month, isFiveRenew) }}</span>
        /月
      </div>
      <!-- <div class="money" v-if="isFiveRenew">
        折合每月
        <span class="discount">${{ handlePrice(data.$, data.month) }}</span>
        USD
      </div> -->
      <div class="money">
        <img
          class="img"
          v-if="data.type === 1 && !isFiveRenew"
          src="@/assets/image/vip_tip_icon.png"
          alt=""
        />
        会员期内,每月首单
        <span class="discount">{{ isFiveRenew ? '还减' : '立减' }}$19.9</span>
      </div>
    </template>
    <el-button class="buy-btn" :class="[`btn-${data.type}`]" type="primary" v-if="purchased" @click="toBuy">
      {{ isFiveRenew ? '半价续费' : '立即续费' }}
    </el-button>
    <el-button class="buy-btn" :class="[`btn-${data.type}`]" type="primary" v-else @click="toBuy">
      立即开通
    </el-button>
    <ul class="tip">
      <li>可使用人民币支付</li>
      <el-popover
        placement="bottom"
        popper-style="box-shadow: 0px 6px 10px 0px rgba(223,228,237,0.5);border-radius: 8px;border: none;padding: 20px"
        title=""
        :width="310"
        trigger="hover"
      >
        <template #reference>
          <li>支持七天无理由退会员费</li>
        </template>
        <div class="popover-tip-1">支持七天无理由退会员费</div>
        <div class="popover-tip-2">
          购买会员后若不满意，7天内可申请退还会员费，无需任何理由，当天申请当天退款。
        </div>
      </el-popover>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import CountDown from '@/components/public/CountDown.vue'
import { useUserStore } from '@/stores/modules/user'
import { openLogin } from '@/hooks/useLogin'
import { endTimeToDay } from '@/utils/time'
import { computed } from 'vue'
import { ElMessageBox } from 'element-plus'
import aegis from '@/utils/aegis'
import Aegis from 'aegis-web-sdk'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { isFiveRenew, oneOrderInfo, twoOrderInfo } = useShowRenewDialog()

const store = useUserStore()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['action', 'countDownEnd'])

const purchased = computed(() => store.isVip())

function handlePrice(price: number, month: number, isFiveRenew: boolean) {
  if (isFiveRenew) {
    return +(price / month / 2).toFixed(2)
  } else {
    return +(price / month).toFixed(2)
  }
}

const memberRsidueTime = computed(() => {
  let result = 0
  if (store.userInfo.businessVO?.memberValidity) {
    result = endTimeToDay(store.userInfo.businessVO.memberValidity)
  }
  return result
})

function toBuy() {
  if (!store.userInfo.account) {
    try {
      aegis?.report({
        msg: '未登录用户点击购买会员',
        level: Aegis.logType.REPORT,
        ext1: props.data.title,
        trace: 'trace',
      })
    } catch (error) {
      console.log('日志上报失败')
    }
    openLogin()
    return
  }
  if (store.isVip() || store.isOwnerAcc()) {
    emits('action')
    return
  }
  let div = ''
  if (!store.isVip()) {
    div = `<p>仅主账号可开通会员</p><p style="font-size: 14px;color: var(--text-gray-color)">如果要开通会员，请联系主账号进行开通~</p>`
  }
  // if (store.isVip()) {
  //   div = `<p>仅主账号可续费会员</p><p style="font-size: 14px;color: var(--text-gray-color)">如果会员快要到期，请联系主账号进行续费~</p>`
  // } else {
  //   div = `<p>仅主账号可开通会员</p><p style="font-size: 14px;color: var(--text-gray-color)">如果要开通会员，请联系主账号进行开通~</p>`
  // }
  ElMessageBox.alert(div, '温馨提醒', {
    confirmButtonText: '确定',
    type: 'warning',
    dangerouslyUseHTMLString: true,
  })
}
</script>

<style scoped lang="scss">
.vip-setMeal-box {
  --bgColor: linear-gradient(143deg, #fdfeff 0%, #ddeaf4 100%);
  --textColor: #252323;
  --vipColor: #f49351;
  width: 50%;
  padding: 75px 30px 20px;
  background: var(--bgColor);
  box-shadow: 0px 12px 18px 0px rgba(211, 219, 230, 0.5);
  border-radius: 16px;
  border: 1px solid #ffffff;
  position: relative;
  font-family:
    PingFangSC,
    PingFang SC;

  .activity-box {
    background: linear-gradient(96deg, #fff7eb 1%, #fad4c3 99%);
    border-radius: 16px 0px 32px 0px;
    font-weight: 400;
    font-size: 0.75rem;
    color: #252323;
    padding: 8px 20px;
    position: absolute;
    top: 0;
    left: 0;

    div:nth-child(1) {
      font-weight: 600;
      font-size: 1.01rem;
    }
  }

  .refund-icon {
    position: absolute;
    top: 0px;
    right: 0px;

    img {
      width: 28px;
    }

    .refund-icon-tip {
      position: absolute;
      top: 0;
      right: 0;
      width: 150px;
      height: 90px;
      overflow: hidden;

      div {
        position: relative;
        top: 12px;
        right: 12px;
        width: 200px;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
        padding: 10px;
        transform: rotateZ(30deg);

        &.bg-1 {
          background: linear-gradient(90deg, #fff2dd 0%, #ffcf8e 100%);
          color: #5e3d15;
        }
        &.bg-2 {
          background: linear-gradient(90deg, #dcebfd 0%, #b4cce9 100%);
          color: #254974;
        }
      }
    }
  }

  .icon {
    width: 180px;
  }

  .title {
    font-weight: 600;
    font-size: 2.1rem;
    color: var(--vipColor);
    margin-bottom: 13px;
    gap: 2px;

    img {
      width: 36px;
      height: 36px;
    }
  }

  .money {
    position: relative;
    font-weight: 400;
    font-size: 1rem;
    color: var(--textColor);
    .dicount-large {
      color: #7d7d7d;
      text-decoration: line-through;
      margin-right: 10px;
    }
    .large {
      font-size: 2.25rem;
    }
    .small {
      // font-size: 1.86rem;
      font-size: 2.25rem;
      color: var(--vipColor);
    }
    .discount {
      margin-left: 4px;
      font-size: 1.5rem;
      color: var(--vipColor);
    }
    .img {
      position: absolute;
      width: 117px;
      height: 27px;
      right: -85px;
      top: -20px;
    }
  }

  .buy-btn {
    width: 60%;
    color: var(--textColor);
    border-radius: 28px;
    border: 1px solid #fff;
    margin: 30px auto 20px;
    height: 56px;
    font-size: 1.3rem;

    &.btn-1 {
      background: linear-gradient(90deg, #fffdfa 0%, #fff3ea 97%);
      box-shadow: 0px 6px 10px 0px rgba(199, 166, 150, 0.5);
    }
    &.btn-2 {
      background: linear-gradient(90deg, #ffffff 0%, #eef7ff 100%, #eef7ff 100%);
      box-shadow: 0px 6px 10px 0px rgba(128, 145, 176, 0.5);
    }
  }

  .tip {
    font-weight: 400;
    font-size: 0.9rem;
    color: #25232380;
    margin: 0;
  }
}
.popover-tip-1 {
  font-weight: 600;
  font-size: 16px;
  color: #1e2937;
  font-family:
    PingFangSC,
    PingFang SC;
  margin-bottom: 5px;
}
.popover-tip-2 {
  font-weight: 400;
  font-size: 14px;
  color: #1e293780;

  font-family:
    PingFangSC,
    PingFang SC;
}
</style>
