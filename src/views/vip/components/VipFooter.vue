<template>
  <div class="flex-column vip-footer">
    <img :src="qrcodeUrl" alt="" />
    <div class="text">扫码添加，随时需要，随时咨询</div>
  </div>
</template>

<script setup lang="ts">
import QRCode from 'qrcode'
import { getCurrentInstance, onMounted, ref } from 'vue'
import { getChannelCode  } from '@/utils/auth'
import { getChannelQrcode,getVipQrcode } from '@/api/user'

const { proxy } = getCurrentInstance() as any

const qrcodeUrl = ref('')

const getKefuQrcodeUrl = async () => {
  const channelCode = getChannelCode()
  if (!channelCode) {
    const res: any = await getVipQrcode()
    qrcodeUrl.value = res.data.url || ''
    // qrcodeUrl.value = proxy.$connectMeUrl
    return
  }
  const res: any = await getChannelQrcode(channelCode)
  if (res?.code == 200 && res?.data) {
    if (res.data.type == 2) {
      qrcodeUrl.value = res.data.url
    } else if (res.data.type == 3) {
      try {
        const dataUrl = await QRCode.toDataURL(res.data.url as string, {
          errorCorrectionLevel: 'H',
          width: 200,
        })
        qrcodeUrl.value = dataUrl
      } catch (err) {
        console.error('Failed to generate QR Code', err)
      }
    }
  }
}

onMounted(() => {
  getKefuQrcodeUrl()
})
</script>

<style scoped lang="scss">
.vip-footer {
  width: 100%;
  height: 300px;
  margin-top: 60px;
  background: #252323;
  gap: 24px;
  justify-content: center;

  img {
    width: 150px;
    height: 150px;
  }
  .text {
    font-weight: 600;
    font-size: 24px;
    color: #ffffff;
    line-height: 33px;
    background: linear-gradient(180deg, #fff9f3 0%, #ffbb88 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>
