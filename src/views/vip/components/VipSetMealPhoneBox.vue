<template>
  <div class="vip-setMeal-phone-box">
    <div
      class="flex-column vip-setMeal-box"
      :style="{ '--bgColor': data.bgColor, '--textColor': data.textColor }"
    >
      <div class="flex-between w-100">
        <div class="flex-center title" :class="[`type-color-${data.type}`]">
          <span>{{ data.label }}</span>
          <img v-if="data.type == 1" src="@/assets/image/recommend_icon.png" alt="" />
        </div>
        <div class="money">
          <template v-if="!isFiveRenew">折合</template>
          <span
            class="dicount-large"
            :class="[`type-color-${data.type}`]"
            v-if="isFiveRenew && memberRsidueTime < 30 && twoOrderInfo.type && twoOrderInfo.type == 4"
          >
            ${{ handlePrice(data.$, data.month, false) }}
          </span>
          <span class="small" :class="[`type-color-${data.type}`]">
            ${{ handlePrice(data.$, data.month, isFiveRenew) }}
          </span>
          /月
        </div>
      </div>
      <div class="money w-100" style="margin-top: 10px">
        <div style="position: relative; width: fit-content;">
          <span>会员期内,每月首单</span>
          <span class="discount" :class="[`type-color-${data.type}`]">
            {{ isFiveRenew ? '还减' : '立减' }}$19.9
          </span>
          <img
            class="img"
            v-if="data.type === 1 && !isFiveRenew"
            src="@/assets/image/vip_tip_mobile_icon.png"
            alt=""
          />
        </div>
      </div>

      <ul class="tip w-100">
        <li>可使用人民币支付</li>
        <li>支持七天无理由退会员费</li>
      </ul>

      <el-button class="buy-btn" :class="[`btn-${data.type}`]" type="primary" v-if="purchased" @click="toBuy">
        {{ isFiveRenew ? '半价续费' : '立即续费' }}
      </el-button>
      <el-button class="buy-btn" :class="[`btn-${data.type}`]" type="primary" v-else @click="toBuy">
        立即开通
      </el-button>
    </div>

    <div class="activity-box" v-if="data.activityPresentedTime">
      <img src="@/assets/image/vip_tip_bottom_1.png" alt="" />
      <div class="flex-center gap-5">
        <div>
          <span>活动限时</span>
          <span style="color: #f49351">加赠{{ data.activityPresentedTime }}</span>
        </div>
        <div class="flex-start" style="align-items: baseline">
          <span>仅剩:&nbsp;</span>
          <CountDown
            :endTime="data.activityTime"
            showDays
            background
            style="color: #5e3d15; --countdown-bg-color: #ffd193"
            d=":"
            h=":"
            m=":"
            s=""
            @end="$emit('countDownEnd')"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import CountDown from '@/components/public/CountDown.vue'
import { useUserStore } from '@/stores/modules/user'
import { openLogin } from '@/hooks/useLogin'
import { endTimeToDay } from '@/utils/time'
import { computed } from 'vue'
import { ElMessageBox } from 'element-plus'
import aegis from '@/utils/aegis'
import Aegis from 'aegis-web-sdk'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { isFiveRenew, oneOrderInfo, twoOrderInfo } = useShowRenewDialog()

const store = useUserStore()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['action', 'countDownEnd'])

const purchased = computed(() => store.isVip())

function handlePrice(price: number, month: number, fiveRenew: boolean) {
  if (fiveRenew) {
    return +(price / month / 2).toFixed(2)
  } else {
    return +(price / month).toFixed(2)
  }
}

const memberRsidueTime = computed(() => {
  let result = 0
  if (store.userInfo.businessVO?.memberValidity) {
    result = endTimeToDay(store.userInfo.businessVO.memberValidity)
  }
  return result
})

function toBuy() {
  if (!store.userInfo.account) {
    try {
      aegis?.report({
        msg: '未登录用户点击购买会员',
        level: Aegis.logType.REPORT,
        ext1: props.data.title,
        trace: 'trace',
      })
    } catch (error) {
      console.log('日志上报失败')
    }
    openLogin()
    return
  }
  if (store.isVip() || store.isOwnerAcc()) {
    emits('action')
    return
  }
  let div = ''
  if (!store.isVip()) {
    div = `<p>仅主账号可开通会员</p><p style="font-size: 14px;color: var(--text-gray-color)">如果要开通会员，请联系主账号进行开通~</p>`
  }
  // if (store.isVip()) {
  //   div = `<p>仅主账号可续费会员</p><p style="font-size: 14px;color: var(--text-gray-color)">如果会员快要到期，请联系主账号进行续费~</p>`
  // } else {
  //   div = `<p>仅主账号可开通会员</p><p style="font-size: 14px;color: var(--text-gray-color)">如果要开通会员，请联系主账号进行开通~</p>`
  // }
  ElMessageBox.alert(div, '温馨提醒', {
    confirmButtonText: '确定',
    type: 'warning',
    dangerouslyUseHTMLString: true,
  })
}
</script>

<style scoped lang="scss">
.vip-setMeal-phone-box {
  width: 100vw;

  & + .vip-setMeal-phone-box {
    margin-top: 24px;
  }
}
.vip-setMeal-box {
  --bgColor: linear-gradient(143deg, #fdfeff 0%, #ddeaf4 100%);
  --textColor: #252323;
  --vipColor: #f49351;
  --vipColor2: #557b98;
  width: calc(100% - 48px);
  padding: 24px 22px;
  background: var(--bgColor);
  background-size: cover;
  box-shadow: 0px 12px 18px 0px rgba(211, 219, 230, 0.5);
  border-radius: 16px;
  border: 1px solid #ffffff;
  box-sizing: border-box;
  margin: 0 auto;
  position: relative;
  font-family:
    PingFangSC,
    PingFang SC,
    "D-DIN";

  .w-100 {
    width: 100%;
  }

  .type-color-1 {
    color: var(--vipColor);
  }
  .type-color-2 {
    color: var(--vipColor2);
  }

  .title {
    font-weight: 600;
    font-size: 24px;
    gap: 2px;

    img {
      width: 26px;
      height: 26px;
    }
  }

  .money {
    position: relative;
    font-weight: 400;
    font-size: 14px;
    color: var(--textColor);

    .dicount-large {
      color: #7d7d7d;
      text-decoration: line-through;
      margin-right: 10px;
    }
    .large {
      font-size: 36px;
    }
    .small {
      font-size: 25px;
    }
    .discount {
      margin-left: 3px;
    }
    .img {
      width: 100px;
      height: 25px;
      margin-left: 3px;
      position: absolute;
      bottom: 5px;
      left: 100%;
    }
  }

  .buy-btn {
    width: 110px;
    color: var(--textColor);
    border-radius: 28px;
    border: 1px solid #fff;
    height: 33px;
    font-size: 14px;
    position: absolute;
    right: 20px;
    bottom: 24px;

    &.btn-1 {
      background: linear-gradient(90deg, #fffdfa 0%, #fff3ea 97%);
      box-shadow: 0px 6px 10px 0px rgba(199, 166, 150, 0.5);
    }
    &.btn-2 {
      background: linear-gradient(90deg, #ffffff 0%, #eef7ff 100%, #eef7ff 100%);
      box-shadow: 0px 6px 10px 0px rgba(128, 145, 176, 0.5);
    }
  }

  .tip {
    font-weight: 400;
    font-size: 12px;
    color: #25232380;
    margin: 14px 0 0;
    padding-inline-start: 32px;
    line-height: 16px;
  }
}

.activity-box {
  position: relative;
  width: calc(100% - 48px);
  height: 40px;
  margin: 0 auto;
  font-size: 14px;

  img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }

  & > div {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 2;
    width: 100%;
    height: 34px;
    padding: 0;
    line-height: 23px;
  }
}
</style>
