<template>
  <div
    class="flex-center customer-service-icon"
    :class="{ 'icon-right': dialogVisible }"
    @click="handleOpenKefuQrcode"
  >
    <svg
      t="1737622377991"
      class="icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      p-id="1546"
      :width="isMobile ? '30' : '50'"
      :height="isMobile ? '30' : '50'"
    >
      <path d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#FFFFFF" p-id="1547"></path>
      <path
        d="M646.890667 687.786667a9.408 9.408 0 0 0 1.173333 14.357333 120.746667 120.746667 0 0 1 37.205333 72.533333 39.317333 39.317333 0 1 0 42.026667-49.450666 120.64 120.64 0 0 1-67.093333-37.44 9.408 9.408 0 0 0-13.312 0z"
        fill="#FB6500"
        p-id="1548"
      ></path>
      <path
        d="M806.506667 625.536c-6.4 6.357333-10.368 14.72-11.306667 23.658667a120.64 120.64 0 0 1-37.333333 67.2 9.408 9.408 0 1 0 14.378666 12.010666 120.64 120.64 0 0 1 72.512-37.205333 39.317333 39.317333 0 1 0-38.144-65.664h-0.106666z"
        fill="#0082EF"
        p-id="1549"
        data-spm-anchor-id="a313x.search_index.0.i5.5a903a81NtkST3"
        class=""
      ></path>
      <path
        d="M695.744 514.666667a39.317333 39.317333 0 0 0 23.658667 66.837333 120.64 120.64 0 0 1 67.2 37.333333 9.408 9.408 0 1 0 12.010666-14.378666 120.746667 120.746667 0 0 1-37.205333-72.490667 39.317333 39.317333 0 0 0-65.664-17.301333z"
        fill="#2DBC00"
        p-id="1550"
      ></path>
      <path
        d="M676.437333 576.106667l-0.704 0.704a120.533333 120.533333 0 0 1-73.557333 38.485333 39.082667 39.082667 0 0 0-17.429333 65.664 39.317333 39.317333 0 0 0 66.858666-23.658667 120.746667 120.746667 0 0 1 37.418667-67.2 9.408 9.408 0 1 0-12.586667-13.994666z"
        fill="#FFCC00"
        p-id="1551"
      ></path>
      <path
        d="M438.912 236.757333c-70.506667 7.765333-134.4 37.909333-180.309333 84.992a238.08 238.08 0 0 0-44.032 61.909334 215.637333 215.637333 0 0 0 15.189333 217.749333c12.48 18.816 32.96 42.368 51.669333 59.093333l-8.469333 66.602667-0.938667 2.837333c-0.234667 0.810667-0.234667 1.749333-0.362666 2.56l-0.213334 2.133334 0.213334 2.133333a21.418667 21.418667 0 0 0 32.256 16.576h0.341333l1.301333-0.938667 20.266667-10.112 60.373333-30.378666a311.68 311.68 0 0 0 88.256 12.138666c36.906667 0.085333 73.578667-6.293333 108.288-18.837333a39.189333 39.189333 0 0 1-26.709333-41.088 265.877333 265.877333 0 0 1-111.36 11.093333l-5.973333-0.853333a269.184 269.184 0 0 1-40.042667-8.341333 27.306667 27.306667 0 0 0-21.418667 2.24l-1.642666 0.810666-49.664 29.184-2.133334 1.301334c-1.173333 0.704-1.749333 0.938667-2.346666 0.938666a3.413333 3.413333 0 0 1-3.178667-3.52l1.877333-7.658666 2.24-8.362667 3.541334-13.76 4.117333-15.296a20.842667 20.842667 0 0 0-7.530667-23.189333 217.173333 217.173333 0 0 1-50.133333-52.266667 169.834667 169.834667 0 0 1-12.245333-171.605333 196.202667 196.202667 0 0 1 35.306666-49.429334c37.653333-38.848 90.624-63.573333 149.248-69.930666 20.266667-2.218667 40.704-2.218667 60.949334 0 58.282667 6.72 110.997333 31.786667 148.437333 70.4 14.464 14.933333 26.24 31.786667 34.944 49.664a168.682667 168.682667 0 0 1 17.536 74.730666c0 2.709333-0.213333 5.418667-0.341333 8.021334a39.189333 39.189333 0 0 1 48.256 5.632l1.770666 2.133333a214.442667 214.442667 0 0 0-21.418666-111.573333 240.341333 240.341333 0 0 0-43.562667-61.930667 295.424 295.424 0 0 0-179.626667-85.546667 331.093333 331.093333 0 0 0-72.725333-0.256z"
        fill="#0082ef"
        p-id="1552"
        data-spm-anchor-id="a313x.search_index.0.i0.5a903a81NtkST3"
        class="selected"
      ></path>
    </svg>

    <div class="flex-column customer-service-box" :class="[dialogVisible ? 'customer-service-box-show' : '']" @click.stop>
      <div class="service-qrcode-img">
        <img :src="qrcodeUrl" alt="" />
      </div>
      <div style="font-size: 14px; margin-top: 5px">扫码添加</div>
      <div>随时需要，随时咨询</div>
      <div class="close-box" @click.stop="closeServiceBox"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import QRCode from 'qrcode'
import { getCurrentInstance, onMounted, ref } from 'vue'
import { getChannelCode } from '@/utils/auth'
import { getChannelQrcode, getVipQrcode } from '@/api/user'
import { useUserStore } from '@/stores/modules/user'

const { proxy } = getCurrentInstance() as any
const store = useUserStore()

const qrcodeUrl = ref('')
const dialogVisible = ref(false)
const isMobile = ref(false)

const getKefuQrcodeUrl = async () => {
  const channelCode = getChannelCode()
  if (!channelCode) {
    const res: any = await getVipQrcode()
    qrcodeUrl.value = res.data.url || ''
    // qrcodeUrl.value = proxy.$connectMeUrl
    return
  }
  const res: any = await getChannelQrcode(channelCode)
  if (res?.code == 200 && res?.data) {
    if (res.data.type == 2) {
      qrcodeUrl.value = res.data.url
    } else if (res.data.type == 3) {
      try {
        const dataUrl = await QRCode.toDataURL(res.data.url as string, {
          errorCorrectionLevel: 'H',
          width: 200,
        })
        qrcodeUrl.value = dataUrl
      } catch (err) {
        console.error('Failed to generate QR Code', err)
      }
    }
  }
}

function handleOpenKefuQrcode() {
  getKefuQrcodeUrl()
  dialogVisible.value = true
}
function closeServiceBox() {
  dialogVisible.value = false
}

onMounted(() => {
  getKefuQrcodeUrl()

  if (!store.userInfo.account) {
    const pageBox = document.querySelector('.vip-page')

    if (pageBox) {
      // 未登录时，滚动到指定位置,显示客服弹窗
      function handleScroll() {
        if (pageBox && pageBox.scrollTop >= window.innerHeight) {
          handleOpenKefuQrcode()
          pageBox.removeEventListener('scroll', handleScroll)
        }
      }
      pageBox.addEventListener('scroll', handleScroll)
    }
  }

  if (window.innerWidth <= 768) {
    isMobile.value = true
  }
})
</script>

<style scoped lang="scss">
.customer-service-icon {
  width: 62px;
  height: 62px;
  position: fixed;
  bottom: 10%;
  right: 60px;
  z-index: 999;
  cursor: pointer;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 0 10px 1px rgb(0 0 0 / 25%);
  transition: right 0.3s;

  @include mediaTo('phone') {
    width: 50px;
    height: 50px;
    right: 18px;
    bottom: 15%;

    svg {
      width: 45px;
      height: 38px;
    }
  }
  &.icon-right {
    right: 60px;
  }

  .customer-service-box {
    font-size: 12px;
    position: absolute;
    bottom: 50%;
    right: 50%;
    transform: translate(50%, 50%) scale(0);
    background: #fff;
    padding: 0 0 15px;
    border-radius: 8px;
    box-shadow: 0 0 10px 1px rgb(0 0 0 / 25%);
    cursor: auto;
    opacity: 0;
    z-index: -1;
    transition:
      opacity,
      transform 1.5s;

    &-show {
      opacity: 1;
      z-index: 1;
      transform: translate(50%, 50%) scale(1);
    }

    .service-qrcode-img {
      padding: 15px 15px 0;

      img {
        width: 110px;
      }
    }

    .close-box {
      position: absolute;
      top: -5px;
      right: -5px;
      width: 15px;
      height: 15px;
      border-radius: 50%;
      box-shadow: 0 0 10px 1px rgb(0 0 0 / 25%);
      background: #fff;
      cursor: pointer;
      transform: rotate(45deg);
      --close-color: #999;

      &:hover {
        box-shadow: 0 0 10px 1px rgb(0 0 0 / 35%);
        --close-color: #bd3d3d;
      }

      &::before {
        content: '';
        display: block;
        position: absolute;
        top: 3px;
        left: 7px;
        width: 1px;
        height: 9px;
        background: var(--close-color);
      }
      &::after {
        content: '';
        display: block;
        position: absolute;
        top: 7px;
        left: 3px;
        width: 9px;
        height: 1px;
        background: var(--close-color);
      }
    }
  }
}
</style>
