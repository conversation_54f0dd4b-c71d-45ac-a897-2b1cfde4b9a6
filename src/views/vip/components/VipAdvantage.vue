<template>
  <div class="flex-start vip-advantage">
    <div class="flex-start item-box">
      <div class="item-left">
        <div class="item-title">快速创建订单，提升使用体验</div>
        <div class="item-title-2" v-if="winWidth768">多产品同时下单；智能信息获取；一键提交，方便快捷</div>
        <ul v-else>
          <li>多产品同时下单</li>
          <li>智能信息获取</li>
          <li>一键提交，方便快捷</li>
        </ul>
        <div class="item-icon">
          <!-- <img src="@/assets/image/vip_arrow_right.png" alt=""> -->
        </div>
      </div>
      <div class="item-right">
        <img
          v-if="winWidth768"
          :src="$picUrl + 'static/assets/customer/vip/vip_advantage_mobile_1_v2.webp'"
          alt=""
        />
        <img v-else :src="$picUrl + 'static/assets/customer/vip/vip_advantage_1.webp'" alt="" />
      </div>
    </div>

    <div class="flex-start item-box">
      <div class="item-left">
        <div class="item-title">订单信息全景化整合</div>
        <div class="item-title-2" v-if="winWidth768">订单信息管理、状态追踪、流转记录</div>
        <ul v-else>
          <li>订单信息管理</li>
          <li>订单状态追踪</li>
          <li>订单流转记录</li>
        </ul>
        <div class="item-icon">
          <!-- <img src="@/assets/image/vip_arrow_right.png" alt=""> -->
        </div>
      </div>
      <div class="item-right">
        <img
          v-if="winWidth768"
          :src="$picUrl + 'static/assets/customer/vip/vip_advantage_mobile_2_v2.webp'"
          alt=""
        />
        <img v-else :src="$picUrl + 'static/assets/customer/vip/vip_advantage_2.webp'" alt="" />
      </div>
    </div>

    <div class="flex-start item-box">
      <div class="item-left">
        <div class="item-title">高效工作台</div>
        <div class="item-title-2" v-if="winWidth768">订单全流程便捷管理；会员权益与资源一览</div>
        <ul v-else>
          <li>订单全流程便捷管理</li>
          <li>会员权益与资源一览</li>
        </ul>
        <div class="item-icon">
          <!-- <img src="@/assets/image/vip_arrow_right.png" alt=""> -->
        </div>
      </div>
      <div class="item-right">
        <img
          v-if="winWidth768"
          :src="$picUrl + 'static/assets/customer/vip/vip_advantage_mobile_3_v2.webp'"
          alt=""
        />
        <img v-else :src="$picUrl + 'static/assets/customer/vip/vip_advantage_3.webp'" alt="" />
      </div>
    </div>

    <div class="flex-start item-box">
      <div class="item-left">
        <div class="item-title">子主账户协同办公</div>
        <div class="item-title-2" v-if="winWidth768">高效的账户管理体系</div>
        <ul v-else>
          <li>高效的账户管理体系</li>
        </ul>
        <div class="item-icon">
          <!-- <img src="@/assets/image/vip_arrow_right.png" alt=""> -->
        </div>
      </div>
      <div class="item-right">
        <img
          v-if="winWidth768"
          :src="$picUrl + 'static/assets/customer/vip/vip_advantage_mobile_4_v2.webp'"
          alt=""
        />
        <img v-else :src="$picUrl + 'static/assets/customer/vip/vip_advantage_4.webp'" alt="" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

const winWidth768 = ref(false)

onMounted(() => {
  if (window.innerWidth <= 768) {
    winWidth768.value = true
  }
})
</script>

<style lang="scss" scoped>
.vip-advantage {
  gap: 50px;

  @include mediaTo('phone') {
    background: transparent;
    flex-direction: column;
    gap: 32px;
  }

  .item-box {
    flex-shrink: 0;
    box-sizing: border-box;
    width: 1100px;
    height: 560px;
    padding: 24px 14px;
    border-radius: 16px;
    background-color: #fff;

    @include mediaTo('phone') {
      flex-direction: column;
      width: calc(100vw - 48px);
      height: auto;
      padding: 0px;
    }

    .item-left {
      width: 30%;
      padding-left: 80px;
      box-sizing: border-box;

      @include mediaTo('phone') {
        width: 100%;
        padding-left: 0px;
      }

      .item-title {
        font-size: 18px;
        font-weight: 400;
        color: var(--text-color);

        @include mediaTo('phone') {
          color: #4c4c4c;
        }
      }
      .item-title-2 {
        font-size: 14px;
        color: #1e293780;
        margin: 10px 0 24px;
      }
      ul {
        margin: 12px 0 20px;
        padding: 0 0 0 22px;

        li {
          font-weight: 400;
          font-size: 18px;
          line-height: 25px;
          color: #1e293780;
        }
      }

      .item-icon {
        img {
          width: 32px;
          height: 32px;
          cursor: pointer;
        }
      }
    }
    .item-right {
      width: 70%;

      @include mediaTo('phone') {
        width: 100%;
      }

      img {
        width: 100%;

        @include mediaTo('phone') {
          box-shadow: 0px 0px 10px 0px rgba(207, 214, 223, 0.45);
          border-radius: 8px;
          display: block;
        }
      }
    }
  }
}
</style>
