<template>
  <div class="vip-problem">
    <el-collapse v-model="activeName">
      <el-collapse-item v-for="item in list" :key="item.key" :name="item.key" :title="item.title">
        <div v-html="item.content"></div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

const activeName = ref(['1', '2'])

const list = [
  {
    key: '1',
    title: '是否购买完会员才可下单？',
    content: '是的，我们是实行会员制的，需要成为会员才能享受超低视频价的哦。',
  },
  {
    key: '2',
    title: '会员费和视频费分别怎么算？',
    content: `<span style="font-weight: 800;color: #1E2937;">·</span>
              <span style="font-weight: 600;">会员费：年度会员大概是1710元人民币</span><span style="font-size: 14px">（根据汇率会有所波动）</span><br/>
              <span style="font-weight: 800;color: #1E2937;">·</span>
              <span style="font-weight: 600;">视频初始单价：280元人民币</span><span style="font-size: 14px;line-height: 2">（费用明细：$29.9(千位模特一口价佣金)+$1.79(模特佣金代缴税费)+$1.61(PayPal代付手续费)+$6(服务费) × 百度美元实时汇率）</span><br/>
              <span style="font-size: 14px;line-height: 2">若参与活动1(会员每月首单立减19.9美金)，可额外优惠143元</span><br/>
              <span style="font-size: 14px;line-height: 2">若参与活动2(同产品满5单立减100)，可额外优惠20元</span><br/>
              <span style="font-size: 14px;line-height: 2">若参与活动3(充值满10000元送500)，可额外优惠13元</span><br/>
              <span style="font-size: 14px;line-height: 2">（以上活动均可叠加参与！！）</span>`,
  },
  {
    key: '3',
    title: '所有费用可以开票吗？',
    content: `可以的，您支付的所有会员费和视频费皆可开具增值税发票。`,
  },
  {
    key: '4',
    title: '怎么发货给模特？',
    content:
      '对接好模特后会为您提供模特的地址信息，您这边发货，能配送到达即可。（建议FBA仓或海外仓发货较快/国内自发货较慢）',
  },
  {
    key: '5',
    title: '拍摄的视频不满意可以售后吗？',
    content:
      '蜗牛模特的质量都是很高的并且每个模特都有视频案例可以提前了解，如果视频质量明显不符合要求，或者未能正确展示产品，可能导致对真实购买者的误导，蜗牛可以提供免费补拍视频或重拍视频的售后服务。',
  },
  {
    key: '6',
    title: '是否有试用订单？',
    content: '蜗牛海拍没有提供试用单，可在模特库中查看视频案例，蜗牛的模特及视频质量都是很优质的哦。',
  },
  {
    key: '7',
    title: '版权归我们吗？',
    content:
      '所有素人创作者免费合规版权，商家可自行商用并发布到不同社媒平台或电商平台，真正做到一视频多用途。',
  },
  {
    key: '8',
    title: '对会员不满意可以退会员费吗？',
    content:
      '蜗牛海拍会员支持七天无理由退会员费。购买会员后若您不满意，7天内可申请退还会员费，无需任何理由，当天申请当天退款。',
  },
]

// const init = () => {
//   const target = dayjs('2025-06-03 00:00:00')
//   const isAfterOrEqual = dayjs().isSame(target, 'second') || dayjs().isAfter(target)
//   if (isAfterOrEqual) {
//     list[1].content = `会员费：年度会员大概是1720元人民币（根据汇率会有所波动）<br/>
//                视频单价(不参与活动)：$29.9(千位模特一口价)+$1.79(美金代缴税费)+$1.61(PayPal代付手续费)+$6(包对接包剪辑包上传包售后服务费) × 百度美元实时汇率≈283元人民币<br/>
//               视频单价(参与活动)：若同时叠加“活动1: 充值10000赠送500元”和“活动2: 每月首单立减19.9”，单个视频还能额外再减155.5元，最终单个视频单价可低至127.5人民币`
//   }
// }
//
// init()

onMounted(() => {
  if (window.innerWidth <= 768) {
    activeName.value = ['1']
  }
})
</script>

<style scoped lang="scss">
.vip-problem {
  box-sizing: border-box;
  width: 100%;
  padding: 22px;
  background: linear-gradient(180deg, #f1f7ff 0%, #f5f7ff 28%, #ffffff 100%);
  border-radius: 16px;
  font-family:
    PingFangSC,
    PingFang SC;

  @include mediaTo('phone') {
    width: calc(100vw - 48px);
    margin: 0 auto;
    padding: 16px;
  }

  :deep(.el-collapse) {
    border: none;

    .el-collapse-item {
      --el-collapse-header-height: 66px;

      &__header {
        background: transparent;
        padding: 0 10px;
        font-weight: 600;
        font-size: 18px;
        color: #1e2937;
        line-height: 28px;
        text-align: left;
        border-bottom: 1px dashed #cac1b7;
      }
      &__content {
        padding-bottom: 25px;
        font-size: 16px;
        color: #1e293799;
        line-height: 1.5;
      }
      &__wrap {
        background: transparent;
        padding: 0 10px;
        font-weight: 400;
        font-size: 18px;
        color: #1e2937;
        line-height: 25px;
        border-bottom: 1px dashed #cac1b7;
      }

      &:last-child {
        .el-collapse-item__header {
          border-bottom: none;
        }
        .el-collapse-item__wrap {
          border-bottom: none;
        }
      }

      &.is-active {
        .el-collapse-item__header {
          border-bottom-color: transparent;
        }
      }
    }
  }
}
</style>
