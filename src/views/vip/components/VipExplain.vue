<template>
  <div class="flex-start vip-explain">
    <div class="explain-item" v-for="(item, index) in list" :key="index">
      <div class="flex-start title">
        <img :src="item.icon" alt="" />
        <span>{{ item.title }}</span>
      </div>
      <ul>
        <li v-for="(tip, index) in item.tips" :key="index">
          <span>{{ tip }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import VIP_ICON_YJ from '@/assets/image/center/VIP_icon_yj.png'
import VIP_ICON_MT from '@/assets/image/center/VIP_icon_mt.png'
import VIP_ICON_PT from '@/assets/image/center/VIP_icon_pt.png'
import VIP_ICON_KF from '@/assets/image/center/VIP_icon_kf.png'
import VIP_ICON_BQ from '@/assets/image/center/VIP_icon_bq.png'

const list = [
  {
    icon: VIP_ICON_YJ,
    title: '佣金超低价',
    tips: ['亚马逊影响者及素人模特视频拍摄佣金均享一口价29.9USD', '素人照片拍摄一口价5USD'],
  },
  {
    icon: VIP_ICON_MT,
    title: '模特随意选',
    tips: ['超千名优质外模随意挑选', '合作模特遍布美国、加拿大、德国、法国、意大利等国家'],
  },
  {
    icon: VIP_ICON_PT,
    title: '全平台全品类',
    tips: [
      '视频可发布于亚马逊、Tik Tok、独立站等平台',
      '支持服装、车载用品、安装产品、儿童玩具、宠物用品、成人用品等所有品类拍摄',
    ],
  },
  {
    icon: VIP_ICON_KF,
    title: '优质售后保障',
    tips: ['蜗牛与模特为长期深度合作关系，如有需要，将有专属客服全程为您与模特沟通补拍、重拍等售后需求'],
  },
  {
    icon: VIP_ICON_BQ,
    title: '免费合规版权',
    tips: [
      '选择素人模特拍摄可免费提供无损原视频&版权供品牌使用，支持卖家&品牌方用于任何商业用途，无版权纠纷',
    ],
  },
]
</script>

<style scoped lang="scss">
.vip-explain {
  width: 100%;
  flex-wrap: wrap;
  gap: 30px;
  font-family: PingFangSC, PingFang SC;

  @include mediaTo('phone') {
    flex-direction: column;
    gap: 20px;
  }

  .explain-item {
    background: linear-gradient(180deg, #f3f8ff 0%, #f5f8ff 31%, #ffffff 100%);
    border-radius: 16px;
    border: 1px solid #ffffff;
    height: 200px;
    padding: 25px 25px 0;
    box-sizing: border-box;
    flex-grow: 1;

    @include mediaTo('phone') {
      height: 140px;
      padding: 14px 14px 0;

      img {
        display: none;
      }
    }
    
    img {
      width: 44px;
      height: 44px;
      margin-right: 10px;
    }

    $i: 1;
    @while $i <= 5 {
      &:nth-child(#{$i}) {
        @if $i > 2 {
          width: 30%;

          .title {
            img {
              width: 36px;
              height: 36px;
            }
            span {
              font-size: 20px;
            }
          }
          ul {
            li {
              font-size: 14px;
              line-height: 26px;
            }
          }
        } @else {
          width: 48%;
          .title {
            span {
              font-size: 24px;
            }
          }
          ul {
            li {
              font-size: 18px;
              line-height: 32px;
            }
          }
        }
      }
      $i: $i + 1;
    }

    @include mediaTo('phone') {
      $i: 1;
      @while $i <= 5 {
        &:nth-child(#{$i}) {
          width: calc(100vw - 48px);
          box-shadow: 0px 2px 10px 0px rgba(231,224,214,0.3);

          .title {
            span {
              font-size: 18px;
            }
          }
          ul {
            li {
              font-size: 14px;
              line-height: 20px;
            }
          }
        }
        $i: $i + 1;
      }
    }

    .title {
      span {
        font-weight: 600;
        color: #1e2937;
      }
    }

    ul {
      margin: 5px 0 0;
      padding-left: 20px;

      @include mediaTo('phone') {
        list-style-type: none;
        padding-left: 0px;
      }

      li {
        font-weight: 400;
        color: #1e2937;

        &::marker {
          font-size: 10px;
        }

        @include mediaTo('phone') {
          color: #1e2937b3;
        }

        span {
          vertical-align: middle;
        }
      }
    }
  }
}
</style>
