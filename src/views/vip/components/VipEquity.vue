<template>
  <div class="flex-between vip-equity">
    <div class="equity-box">
      <div class="title">服务权益</div>

      <div class="flex-start equity-item" v-for="(item, i) in list1" :key="i">
        <img v-if="winWidth768" src="@/assets/image/vip_right_2.png" alt="" />
        <img v-else src="@/assets/image/vip_right.png" alt="" />
        <div class="one-ell content">
          {{ item.content }}
          <span v-if="item.tip">{{ item.tip }}</span>
        </div>
      </div>
    </div>
    <div class="equity-box">
      <div class="title">平台权益</div>

      <div class="flex-start equity-item" v-for="(item, i) in list2" :key="i">
        <img v-if="winWidth768" src="@/assets/image/vip_right_2.png" alt="" />
        <img v-else src="@/assets/image/vip_right.png" alt="" />
        <div class="one-ell content">
          {{ item.content }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

const list1 = [
  {
    content: '专属客服服务',
  },
  {
    content: '所有费用提供正规发票',
  },
  {
    content: '根据参考视频定制化拍摄',
  },
  {
    content: '视频、缩略图代上传',
    tip: '(美国亚马逊素人拍摄专项服务)',
  },
  {
    content: '提供版权和原片，视频可供商用至多平台',
    tip: '(素人创作者)',
  },
  {
    content: '4k超清画质视频',
  },
]
const list2 = [
  {
    content: '不限量子账号',
  },
  {
    content: '视频拍摄高效跟进',
  },
  {
    content: '离职员工订单继承',
  },
  {
    content: '境外物流跟踪',
  },
  {
    content: '180天视频保留',
  },
  {
    content: '专业剪辑服务',
  },
]

const winWidth768 = ref(false)

onMounted(() => {
  if (window.innerWidth <= 768) {
    winWidth768.value = true
  }
})
</script>

<style scoped lang="scss">
.vip-equity {
  width: 100%;
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  box-sizing: border-box;
  gap: 30px;
  font-family:
    PingFangSC,
    PingFang SC;

  @include mediaTo('phone') {
    background: transparent;
    flex-direction: column;
    padding: 0 24px;
  }

  .equity-box {
    width: 50%;

    @include mediaTo('phone') {
      width: 100%;
    }

    .title {
      font-weight: 600;
      font-size: 18px;
      color: #1e2937;
      background: linear-gradient(90deg, #eff6ff 0%, #f0f2ff 100%);
      border-radius: 16px;
      padding: 15px 22px;

      @include mediaTo('phone') {
        color: #214674;
      }
    }

    .equity-item {
      gap: 15px;
      padding: 15px 0 15px 22px;

      @include mediaTo('phone') {
        width: 100%;
        box-sizing: border-box;
        gap: 6px;
        padding: 10px 0 10px 14px;
      }

      &:nth-child(2n + 1) {
        background: #f6f8fa;
        border-radius: 16px;
      }

      img {
        width: 24px;
        height: 24px;

        @include mediaTo('phone') {
          width: 18px;
          height: 18px;
        }
      }
      .content {
        width: 100%;
        font-weight: 400;
        font-size: 18px;
        color: #1e2937;
        line-height: 25px;

        span {
          color: var(--text-gray-color);
          margin-left: 3px;
        }

        @include mediaTo('phone') {
          color: #585858;
          font-size: 14px;
          width: 90%;
          word-break: break-word;
          white-space: pre-wrap;

          &.one-ell {
            text-overflow: initial;
            overflow: initial;
          }

          span {
            color: #8a9098;
            font-size: 11px;
            display: block;
            margin-left: 0px;
            line-height: 14px;
          }
        }
      }
    }
  }
}
</style>
