<template>
  <div class="index-page">
    <div
      class="menu-box"
      :class="{
        'is-collapse': isCollapse,
      }"
      @click="checkLogin"
    >
      <Menu />
    </div>
    <Main>
      <RouterView v-slot="{ Component, route }">
        <Transition name="fade-transform" mode="out-in">
          <KeepAlive :max="3" :include="keepAliveArr">
            <component :is="Component" :key="route.name" />
          </KeepAlive>
        </Transition>
      </RouterView>
    </Main>

    <!-- <LoginDialog /> -->
    <!-- <WelcomeDialog v-if="store.userInfo.account" /> -->
    <!-- <Viewer /> -->
    <Tooltips />
    <RenewDialog />
    <!-- <MobileDeviceDialog /> -->
  </div>
</template>

<script lang="ts" setup>
import Menu from '@/components/layout/MenuBar.vue'
import Main from '@/components/layout/main.vue'
// import LoginDialog from '@/views/login/LoginDialog.vue'
// import WelcomeDialog from '@/components/public/dialog/WelcomeDialog.vue'
// import MobileDeviceDialog from '@/components/public/dialog/MobileDeviceDialog.vue'
// import Viewer from '@/components/public/viewer/index.vue'
import Tooltips from '@/components/public/Tooltips.vue'
import RenewDialog from '@/components/public/dialog/RenewDialog.vue'
import { useTheme } from '@/hooks/useTheme'
import { RouterView } from 'vue-router'
import { ref } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { openLogin } from '@/hooks/useLogin'
import { isMobileDevice } from '@/utils/public'

defineOptions({
  name: 'home',
})

const store = useUserStore()

const isCollapse = ref<boolean>(false)

// 主题
const { theme } = useTheme()
const switchBtn = ref<boolean>(false)
if (theme.value == 'dark') {
  switchBtn.value = true
} else {
  switchBtn.value = false
}

// 缓存组件名称
const keepAliveArr = ref<string[]>([])

// function init() {
//   if (!store.userInfo.account) {
//     // nextTick(() => {
//     //   openLogin()
//     // })
//   }
// }

// 菜单栏收起/展开
function collapseFn() {
  // console.log('菜单栏收起/展开')
  isCollapse.value = !isCollapse.value
}

// switch change
function switchChange(val: boolean) {
  if (val) {
    theme.value = 'dark'
  } else {
    theme.value = 'light'
  }
}

function checkLogin() {
  if (!store.userInfo.account) {
    openLogin()
  }
}


if (isMobileDevice()) {
  isCollapse.value = true
  keepAliveArr.value = ['ModelListPage']
}
</script>

<style lang="scss" scoped>
@use '@/styles/transition.scss';
.index-page {
  display: flex;
  overflow-x: auto;
  height: 100vh;
  background: var(--bg);

  .menu-box {
    position: relative;
    z-index: 9;
    width: 160px;
    height: 100%;
    // border-right: 1px solid #bababa8a;
    background-color: transparent;
    // overflow: hidden;
    transition: 0.3s;
    // min-width: 64px;
    background-image: var(--menu-bg-image);
    background-size: 100% 100%;
    flex-shrink: 0;

    &.is-collapse {
      display: none;
      width: 0px;
      overflow: hidden;
    }
  }
}
</style>

<style lang="scss">
.el-tooltip-vip {
  padding: 0;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  // --el-bg-color-overlay: linear-gradient(90deg, #f0fdff 0%, #f4f9ff 100%);
  --el-border-color-light: #ffffff;
}
.pages {
  position: relative;
  padding: 0 28px 10px;
  box-sizing: border-box;
  // tabsNav 70 备案号 28
  // height: calc(100vh - 86px);
  height: 100%;
  overflow-y: auto;
  background-color: transparent;
  color: var(--text-color);

  .search-box {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.24) 0%, #ffffff 100%);
    border-radius: 14px;
    border: 1px solid #fff;
    padding: 21px;
  }
  .w-bg {
    background: #ffffff;
    border-radius: 14px;
    padding: 20px;
  }
}
.pagination-box {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}
.table-page-box {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
</style>
