<template>
  <div class="pages workbench-info-page">
    <div class="flex-between workbench-banner" v-if="store.userInfo.account">
      <img src="@/assets/image/banner1.png" alt="" @click="toTopUpPage" />
      <img src="@/assets/image/banner2.webp" alt="" @click="toCreateOrder" />
    </div>

    <div class="flex-between header-box">
      <div class="title" v-if="!store.userInfo.account">
        <span>请先</span>
        <span style="color: var(--el-color-primary); cursor: pointer" @click="openLogin">登录</span>
      </div>
      <template v-else>
        <div class="info-box">
          <div class="flex-center" style="position: relative; gap: 16px">
            <el-avatar
              v-if="store.userInfo.pic"
              class="user-avatar"
              icon="UserFilled"
              :src="store.userInfo.pic"
            />
            <div v-else class="flex-center user-avatar-box">
              {{ userAvatarName }}
            </div>

            <img v-if="store.isVip()" class="vip-icon-img" src="@/assets/icon/icon_y_vip.png" alt="" />
            <img v-else class="vip-icon-img" src="@/assets/icon/icon_n_vip.png" style="bottom: -1px" alt="" />

            <div class="user-info">
              <div class="flex-start gap-10">
                <div class="name">{{ store.userInfo.nickName }}</div>
                <template v-if="store.isVip() || store.isViped()">
                  <OwnerTag />
                </template>
              </div>
              <div class="business-name">
                <span v-if="store.userInfo.businessVO?.name">
                  {{ store.userInfo.businessVO.name }}
                </span>
              </div>
            </div>
          </div>
        </div>
        <el-button
          class="custom-radius"
          v-if="store.isOwnerAcc() && store.isVip()"
          type="primary"
          @click="toAccountManager"
        >
          <template #icon>
            <img src="@/assets/icon/workbench/users.png" alt="" style="width: 16px; height: 16px" />
          </template>
          子账号管理
        </el-button>
      </template>
    </div>

    <div class="flex-between card-box">
      <div class="card-item-25" v-loading="loading">
        <div class="flex-between title">
          <span>进行中的订单</span>
          <img src="@/assets/icon/workbench/order.png" alt="" />
        </div>
        <div class="number-box">
          <div class="number">{{ Math.floor(orderNumberAnim) }}</div>
        </div>
        <div class="flex-end">
          <el-button type="primary" link :disabled="!store.userInfo.account" @click="toOrderList()">
            去查看
          </el-button>
        </div>
      </div>
      <div class="card-item-25" v-loading="loading">
        <div class="flex-between title">
          <span>需支付订单</span>
          <img src="@/assets/icon/workbench/finance.png" alt="" />
        </div>
        <div class="number-box">
          <div class="number">{{ Math.floor(orderNeedPayNumberAnim) }}</div>
        </div>
        <div class="flex-end">
          <el-button type="primary" link :disabled="orderNeedPayNumber == 0" @click="toPayPage">
            去支付
          </el-button>
        </div>
      </div>
      <div class="card-item-25" v-loading="loading">
        <div class="flex-between title">
          <span>需发货订单</span>
          <img src="@/assets/icon/workbench/commodity.png" alt="" />
        </div>
        <div class="number-box">
          <div class="number">{{ Math.floor(orderNeedDeliveryNumberAnim) }}</div>
        </div>
        <div class="flex-end">
          <el-button
            type="primary"
            link
            :disabled="orderNeedDeliveryNumber == 0"
            @click="toOrderList(ORDER_STATUS['需发货'])"
          >
            去发货
          </el-button>
        </div>
      </div>
      <div class="card-item-25" v-loading="loading">
        <div class="flex-between title">
          <span>需确认素材</span>
          <img src="@/assets/icon/workbench/folder.png" alt="" />
        </div>
        <div class="number-box">
          <div class="number">{{ Math.floor(orderNeedConfirmNumberAnim) }}</div>
        </div>
        <div class="flex-end">
          <el-button
            type="primary"
            link
            :disabled="orderNeedConfirmNumber == 0"
            @click="toOrderList(ORDER_STATUS['需确认'])"
          >
            去确认
          </el-button>
        </div>
      </div>
    </div>

    <div class="flex-between card-box">
      <div class="card-item-50" v-loading="amountLoading">
        <div class="flex-between title">
          <span>账户余额</span>
          <!-- <img src="@/assets/icon/workbench/wallet.png" alt="" /> -->
          <!-- <img class="banner" src="@/assets/icon/workbench/banner.png" alt="" /> -->
        </div>
        <div class="number-box">
          <div class="flex-start number">
            <span class="span">¥</span>
            {{ amountAnim ? amountAnim.toFixed(2) : 0 }}
          </div>
        </div>
        <div class="flex-between">
          <div class="flex-start hint" v-if="lockBalance">
            已锁定金额：¥{{ lockBalance }}
            <el-button type="primary" link style="margin-left: 4px" @click="viewLockRecord">
              锁定记录
            </el-button>
          </div>
          <div class="hint" v-else></div>
        </div>
        <div class="flex-column un-pay-number">
          <el-button class="custom-radius" type="primary" @click="toTopUpPage">
            <template #icon>
              <img src="@/assets/icon/wallet.png" alt="" style="width: 16px; height: 16px" />
            </template>
            去充值
          </el-button>
          <div class="flex-end pay-num">
            <template v-if="unWalletPayNum">
              <div>您有{{ unWalletPayNum }}笔未支付的充值订单，</div>
              <el-button type="primary" link @click="toPayRecordOrder">立即查看</el-button>
            </template>
          </div>
        </div>
      </div>
      <div class="card-item-50">
        <div class="flex-between title">
          <span>会员有效期至</span>
          <!-- <img src="@/assets/icon/workbench/member.png" alt="" /> -->
        </div>
        <div class="number-box">
          <div class="flex-start gap-10 number" v-if="store.isVip()">
            <span v-if="store.userInfo.businessVO?.memberValidity">
              {{ handleDate(store.userInfo.businessVO.memberValidity) }}
            </span>
            <el-tag v-if="store.userInfo.businessVO?.memberStatus === 2" type="danger">即将到期</el-tag>
          </div>
          <div class="flex-start gap-10 text" v-else>
            <span>
              {{
                store.isViped()
                  ? `会员已于${handleDate(store.userInfo.businessVO?.memberValidity)}到期`
                  : '您未开通会员'
              }}
            </span>
          </div>
        </div>
        <div class="flex-between">
          <div class="hint">
            <span v-if="store.userInfo.businessVO?.memberValidity && store.isVip()">
              剩余
              <span :style="{ color: memberRsidueTime < 30 ? '#fb8b41' : '' }">
                {{ endTimeToDay(store.userInfo.businessVO.memberValidity) }}
              </span>
              天
              <span v-if="memberRsidueTime < 30">到期</span>
            </span>
          </div>
          <div class="flex-column un-pay-number" v-if="store.isOwnerAcc()">
            <el-button
              v-if="store.isOwnerAcc() && ((store.isVip() && memberRsidueTime >= 30) || !isFiveRenew)"
              class="custom-radius"
              type="primary"
              :disabled="!store.userInfo.account"
              @click="toVipPage"
            >
              <template #icon>
                <img src="@/assets/icon/workbench/money.png" alt="" style="width: 16px; height: 16px" />
              </template>
              {{ store.isVip() || store.isViped() ? '去续费' : '去开通' }}
            </el-button>
            <el-button
              v-if="!store.isOwnerAcc() && ((store.isVip() && memberRsidueTime >= 30) || !isFiveRenew)"
              class="custom-radius"
              type="primary"
              :disabled="!store.userInfo.account"
              @click="toVipPage"
            >
              <template #icon>
                <img src="@/assets/icon/workbench/money.png" alt="" style="width: 16px; height: 16px" />
              </template>
              去续费
            </el-button>

            <el-button
              v-if="store.isVip() && memberRsidueTime < 30 && isFiveRenew"
              class="custom-radius"
              type="primary"
              :disabled="!store.userInfo.account"
              @click="toVipPage"
            >
              半价续费
            </el-button>
            <div class="flex-end pay-num">
              <template v-if="unVipPayNum">
                <div>您有{{ unVipPayNum }}笔未支付的会员充值订单，</div>
                <el-button type="primary" link @click="toVipRecord">立即查看</el-button>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>

    <TableList />

    <RecordDialog ref="RecordDialogRef" />
  </div>
</template>

<script lang="ts" setup>
import OwnerTag from '@/components/public/tag/OwnerTag.vue'
import TableList from '@/views/workbench/info/table.vue'
import RecordDialog from '@/views/finance/wallet/components/RecordDialog.vue'
import { openLogin } from '@/hooks/useLogin'
import { endTimeToDay } from '@/utils/time'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { computed, ref } from 'vue'
import { useTransition } from '@vueuse/core'
import { workbenchOrderStatusCount, needPayOrderList, getBusinessParticipatoryActivity } from '@/api/order'
import { ORDER_STATUS } from '@/utils/order'
import { getBusinessBalanceDetailVo } from '@/api/user'
import { getOnlineUnPay } from '@/api/wallet'
import { getMemberUnPay } from '@/api/vip'
import currency from 'currency.js'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
import { getToken } from '@/utils/auth'
import { useTooltips } from '@/hooks/useTooltips'

const { showTooltips } = useTooltips()

import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
import aegis from '@/utils/aegis'
import Aegis from 'aegis-web-sdk'
const { isFiveRenew, handelRenewDialog } = useShowRenewDialog()

const router = useRouter()

const store = useUserStore()

const RecordDialogRef = ref()

const userAvatarName = computed(() => {
  if (store.userInfo.account) {
    if (store.userInfo.nickName) {
      return store.userInfo.nickName.substring(0, 1)
    }
    if (store.userInfo.name) {
      return store.userInfo.name.substring(0, 1)
    }
  }
  return ''
})

const memberRsidueTime = computed(() => {
  let result = 0
  if (store.userInfo.businessVO?.memberValidity) {
    result = endTimeToDay(store.userInfo.businessVO.memberValidity)
  }
  return result
})

const loading = ref(false)
const orderNumber = ref(0)
const orderNeedPayNumber = ref(0)
const orderNeedDeliveryNumber = ref(0)
const orderNeedConfirmNumber = ref(0)

const orderNumberAnim = useTransition(orderNumber, {
  duration: 500,
})
const orderNeedPayNumberAnim = useTransition(orderNeedPayNumber, {
  duration: 500,
})
const orderNeedDeliveryNumberAnim = useTransition(orderNeedDeliveryNumber, {
  duration: 500,
})
const orderNeedConfirmNumberAnim = useTransition(orderNeedConfirmNumber, {
  duration: 500,
})

const amountLoading = ref(false)
const amount = ref(0)
const amountAnim = useTransition(amount, {
  duration: 500,
})
const orderLockBalance = ref(0)
const payOutLockBalance = ref(0)
const useBalance = ref(0)

const lockBalance = computed(() => {
  // if (payOutLockBalance.value > 0 && orderLockBalance.value > 0) {
  //   return `其中已被提现及订单锁定${useBalance.value}元`
  // }
  // if (payOutLockBalance.value > 0) {
  //   if (amount.value === payOutLockBalance.value) {
  //     return `余额已被提现锁定`
  //   }
  //   return `其中已被提现锁定${payOutLockBalance.value}元`
  // }
  // if (orderLockBalance.value > 0) {
  //   if (amount.value === orderLockBalance.value) {
  //     return `余额已被订单锁定`
  //   }
  //   return `其中已被订单锁定${orderLockBalance.value}元`
  // }
  // return ''
  return currency(orderLockBalance.value).add(payOutLockBalance.value).value
})

const unWalletPayNum = ref(0)
const unWalletPayOrderNums = ref<any[]>([])
// 钱包充值记录
function toPayRecordOrder() {
  if (unWalletPayNum.value) {
    if (unWalletPayNum.value === 1) {
      store.realTimeCheckVip().then(() => {
        router.push({
          name: 'finance-wallet-pay',
          state: {
            prepayId: unWalletPayOrderNums.value[0].id,
            orderNum: unWalletPayOrderNums.value[0].prepayNum,
            payType: unWalletPayOrderNums.value[0].payType,
          },
        })
      })
      return
    }
    router.push('/finance/wallet/topUp/record')
  }
}
function toTopUpPage() {
  if (!store.userInfo.account) return openLogin()
  try {
    aegis?.report({
      msg: '用户点击10000-500',
      level: Aegis.logType.REPORT,
      trace: 'trace',
    })
  } catch (error) {
    console.log('日志上报失败')
  }
  if (store.isVip()) {
    router.push('/finance/wallet/topUp')
  } else {
    ElMessage.error('请先开通会员！')
  }
}

const unVipPayNum = ref(0)
const unVipPayOrderNums = ref<any[]>([])
function getMemberOrderNum() {
  if (store.isOwnerAcc() || store.isVip()) {
    getMemberUnPay().then(res => {
      if (res.data) {
        unVipPayNum.value = res.data.unPayNum
        unVipPayOrderNums.value = res.data.unPayOrderList
      }
    })
  }
}
function toVipRecord() {
  if (unVipPayNum.value === 1) {
    // 只有一条未支付订单-去支付
    store.realTimeCheckVip().then(() => {
      if (store.isOwnerAcc()) {
        router.push({
          name: 'center-pay',
          state: {
            orderNum: unVipPayOrderNums.value[0].orderNum,
            type: unVipPayOrderNums.value[0].packageType,
            payType: unVipPayOrderNums.value[0].payType,
          },
        })
      }
    })
  } else if (unVipPayNum.value > 1) {
    // 多条未支付订单-开通记录列表
    const { href } = router.resolve({ name: 'center-tradingRecord' })
    window.open(href, '_blank')
  }
}

// 查看余额锁定记录
function viewLockRecord() {
  RecordDialogRef.value?.open()
}

// 跳转到创建订单
function toCreateOrder() {
  if (!store.userInfo.account) return openLogin()
  try {
    aegis?.report({
      msg: '用户点击立减100',
      level: Aegis.logType.REPORT,
      trace: 'trace',
    })
  } catch (error) {
    console.log('日志上报失败')
  }
  const { href } = router.resolve({ path: '/order/create' })
  window.open(href, '_blank')
}

// 跳转到会员页
function toVipPage() {
  router.push('/vip')
}

// 跳转到子账号管理
function toAccountManager() {
  if (store.isOwnerAcc()) {
    router.push('/center/acc')
  }
}
// 跳支付页
function toPayPage() {
  if (orderNeedPayNumber.value === 1) {
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在跳转...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    needPayOrderList({
      pageNum: 1,
      pageSize: 1,
    })
      .then(res => {
        if (res.data?.rows?.length) {
          if (res.data.rows[0].mergeId) {
            toPay('', res.data.rows[0].mergeId, null)
          } else if (res.data.rows[0].orderListVOS?.length) {
            toPay(res.data.rows[0].orderListVOS[0].orderNum, null, res.data.rows[0].orderListVOS[0].payType)
          }
        }
        if (res.data.total === 0) {
          ElMessageBox.alert('订单状态变更，请刷新页面', '提示', {
            showClose: false,
            closeOnClickModal: false,
            closeOnPressEscape: false,
            confirmButtonText: '刷新',
          }).then(() => {
            window.location.reload()
          })
        }
      })
      .finally(() => {
        el_loading.close()
      })
    return
  }
  toOrderList(ORDER_STATUS['需支付'])
}
// 跳支付页
function toPay(orderNum: any, mergeId: any, payType: any) {
  if (!orderNum && !mergeId) {
    ElMessage.error('参数错误！')
    throw new Error('Missing key field')
  }
  store.realTimeCheckVip().then(() => {
    if (store.isVip()) {
      router.push({
        name: 'order-pay',
        state: { orderNum, mergeId, payType },
      })
    } else {
      ElMessage.error('请先开通会员！')
    }
  })
}

// 跳转到订单列表
function toOrderList(tab?: ORDER_STATUS) {
  let path = '/order/list'
  if (tab) {
    path += `?tab=${tab}&u=1`
  } else {
    path += `?tabs=${ORDER_STATUS['待匹配']},${ORDER_STATUS['需发货']},${ORDER_STATUS['待完成']},${ORDER_STATUS['需确认']}&u=1`
  }
  router.push(path)
}

// 获取余额
function getAmount() {
  amountLoading.value = true
  getBusinessBalanceDetailVo()
    .then(res => {
      if (res.data) {
        amount.value = res.data.balance
        orderLockBalance.value = res.data.orderLockBalance
        payOutLockBalance.value = res.data.payOutLockBalance
        useBalance.value = res.data.useBalance
      }
    })
    .finally(() => {
      amountLoading.value = false
    })
  getOnlineUnPay().then(res => {
    if (res.data) {
      unWalletPayNum.value = res.data.unPayNum
      unWalletPayOrderNums.value = res.data.unPayOnlineList
    }
  })
}

function handleDate(time?: string) {
  if (!time) return ''
  const date = new Date(time)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  return `${year}年${month}月${day}日`
}

async function init() {
  if (getToken()) {
    await store.getUserInfo()
  }

  if (!store.userInfo.account) return
  if (store.isVip()) {
    // handelRenewDialog()
    // handleMemberDialog()
  }

  loading.value = true

  if (store.userInfo.account) {
    workbenchOrderStatusCount()
      .then((res: any) => {
        if (res.data) {
          orderNumber.value = res.data.underwayCount || 0
          orderNeedPayNumber.value = res.data.unPayCount || 0
          orderNeedDeliveryNumber.value = res.data.unFilledCount || 0
          orderNeedConfirmNumber.value = res.data.needConfirmCount || 0
        }
      })
      .finally(() => {
        loading.value = false
      })
    getAmount()
    getMemberOrderNum()
  } else {
    loading.value = false
  }
  try{
    aegis?.report({
      msg: '登录用户查看工作台内容',
      level: Aegis.logType.REPORT,
    })
  }catch (error) {
    console.log("日志上报失败")
  }
}
init()
</script>

<style lang="scss" scoped>
.workbench-info-page {
  padding: 0 28px 10px 20px;

  .workbench-banner {
    cursor: pointer;
    width: 100%;
    // height: 56px;
    line-height: 0;
    margin-bottom: 16px;
    gap: 16px;

    img {
      width: calc(50% - 8px);
      // height: 100%;
      border-radius: 6px;
    }
  }

  .header-box {
    background-color: #fff;
    padding: 20px;
    height: 56px;
    margin-bottom: 16px;
    border-radius: 6px;

    .info-box {
      .user-avatar {
        --el-avatar-size: 56px;
        --el-avatar-icon-size: 22px;
        cursor: pointer;
      }
      .user-avatar-box {
        cursor: pointer;
        font-size: 18px;
        color: #ffffff;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: var(--el-color-primary-light-1);
      }
      .vip-icon-img {
        position: absolute;
        bottom: 0;
        left: 3px;
        width: 50px;
        height: 16px;
      }
      .user-info {
        text-align: left;
        margin-right: 10px;
        line-height: 21px;
        font-size: 13px;

        .name {
          font-weight: 500;
          font-size: 16px;
          color: var(--text-color);
        }

        .business-name {
          color: #0009;
        }
      }
    }
  }

  .card-box {
    gap: 16px;
    margin-bottom: 16px;

    .card-item-25 {
      width: 25%;
      height: 150px;
      padding: 20px;
      box-sizing: border-box;
      background-color: #fff;
      color: var(--text-color);
      border-radius: 6px;
    }

    .card-item-50 {
      width: 50%;
      height: 150px;
      padding: 20px;
      box-sizing: border-box;
      background-color: #fff;
      color: var(--text-color);
      border-radius: 6px;
      position: relative;

      .number-box {
        margin-bottom: 2px;

        .number {
          margin-top: 20px;
        }
      }
      .hint {
        line-height: 32px;
        flex-shrink: 0;
      }
    }

    .title {
      align-items: flex-start;
      font-size: 16px;

      img {
        width: 40px;
        height: 40px;

        &.banner {
          width: auto;
          height: 50px;
          position: absolute;
          top: 0;
          right: 0;
          z-index: 2;
        }
      }
    }
    .number-box {
      margin: 8px 0 16px;

      .number {
        font-weight: bold;
        font-size: 28px;
        line-height: 30px;
        width: 100%;
        overflow: hidden;

        .span {
          font-size: 22px;
        }

        .el-tag {
          font-weight: 400;
          font-size: 14px;
          color: #f60000dd;
          height: 20px;
        }
      }
      .text {
        font-size: 14px;
        line-height: 30px;
      }
    }

    .hint {
      font-size: 14px;
      color: #0006;
    }
    .un-pay-number {
      font-size: 14px;
      color: #0006;
      align-items: flex-end;
      position: absolute;
      right: 20px;
      bottom: 21px;
      gap: 1px;

      .pay-num {
        width: 300px;
        height: 32px;
        line-height: 32px;
      }
    }
  }
}
</style>
