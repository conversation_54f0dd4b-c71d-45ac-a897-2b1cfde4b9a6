<template>
  <div class="table-box">
    <div class="table-title">我的订单</div>
    <div v-loading="tableLoading">
      <div
        v-if="!store.userInfo.account || total === 0"
        style="width: 100%"
        :style="{ height: tableHeight + 'px' }"
      >
        <Empty style="--empty-desc-color: #5696c0" description="暂无订单" :image-size="180" />
      </div>
      <el-table v-else :data="tableData" style="width: 100%" :max-height="tableHeight">
        <el-table-column prop="videoCode" label="视频编码" align="center" min-width="130">
          <template v-slot="{ row }">
            <span>{{ row.videoCode || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="productChinese"
          label="产品名称"
          align="center"
          min-width="280"
          :show-overflow-tooltip="{
            'popper-class': 'public-tooltips product-popper',
          }"
        />
        <el-table-column prop="nickName" label="拍摄模特" align="center" min-width="150">
          <template v-slot="{ row }">
            {{ row.shootModel?.account ? row.shootModel.name : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="modelType" label="模特类型" align="center" min-width="150">
          <template v-slot="{ row }">
            <span v-if="row.modelType == '3'">影/素都可以</span>
            <template v-for="dict in biz_model_type" :key="dict.value">
              <span v-if="dict.value == row.modelType">
                {{ dict.label }}
              </span>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="user" label="订单运营" align="center" min-width="130">
          <template v-slot="{ row }">
            <div v-if="row.createOrderUserName">{{ row.createOrderUserName }}</div>
            <div v-else-if="row.createOrderUserNickName">{{ row.createOrderUserNickName }}</div>
            <div v-else>-</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态" align="center" min-width="150">
          <template v-slot="{ row }">
            <span>
              {{ orderStatus(row.status) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" align="center" width="170">
          <template v-slot="{ row }">
            <template v-if="row.status === ORDER_STATUS['需发货']">
              <el-button
                v-if="row.isObject === 0 && row.logisticFlag"
                type="primary"
                link
                @click="handleAction('发货', row)"
              >
                填入物流单号
              </el-button>
              <el-button
                v-if="row.isObject === 0 && !row.logisticFlag"
                type="primary"
                link
                @click="handleAction('发货', row)"
              >
                去发货
              </el-button>
            </template>
            <template v-else-if="row.status === ORDER_STATUS['需确认']">
              <el-button link type="primary" @click="handleAction('验收素材', row)">查看素材</el-button>
            </template>
            <el-button v-else link type="primary" @click="handleAction('查看详情', row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex-end" style="margin-top: 12px" v-if="store.userInfo.account && total > 0">
      <el-button style="line-height: 32px" link type="primary" @click="router.push('/order/list')">
        查看全部订单
      </el-button>
    </div>

    <ConfrimDeliverGoods
      ref="ConfrimDeliverGoodsRef"
      :title="ConfrimDeliverGoodsTitle"
      :flag="logisticFlag"
      @success="handleQuery"
    />

    <CheckAndAcceptMaterial ref="CheckAndAcceptMaterialRef" @success="handleQuery" />
  </div>
</template>

<script lang="ts" setup>
import ConfrimDeliverGoods from '@/views/order/components/dialog/ConfirmDeliverGoods.vue'
import CheckAndAcceptMaterial from '@/views/order/components/dialog/CheckAndAcceptMaterial.vue'
import { accountOrderList } from '@/api/order'
import { orderStatus, ORDER_STATUS } from '@/utils/order'
import { getCurrentInstance, onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'

const { proxy } = getCurrentInstance() as any

const router = useRouter()
const store = useUserStore()

const { biz_model_type } = proxy.useDict('biz_model_type')

const tableData = ref<any[]>([])
const tableLoading = ref(false)
const total = ref(0)
const tableHeight = ref(300)

function handleQuery() {
  tableLoading.value = true
  accountOrderList()
    .then(res => {
      tableData.value = res.data.rows
      total.value = res.data.total
    })
    .finally(() => {
      tableLoading.value = false
    })
}

// 发货
const ConfrimDeliverGoodsRef = ref<InstanceType<typeof ConfrimDeliverGoods>>()
const ConfrimDeliverGoodsTitle = ref('确认发货')
const logisticFlag = ref(false)
// 验收素材
const CheckAndAcceptMaterialRef = ref<InstanceType<typeof CheckAndAcceptMaterial>>()

function handleAction(action: string, row: any) {
  if (action === '发货') {
    ConfrimDeliverGoodsTitle.value = '订单发货信息'
    logisticFlag.value = row.logisticFlag === 1
    ConfrimDeliverGoodsRef.value?.open(row.id, 1)
  } else if (action === '验收素材') {
    CheckAndAcceptMaterialRef.value?.open(row.id, row.status, {
      link: row.productLink,
      shootModelType: row.shootModel?.type || '',
      platform: row.platform,
      modelType: row.modelType,
      nation: row.shootingCountry,
    })
  } else if (action === '查看详情') {
    const { href } = router.resolve({ path: '/order/details/' + row.id })
    window.open(href, '_blank')
  }
}

function handleTableHeight() {
  let h = window.innerHeight - 754
  if (h > 700) {
    tableHeight.value = 700
  } else if (h > 300) {
    tableHeight.value = h
  } else {
    tableHeight.value = 300
  }
}
onMounted(() => {
  handleTableHeight()

  if (store.userInfo.account) {
    handleQuery()
  }

  window.addEventListener('resize', handleTableHeight)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleTableHeight)
})
</script>

<style lang="scss" scoped>
.table-box {
  padding: 20px;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 6px;

  .table-title {
    font-weight: 500;
    font-size: 16px;
    color: var(--text-color);
    line-height: 20px;
    margin-bottom: 12px;
  }
}
:deep(.el-table) {
  .el-popper {
    &.product-popper {
      max-width: 600px;
    }
  }
}
</style>
