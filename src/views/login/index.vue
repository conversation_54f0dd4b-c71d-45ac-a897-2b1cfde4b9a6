<template>
  <div class="login-page">
    <div class="bg">
      <div class="logo-box">
        <img :src="logo" alt="">
      </div>
      <img class="bg-img" :src="bg" alt="">
      
      <div class="login-box">

        <div class="login-form">

          <div class="flex-center login-title">
            <div class="title" :class="{ active: curTabs === 0 }" @click="tabsChange(0)">扫码登录</div>
            <div class="title" :class="{ active: curTabs === 1 }" @click="tabsChange(1)">账号登录</div>
          </div>

          <QrCode
            v-show="curTabs === 0"
            ref="QrCodeRef"
            size="large"
            :http-code="wechatQrcode"
            :check-code="checkQrcode"
            @change="chageCheckQrcode"
            @success="checkSuccess"
          />
          <div v-show="curTabs === 0" class="qrcode-tips">{{ qrCodeTips }}</div>
          
          <el-form
            v-show="curTabs === 1"
            ref="formRef"
            :model="form"
            label-position="top"
            :rules="rules"
            @submit.prevent
          >
            <el-form-item prop="acc">
              <div class="input-box">
                <el-input
                  placeholder="请输入账号"
                  prefix-icon="User"
                  v-model="form.acc"
                  clearable></el-input>
              </div>
            </el-form-item>
            <el-form-item prop="pwd">
              <div class="input-box">
                <el-input
                  placeholder="请输入密码"
                  prefix-icon="Lock"
                  v-model="form.pwd"
                  type="password"
                  show-password></el-input>
              </div>
            </el-form-item>

            <div class="flex-between">
              <el-checkbox v-model="checked">记住密码</el-checkbox>
              <el-button link type="info">忘记密码</el-button>
            </div>
    
            <el-button
              :type="elBtnType"
              native-type="submit"
              round
              @click="loginBtn()"
              :loading="loginLoading"
              class="login-btn"
              >登录</el-button
            >
          </el-form>
          <!-- <div class="change-theme-btn">
            <el-dropdown trigger="click" @command="changeTheme">
              <span class="el-dropdown-link">
                主题
                <el-icon class="el-icon--right">
                  <arrow-down />
                </el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="light" :disabled="theme == 'light'"
                    >light</el-dropdown-item
                  >
                  <el-dropdown-item command="dark" :disabled="theme == 'dark'"
                    >dark</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div> -->
        </div>
      </div>
    </div>
    <div class="login-footer">
      闽ICP备2024057555号 - 泉州润一进出口贸易有限公司
    </div>
  </div>
</template>

<script lang="ts" setup>
import logo from '@/assets/logo.png'
import bg from '@/assets/image/login-bg.svg'
import { wechatQrcode, checkQrcode } from '@/api/wechat'
import { loginApi } from '@/api/user'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
// import { useTheme, LOCAL_THEME_KEY } from '@/hooks/useTheme'
import { setToken } from '@/utils/auth'
import { encryptionValue, decryptionValue } from '@/utils/crypto'
// import type { Theme } from '@/hooks/useTheme'
import QrCode from '@/components/public/qrcode/QrCode.vue'
import type { CODE_TYPE, QRCODE_STATUS } from '@/components/public/qrcode/type'
// import LineInput from '@/components/public/input.vue'

const router = useRouter()
const store = useUserStore()

const QrCodeRef = ref()
const formRef = ref()
const form = ref({
  acc: '',// 16607935
  pwd: '',// 123456
})
const qrCodeTips = ref('打开微信扫一扫')
const loginLoading = ref(false)
const checked = ref(false)
const curTabs = ref(0)

const elBtnType = ref('primary')
// const { theme } = useTheme()

// changeTheme((localStorage.getItem(LOCAL_THEME_KEY) as Theme) || 'light')

// function changeTheme(value: Theme) {
//   theme.value = value
//   if (value == 'light') {
//     elBtnType.value = 'primary'
//   } else if (value == 'dark') {
//     elBtnType.value = 'success'
//   }
// }

const rules = {
  acc: [
    { required: true, message: '请输入账号', trigger: 'blur' },
  ],
  pwd: [
    { required: true, message: '请输入密码', trigger: 'blur' },
  ],
}

function tabsChange(t: number) {
  if(curTabs.value !== t) {
    curTabs.value = t
  }
}

function chageCheckQrcode(code: {
  res: any
  ticket: string
  type: CODE_TYPE
  update: (status: QRCODE_STATUS | string, data: any) => void
  next: () => void
  stop: () => void
}) {
  if(code.res.data.loginStatus === 'WAITING') {
    qrCodeTips.value = '打开微信扫一扫'
  } else if(code.res.data.loginStatus === 'LOGINING') {
    qrCodeTips.value = '请在手机上确认'
  }
}
function checkSuccess(data: any) {
  ElMessage({ message: '登录成功！', type: 'success' })
  setToken(data.token)
  store.getUserInfo().then(res => {
    if(res.businessVO.memberCode) {
      router.replace('/order/list')
    } else {
      router.replace('/model/list')
    }
  })
}

const localStorageKey = 'wnkj-login'
function loginBtn() {
  if (loginLoading.value) return
  formRef.value.validate((valid: any) => {
    if (valid) {
      loginLoading.value = true
      if(checked.value) {
        localStorage.setItem(localStorageKey, encryptionValue(form.value.acc + '#' + form.value.pwd))
      }
      loginApi({
        username: form.value.acc,
        password: form.value.pwd
      }).then( res => {
        // console.log('===login===',res);
        if(res.code == 200){
          ElMessage({ message: '登录成功！', type: 'success' })
          setToken(res.data.access_token)
          // store.setUserRouterList()
          // router.replace('/index')
          store.getUserInfo().then(res => {
            QrCodeRef.value?.clear()
            if(res.businessVO.memberCode) {
              router.replace('/order/list')
            } else {
              router.replace('/model/list')
            }
          })
        } else {
          ElMessage({ message: '登录失败！', type: 'error' })
        }
      }).finally(() => {
        loginLoading.value = false
      })
    } else {
      ElMessage({ message: '请输入用户名和密码！！！', type: 'warning' })
    }
  })
}


const info = localStorage.getItem(localStorageKey)
if(info) {
  const str = decryptionValue(info)
  const obj = str.split('#')
  if(obj.length === 2) {
    checked.value = true
    form.value.acc = obj[0]
    form.value.pwd = obj[1]
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background-color: var(--bg);

  .bg {
    position: absolute;
    width: 92%;
    height: 100%;

    .logo-box {
      padding: 20px 0;
      font-size: 20px;
      font-weight: bold;
      display: flex;
      align-content: center;

      img {
        width: 40px;
        height: 40px;
        margin-right: 15px;
      }
    }

    .bg-img {
      width: 100%;
      height: 85%;
    }
  }

  .login-box {
    position: absolute;
    top: 0;
    right: 10%;
    height: 100%;
    display: flex;
    align-items: center;

    .login-form {
      width: 330px;
      height: 350px;
      padding: 30px;
      box-sizing: border-box;
      border-radius: 20px;
      background-color: var(--login-box-bg);
  
      .login-title {
        font-size: 22px;
        width: 100%;
        text-align: center;
        margin: 10px 0 30px 0;
        color: var(--login-title);
        font-weight: 300;

        .title {
          position: relative;
          width: 50%;
          cursor: pointer;

          &.active {
            color: var(--login-title-active);
            font-weight: bold;
            cursor: none;

            &::after {
              content: '';
              position: absolute;
              bottom: -5px;
              left: 50%;
              width: 40px;
              height: 5px;
              margin-left: -20px;
              border-radius: 3px;
              background: var(--el-color-primary);
            }
          }
        }
      }
      .input-box {
        width: 100%;
      }
      .login-btn {
        width: 100%;
        margin-top: 10px;
      }

      .qrcode-tips {
        text-align: center;
        font-size: 25px;
        font-weight: bold;
        color: var(--el-color-success);
      }
    }
  }

  .change-theme-btn {
    display: flex;
    justify-content: flex-end;
    margin: 10px;
    .el-dropdown-link {
      cursor: pointer;
      color: var(--login-title);
      display: flex;
      align-items: center;
    }
  }

  .login-footer {
    height: 40px;
    line-height: 40px;
    position: fixed;
    bottom: 0;
    width: 100%;
    text-align: center;
    color: var(--text-color);
    font-family: Arial;
    font-size: 12px;
    letter-spacing: 1px;
  }
}
</style>
