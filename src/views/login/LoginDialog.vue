<template>
  <el-dialog
    v-model="loginDialogVisible"
    ref="loginDialogRef"
    :modal-class="router.currentRoute.value.path === '/model/list' ? 'login-modal login-dialog-box' : 'login-dialog-box'"
    :width="isMobileDevice() ? '352px' : '420px'"
    title=""
    align-center
    :lock-scroll="lockScroll"
    :show-close="showClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    append-to-body
    @close="handleClose"
    style="border-radius: 12px; padding: 20px"
  >
    <div class="flex-column content-box">
      <div v-if="loginType">
        <h2 style="color: var(--text-color); text-align: center">微信扫码 1秒登录</h2>
        <div class="title">
          <span>首次登录，需扫码添加企微客服</span>
        </div>
        <QrCode
          v-if="checkedAgreement"
          ref="QrCodeRef"
          :key="QrCodeKey"
          :http-code="handleQrCodeRequest"
          :code-type="0"
          size="large"
          @change="handleChange"
          @success="checkSuccess"
        />
        <div v-else class="qrcode-modal">
          <img src="@/assets/image/agreement-qrcode.png" alt="" />
        </div>
      </div>
      <el-button
        class="switch-btn2"
        link
        type="primary"
        icon="ArrowLeft"
        v-if="!loginType"
        @click="loginType = !loginType"
      >
        返回扫码登录
      </el-button>
      <div class="flex-column login-box login-form" v-show="!loginType">
        <h2 style="font-weight: 500">手机号登录</h2>
        <div class="login-box-tip">
          请使用已绑定的手机号登录，未绑定可扫码登陆后前往个人资料页面进行手机号绑定
        </div>
        <el-form ref="formRef" :model="form" label-position="top" :rules="rules" status-icon @submit.prevent>
          <!-- <el-form-item prop="acc">
            <div class="input-box">
              <el-input placeholder="请输入账号" prefix-icon="User" v-model="form.acc" clearable></el-input>
            </div>
          </el-form-item>
          <el-form-item prop="pwd">
            <div class="input-box">
              <el-input
                placeholder="请输入密码"
                prefix-icon="Lock"
                v-model="form.pwd"
                type="password"
                show-password
              ></el-input>
            </div>
          </el-form-item> -->
          <el-form-item prop="phone" label="" ref="phoneRef">
            <div class="input-box">
              <el-input
                placeholder="请输入手机号"
                prefix-icon="User"
                maxlength="11"
                v-model="form.phone"
              ></el-input>
            </div>
            <template #error="{ error }">
              <div class="form-error-box">
                <el-icon><CircleCloseFilled /></el-icon>
                {{ error }}
              </div>
            </template>
          </el-form-item>
          <el-form-item prop="code" label="">
            <div class="input-box code-box">
              <el-input
                placeholder="请输入验证码"
                prefix-icon="Lock"
                maxlength="4"
                v-model="form.code"
              ></el-input>
              <el-button class="code-btn" link :loading="codeBtnLoading" @click.stop="getPhoneCode">
                <template v-if="codeTime">
                  {{ codeTime + ' 秒后可再次获取' }}
                </template>
                <template v-else>获取验证码</template>
              </el-button>
            </div>
            <template #error="{ error }">
              <div class="form-error-box">
                <el-icon><CircleCloseFilled /></el-icon>
                {{ error }}
              </div>
            </template>
          </el-form-item>

          <!-- <div class="flex-between">
            <el-checkbox v-model="checked">记住密码</el-checkbox>
            <el-button link type="info" @click="openUpdatePwd">忘记密码</el-button>
          </div> -->

          <el-button
            type="primary"
            native-type="submit"
            round
            @click="loginBtn()"
            :loading="loginLoading"
            class="login-btn"
          >
            登录
          </el-button>
        </el-form>
      </div>
      <el-button class="switch-btn" link type="primary" v-if="loginType" @click="loginType = !loginType">
        使用手机号登录
      </el-button>
      <div style="font-size: 12px; color: #8c8c8c" v-if="loginType">未绑定手机号的账户请使用扫码登录</div>
      <!-- <div class="flex-start agreement-box">
        <el-tooltip
          :visible="agreementPopover"
          content="请仔细阅读协议，并同意"
          placement="top"
          effect="light"
          trigger="click"
          :offset="3"
        >
          <el-checkbox
            v-model="checkedAgreement"
            @change="() => (agreementPopover = false)"
            id="agreement"
            size="small"
          ></el-checkbox>
        </el-tooltip>
        <div class="text-n-all tips-label">
          <label for="agreement">我已仔细阅读</label>
          <span class="blue" @click.stop="openDialog(1)">《用户协议》</span>
          和
          <span class="blue" @click.stop="openDialog(2)">《隐私政策》</span>
          <label for="agreement">，接受并同意遵守所有条款</label>
        </div>
      </div> -->
      <div class="flex-center agreement-box">
        <div class="text-n-all tips-label">
          <label>注册登录即代表同意</label>
          <span class="blue" @click.stop="openDialog(1)">《用户协议》</span>
          和
          <span class="blue" @click.stop="openDialog(2)">《隐私政策》</span>
        </div>
      </div>
      <!-- <div class="flex-between" style="gap: 20px">
        <div class="tips">泉州润一进出口贸易有限公司</div>
        <div class="flex-start tips">
          <span>备案号：</span>
          <el-link type="primary" target="_blank" href="https://beian.miit.gov.cn/">
            闽ICP备2024057555号
          </el-link>
        </div>
      </div> -->
    </div>

    <ImageCodeDialog />
    <el-dialog
      v-model="bindDialogVisible"
      ref="bindDialogRef"
      width="350px"
      title=""
      align-center
      :lock-scroll="false"
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      destroy-on-close
      append-to-body
      style="border-radius: 12px; padding: 10px"
    >
      <div class="flex-column content-box">
        <div v-if="bindType" style="padding-bottom: 30px">
          <h2 class="bind-title">
            首次登录需打开微信
            <br />
            扫一扫绑定后登录
          </h2>
          <div class="title">
            <span>注册成功后使用微信扫一扫&nbsp;1秒即可登录</span>
          </div>
          <QrCode
            ref="BindQrCodeRef"
            :key="QrCodeKey"
            :http-code="handleBindRequest"
            :load="handleLoadBindQrCode"
            size="large"
            @success="bindCheckSuccess"
          />
        </div>
        <div class="flex-column login-box" v-else>
          <h2 style="margin: 0">绑定手机号即可进入平台</h2>
          <div class="title">
            <span>注册成功后使用微信扫一扫&nbsp;1秒即可登录</span>
          </div>
          <el-form
            ref="bindFormRef"
            :model="form"
            label-position="top"
            :rules="rules"
            status-icon
            @submit.prevent
          >
            <el-form-item ref="phoneRef" prop="phone" label="">
              <div class="input-box">
                <el-input
                  placeholder="请输入手机号"
                  prefix-icon="User"
                  maxlength="11"
                  v-model="form.phone"
                ></el-input>
              </div>
              <template #error="{ error }">
                <div class="form-error-box">
                  <el-icon><CircleCloseFilled /></el-icon>
                  {{ error }}
                </div>
              </template>
            </el-form-item>
            <el-form-item prop="code" label="">
              <div class="input-box code-box">
                <el-input
                  placeholder="请输入验证码"
                  prefix-icon="Lock"
                  maxlength="4"
                  v-model="form.code"
                ></el-input>
                <el-button class="code-btn" link :loading="codeBtnLoading" @click.stop="getPhoneCode">
                  <template v-if="codeTime">
                    {{ codeTime + ' 秒后可再次获取' }}
                  </template>
                  <template v-else>获取验证码</template>
                </el-button>
              </div>
              <template #error="{ error }">
                <div class="form-error-box">
                  <el-icon><CircleCloseFilled /></el-icon>
                  {{ error }}
                </div>
              </template>
            </el-form-item>
            <el-button
              type="primary"
              native-type="submit"
              round
              @click="bindFormValidate()"
              :loading="loginLoading"
              class="login-btn"
            >
              登录
            </el-button>
          </el-form>
        </div>
      </div>
    </el-dialog>
    <!-- <ResetPassword
      ref="ResetPasswordRef"
      :title="pwdTitle"
      is-check-identity
      is-login-reset
      append-to-body
      :close-on-click-modal="false"
      @checkSuccess="pwdTitle = '修改密码'"
      @toLogin="toCodeLogin"
    /> -->
  </el-dialog>
</template>

<script setup lang="ts">
import QrCode from '@/components/public/qrcode/QrCode.vue'
// import ResetPassword from '@/views/center/components/ResetPassword.vue'
import ImageCodeDialog from '@/views/login/ImageCodeDialog.vue'
import { loginApi, phoneLoginApi, checkPhone } from '@/api/user'
import { wechatQrcode } from '@/api/wechat'
import { orderListCount } from '@/api/order'
import { setToken, getChannelCode } from '@/utils/auth'
import { encryptionValue, decryptionValue } from '@/utils/crypto'
import { useUserStore } from '@/stores/modules/user'
import { computed, ref, toRefs } from 'vue'
import { ElLoading, ElMessage, ElNotification, ElFormItem } from 'element-plus'
import { useRouter } from 'vue-router'
import { phone_reg } from '@/utils/RegExp'
import { showAgreementDialog } from '@/views/wnDialogFun/index'
import useImgOrPhoneCode from '@/hooks/useImgOrPhoneCode'
import { loginDialogVisible, closeLogin, loginSuccessAction } from '@/hooks/useLogin'
import { isMobileDevice } from '@/utils/public'

const props = defineProps({
  showClose: {
    type: Boolean,
    default: true,
  },
})

const router = useRouter()
const store = useUserStore()

const lockScrollRouterList: any[] = ['model-details']
const lockScroll = computed(() => {
  if (lockScrollRouterList.includes(router.currentRoute.value.name)) {
    return true
  }
  return false
})

const QrCodeKey = ref(0)
const loginType = ref(true)
const formRef = ref()
// const ResetPasswordRef = ref<InstanceType<typeof ResetPassword>>()
const QrCodeRef = ref<InstanceType<typeof QrCode>>()
const loginLoading = ref(false)
const checked = ref(false)
const agreementPopover = ref(false)
const checkedAgreement = ref(true)

const {
  codeTime,
  codeBtnLoading,
  phoneForm,
  sendPhoneCode, // 发送手机验证码
} = useImgOrPhoneCode()

const form = ref({
  acc: '', // 16607935
  pwd: '', // 123456
  ...toRefs(phoneForm.value),
})
// const pwdTitle = ref('忘记密码')

const bindFormRef = ref()
const bindDialogVisible = ref(false)
const bindType = ref(true)
const bindDialogUrl = ref('')
const bindDialogTicket = ref('')

const rules = {
  acc: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: phone_reg, message: '请输入正确的手机号', trigger: 'change' },
  ],
  pwd: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  code: [{ required: true, message: '请输入验证码', trigger: 'manul' }],
}

const loginDialogRef = ref()

const localStorageKey = 'wnkj-login'
function loginBtn() {
  if (!checkedAgreement.value) {
    agreementPopover.value = true
    return
  }
  if (loginLoading.value) return
  formValidate()
  // accLogin()
}
// 手机号登录校验
function formValidate() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      phoneLogin()
    } else {
      ElMessage.warning('请输入手机号和验证码！')
    }
  })
}
// 绑定手机号校验
function bindFormValidate() {
  bindFormRef.value.validate((valid: any) => {
    if (valid) {
      phoneLogin(bindDialogTicket.value)
    } else {
      ElMessage.warning('请输入手机号和验证码！')
    }
  })
}

// 手机号登录
function phoneLogin(ticket: string = '') {
  loginLoading.value = true
  phoneLoginApi({
    phone: form.value.phone,
    phoneCaptcha: form.value.code,
    ticket,
    linkCode: getChannelCode() || undefined,
  })
    .then(res => {
      if (res.code == 200 && res.data) {
        if (res.data.loginStatus == 'LOGIN_SUCCESS') {
          ElMessage({ message: '登录成功！', type: 'success' })
          setToken(res.data.token)
          QrCodeRef.value?.clear()
          handleLoginSuccess()
        } else if (res.data.loginStatus == 'LOGIN_NO_WE_CHAT') {
          bindDialogUrl.value = res.data.qrcode
          bindDialogTicket.value = res.data.ticket
          bindType.value = true
          bindDialogVisible.value = true
        }
      } else if (res.msg.indexOf('验证码错误') > -1) {
        ElMessage({
          customClass: 'wn-black-message',
          message: res.msg,
        })
      } else {
        ElMessage.error('登录失败！')
      }
    })
    .finally(() => {
      loginLoading.value = false
    })
}

// 账号密码登录
function accLogin() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      loginLoading.value = true
      if (checked.value) {
        localStorage.setItem(localStorageKey, encryptionValue(form.value.acc + '#' + form.value.pwd))
      }
      loginApi({
        username: form.value.acc,
        password: form.value.pwd,
      })
        .then(res => {
          if (res.code == 200) {
            ElMessage.success('登录成功！')
            setToken(res.data.access_token)
            QrCodeRef.value?.clear()
            bindDialogVisible.value = false
            handleLoginSuccess()
          } else {
            ElMessage.error('登录失败！')
          }
        })
        .finally(() => {
          loginLoading.value = false
        })
    } else {
      ElMessage.warning('请输入用户名和密码！')
    }
  })
}

function handleClose() {
  closeLogin()
}

function handleQrCodeRequest(params: any) {
  const code = getChannelCode()
  if (code) {
    params.code = code
  }
  params.domain = window.location.origin
  return wechatQrcode(params)
}

function openDialog(type: number) {
  if (type === 1) {
    showAgreementDialog('user').then(() => {
      checkedAgreement.value = true
    })
  }
  if (type === 2) {
    showAgreementDialog('privacy').then(() => {
      checkedAgreement.value = true
    })
  }
}

// 获取手机验证码
// const phoneRef = ref<InstanceType<typeof ElFormItem>>()
const phoneRef = ref()

function getPhoneCode() {
  if (!checkedAgreement.value) {
    agreementPopover.value = true
    return
  }
  if (codeTime.value) return
  if (bindDialogVisible.value) {
    bindFormRef.value.validateField('phone', (valid: any) => {
      if (valid) {
        checkPhone({ phone: form.value.phone }).then(res => {
          if (res.data) {
            sendPhoneCode({ phoneNum: form.value.phone, ticket: bindDialogTicket.value, isRegister: true })
          } else {
            phoneRef.value.validateMessage = '此手机号暂未绑定，无法获取验证码'
            phoneRef.value.validateState = 'error'
          }
        })
      }
    })
  } else {
    formRef.value.validateField('phone', (valid: any) => {
      if (valid) {
        checkPhone({ phone: form.value.phone }).then(res => {
          if (res.data) {
            sendPhoneCode({ phoneNum: form.value.phone })
          } else {
            phoneRef.value.validateMessage = '此手机号暂未绑定，无法获取验证码'
            phoneRef.value.validateState = 'error'
          }
        })
      }
    })
  }
}

function handleLoginSuccess() {
  const el_loading = ElLoading.service({
    lock: true,
    text: '',
    background: 'rgba(255, 255, 255, 0.5)',
  })
  store
    .getUserInfo()
    .then(res => {
      orderListCount()
        .then(res => {
          if (loginSuccessAction.value.fun) {
            loginSuccessAction.value.fun()
          }
          if (loginSuccessAction.value.eval) {
            eval(loginSuccessAction.value.eval)
          }
          if (loginSuccessAction.value.intercept) {
            closeLogin()
            el_loading.close()
            return
          }
          if (res.data > 0 && store.isViped()) {
            window.location.href = window.location.origin + '/workbench/info'
          } else {
            window.location.href = window.location.origin
          }
          closeLogin()
          el_loading.close()
        })
        .catch(() => {
          window.location.href = window.location.origin
          closeLogin()
          el_loading.close()
        })
    })
    .catch(() => {
      window.location.href = window.location.origin
      el_loading.close()
    })
}

// function openUpdatePwd() {
//   pwdTitle.value = '忘记密码'
//   ResetPasswordRef.value?.open()
// }

// function toCodeLogin() {
//   QrCodeKey.value++
//   loginType.value = true
// }

function handleChange({ res, ticket }: { res: any; ticket: string }) {
  if (res.data.loginStatus === 'LOGIN_NO_PHONE') {
    form.value.phone = ''
    form.value.code = ''
    bindType.value = false
    bindDialogUrl.value = ''
    bindDialogTicket.value = ticket
    bindDialogVisible.value = true
  }
}
function checkSuccess(data: any) {
  ElMessage.success('登录成功！')
  setToken(data.token)
  handleLoginSuccess()
}
function handleLoadBindQrCode() {
  ElNotification({
    title: '绑定提示',
    message: '请重新获取验证码登录',
    type: 'warning',
    duration: 1000 * 15,
  })
  bindDialogVisible.value = false
}
function handleBindRequest() {
  return new Promise((resolve, reject) => {
    if (bindDialogUrl.value && bindDialogTicket.value) {
      resolve({
        data: {
          qrcode: bindDialogUrl.value,
          ticket: bindDialogTicket.value,
        },
      })
    } else {
      reject('error')
    }
  })
}
function bindCheckSuccess(data: any) {
  ElMessage.success('绑定成功！')
  setToken(data.token)
  bindDialogVisible.value = false
  if (loginSuccessAction.value.fun) {
    loginSuccessAction.value.fun()
  }
  if (loginSuccessAction.value.eval) {
    eval(loginSuccessAction.value.eval)
  }
  if (loginSuccessAction.value.intercept) {
    closeLogin()
    return
  }
  window.location.href = window.location.origin
  closeLogin()
}

const info = localStorage.getItem(localStorageKey)
if (info) {
  const str = decryptionValue(info)
  const obj = str.split('#')
  if (obj.length === 2) {
    checked.value = true
    form.value.acc = obj[0]
    form.value.pwd = obj[1]
  }
}
</script>

<style scoped lang="scss">
:deep(.el-input__wrapper) {
  border-radius: 20px;
}
:deep(.el-form) {
  .is-success {
    .el-input__suffix-inner {
      color: var(--el-color-primary);
    }
  }
}

.form-error-box {
  color: var(--el-color-danger);
  font-size: 12px;
  line-height: 20px;
  position: absolute;
  top: 100%;
  left: 15px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.content-box {
  padding: 0 10px 10px;
  pointer-events: auto;

  .qrcode-modal {
    width: 180px;
    height: 180px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
    }

    &::after {
      content: '确认下方《用户协议》及《隐私政策》后显示二维码';
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.65);
      color: #fff;
      font-size: 12px;
      text-align: center;
      padding: 10px;
      box-sizing: border-box;
    }
  }

  .login-box {
    width: 100%;
    // margin-top: 20px;

    &-tip {
      color: #8c8c8c;
      padding: 0px 51px;
      text-align: center;
      font-size: 12px;

      @include mediaTo('phone') {
        padding: 0px 20px;
      }
    }

    form {
      width: 80%;
    }

    :deep(.el-form) {
      .el-form-item__label {
        color: var(--el-color-info);

        &::before {
          content: '';
        }
      }
      .el-form-item {
        margin-bottom: 14px;
      }
    }

    h2 {
      // margin-bottom: 15px;
      margin: 0px 0px 0px;
      color: var(--text-color);
    }
    .input-box {
      width: 100%;
      margin-top: 5px;

      :deep(.el-input) {
        --el-input-height: 40px;
      }
    }
    .code-box {
      position: relative;
      :deep(.el-input) {
        .el-input__suffix {
          display: none;
        }
      }

      .code-btn {
        --el-font-size-base: 13px;
        position: absolute;
        top: 0;
        right: 0;
        color: #169bd5;
        width: 125px;
        height: 40px;
        padding: 0 5px;

        &::before {
          content: '';
          display: inline-block;
          height: 14px;
          border-left: 1px solid #e8ebf0;
          position: absolute;
          left: 0;
          top: calc(50% - 7px);
        }
      }
    }
    .login-btn {
      width: 100%;
      margin: 8px 0 20px;
      padding: 18px 15px;
    }
  }

  .switch-btn {
    margin: 16px 0 0px;
  }
  .switch-btn2 {
    position: absolute;
    top: 17px;
    left: 10px;
  }

  h2 {
    margin: 0 0 2px;
  }

  .title {
    font-size: 13px;
    color: var(--text-color);
    margin: 0 0 22px;
    text-align: center;
    position: relative;

    span {
      position: relative;
      z-index: 1;
    }

    &::before {
      content: '';
      display: block;
      position: absolute;
      bottom: 0;
      right: 0;
      z-index: 0;
      width: 100%;
      height: 10px;
      background: linear-gradient(90deg, rgba(225, 218, 255, 0) 0%, #e1daff 44%, #a0c8ff 100%);
      border-radius: 1px;
    }
  }

  .tips {
    text-align: center;
    font-size: 12px;
    color: var(--el-color-info);
    margin-top: 5px;
    align-content: baseline;

    .el-link {
      font-size: 12px;
      // line-height: 17px;
      // top: -1px;
    }
  }
  .agreement-box {
    width: 300px;
    margin: 0 0 10px;
    align-items: baseline;

    :deep(.el-checkbox) {
      margin-right: 8px;
      position: relative;
      top: 1px;
    }

    .tips-label {
      color: var(--el-color-info);
      font-size: 12px;

      .blue {
        cursor: pointer;
        color: var(--el-color-primary);
      }
    }
  }

  .bind-title {
    text-align: center;
    color: var(--text-color);
  }
}
</style>

<style>
</style>
