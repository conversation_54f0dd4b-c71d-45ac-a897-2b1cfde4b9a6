<template>
  <el-dialog
    v-model="imgCodeDialogVisible"
    ref="imgCodeDialogRef"
    width="320px"
    title=""
    align-center
    :lock-scroll="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    destroy-on-close
    :z-index="999999"
    append-to-body
    style="padding: 10px;border-radius: 16px"
  >
    <div class="flex-column gap-5 img-code-box">
      <div class="img-code-title">请按照图形输入字母数字</div>
      <div class="flex-column img-code" v-loading="imgCodeLoading" @click="getImageCode()">
        <img :src="imgCodeUrl" alt="" />
      </div>
      <div class="img-code-tips" @click="getImageCode()">看不清？换一张</div>
      <div class="code-input">
        <el-input placeholder="请输入图片中的字符" v-model="imgCode"></el-input>
      </div>
      <div style="width: 100%; margin-top: 20px; text-align: center">
        <el-button
          style="width: 120px; color: #fff"
          type="primary"
          round
          :disabled="imgCodeLoading"
          :loading="imgCodeBtnLoading"
          @click="checkImageCode()"
        >
          确 定
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref } from 'vue'
import useImgOrPhoneCode from '@/hooks/useImgOrPhoneCode'

const {
  imgCodeDialogVisible,
  imgCode,
  imgCodeUrl,
  imgCodeLoading,
  getImageCode, // 获取图片验证码
  checkImgCode, // 检测图片验证码
} = useImgOrPhoneCode()

const imgCodeBtnLoading = ref(false)

// 检测图片验证码
function checkImageCode() {
  if (imgCodeLoading.value) return
  if (!imgCode.value) {
    ElMessage.warning('请输入验证码！')
    return
  }
  imgCodeBtnLoading.value = true
  checkImgCode(() => {
    imgCodeBtnLoading.value = false
  })
}
</script>

<style scoped lang="scss">
.img-code-box {
  padding: 0 10px 10px;

  .img-code-title {
    color: var(--text-color);
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 10px;
  }

  .code-input {
    width: 80%;

    :deep(.el-input__wrapper) {
      padding: 4px 8px;
    }
  }
  .img-code {
    width: 90px;
    height: 38px;
    cursor: pointer;

    img {
      width: 100%;
      height: 100%;
    }
  }
  .img-code-tips {
    font-size: 12px;
    color: var(--text-gray-color);
    margin: 5px 0 12px;
    cursor: pointer;
  }
}
</style>
