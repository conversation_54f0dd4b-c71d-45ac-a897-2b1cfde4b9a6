<template>
  <div class="pages">
    <OrderDetailsHeader :data="orderData" @action="handleButtonAction" @countDownEnd="countDownEnd" />

    <OrderDetailsInfo v-loading="loading" :data="orderData" @refresh="getOrderDetails" />

    <ConfrimDeliverGoods
      ref="ConfrimDeliverGoodsRef"
      :title="ConfrimDeliverGoodsTitle"
      :flag="logisticFlag"
      :first="ConfrimDeliverGoodsFirst"
      :reissue-disabled="ConfrimDeliverGoodsDisable"
      @success="getOrderDetails" />

    <ModelAddressUpdateTips ref="ModelAddressUpdateTipsRef" @change="addressUpdateTipsChange" />
      
    <ConfirmUpload ref="ConfirmUploadRef" @success="getOrderDetails" />
    <MessageBoxDialog
      ref="MessageBoxDialogRef"
      title=""
      :content="MsgBoxDialogContent"
      :warningText="MsgBoxDialogWarning"
      confirmText="确认"
      @confirm="msgBoxConfirm"
    />

    <!-- <CheckAndAcceptMaterial ref="CheckAndAcceptMaterialRef" @success="getOrderDetails" /> -->
  </div>
</template>

<script setup lang="ts">
import OrderDetailsHeader from '@/views/order/components/OrderDetailsHeader.vue'
import OrderDetailsInfo from '@/views/order/components/OrderDetailsInfo.vue'
import ConfrimDeliverGoods from '@/views/order/components/dialog/ConfirmDeliverGoods.vue'
import ModelAddressUpdateTips from '@/views/order/components/dialog/ModelAddressUpdateTips.vue'
import ConfirmUpload from '@/views/order/components/dialog/ConfirmUpload.vue'
import MessageBoxDialog from '@/components/public/dialog/MessageBoxDialog.vue'
// import CheckAndAcceptMaterial from '@/views/order/components/dialog/CheckAndAcceptMaterial.vue'
import { useRoute, useRouter } from 'vue-router'
import { orderDetails } from '@/api/order'
import { computed, nextTick, ref } from 'vue'
import useOrderApi from '@/hooks/useOrderApi'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'

const {
  handleCancelOrder,
  handleConfirmProduct,
  handleConfirmModel,
  checkModelAddress
} = useOrderApi()

const store =  useUserStore()
const route = useRoute()
const router = useRouter()

const orderData = ref<{
  [x: string]: any
}>({})
const shipInfoVO = ref()
const loading = ref(false)

const ConfrimDeliverGoodsRef = ref<InstanceType<typeof ConfrimDeliverGoods>>()
const ConfrimDeliverGoodsTitle = ref('确认发货')
const ConfrimDeliverGoodsFirst = ref(true)
const ConfrimDeliverGoodsDisable = ref(false)
const reissue = ref<0 | 1>(1)
const logisticFlag = computed(() => {
  if (orderData.value?.orderLogisticSimpleVOS?.length) {
    return orderData.value.orderLogisticSimpleVOS[0]?.logisticFlag === 1
  }
  return false
})
const ModelAddressUpdateTipsRef = ref<InstanceType<typeof ModelAddressUpdateTips>>()
const ConfirmUploadRef = ref<InstanceType<typeof ConfirmUpload>>()
const MessageBoxDialogRef = ref<InstanceType<typeof MessageBoxDialog>>()
const MsgBoxDialogContent = ref('确认成品内容无误？')
const MsgBoxDialogWarning = ref('为了保证您的售后权益，请先确认成品内容无误后，再确认成品完成')
const MsgBoxDialogType = ref('确认成品')
const MsgBoxDialogOrderId = ref(0)
// const CheckAndAcceptMaterialRef = ref<InstanceType<typeof CheckAndAcceptMaterial>>()

function getOrderDetails() {
  if(!route.params.orderId) {
    window.location.href = '/model/list'
    return
  }
  loading.value = true
  orderDetails(route.params.orderId).then((res) => {
    // console.log(res);
    orderData.value = res.data
    if (res.data.shipInfoVO) {
      shipInfoVO.value = res.data.shipInfoVO
      if(route.query.t && shipInfoVO.value.logisticInfo?.length) {
        shipInfoVO.value.logisticInfo[0].maxHeight = shipInfoVO.value.logisticInfo[0].logisticInfo?.length ? shipInfoVO.value.logisticInfo[0].logisticInfo.length * 70 +'px' : '0px'
      }
    }
  }).finally(() => loading.value = false)
}

function handleButtonAction(btn: string) {
  // console.log(btn);
  switch (btn) {
    case '取消订单':
      handleCancelOrder(orderData.value.orderSimpleVO.orderNum, () => getOrderDetails())
      break
    case '立即支付':
      if (!store.isVip()) {
        ElMessage({
          type: 'warning',
          message: store.isViped() ? '您的会员已过期，请开通会员后再进行操作！' : '请先开通会员！',
          duration: 2000,
        })
      } else {
        router.push({ name: 'order-pay', state: {orderNum: orderData.value.orderSimpleVO.orderNum} })
      }
      break
    case '发货':
      ConfrimDeliverGoodsTitle.value = '订单发货信息'
      ConfrimDeliverGoodsFirst.value = true
      ConfrimDeliverGoodsDisable.value = false
      reissue.value = 1
      // 检测模特收货地址是否更新
      checkModelAddressChange(orderData.value.orderVideoSimpleVO.id)
      break
    case '添加物流':
      ConfrimDeliverGoodsTitle.value = '添加物流'
      // ConfrimDeliverGoodsFirst.value = false
      ConfrimDeliverGoodsDisable.value = true
      // reissue.value = 0
      checkModelAddressChange(orderData.value.orderVideoSimpleVO.id)
      break
    case '可以上传':
      ConfirmUploadRef.value?.open(orderData.value.orderVideoSimpleVO.id, false)
      break;
    case '取消上传':
      ConfirmUploadRef.value?.open(orderData.value.orderVideoSimpleVO.id, true)
      break;
    case '确认模特':
      MsgBoxDialogOrderId.value = orderData.value.orderVideoSimpleVO.id
      MsgBoxDialogContent.value = '确认模特？'
      MsgBoxDialogWarning.value = ''
      MsgBoxDialogType.value = '确认模特'
      MessageBoxDialogRef.value?.open()
      break;
    case '确认成品':
      MsgBoxDialogOrderId.value = orderData.value.orderVideoSimpleVO.id
      MsgBoxDialogContent.value = '确认成品内容无误？'
      MsgBoxDialogWarning.value = '为了保证您的售后权益，请先确认成品内容无误后，再确认成品完成'
      MsgBoxDialogType.value = '确认成品'
      MessageBoxDialogRef.value?.open()
      break;
    default:
      break;
  }
}
function msgBoxConfirm(close: () => void) {
  if(MsgBoxDialogType.value == '确认成品') {
    handleConfirmProduct(MsgBoxDialogOrderId.value, () => {
      close()
      getOrderDetails()
    })
  } else if(MsgBoxDialogType.value == '确认模特') {
    handleConfirmModel(MsgBoxDialogOrderId.value, () => {
      close()
      getOrderDetails()
    })
  }
}
// 检测模特收货地址是否更新
function checkModelAddressChange(id: any) {
  ConfrimDeliverGoodsRef.value?.open(id, reissue.value)
  // checkModelAddress(id, (res: any) => {
  //   if(res.data.code == 200){
  //     ConfrimDeliverGoodsRef.value?.open(id, reissue.value)
  //   }
  //   if(res.data.code == 500){
  //     ModelAddressUpdateTipsRef.value?.open(id)
  //   }
  // })
}
// 模特收货地址更新确认
function addressUpdateTipsChange(id: any, y: number) {
  if(y === 0) {
    ConfrimDeliverGoodsRef.value?.open(id, reissue.value)
  }
}

// 订单倒计时结束
function countDownEnd() {
  console.log('countDownEnd');
  nextTick(() => {
    getOrderDetails()
  })
}

getOrderDetails()
</script>

<style scoped lang="scss">
.pages {
  min-width: 1200px;
  padding: 10px 30px 0 25px;
}
</style>
