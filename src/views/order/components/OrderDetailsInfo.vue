<template>
  <div class="flex-start gap-10 info-box">
    <div class="left-box">
      <InfoCard
        v-if="
          orderVideoVO?.status >= ORDER_STATUS['需确认'] &&
          (orderVideoUploadlinkVO != null || orderFeedBackMaterialVO?.length)
        "
        type="video"
        :orderVideoVO="orderVideoVO"
        :orderVideoUploadlinkVO="orderVideoUploadlinkVO"
        :orderFeedBackMaterialVO="orderFeedBackMaterialVO"
        @action="handleAction"
      />
      <InfoCard type="product" :orderVO="orderVO" :orderVideoVO="orderVideoVO" :shipInfoVO="shipInfoVO" />
      <InfoCard type="order" :orderVO="orderVO" :orderVideoVO="orderVideoVO" />
    </div>

    <div class="right-box">
      <InfoCard
        v-if="orderVideoVO?.intentionModel || orderVideoVO?.shootModel"
        type="model"
        :steps="orderVideoFlowNodeDiagramVOS"
        :orderVideoVO="orderVideoVO"
      />
      <InfoCard
        v-if="logisticVO && logisticVO[0]?.number && orderVideoVO?.isObject === 0"
        type="logistics"
        :logisticVO="logisticVO"
        @action="handleAction"
      />
      <InfoCard type="records" :steps="orderVideoOperateVOS" />
    </div>

    <AllLogisticsTab ref="AllLogisticsTabRef" />
    <CheckAndAcceptMaterial ref="CheckAndAcceptMaterialRef" @success="$emit('refresh')" />
  </div>
</template>

<script setup lang="ts">
// import Title from '@/components/public/Title.vue'
import InfoCard from '@/views/order/components/InfoCard.vue'
import AllLogisticsTab from '@/views/order/components/dialog/AllLogisticsTab.vue'
import CheckAndAcceptMaterial from '@/views/order/components/dialog/CheckAndAcceptMaterial.vue'
import { computed, ref } from 'vue'
import { ORDER_STATUS } from '@/utils/order'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['refresh'])

const AllLogisticsTabRef = ref<InstanceType<typeof AllLogisticsTab>>()
const CheckAndAcceptMaterialRef = ref<InstanceType<typeof CheckAndAcceptMaterial>>()

// 订单信息
const orderVO = computed(() => props.data.orderSimpleVO)
const orderVideoVO = computed(() => props.data.orderVideoSimpleVO)
// 视频上传信息
const orderVideoUploadlinkVO = computed(() => props.data.orderVideoUploadLinkSimpleVO)
// 反馈素材信息
const orderFeedBackMaterialVO = computed(() => props.data.orderFeedBackSimpleVOS || [])
// 订单状态节点信息
const orderVideoFlowNodeDiagramVOS = computed(() => props.data.orderVideoFlowNodeDiagramVOS || [])
// 发货
const shipInfoVO = computed(() => {
  if (props.data.orderLogisticSimpleVOS?.length) {
    return {
      ...props.data.orderLogisticSimpleVOS[0].shippingInfoSimpleVO,
      reissue: props.data.orderLogisticSimpleVOS[0].reissue,
    }
  }
  return {}
})
// 物流信息
const logisticVO = computed(() => {
  if (props.data.orderLogisticSimpleVOS?.length) {
    return props.data.orderLogisticSimpleVOS.map((item: any) => {
      return {
        ...item,
        steps: item.logisticInfo?.map((logs: any) => ({
          title: logs.mainStatus,
          description: (logs.city ? '[' + logs.city + '] ' : '') + (logs.description || '-'),
          time: logs.curTime,
        })) || [],
      }
    })
  }
  return null
})
// 订单流转记录
const orderVideoOperateVOS = computed(() => {
  if (props.data.orderVideoOperateVOS) {
    return props.data.orderVideoOperateVOS.map((item: any) => ({
      title: item.eventName,
      description: item.eventContent,
      time: item.eventExecuteTime,
      recordId: item.eventName === '修改订单信息' ? props.data.orderVideoSimpleVO.id : '',
    }))
  }
  return []
})

function handleAction(action: string) {
  if (action === '所有物流') {
    AllLogisticsTabRef.value?.open(logisticVO.value)
  } else if (action === '验收素材') {
    CheckAndAcceptMaterialRef.value?.open(orderVideoVO.value.id, orderVideoVO.value.status, {
      link: orderVideoVO.value.productLink,
      shootModelType: orderVideoVO.value.shootModel?.type || '',
      platform: orderVideoVO.value.platform,
      modelType: orderVideoVO.value.modelType,
      nation: orderVideoVO.value.shootingCountry,
    })
  }
}
</script>

<style scoped lang="scss">
.info-box {
  margin-top: 20px;
  align-items: flex-start;

  .left-box {
    flex: 1;
    width: calc(100% - 360px - 10px);
  }
  .right-box {
    width: 360px;
  }
}
</style>
