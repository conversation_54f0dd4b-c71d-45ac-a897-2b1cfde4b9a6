<template>
  <div class="info-card">
    <template v-if="type === 'video'">
      <div class="title" v-if="orderVideoUploadlinkVO != null">视频上传信息</div>

      <div class="content" v-if="orderVideoUploadlinkVO != null">
        <el-row>
          <el-col :span="8">
            <div class="label">视频标题</div>
            <div class="text">{{ orderVideoUploadlinkVO?.videoTitle || '-' }}</div>
          </el-col>
          <el-col :span="8">
            <div class="label">视频封面</div>
            <el-button v-if="orderVideoUploadlinkVO?.videoCover" link type="primary" @click="look('video')">
              查看
            </el-button>
            <div v-else>-</div>
          </el-col>
          <el-col :span="8">
            <div class="label">提交人</div>
            <div class="text" v-if="orderVideoUploadlinkVO?.object === 1">
              {{ orderVideoUploadlinkVO?.company?.name || orderVideoUploadlinkVO?.company?.nickName || '-' }}
            </div>
            <div class="text" v-else-if="orderVideoUploadlinkVO?.object === 2">
              {{ orderVideoUploadlinkVO?.back?.name || '-' }}
            </div>
            <div class="text" v-else>-</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="16">
            <el-tooltip
              effect="dark"
              content="确认无误的视频将会上传至该链接中"
              placement="top-start"
              :offset="5"
            >
              <div class="label">上传链接</div>
            </el-tooltip>
            <div class="one-ell productLink">
              <el-link
                v-if="orderVideoUploadlinkVO?.needUploadLink"
                type="primary"
                target="_blank"
                :href="orderVideoUploadlinkVO.needUploadLink"
              >
                {{ orderVideoUploadlinkVO.needUploadLink }}
              </el-link>
              <span v-else>-</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">提交时间</div>
            <div class="text">{{ orderVideoUploadlinkVO?.time || '-' }}</div>
          </el-col>
        </el-row>
      </div>

      <div class="flex-between title" v-show="orderFeedBackMaterialVO?.length">
        <span>素材信息</span>
        <el-button
          v-if="
            ORDER_STATUS['需确认'] === orderVideoVO?.status &&
            (store.isOwnerAcc() || store.userInfo.account == orderVideoVO?.createOrderUserAccount)
          "
          class="btn-mini"
          type="primary"
          round
          size="small"
          @click="handleAction('验收素材')"
        >
          确认
        </el-button>
      </div>

      <div class="content" v-if="orderFeedBackMaterialVO?.length">
        <el-row v-for="(item, index) in orderFeedBackMaterialVO" :key="index">
          <el-col :span="16">
            <el-tooltip effect="dark" placement="top-start" :offset="5">
              <template #content="{ row }">
                <div>原{{ item.type == '1' ? '视频' : '照片' }}地址，可供下载</div>
              </template>
              <div class="label" style="display: flex; width: 110px">
                素材链接
                <span style="margin-left: 20px">{{ item.type == '1' ? '视频' : '照片' }}</span>
              </div>
            </el-tooltip>
            <div class="one-ell productLink">
              <el-link v-if="item.url" type="primary" target="_blank" :href="item.url">
                {{ item.url }}
              </el-link>
              <span v-else>-</span>
            </div>
            <el-tag style="margin-left: 5px" v-if="item.isNew" type="danger" effect="dark" size="small">
              new
            </el-tag>
          </el-col>
          <el-col :span="8">
            <div class="label">反馈时间</div>
            <div class="text">{{ item.createTime || '-' }}</div>
          </el-col>
        </el-row>
      </div>
    </template>

    <template v-else-if="type === 'product'">
      <div class="flex-between title">
        <span>产品信息</span>
        <!-- <el-tooltip
          v-if="orderVideoVO?.lastChangeTime"
          effect="dark"
          :content="`最后变更时间：${orderVideoVO.lastChangeTime}`"
          placement="top"
        >
          <el-button class="btn-mini" type="warning" round size="small">变更</el-button>
        </el-tooltip> -->
      </div>

      <div class="flex-start gap-10 content">
        <svg v-if="!orderVideoVO?.productPic" aria-hidden="true" style="width: 100px; height: 100px">
          <use xlink:href="#icon-zanwutupian"></use>
        </svg>
        <el-image
          v-else
          style="width: 100px; height: 100px"
          :src="$picUrl + orderVideoVO?.productPic + '!thumbnail200'"
          fit="scale-down"
          @click="showViewer([orderVideoVO?.productPic], { scale: 'thumbnail200' })"
        >
          <template #error>
            <div class="img-error">加载失败</div>
          </template>
        </el-image>
        <div style="width: 100%">
          <el-row>
            <el-col :span="16" style="margin-bottom: 3px">
              <div class="label">中文名称</div>
              <div class="text">{{ orderVideoVO?.productChinese }}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="16" style="margin-bottom: 3px">
              <div class="label">产品名称</div>
              <div class="text">{{ orderVideoVO?.productEnglish }}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="16" style="margin-bottom: 3px">
              <div class="label">产品链接</div>
              <div class="one-ell productLink">
                <el-link
                  v-if="orderVideoVO?.productLink && http_reg.test(orderVideoVO.productLink)"
                  type="primary"
                  target="_blank"
                  :href="orderVideoVO?.productLink"
                >
                  {{ orderVideoVO?.productLink }}
                </el-link>
                <span v-else>{{ orderVideoVO?.productLink }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="title">拍摄信息</div>

      <div class="content">
        <el-row>
          <el-col :span="8">
            <div class="label">使用平台</div>
            <div class="text">
              <template v-for="dict in biz_model_platform" :key="dict.value">
                <span v-if="dict.value == orderVideoVO?.platform">{{ dict.label }}</span>
              </template>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">拍摄国家</div>
            <div class="text">
              <template v-for="dict in biz_nation" :key="dict.value">
                <span v-if="dict.value == orderVideoVO?.shootingCountry">{{ dict.label }}</span>
              </template>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">模特类型</div>
            <div class="text">
              <template v-for="dict in biz_model_type" :key="dict.value">
                <span v-if="dict.value == orderVideoVO?.modelType">{{ dict.label }}</span>
              </template>
              <span v-if="orderVideoVO?.modelType == 3">影/素都可以</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="label">视频格式</div>
            <div class="text" v-if="orderVideoVO?.videoFormat != null">
              <template v-for="item in videoFormatOptions" :key="item.value">
                <span v-if="item.value == orderVideoVO?.videoFormat">{{ item.label }}</span>
              </template>
            </div>
            <div class="text" v-else>-</div>
          </el-col>
          <el-col :span="8">
            <div class="label">照片数量</div>
            <div class="text" v-if="orderVideoVO?.picCount != null">
              <template v-for="dict in picCountOptions" :key="dict.value">
                <span v-if="dict.value == orderVideoVO.picCount">{{ dict.label }}</span>
              </template>
            </div>
            <div class="text" v-else>-</div>
          </el-col>
          <el-col :span="8">
            <div class="label">参考图片</div>
            <el-button
              v-if="orderVideoVO?.referencePic?.length"
              link
              type="primary"
              @click="look('reference')"
            >
              查看
            </el-button>
            <div v-else>-</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="label">意向模特</div>
            <div class="text">
              {{ orderVideoVO?.intentionModel?.name || '-' }}
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">参考视频</div>
            <div class="one-ell productLink">
              <template v-if="orderVideoVO?.referenceVideoLink">
                <el-link
                  v-if="http_reg.test(orderVideoVO.referenceVideoLink)"
                  type="primary"
                  target="_blank"
                  :href="orderVideoVO.referenceVideoLink"
                >
                  {{ orderVideoVO.referenceVideoLink }}
                </el-link>
                <span v-else>{{ orderVideoVO.referenceVideoLink }}</span>
              </template>
              <template v-else>-</template>
            </div>
          </el-col>
          <!-- <el-col :span="8">
            <div class="label">视频时长</div>
            <div class="text">
              {{ orderVideoVO?.videoDuration ? `${orderVideoVO.videoDuration}s` : '-' }}
            </div>
          </el-col> -->
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="label">产品卖点</div>
            <div class="template-pre text">
              <template v-if="orderVideoVO?.sellingPointProduct">
                {{ orderVideoVO.sellingPointProduct }}
              </template>
              <template v-else>-</template>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="label">拍摄建议</div>
            <div class="template-pre text">
              <template v-if="orderVideoVO?.shootRequired.length">
                <div v-for="(item, index) in orderVideoVO.shootRequired" :key="index">
                  {{ item.content }}
                </div>
              </template>
              <template v-else>-</template>
            </div>
          </el-col>
        </el-row>
      </div>

      <div
        class="flex-start gap-10 title"
        v-if="
          orderVideoVO?.status >= ORDER_STATUS['需发货'] &&
          orderVideoVO?.status !== ORDER_STATUS['交易关闭'] &&
          orderVideoVO?.isObject === 0
        "
      >
        <span>发货信息</span>
        <CopyButton class="all-copy-btn" type="primary" :copy-content="handleCopy">
          <el-icon style="margin-right: 5px"><CopyDocument /></el-icon>
          复制全部
        </CopyButton>
      </div>

      <div
        class="content"
        v-if="
          orderVideoVO?.status >= ORDER_STATUS['需发货'] &&
          orderVideoVO?.status !== ORDER_STATUS['交易关闭'] &&
          orderVideoVO?.isObject === 0
        "
      >
        <el-row>
          <template v-for="(item, index) in addressLabels">
            <el-col :span="8" v-if="index < 3">
              <div class="label long-label">{{ item.label }}</div>
              <div class="text">{{ item.value }}</div>
            </el-col>
          </template>
        </el-row>
        <el-row>
          <template v-for="(item, index) in addressLabels">
            <el-col :span="8" v-if="index >= 3">
              <div class="label long-label" :class="{ 'long-label': index > 3 }">{{ item.label }}</div>
              <div class="text">{{ item.value }}</div>
            </el-col>
          </template>
        </el-row>
        <el-row>
          <el-col :span="8" v-if="shipInfoVO?.phone">
            <div class="label long-label">TEL</div>
            <div class="text template-pre">{{ shipInfoVO?.phone }}</div>
          </el-col>
          <el-col :span="8" v-else>
            <div class="label long-label">发货备注</div>
            <div class="text template-pre">{{ shipInfoVO?.shippingRemark || '-' }}</div>
          </el-col>
          <el-col :span="8">
            <div class="label long-label">发货图片</div>
            <el-button v-if="shipInfoVO?.shippingPics.length" link type="primary" @click="look('goods')">
              查看
            </el-button>
            <div v-else>-</div>
          </el-col>
          <el-col :span="8">
            <div class="label long-label">是否补发</div>
            <div class="text">{{ shipInfoVO?.reissue === 0 ? '是' : '否' }}</div>
          </el-col>
        </el-row>
        <el-row v-if="shipInfoVO?.phone">
          <el-col :span="8">
            <div class="label long-label">发货备注</div>
            <div class="text template-pre">{{ shipInfoVO?.shippingRemark || '-' }}</div>
          </el-col>
        </el-row>
      </div>
    </template>

    <template v-else-if="type === 'order'">
      <div class="title">订单信息</div>

      <div class="content">
        <el-row v-if="orderVideoVO?.status === ORDER_STATUS['需支付']">
          <el-col :span="12">
            <div class="label">订单号</div>
            <div class="text">{{ orderVO?.orderNum }}</div>
          </el-col>
          <el-col :span="12">
            <div class="label">下单用户</div>
            <div class="text">{{ orderVO?.orderUserName || orderVO?.orderUserNickName || '-' }}</div>
          </el-col>
        </el-row>
        <template v-else>
          <el-row>
            <el-col :span="8">
              <div class="label">订单号</div>
              <div class="text">{{ orderVO?.orderNum }}</div>
            </el-col>
            <el-col :span="8">
              <div class="label">下单用户</div>
              <div class="text">{{ orderVO?.orderUserName || orderVO?.orderUserNickName || '-' }}</div>
            </el-col>
            <el-col :span="8">
              <div class="label">下单时间</div>
              <div class="text">{{ orderVO?.orderTime }}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <div class="label">支付方式</div>
              <div class="text">
                {{
                  (orderVO?.payTime || ORDER_STATUS['待审核'] == orderVideoVO?.status) && orderVO?.payType
                    ? payType(orderVO?.payType)
                    : '-'
                }}
                {{ orderVO?.payType == 7 ? '- ' + payMoneyTypeStatus(orderVO?.payTypeDetail) : '' }}
              </div>
            </el-col>
            <el-col :span="8">
              <div class="label">支付用户</div>
              <div class="text">{{ orderVO?.payUserName || orderVO?.payUserNickName || '-' }}</div>
            </el-col>
            <el-col :span="8">
              <div class="label">支付时间</div>
              <div class="text">{{ orderVO?.payTime || '-' }}</div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <div class="label">百度汇率</div>
              <div class="text">
                {{
                  (orderVO?.payTime || ORDER_STATUS['待审核'] == orderVideoVO?.status) &&
                  orderVO?.currentExchangeRate
                    ? orderVO.currentExchangeRate
                    : '-'
                }}
              </div>
            </el-col>
          </el-row>
        </template>
      </div>

      <div
        class="title"
        v-if="
          orderVideoVO?.status !== ORDER_STATUS['需支付'] && orderVideoVO?.status !== ORDER_STATUS['交易关闭']
        "
      >
        已支付
      </div>

      <div
        class="content"
        v-if="
          orderVideoVO?.status !== ORDER_STATUS['需支付'] && orderVideoVO?.status !== ORDER_STATUS['交易关闭']
        "
      >
        <el-row>
          <el-col :span="8" v-if="orderVO?.payNum">
            <div class="label">支付号</div>
            <div class="text">{{ orderVO?.payNum }}</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="label">视频佣金</div>
            <div class="text">${{ orderVideoVO?.videoPrice }}</div>
          </el-col>
          <el-col :span="8">
            <div class="label long-label">PayPal代付手续费</div>
            <div class="text">
              ${{ orderVideoVO?.exchangePrice ? orderVideoVO?.exchangePrice.toFixed(2) : 0 }}
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label long-label">蜗牛服务费</div>
            <div class="text">${{ orderVideoVO?.servicePrice }}</div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="label">照片佣金</div>
            <div class="text" v-if="orderVideoVO?.picCount != null">${{ orderVideoVO?.picPrice }}</div>
            <div class="text" v-else>-</div>
          </el-col>
          <el-col :span="8">
            <div class="label long-label">佣金代缴税费</div>
            <div class="text">
              ${{ orderVideoVO?.commissionPaysTaxes ? orderVideoVO.commissionPaysTaxes.toFixed(2) : 0 }}
            </div>
          </el-col>
          <el-col :span="8" v-if="handleShowDiscount(orderVideoVO?.orderDiscountDetailVOS, '1')">
            <div class="label long-label">限时满减活动</div>
            <div class="text">
              -{{ hanldeDiscount(orderVideoVO?.orderDiscountDetailVOS, '1') }} CNY
              <!-- ${{ orderVideoVO?.commissionPaysTaxes ? orderVideoVO.commissionPaysTaxes.toFixed(2) : 0 }} -->
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" v-if="handleShowDiscount(orderVideoVO?.orderDiscountDetailVOS, '4')">
            <div class="label long-label">每月首单返还会员费</div>
            <div class="text">
              -{{ hanldeDiscount(orderVideoVO?.orderDiscountDetailVOS, '4') }} USD
              <!-- ${{ orderVideoVO?.commissionPaysTaxes ? orderVideoVO.commissionPaysTaxes.toFixed(2) : 0 }} -->
            </div>
          </el-col>
          <el-col :span="8">
            <div class="label">订单合计</div>
            <div class="text" v-if="orderVideoVO?.amountDollar != null">
              ${{ orderVideoVO?.amountDollar ? orderVideoVO.amountDollar.toFixed(2) : 0 }}
            </div>
            <div class="text" v-else>-</div>
          </el-col>
        </el-row>
      </div>
    </template>

    <template v-else-if="type === 'model'">
      <div class="title">{{ orderVideoVO?.shootModel?.id ? '拍摄模特' : '意向模特' }}</div>

      <div class="flex-start gap-10 content">
        <el-image class="model-img" :src="$picUrl + modelVO?.modelPic + '!3x4'" fit="fill">
          <template #error>
            <div class="image-error">
              <el-icon :size="25" color="#ccc"><Picture /></el-icon>
            </div>
          </template>
        </el-image>
        <div style="width: 100%">
          <div class="text" style="margin-bottom: 3px">{{ modelVO?.name }}</div>
          <div class="flex-start gap-10" style="margin-bottom: 3px">
            <NationTag :type="modelVO?.nation" isIcon color="var(--text-color)" />
            <ModelTypeTag :type="modelVO?.type" isIcon color="var(--text-color)" />
          </div>
          <el-row v-if="steps?.length && steps[1]?.time">
            <el-col :span="24" style="margin-bottom: 3px">
              <div class="label long-label">匹配时间：</div>
              <div class="text">{{ steps[1].time }}</div>
            </el-col>
          </el-row>
        </div>
      </div>
    </template>

    <template v-else-if="type === 'logistics'">
      <div class="flex-between title">
        <span>物流信息</span>
        <el-button
          v-if="logisticVO && logisticVO.length > 1"
          type="primary"
          link
          @click="handleAction('所有物流')"
        >
          所有物流信息
        </el-button>
      </div>

      <div class="content" v-if="logisticVO">
        <!-- <div v-if="!shipInfoVO && orderVideoVO?.isObject" class="text">无需发货</div>
        <div v-else-if="orderVideoVO?.status < ORDER_STATUS['需发货']" class="text">暂无信息</div>
        <template v-else> -->
        <div style="height: 300px; overflow: hidden auto">
          <div class="flex-start" style="margin-bottom: 3px; align-items: flex-start">
            <div class="label">物流状态</div>
            <div class="text">{{ logisticVO[0].mainStatus || '-' }}</div>
          </div>
          <div class="flex-start" style="margin-bottom: 3px; align-items: flex-start">
            <div class="label">物流单号</div>
            <div class="text">{{ logisticVO[0].number }}</div>
          </div>
          <div class="flex-start" style="margin-bottom: 10px" v-if="logisticVO[0]?.remark">
            <div class="label">备注</div>
            <div class="text">{{ logisticVO[0].remark }}</div>
          </div>
          <DotSteps
            :active="-1"
            :steps="logisticVO[0].steps"
            is-border
            direction="vertical"
            width="245px"
            class="logistics-steps"
          >
            <template #title="{ step }">
              <div class="logistics-step-title">
                <span>{{ step.title }}</span>
                <div class="time">
                  {{ step.time }}
                </div>
              </div>
            </template>
            <template #description="{ step }">
              <div class="logistics-step-description">
                {{ step.description }}
              </div>
            </template>
          </DotSteps>
        </div>
        <!-- </template> -->
      </div>
    </template>

    <template v-else-if="type === 'records'">
      <div class="title" style="margin-bottom: 10px">流转记录</div>

      <div style="height: 250px; overflow: hidden auto">
        <DotSteps :active="-1" :steps="steps" is-border direction="vertical" width="320px">
          <template #title="{ step }">
            <div class="records-step-title">
              <div class="flex-start gap-10" style="color: var(--text-color)" v-if="step.recordId">
                {{ step.title }}
                <el-button link type="primary" @click="HistoryChangeRef?.open(step.recordId)">
                  查看变更记录
                </el-button>
              </div>
              <span v-else>{{ step.title }}</span>
              <div class="time">
                {{ step.time }}
              </div>
            </div>
          </template>
          <template #description="{ step }">
            <div class="text-n-all records-step-description">
              {{ step.description }}
            </div>
          </template>
        </DotSteps>
      </div>
      <HistoryChange ref="HistoryChangeRef" />
    </template>
  </div>
</template>

<script setup lang="ts">
import CopyButton from '@/components/public/button/CopyButton.vue'
import NationTag from '@/components/public/tag/NationTag.vue'
import ModelTypeTag from '@/components/public/tag/ModelTypeTag.vue'
import DotSteps from '@/components/public/steps/DotSteps.vue'
import HistoryChange from '@/views/order/components/dialog/HistoryChange.vue'
import { computed, getCurrentInstance, ref, type PropType } from 'vue'
import { payType, ORDER_STATUS, payMoneyTypeStatus } from '@/utils/order'
import { picCountOptions, videoFormatOptions } from '@/utils/data'
import { http_reg } from '@/utils/RegExp'
import { addressInfoMap } from '@/views/order/details/data'
import { useViewer } from '@/hooks/useViewer'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'

const store = useUserStore()

// 视频 | 产品 | 订单 | 模特 | 物流 | 记录
type CardType = 'video' | 'product' | 'order' | 'model' | 'logistics' | 'records'

const props = defineProps({
  type: {
    type: String as PropType<CardType>,
  },
  // 订单信息
  orderVO: {
    type: Object,
  },
  orderVideoVO: {
    type: Object,
  },
  // 视频上传信息
  orderVideoUploadlinkVO: {
    type: Object,
  },
  // 反馈素材信息
  orderFeedBackMaterialVO: {
    type: Array as PropType<any[]>,
  },
  // 流转记录 | 节点信息
  steps: {
    type: Array as PropType<any[]>,
  },
  // 物流信息
  logisticVO: {
    type: Array as PropType<any[]>,
  },
  // 发货信息
  shipInfoVO: {
    type: Object,
  },
})

const modelVO = computed(() => {
  if (props.orderVideoVO?.shootModel) {
    return props.orderVideoVO.shootModel
  }
  return props.orderVideoVO?.intentionModel || {}
})

const { showViewer } = useViewer()

const HistoryChangeRef = ref()

const emits = defineEmits(['action'])

const { proxy } = getCurrentInstance() as any

const { biz_nation, biz_nation_e, biz_model_type, biz_model_platform } = proxy.useDict(
  'biz_nation',
  'biz_nation_e',
  'biz_model_type',
  'biz_model_platform'
)

type NationCode = '1' | '2' | '3' | '4' | '5' | '6' | '7'
const addressLabels = computed(() => {
  let n: NationCode | '' = props.shipInfoVO ? ((props.shipInfoVO.nation + '') as NationCode) : ''
  if (n) {
    return addressInfoMap[n].map((item: any) => {
      let value = ''
      if (item.key === 'nation') {
        value = bizNationMap(n)
      } else if (props.shipInfoVO) {
        value = props.shipInfoVO[item.key] || '-'
      }
      return { label: item.label, key: item.key, value }
    })
  }
  return []
})
const handleCopy = computed(() => {
  let str = ''
  addressLabels.value.forEach((item: any) => {
    str += `${item.label}：${item.value}\n`
  })
  if (props.shipInfoVO?.phone) {
    str += `TEL：${props.shipInfoVO.phone}`
  }
  return str
})

function bizNationMap(val: string) {
  let n = biz_nation_e.value.find((item: any) => item.value == val)
  return n ? n.label : '-'
}

function look(type: string) {
  if (type === 'goods') {
    if (!props.shipInfoVO?.shippingPics.length) {
      ElMessage.warning('暂无发货图片')
      return
    }
    showViewer(props.shipInfoVO?.shippingPics, { scale: 'raw' })
  } else if (type === 'video') {
    if (!props.orderVideoUploadlinkVO?.videoCover) {
      ElMessage.warning('暂无视频封面')
      return
    }
    showViewer([props.orderVideoUploadlinkVO?.videoCover], { scale: 'raw' })
  } else if (type === 'reference') {
    if (!props.orderVideoVO?.referencePic?.length) {
      ElMessage.warning('暂无参考图片')
      return
    }
    showViewer(props.orderVideoVO?.referencePic, { scale: 'raw' })
  }
}
const hanldeDiscount = (list: any, type: any) => {
  return list.find((item: any) => item.type == type).amount
}
const handleShowDiscount = (list: any, type: any) => {
  return list && list.length > 0 ? list.some((item: any) => item.type == type) : false
}
function handleAction(btn: string) {
  emits('action', btn)
}
</script>

<style scoped lang="scss">
.info-card {
  background: #fff;
  padding: 13px 18px;
  margin-bottom: 10px;
  border: 1px solid #fff;
  border-radius: 8px;
  box-shadow: 3px 3px 4px 0px var(--shadow-gray);

  .title {
    font-size: 15px;
    font-weight: 500;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--border-gray-color);

    .btn-mini {
      padding: 2px 8px;
      --el-button-size: 20px;
    }

    &.flex-start {
      align-items: baseline;
    }
  }
  .content {
    width: 100%;
    padding: 15px 0 0;

    .el-image {
      flex-shrink: 0;
    }

    .label {
      flex-shrink: 0;
      width: 66px;
      font-size: 14px;
      font-weight: 400;
      color: var(--text-gray-color);
    }
    .long-label {
      width: fit-content;
      padding-right: 10px;
      min-width: 60px;
      max-width: 150px;

      & + .text {
        padding-right: 10px;
      }
    }
    .text {
      font-size: 14px;
      // flex-shrink: 0;
      width: calc(100% - 95px);
      word-break: break-all;
    }

    .el-col {
      display: flex;
      align-items: baseline;

      .el-button {
        position: relative;
        bottom: 1px;
      }
    }

    .el-col-8,
    .el-col-12,
    .el-col-16,
    .el-col-24 {
      margin-bottom: 10px;
    }

    .productLink {
      position: relative;
      // padding-right: 50px;
      width: fit-content;
      max-width: 80%;

      // .btn {
      //   position: absolute;
      //   top: 3px;
      //   right: 0;
      //   padding: 3px 8px;
      //   height: auto;
      //   z-index: 9;
      // }

      :deep(.el-link) {
        display: contents;

        .el-link__inner {
          display: inline;
        }
      }
    }

    .model-img {
      width: 100%;
      max-width: 70px;
      height: 100%;
    }
    .image-error {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .content + .title {
    margin-top: 25px;
  }

  .logistics-steps {
    padding-left: 75px;
  }
  .logistics-step-title {
    position: relative;

    span {
      color: var(--text-color);
    }

    .time {
      position: absolute;
      top: 0;
      left: -112px;
      width: 75px;
      color: var(--text-gray-color);
      font-size: 12px;
      text-align: center;
    }
  }
  .logistics-step-description {
    color: var(--text-gray-color);
    font-size: 12px;
  }
  .records-step-title {
    position: relative;
    font-size: 14px;
    padding-left: 68px;

    span {
      color: var(--text-color);
    }

    .time {
      position: absolute;
      top: 0;
      left: -6px;
      width: 75px;
      color: var(--text-gray-color);
      font-size: 12px;
      text-align: left;
    }
  }
  .records-step-description {
    color: var(--text-gray-color);
    font-size: 12px;
    line-height: 18px;
    padding-left: 68px;
    width: 77%;
  }
}
</style>
