<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    :disabled="isDisabled"
    :validate-on-rule-change="false"
    label-width="110px"
    class="order-form"
    v-loading="loading"
  >
    <el-row v-if="!store.isVip()">
      <el-col :span="24" class="flex-between nomember-tips">
        <span class="nomember-tips__text">需开通蜗牛会员后才可创建订单哦~</span>
        <span class="nomember-tips__btn" @click="goOpenMember">去开通会员</span>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="form-back" style="background: #f7fbfa">
          <!-- <div class="prompt-title">—— 必填项 ——</div> -->
          <div class="prompt-title">
            <Title><span style="font-size: 17px">必填项</span></Title>
          </div>
          <el-form-item label="使用平台" prop="platform">
            <template #label>
              <div class="flex-end">
                <span>使用平台</span>
                <el-icon
                  style="margin-left: 2px"
                  color="rgb(155, 166, 186)"
                  @mouseenter="showTooltips($event, 'order-create-platform')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <el-radio-group v-model="form.platform" @change="changeFormValue($event, 'platform')">
              <el-radio-button
                style="border-radius: 15px"
                class="radio-button__round"
                v-for="item in model_platform"
                :key="item.value"
                :value="item.value"
                :label="item.value"
                :disabled="item.value == '3' && form.picCount && props.type === 'edit'"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="视频格式" prop="videoFormat">
            <template #label>
              <div class="flex-end">
                <span>视频格式</span>
                <el-icon
                  style="margin-left: 2px"
                  color="rgb(155, 166, 186)"
                  @mouseenter="showTooltips($event, 'order-create-videoFormat')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <el-radio-group v-model="form.videoFormat">
              <el-radio-button
                v-for="item in videoFormatList"
                :key="item.value"
                :value="item.value"
                :label="item.value"
                :disabled="Platform['Tiktok'] == form.platform"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="拍摄国家" prop="shootingCountry">
            <template #label>
              <div class="flex-end">
                <span>拍摄国家</span>
                <el-icon
                  style="margin-left: 2px"
                  color="rgb(155, 166, 186)"
                  @mouseenter="showTooltips($event, 'order-create-nation')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <el-radio-group
              v-model="form.shootingCountry"
              @change="changeFormValue($event, 'shootingCountry')"
            >
              <el-radio-button
                v-for="item in biz_nation"
                :key="item.value"
                :value="item.value"
                :label="item.value"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="模特类型" prop="modelTypeList">
            <template #label>
              <div>
                <div>模特类型</div>
                <div style="font-size: 12px; margin-top: -15px; text-align: center" v-if="isMultiSelModel">
                  (可多选)
                </div>
              </div>
            </template>
            <el-checkbox-group
              v-model="form.modelTypeList"
              @change="changeModelValue($event)"
              v-if="isMultiSelModel"
            >
              <el-checkbox-button
                v-for="item in biz_model_type"
                :key="item.value"
                :value="item.value"
                @mouseenter="handleMouseEnterModelType($event, item.value)"
              >
                {{ item.label }}
              </el-checkbox-button>
            </el-checkbox-group>
            <el-radio-group v-model="form.modelType" @change="changeFormValue($event, 'modelType')" v-else>
              <el-radio-button
                v-for="item in biz_model_type"
                :key="item.value"
                :value="item.value"
                :label="item.value"
                :disabled="isSelModel"
                @mouseenter="handleMouseEnterModelType($event, item.value, true)"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="产品中文名" prop="productChinese">
            <el-input
              v-model="form.productChinese"
              placeholder="例如：女童压力裤"
              maxlength="30"
              show-word-limit
              clearable
            />
          </el-form-item>
          <el-form-item label="产品英文名" prop="productEnglish">
            <el-input
              v-model="form.productEnglish"
              placeholder="例如：Girls compression shorts"
              maxlength="50"
              show-word-limit
              clearable
            />
          </el-form-item>
          <el-form-item label="产品链接" prop="isLink">
            <template #label>
              <div class="flex-end">
                <span>产品链接</span>
                <el-icon
                  style="margin-left: 2px"
                  color="rgb(155, 166, 186)"
                  @mouseenter="showTooltips($event, 'order-create-productLink')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <el-radio-group v-model="form.isLink" @change="changeIsLink">
              <el-radio-button
                v-for="item in [
                  { label: '有链接', value: 1 },
                  { label: '无链接', value: 0 },
                ]"
                :key="item.value"
                :value="item.value"
                :label="item.value"
                :disabled="isSelectLinkDisabled"
                @mouseenter="handleMouseEnterLinkType($event, item.value, isSelectLinkDisabled)"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>

            <el-input
              style="margin-top: 10px"
              maxlength="1000"
              v-if="form.isLink"
              v-model="form.productLink"
              placeholder="请输入产品链接"
              clearable
              @input="getLink"
            />
          </el-form-item>
          <el-form-item v-if="!form.isLink" label="产品图" prop="productPicInfo">
            <div class="image-list" v-for="(item, i) in form.filePicList" :key="item.id">
              <div @click.stop="doDeleteProUrl(i)" class="image-modal flex-around">
                <!-- v-if="props.type === 'add' || props.type === 'editCartOrder'" -->
                <img class="icon" src="@/assets/icon/icon_close.png" alt="" />
                <!-- <el-icon
                  size="20"
                  color="#ffffffc4"
                  v-if="props.type === 'add' || props.type === 'editCartOrder'"
                  @click="doDeletePicUrl(i)"
                >
                  <Delete style="cursor: pointer" />
                </el-icon>
                <el-icon size="20" color="#ffffffc4" @click="doShowPicUrl(i)">
                  <View style="cursor: pointer" />
                </el-icon> -->
              </div>
              <el-image
                ref="imgViewRef"
                preview-teleported
                :src="pathHead + item.picUrl + '!thumbnail200'"
                fit="scale-down"
                class="image-item"
                :preview-src-list="item.picUrl ? [pathHead + item.picUrl] : []"
              >
                <!-- <template #error>
                <div class="image-error">
                  <el-icon :size="25"><Picture /></el-icon>
                </div>
              </template> -->
              </el-image>
            </div>
            <div
              class="image-upload"
              @click="doShowSelectImage('product')"
              v-if="
                form.filePicList &&
                form.filePicList.length < 1 &&
                (props.type === 'add' || props.type === 'editCartOrder' || props.type === 'edit')
              "
            >
              <el-icon size="28" color="#909399"><Plus /></el-icon>
            </div>
            <!-- <CropperUpload
              v-model="form.filePicList"
              preview
              :disabled="disabled"
              :fixed-number-arr="[[4, 4]]"
              :limit="cropperUploadLimit"
            ></CropperUpload> -->
            <!-- <el-upload
              v-model:file-list="filePicList"
              action=""
              list-type="picture-card"
              :limit="1"
              :on-preview="handlePictureCardPreview"
              :http-request="uploadPhotoFile"
              :before-upload="beforeUpload"
              :before-remove="beforeRemove"
              :on-exceed="() => ElMessage.warning('最多可上传1张图片！')"
              :class="{
                disabled,
                hidden: filePicList && filePicList.length >= 1,
              }"
            >
              <el-icon><Plus /></el-icon>
            </el-upload> -->

            <!-- <div class="flex-start" v-if="form.productLink && productLinkLoading">
              <el-icon class="loading-icon"><Loading /></el-icon>
              <span style="margin-left: 10px">图片加载中</span>
            </div>
            <el-image
              v-if="form.productPic.picUrl"
              :src="form.productPic.picUrl"
              style="max-width: 100px; max-height: 150px"
            ></el-image>
            <span v-if="productPicUrlError" class="error-text">
              图片获取失败，无需担心，订单生成后，客服会手动上传
            </span> -->
          </el-form-item>

          <el-form-item label="拍摄数量" prop="shootCount" v-if="props.type === 'add'">
            <el-input-number
              :precision="0"
              v-model="form.shootCount"
              :min="1"
              :max="99"
              @change="changeShootCount"
            />
            <div class="shootTips" v-if="form.platform == Platform['Amazon']">
              亚马逊Listing中，Videos区域可上传10个产品视频。
              <div class="flex-start" style="align-items: baseline">
                <div>友情建议：</div>
                <div>
                  <span>拍摄数量填写"5"，占据Videos前5核心位置；</span>
                  <br />
                  <span>若需要10个视频，可填写"10"</span>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
      </el-col>

      <el-col :span="12">
        <div class="form-back" style="background: #f7f8fb; overflow: auto">
          <!-- <div class="prompt-title">—— 非必填项 ——</div> -->
          <div class="prompt-title">
            <Title><span style="font-size: 17px">非必填项</span></Title>
          </div>

          <el-form-item label="意向模特" prop="selModel">
            <template #label>
              <div class="flex-end">
                <span>意向模特</span>
                <el-icon
                  style="margin-left: 2px"
                  color="rgb(155, 166, 186)"
                  @mouseenter="showTooltips($event, 'order-create-intentionModel')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <el-button type="primary" @click="openSelect" round icon="Plus" :disabled="isShowSelectModel">
              选择
              <!-- <span v-if="props.type === 'add'">请选择{{ form.shootCount }}位模特</span> -->
              <!-- <span v-if="props.type === 'edit'">请选择1位模特</span> -->
            </el-button>
            <div v-if="cannotModelNumber > 0" style="color: #d9001b; margin-left: 8px">
              有{{ cannotModelNumber }} 位模特不满足拍摄需求，请更换
            </div>
            <div
              v-if="selectModelNumber > 0 && cannotModelNumber < 1 && props.type === 'add'"
              style="color: #5695c0; margin-left: 8px"
            >
              (还可以选择{{ selectModelNumber }}位模特)
            </div>
            <div class="model-info" v-if="form.selModel && form.selModel.length > 0">
              <TransitionGroup name="list">
                <template v-for="(item, i) in form.selModel" :key="item.id">
                  <div class="model-info__item" v-if="i < 11">
                    <div
                      class="modal-lose flex-around"
                      v-if="cannotModelList && cannotModelList.includes(item.id)"
                      @click="openSelect()"
                    >
                      失效
                    </div>
                    <div class="play-modal flex-around" v-else>
                      <!-- v-if="isChangeModel(item.id)" -->
                      <!-- <span v-if="isChangeModel(item.id)" style="color: #fff" @click="openSelect(item.id, i)">
                    换
                  </span> -->
                      <!-- <template> -->
                      <img
                        style="height: 24px; width: 24px"
                        @click="doDeleteModel(i)"
                        src="@/assets/icon/icon_delete_fff.png"
                        alt=""
                      />
                      <!-- <el-icon size="15" color="#fff"><Delete @click="doDeleteModel(i)" /></el-icon> -->
                      <!-- <el-icon size="15" color="#fff"><EditPen @click="openSelect" /></el-icon> -->
                      <!-- </template> -->
                    </div>
                    <el-image class="head-img" :src="$picUrl + item.modelPic + '!3x4'" fit="fill">
                      <template #error>
                        <el-icon size="50"><Avatar /></el-icon>
                      </template>
                    </el-image>
                    <div
                      class="head-name more-ell"
                      :style="{ color: cannotModelList.includes(item.id) ? '#aaa' : '#000' }"
                    >
                      {{ item.name }}
                    </div>
                  </div>
                </template>
              </TransitionGroup>

              <!-- 15 -->
              <!-- <el-popover
                placement="top-start"
                :title="`已选的意向模特(${form.selModel?.length})`"
                :width="450"
                trigger="hover"
              >
                <template #reference> -->
              <div @click="doShowSelModelList" class="model-more" v-if="form.selModel?.length > 11">
                <div class="model-more__icon"><span style="padding-bottom: 2px">· · ·</span></div>
                <div class="model-more__text">更多</div>
              </div>
              <!-- </template> -->
              <!-- <div v-if="cannotModelNumber > 0" style="color: #d9001b">
                有{{ cannotModelNumber }} 位模特不满足拍摄需求，请更换
              </div> -->
              <!-- 
                <div class="model-info">
                  <div class="model-info__item" v-for="(item, i) in form.selModel" :key="item.id">
                    <div>
                      <div class="play-modal flex-around">
 
                        <img
                          style="height: 24px; width: 24px"
                          @click="doDeleteModel(i)"
                          src="@/assets/icon/icon_delete_fff.png"
                          alt=""
                        />

                      </div>
                      <el-image class="head-img" :src="$picUrl + item.modelPic + '!1x1'" fit="fill">
                        <template #error>
                          <el-icon size="40"><Avatar /></el-icon>
                        </template>
                      </el-image>
                      <div class="head-name more-ell" style="margin-top: -5px">{{ item.name }}</div>
                    </div>
                  </div>
                </div> -->
              <!-- </el-popover> -->
            </div>
          </el-form-item>
          <el-form-item label="拍摄建议" prop="demands">
            <template #label>
              <div style="text-align: center">
                <div>拍摄建议</div>
                <div style="color: #918d8d; font-size: 12px; margin-top: -15px">(英文)</div>
              </div>
            </template>
            <el-input
              v-model="form.demands"
              type="textarea"
              placeholder="请输入产品卖点/产品特性"
              :autosize="{ minRows: 8, maxRows: 8 }"
              maxlength="5000"
              style="font-family: none"
            />
            <!-- <el-input
              v-model="form.demands"
              type="textarea"
              :rows="8"
              placeholder="请输入产品卖点/产品特性"
              @input="handleInput"
              @keydown="handleInputFocus"
              resize="none"
              maxlength="5000"
              style="font-family: none"
            /> -->
            <!-- <div class="demands-count">
              <span :style="{ color: form.demands.length > 800 ? 'red' : '#909399' }">
                {{ form.demands.length }}
              </span>
              / 800
            </div> -->
          </el-form-item>
          <el-form-item
            label="参考视频"
            prop="referenceVideoLink"
            :key="rules.referenceVideoLink[0].required"
          >
            <template #label>
              <div class="flex-end">
                <span>参考视频</span>
                <el-icon
                  style="margin-left: 2px"
                  color="rgb(155, 166, 186)"
                  @mouseenter="showTooltips($event, 'order-create-referenceVideoLink')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <el-input
              maxlength="1000"
              v-model="form.referenceVideoLink"
              placeholder="请输入参考视频的链接"
              clearable
            />
          </el-form-item>
          <el-form-item label="照片数量" prop="picCount" v-if="showPicCount">
            <template #label>
              <div class="flex-end">
                <span>照片数量</span>
                <el-icon
                  style="margin-left: 2px"
                  color="rgb(155, 166, 186)"
                  @mouseenter="showTooltips($event, 'order-create-picCount')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <el-radio-group v-model="form.picCount" :disabled="isShowPic" @change="picCountChange">
              <el-radio-button
                v-for="item in picCountOptions"
                :key="item.value"
                :value="item.value"
                :label="item.value"
                @click="picCountClick(item.value)"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
            <el-button type="primary" link style="margin-left: 10px" @click="lookPhoto">
              查看照片案例
            </el-button>
          </el-form-item>
          <el-form-item label="参考图片" prop="referencePic" v-if="form.picCount">
            <div class="image-list" v-for="(item, i) in form.referencePic" :key="item.id">
              <div @click.stop="doDeletePicUrl(i)" class="image-modal flex-around">
                <!-- v-if="props.type === 'add' || props.type === 'editCartOrder'" -->
                <img class="icon" src="@/assets/icon/icon_close.png" alt="" />
                <!-- <el-icon
                  size="20"
                  color="#ffffffc4"
                  v-if="props.type === 'add' || props.type === 'editCartOrder'"
                  @click="doDeletePicUrl(i)"
                >
                  <Delete style="cursor: pointer" />
                </el-icon>
                <el-icon size="20" color="#ffffffc4" @click="doShowPicUrl(i)">
                  <View style="cursor: pointer" />
                </el-icon> -->
              </div>
              <el-image
                ref="imgViewRef"
                preview-teleported
                :src="pathHead + item.picUrl + '!1x1'"
                fit="fill"
                class="image-item"
                :preview-src-list="item.picUrl ? [pathHead + item.picUrl] : []"
              >
                <!-- <template #error>
                <div class="image-error">
                  <el-icon :size="25"><Picture /></el-icon>
                </div>
              </template> -->
              </el-image>
            </div>
            <!-- &&
            (props.type === 'add' || props.type === 'editCartOrder') -->
            <!-- !disabled_edit && -->
            <div
              class="image-upload"
              @click="doShowSelectImage('picCount')"
              v-if="
                form.referencePic &&
                form.referencePic.length < addPicNumber &&
                (props.type === 'add' || props.type === 'editCartOrder' || props.type === 'edit')
              "
            >
              <el-icon size="28" color="#909399"><Plus /></el-icon>
            </div>
            <!-- <el-upload
            v-model:file-list="form.referencePic"
            action=""
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :limit="5"
            :http-request="uploadPhotoFile"
            :before-upload="beforeUpload"
            :on-exceed="() => ElMessage.warning('最多可上传5张图片！')"
            :class="{
              disabled,

              hidden: form.referencePic && form.referencePic.length >= 5,
            }"
          >
            <el-icon><Plus /></el-icon>
          </el-upload> -->
            <div class="prompt-text" style="width: 100%" v-if="form.picCount">
              您可以上传{{ addPicNumber }}张拍照参考图，供拍摄模特参考;
            </div>
          </el-form-item>
        </div>
      </el-col>
    </el-row>
    <div class="flex-center form-button" v-if="props.type !== 'readOnly'">
      <el-button
        v-if="props.type === 'add'"
        class="place-order-button"
        type="primary"
        round
        plain
        @click="resetForm"
        :loading="loadingBtn"
      >
        一键清空
      </el-button>
      <el-button
        v-if="props.type === 'add'"
        class="place-order-button addCartBtn"
        type="primary"
        round
        @click="addSubmit(true)"
        :loading="loadingBtn"
      >
        加入购物车
      </el-button>
      <div v-if="props.type === 'edit' || props.type === 'editCartOrder'"></div>
      <el-button
        v-if="props.type === 'edit' || props.type === 'editCartOrder'"
        class="place-order-button"
        type="primary"
        round
        @click="addSubmit()"
        :loading="loadingBtn"
      >
        确认修改
      </el-button>
    </div>
  </el-form>
  <PublicDialog
    ref="ModelsDialogRef"
    width="500px"
    :title="`已选的意向模特(${form.selModel?.length})`"
    :showFooterButton="false"
    :titleCenter="false"
    custom-close
    align-center
    destroy-on-close
    :close-on-click-modal="true"
  >
    <div class="model-info" style="padding-bottom: 20px; gap: 16px">
      <div class="model-info__item" v-for="(item, i) in form.selModel" :key="item.id">
        <div>
          <div
            class="modal-lose flex-around"
            v-if="cannotModelList && cannotModelList.includes(item.id)"
            @click="openSelect()"
          >
            失效
          </div>
          <div class="play-modal flex-around">
            <img
              style="height: 24px; width: 24px"
              @click="doDeleteModel(i)"
              src="@/assets/icon/icon_delete_fff.png"
              alt=""
            />
          </div>
          <el-image class="head-img" :src="$picUrl + item.modelPic + '!3x4'" fit="fill">
            <template #error>
              <el-icon size="50"><Avatar /></el-icon>
            </template>
          </el-image>
          <div class="head-name more-ell" style="margin-top: -5px">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </PublicDialog>
  <SelectSingleModel
    :limit="form.shootCount"
    :nation="form.shootingCountry"
    :modelType="form.modelType"
    :platform="form.platform"
    ref="SelectSingleModelRef"
    @success="handleSelectSuccess"
  />
  <UploadProof
    ref="UploadProofRef1"
    :title="uploadProofTitle"
    :fileType="['jpg', 'jpeg', 'png', 'bmp']"
    @success="upSuccess"
    :limit="1"
  >
    <template #tip>
      <div class="el-upload__tip">
        请上传大小不超过
        <span style="color: #f29c2d">5M</span>
        ，格式为
        <span style="color: #f29c2d">png/jpg/jpeg/bmp</span>
        的图片
      </div>
    </template>
  </UploadProof>
  <UploadProof
    ref="UploadProofRef"
    :title="uploadProofTitle"
    :fileType="['jpg', 'jpeg', 'png', 'bmp']"
    @success="upSuccess"
    :limit="addPicNumber1"
  >
    <template #tip>
      <div class="el-upload__tip">
        请上传大小不超过
        <span style="color: #f29c2d">5M</span>
        ，格式为
        <span style="color: #f29c2d">png/jpg/jpeg/bmp</span>
        的图片
      </div>
    </template>
  </UploadProof>

  <el-dialog
    :close-on-click-modal="false"
    v-model="isShowHint"
    :close-on-press-escape="false"
    :show-close="false"
    width="450"
    align-center
  >
    <div class="hint-dialog">
      <div>
        <svg class="svg-icon" aria-hidden="true" style="width: 20px; height: 20px; margin-right: 10px">
          <use xlink:href="#icon-tishi"></use>
        </svg>
      </div>
      <div>
        <div class="hint-dialog__title">{{ hintDialogTitle }}</div>
        <div class="hint-dialog__content">{{ hintDialogContent }}</div>
      </div>
    </div>
    <div class="hint-line"></div>

    <template #footer>
      <div class="footer-btn" style="padding-top: 15px">
        <el-button @click="handleDialogCancel">取消</el-button>
        <el-button type="primary" @click="handleDialogConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import Title from '@/components/public/Title.vue'
import SelectSingleModel from '@/views/model/components/SelectSingleModel.vue'
import UploadProof from '@/components/public/dialog/DragUploadDialog.vue'
import { useRouter } from 'vue-router'
import { getCurrentInstance, ref, type PropType, computed, nextTick, h, watch, toRaw, warn } from 'vue'
import type { OrderForm, FormCompType } from '../type'
import { Platform } from '@/utils/dictType'
// import { debounce } from '@/utils/public'
import { picCountOptions, videoFormatOptions } from '@/utils/data'
import {
  chinese_c_reg,
  chineseCharacter_d_reg,
  englishCharacter_d_chineseCharacter_reg,
} from '@/utils/RegExp'
import {
  createOrder,
  createOrderAddCart,
  updateOrder,
  updateOrderDetails,
  updateCartOrderDetails,
  orderEditCart,
} from '@/api/order'
import { uploadCloudFile, uploadAmazonUrl } from '@/api/index'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { previewImage } from '@/utils/preview'
import { useViewer } from '@/hooks/useViewer'
import { useTooltips } from '@/hooks/useTooltips'
import { MessageBox } from '@/utils/message'

import { addToCartAnimation } from '@/hooks/addToCartAnimation'
// import CropperUpload from '@/components/FileUpload/cropperUpload.vue'

const { showTooltips } = useTooltips()

const { proxy } = getCurrentInstance() as any
const pathHead = proxy.$picUrl
const uploadProofTitle = ref('上传图片')
const store = useUserStore()

const ModelsDialogRef = ref()

const cropperUploadLimit = ref(1)

const hintDialogTitle = ref('')
const hintDialogContent = ref('')

const { biz_nation, biz_model_type } = proxy.useDict('biz_nation', 'biz_model_type')

const router = useRouter()

const emits = defineEmits(['success', 'error', 'successCart'])
const { showViewer } = useViewer()

const props = defineProps({
  type: {
    type: String as PropType<FormCompType>,
    default: 'add',
  },
  orderId: {
    type: [Number, String],
  },
})

type UploadPicFile = {
  name?: string
  url?: string
  id?: number
}[]
const filePicList = ref<UploadPicFile>([])

const addPicNumber = ref(2)
const addPicNumber1 = ref(2)
//判断是否是编辑修改时选择模特
const isShowSelectModel = computed(() => {
  if (
    (props.type === 'edit' || props.type === 'editCartOrder') &&
    form.value.selModel &&
    form.value.selModel.length > 0
  ) {
    return true
  } else return false
})

//判断是否显示照片数'
const showPicCount = computed(() => {
  // if (form.value.modelType == '0' || form.value.platform == '3') return true
  if (
    ((form.value.modelType == '3' || form.value.modelType == '1') &&
      props.type != 'edit' &&
      form.value.platform != '3') ||
    form.value.picCount
  )
    return true
  return false
})

//拍摄数量与意向模特联动效果
function changeShootCount(val: number) {
  if (form.value.selModel?.length && form.value.selModel.length > form.value.shootCount) {
    form.value.selModel = form.value.selModel?.slice(0, val)
  }
}

//判断是否ivp + 之前的disabled
const isDisabled = computed(() => {
  if (!store.isVip() || disabled.value) return true
})
//判断模特类型是否可以多选
const isMultiSelModel = computed(() => {
  if (form.value.shootingCountry == '7' && form.value.platform == Platform['Amazon'] && !form.value.picCount)
    return true
  return false
})
const isSelModel = computed(() => {
  if (form.value.shootingCountry == '7' && form.value.platform == Platform['Amazon'] && form.value.picCount)
    return true
  else {
    formRef.value?.clearValidate('modelTypeList')
    return true
  }
})
//計算可選模特数量
const selectModelNumber = computed(() => {
  return form.value.shootCount - (form.value.selModel?.length || 0) > 0
    ? form.value.shootCount - (form.value.selModel?.length || 0)
    : 0
})
//去开通会员
function goOpenMember() {
  if (store.isOwnerAcc()) {
    router.push('/vip')
  }
}

defineExpose({ saveOrderForm })

const UploadProofRef = ref<InstanceType<typeof UploadProof>>()
const UploadProofRef1 = ref<InstanceType<typeof UploadProof>>()
const imgViewRef = ref()

//展示选择图片弹窗
function doShowSelectImage(type: string) {
  if (type === 'picCount') {
    uploadProofTitle.value = '上传参考图片'
    form.value.picCount ? UploadProofRef.value?.open() : ''
  } else {
    uploadProofTitle.value = '上传产品图片'
    UploadProofRef1.value?.open()
  }
}

function doShowPicUrl(index: number) {
  // previewImage(url,0)
  const list = form.value.referencePic?.map(item => pathHead + item.picUrl) || []
  showViewer(list, { index })
}

function doDeletePicUrl(index: number) {
  // addPicNumber.value++
  form.value.referencePic?.splice(index, 1)
}
function doDeleteProUrl(index: number) {
  form.value.filePicList?.splice(index, 1)
}
function upSuccess(data: any) {
  if (uploadProofTitle.value === '上传参考图片') {
    form.value.referencePic?.push(...data)
  } else {
    form.value.filePicList.push(...data)
  }
  // addPicNumber.value = addPicNumber.value - (form.value.referencePic?.length || 0)
}

//存新增数据
function saveOrderForm() {
  if (props.type === 'add') {
    // ElMessageBox.confirm('是否需要按照账号存信息','温馨提示',{
    //   confirmButtonText: '确定',
    //   cancelButtonText: '关闭',
    // }).then(() => {
    localStorage.setItem('saveOrderForm_' + store.userInfo.account, JSON.stringify(form.value))
    if (cannotModelList.value && cannotModelList.value.length > 0) {
      localStorage.setItem('cannotModelList_' + store.userInfo.account, JSON.stringify(cannotModelList.value))
    }
    // })
  }
}

//查看更多模特
function doShowSelModelList() {
  ModelsDialogRef.value?.open()
}

//删除模特

const doDeleteModel = (index: number) => {
  form.value.selModel?.splice(index, 1)
}
//修改模特

const doEditModel = (data: any, index: number) => {
  console.log(data, 1234)
}

// const handleInput = () => {

//   if (form.value.demands.length >= 800) {
//     form.value.demands = form.value.demands.substring(0, 800)
//     console.log(form.value.demands ,666);

//   }
//   updateFormattedText()

// }
function handleInput() {
  updateFormattedText()
}

const handleInputFocus = (event: any) => {
  if (form.value.demands.length < 5000) {
    if (event.isComposing) return
    const start = event.target.selectionStart
    const end = event.target.selectionEnd
    if (event.key === 'Enter') {
      event.preventDefault()
      const lines = form.value.demands.split('\n').length
      let step = 4
      if (lines === 1) step = 5
      if (lines < 99) {
        const value = form.value.demands
        form.value.demands = value.substring(0, start) + '\n' + value.substring(end)
        updateFormattedText()
        nextTick(() => {
          const selectLines = value.substring(0, end).split('\n').length
          event.target.selectionStart = event.target.selectionEnd = start + step - (selectLines < 9 ? 1 : 0)
          // event.target.selectionStart = event.target.selectionEnd = start + step - (lines < 9 ? 1 : 0)
          event.target.blur()
          event.target.focus()
        })
      }
    } else if (event.key === 'Backspace') {
      const value = form.value.demands
      if (start === end && start > 3) {
        const reg = /(\n\d{1,3}\.)$/
        const reg2 = /(\n\d{1,3})$/
        const str = value.substring(0, start)
        const exec = reg.exec(str)
        const exec2 = reg2.exec(str)
        // console.log(exec,111, exec2);
        if (reg.test(str) && exec) {
          event.preventDefault()
          form.value.demands = value.substring(0, exec['index']) + value.substring(start)
          updateFormattedText()
          event.target.selectionStart = event.target.selectionEnd = start - 1
        } else if (reg2.test(str) && exec2) {
          event.preventDefault()
          form.value.demands = value.substring(0, exec2['index']) + value.substring(start)
          updateFormattedText()
          event.target.selectionStart = event.target.selectionEnd = start - 1
        }
      }
    }
  }
}

const updateFormattedText = () => {
  const lines = form.value.demands.split('\n')
  const formattedLines = lines.map((line, index) => {
    if (!line.startsWith(`${index + 1}.`)) {
      return `${index + 1}.${line.replace(/^\d+\.*/, '')}`
    }
    return line
  })
  let temp = formattedLines.join('\n')
  if (temp.length > 5000) {
    temp = temp.substring(0, 5000)
  }
  form.value.demands = temp
}

const model_platform = ref([
  { label: '亚马逊', value: '0' },
  { label: 'TikTok', value: '1' },
  { label: '其他', value: '2' },
  { label: 'APP/解说', value: '3' },
])

function getInfo() {
  try {
    store.getUserInfo()
  } catch (e) {
    emits('error', e)
    return
  }
}

const isShowPic = computed(() => {
  let isShow = disabled_edit.value
  if (props.type == 'edit') return (isShow = true)
  if (form.value.modelType == '1' || form.value.modelType == '3') isShow = false
  return isShow
})

function init() {
  if (props.type == 'add') {
    // 从模特库跳转过来
    const model = sessionStorage.getItem('orderModelInfo')
    if (model) {
      let modelObj = JSON.parse(model)

      form.value.selModel = [modelObj]
      //默认平台中的第一个
      form.value.platform = modelObj.platform.split(',')[0]
      form.value.shootingCountry = modelObj.nation + ''
      form.value.modelType = modelObj.type + ''
      form.value.modelTypeList = [modelObj.type + '']

      temporaryChangeData.value.modelType = modelObj.type + ''
      temporaryChangeData.value.platform = modelObj.platform.split(',')[0]
      temporaryChangeData.value.shootingCountry = modelObj.nation + ''
      if (form.value.platform == '1') {
        form.value.videoFormat = 2
      }
      if (modelObj.type == 0 || form.value.platform == '3') {
        disabled_edit.value = true
      }
      if (form.value.platform == '0' && modelObj.type == '1' && modelObj.nation == '7') {
        form.value.isLink = 0
      } else {
        form.value.isLink = 1
      }
      sessionStorage.removeItem('orderModelInfo')
    } else {
      const countForm = localStorage.getItem('saveOrderForm_' + store.userInfo.account)
      const cannotModelListStr = localStorage.getItem('cannotModelList_' + store.userInfo.account)
      if (countForm) {
        const countFormObj = JSON.parse(countForm)
        form.value = countFormObj
        temporaryChangeData.value.modelType = countFormObj.modelType
        temporaryChangeData.value.platform = countFormObj.platform
        temporaryChangeData.value.shootingCountry = countFormObj.shootingCountry
        if (countFormObj.modelType == 0 || countFormObj.platform == 3) {
          disabled_edit.value = true
        }
        // productPicUrl.value = form.value.filePicList?[0].url
        // if (form.value.filePicList && form.value.filePicList.length > 0) {
        //   filePicList.value = form.value.filePicList
        //   productPicUrl.value = form.value.filePicList[0].url
        // }
        // selectModelTypeChange(form.value.modelType)
        if (form.value.picCount) {
          if (form.value.picCount === 1) addPicNumber.value = 2
          if (form.value.picCount === 2) addPicNumber.value = 5
        }
      }
      if (cannotModelListStr && cannotModelListStr.length > 0) {
        form.value.selModel = form.value.selModel?.filter(item => {
          return cannotModelList.value.indexOf(item.id) != -1
        })
        localStorage.removeItem('cannotModelList_' + store.userInfo.account)
      }
    }
  }
  if (props.type === 'edit') {
    getOrderDetails()
    disabled_edit.value = true
  }
  if (props.type === 'editCartOrder') {
    getCartDetails()
    // disabled_edit.value = true
  }
  if (props.type === 'readOnly') {
    disabled.value = true
  }
  getInfo()
}

const SelectSingleModelRef = ref()
const SelectSingleModelOneRef = ref()
const formRef = ref()
const form = ref<OrderForm>({
  platform: '0',
  productChinese: '',
  productEnglish: '',
  isLink: 1,
  productLink: '',
  productPicInfo: {
    id: 0,
    name: '',
    picUrl: '',
    videoUrl: '',
  },
  filePicList: [],
  productPic: undefined,
  isObject: 0,
  referenceVideoLink: '',
  videoFormat: 1,
  shootingCountry: '7',
  modelType: '1',
  modelTypeList: ['1'],
  picCount: undefined,
  referencePic: [],
  selModel: [],
  intentionModelIds: [],
  // 拍摄要求
  demands: '',
  shootRequired: [
    {
      content: '',
    },
  ],
  // demands: [
  //   {
  //     value: '',
  //   },
  // ],
  // 限制条件
  conditions: [
    {
      value: '',
    },
  ],
  shootCount: 5,
})
const delForm = ref<{
  demands: any[]
  conditions: any[]
}>({
  demands: [],
  conditions: [],
})
function resetForm() {
  disabled_edit.value = false
  cannotModelList.value = []
  cannotModelNumber.value = 0
  addCatParams.value = ''
  addCatProductPic.value = ''
  form.value = {
    platform: '0',
    productChinese: '',
    productEnglish: '',
    isLink: 1,
    productLink: '',
    productPicInfo: {
      id: 0,
      name: '',
      picUrl: '',
      videoUrl: '',
    },
    filePicList: [],
    productPic: undefined,
    isObject: 0,
    referenceVideoLink: '',
    videoFormat: 1,
    shootingCountry: '7',
    modelType: '1',
    modelTypeList: ['1'],
    picCount: undefined,
    referencePic: [],
    selModel: [],
    intentionModelIds: [],
    // 拍摄要求
    demands: '',
    shootRequired: [
      {
        content: '',
      },
    ],
    // demands: [
    //   {
    //     value: '',
    //   },
    // ],
    // 限制条件
    conditions: [
      {
        value: '',
      },
    ],
    shootCount: 5,
  }
  temporaryChangeData.value = {
    platform: '0',
    shootingCountry: '7',
    modelType: '1',
    modelTypeList: ['1'],
  }
  filePicList.value = []
  productPicUrl.value = ''
  formRef.value?.clearValidate()
}
const loading = ref(false)
const loadingBtn = ref(false)
const disabled_edit = ref(false)
const disabled = ref(false)

const productLinkLoading = ref(false)
const productPicUrlError = ref(false)

const videoFormatList = ref(videoFormatOptions)

//需要更换的模特数量
const cannotModelNumber = ref(0)
const cannotModelList = ref<any[]>([])
function isChangeModel(id: any) {
  return cannotModelList.value.includes(id)
}

const rules = ref({
  platform: [{ required: true, message: '请选择平台', trigger: 'blur' }],
  productChinese: [
    { required: true, message: '请输入产品中文名', trigger: 'blur' },
    // { pattern: chineseCharacter_d_reg, message: '请输入中文', trigger: 'change' },
  ],
  productEnglish: [
    { required: true, message: '请输入产品英文名', trigger: 'blur' },
    { pattern: englishCharacter_d_chineseCharacter_reg, message: '请输入产品英文名', trigger: 'change' },
  ],
  isLink: [{ required: true, validator: checkIsLink, trigger: 'change' }],
  isObject: [{ required: true, message: '请选择是否发货', trigger: 'blur' }],
  referenceVideoLink: [{ required: false, message: '请输入参考视频的链接', trigger: 'blur' }],
  videoFormat: [{ required: true, message: '请选择视频格式', trigger: 'blur' }],
  shootingCountry: [{ required: true, message: '请选择国家', trigger: 'blur' }],
  modelType: [{ required: true, message: '请选择模特类型', trigger: 'change' }],
  modelTypeList: [{ required: true, message: '请选择模特类型', trigger: 'change' }],
  demands: [{ required: false, validator: checkDemands, trigger: 'change' }],
  conditions: [{ required: false, message: '*请用中文，清晰简短描述限制条件', trigger: 'change' }],
  shootCount: [{ required: true, message: '请选择拍摄数量', trigger: 'blur' }],
  productPicInfo: [{ required: true, validator: selectProductPic, trigger: 'blur' }],
  // conditions: [{ required: false, validator: checkConditions, trigger: 'change' }],
})

//根据已添加的图片计算剩余可添加图片数量
watch(
  () => form.value.referencePic?.length,
  (newVal, oldVal) => {
    addPicNumber1.value = addPicNumber.value - (newVal || 0)
  }
)
watch(
  () => addPicNumber.value,
  (newVal, oldVal) => {
    addPicNumber1.value = addPicNumber.value - (form.value.referencePic?.length || 0)
  }
)

watch(
  () => form.value.picCount,
  (newVal, oldVal) => {
    newVal == undefined ? (form.value.referencePic = []) : ''
  }
)

function checkIsLink(rule: any, value: any, callback: any) {
  if (form.value.isLink == 0) return callback()
  if (value && !form.value.productLink) {
    return callback(new Error('请输入产品链接'))
  }
  return callback()
}
function checkDemands(rule: any, value: any, callback: any) {
  if (value) {
    // let index = value.findIndex((v: { value: string }) => v.value && !english_d_reg.test(v.value))
    if (chinese_c_reg.test(value)) {
      return callback(new Error('*请用英文，简短描述重要的拍摄要求'))
    }
  }
  return callback()
}
function selectProductPic(rule: any, value: any, callback: any) {
  if ((value && !form.value.filePicList) || (form.value.filePicList && form.value.filePicList.length === 0)) {
    return callback(new Error('请上传产品图'))
  }
  return callback()
}
function checkConditions(rule: any, value: any, callback: any) {
  if (value) {
    let index = value.findIndex((v: { value: string }) => v.value && !chineseCharacter_d_reg.test(v.value))
    if (index != -1) {
      return callback(new Error('*请用中文，清晰简短描述限制条件'))
    }
  }
  return callback()
}
// 表单联动 模特类型禁用
const modelTypeDisabled = computed(() => {
  if (form.value.platform != '0') {
    // 平台不是亚马逊
    return true
  } else if (form.value.shootingCountry != '7') {
    // 平台亚马逊且国家不是美国
    return true
  }
  if (form.value.picCount && props.type === 'edit') {
    return true
  }
  if (form.value.isLink == 0) {
    // 产品无链接
    return true
  }
  return false
})

//表单联动
const isSelectLinkDisabled = computed(() => {
  if ((form.value.platform == '0' && form.value.modelType == '0') || form.value.modelType == '3') {
    return true
  } else {
    return false
  }
})
//选择链接后清空之前内容
function changeIsLink() {
  // form.value.productLink = ''
  // props.type == 'add'? form.value.referencePic = [] : ''
  filePicList.value = []
}

//模特类型多选切换
const modelTypeLNumber = ref(0)
const isShowHint = ref(false)
// watch(
//   () => form.value.modelTypeList,
//   (newVal, oldVal: any) => {
//     let isOld: any
//     let isNew: any
//     if (oldVal && oldVal.length > 0) {
//       isOld = oldVal.indexOf('1')
//     }
//     if (newVal && newVal.length > 0) {
//       isNew = newVal.indexOf('1')
//     }
//     if (isOld != -1 && isNew == -1) {
//       if (form.value.selModel && form.value.selModel.length > 0) {
//         isShowHint.value = true
//       }
//       hintDialogTitle.value = '拍摄照片服务仅支持素人创作者'
//       hintDialogContent.value = '若选择照片服务，意向模特中已选的亚马逊影响者会被清空哟~'
//       temporaryChangeData.value.modelTypeList = oldVal
//     }
//   }
// )
function handleMouseEnterModelType(e: any, val: any, type?: boolean) {
  if (val == '0') {
    return type
      ? showTooltips(e, 'order-create-model-yxz-disabled')
      : showTooltips(e, 'order-create-model-yxz')
    // return showTooltips(e, 'order-create-model-yxz')
  }
  if (val == '1') {
    return showTooltips(e, 'order-create-model-sr')
  }
  return ''
}

function handleMouseEnterLinkType(e: any, val: any, type?: boolean) {
  if (val == '0' && type) {
    return showTooltips(e, 'order-create-link-wlj-disabled')
  }
}

const oldModelType = ref([])
watch(
  () => form.value.modelTypeList,
  (newVal, oldVal: any) => {
    if (oldVal && oldVal.length > 0) {
      oldModelType.value = oldVal
    }
  }
)

function changeModelValue(value: any) {
  if (value.length > 0) {
    if (value.indexOf('0') != -1) {
      form.value.isLink = 1
    }
  }
  // let list = []
  // if (form.value.selModel && form.value.selModel.length > 0) {
  //   form.value.selModel.indexOf( item => {

  //   })
  // }
  if (value.length == 1) {
    form.value.modelType = value[0]
    if (form.value.selModel && form.value.selModel.length > 0) {
      let isLife = form.value.selModel.findIndex(item => {
        return item.type != value
      })
      if (isLife != -1) {
        hintDialogTitle.value = '变更模特类型会导致已选模特无法满足拍摄需求'
        hintDialogContent.value = '需重新选择模特'
        isShowHint.value = true
      }
    }
  }
  if (value.length == 2) {
    form.value.modelType = '3'
  }
  if (value.length == 0 && form.value.selModel && form.value.selModel.length > 0) {
    hintDialogTitle.value = '变更模特类型会导致已选模特无法满足拍摄需求'
    hintDialogContent.value = '需重新选择模特'
    isShowHint.value = true
  }
}

const changeValue = ref('')

function changeFormValue(value: any, type: string) {
  if (form.value.modelTypeList && form.value.modelTypeList.length == 2) {
    form.value.modelType = '3'
  }
  if (!isMultiSelModel.value) {
    form.value.modelTypeList = ['1']
    form.value.modelType = '1'
    temporaryChangeData.value.modelType = '1'
  }
  let content = ''
  let isConfirm = false
  changeValue.value = value
  if (type == 'platform') {
    content = '平台'
    isConfirm = handleModelPlatform(value)
    hintDialogTitle.value = '变更平台会导致已选模特无法满足拍摄需求'
  } else if (type == 'modelType') {
    content = '模特类型'
    isConfirm = true
    hintDialogTitle.value = '变更模特类型会导致已选模特无法满足拍摄需求'
  } else {
    content = '拍摄国家'
    isConfirm = true
    hintDialogTitle.value = '变更拍摄国家会导致已选模特无法满足拍摄需求'
  }
  hintDialogContent.value = '需重新选择模特'
  if (form.value.selModel && form.value.selModel.length > 0 && isConfirm) {
    isShowHint.value = true
  } else {
    if (type == 'platform') {
      selectPlatformChange(value)
      temporaryChangeData.value.platform = value + ''
    } else if (type == 'shootingCountry') {
      selectNationChange(value)
      temporaryChangeData.value.shootingCountry = value + ''
    } else {
      temporaryChangeData.value.modelType = value + ''
      selectModelTypeChange(value)
    }
  }
  //   ElMessageBox.confirm('', {
  //     customStyle: {
  //       'border-radius': '10px',
  //     },
  //     message: h('div', null, [
  //       h('div', { style: 'text-align: center;' }, `变更${content}会导致已选模特无法满足拍摄需求`),
  //       h('div', { style: 'text-align: center;' }, `需重新选择模特`),
  //     ]),
  //     confirmButtonText: '好的',
  //     cancelButtonText: '取消',
  //     center: true,
  //     roundButton: true,
  //   })
  //     .then(() => {
  //       if (type == 'platform') {
  //         selectPlatformChange(value)
  //         temporaryChangeData.value.platform = value + ''
  //       } else if (type == 'shootingCountry') {
  //         selectNationChange(value)
  //         temporaryChangeData.value.shootingCountry = value + ''
  //       } else {
  //         temporaryChangeData.value.modelType = value + ''
  //         selectModelTypeChange(value)
  //       }
  //     })
  //     .catch(() => {
  //       if (type == 'platform') {
  //         form.value.platform = temporaryChangeData.value.platform
  //       } else if (type == 'shootingCountry') {
  //         form.value.shootingCountry = temporaryChangeData.value.shootingCountry
  //       } else {
  //         form.value.modelType = temporaryChangeData.value.modelType
  //       }
  //     })
  // } else {
  //   if (type == 'platform') {
  //     selectPlatformChange(value)
  //     temporaryChangeData.value.platform = value + ''
  //   } else if (type == 'shootingCountry') {
  //     selectNationChange(value)
  //     temporaryChangeData.value.shootingCountry = value + ''
  //   } else {
  //     temporaryChangeData.value.modelType = value + ''
  //     selectModelTypeChange(value)
  //   }
  // }
}

function handleDialogCancel() {
  isShowHint.value = false
  if (hintDialogTitle.value == '变更平台会导致已选模特无法满足拍摄需求') {
    form.value.platform = temporaryChangeData.value.platform
    if (isMultiSelModel.value) {
      form.value.modelTypeList = oldModelType.value
      if (form.value.modelTypeList && form.value.modelTypeList.length > 0) {
        form.value.modelTypeList.length == 2
          ? (form.value.modelType = '3')
          : (form.value.modelType = form.value.modelTypeList[0])
      }
    } else {
      if (form.value.platform != Platform['Amazon']) {
        form.value.modelType = '1'
      } else {
        form.value.modelType = temporaryChangeData.value.modelType
      }
    }
  } else if (hintDialogTitle.value == '变更拍摄国家会导致已选模特无法满足拍摄需求') {
    form.value.shootingCountry = temporaryChangeData.value.shootingCountry
    if (isMultiSelModel.value) {
      form.value.modelTypeList = oldModelType.value
      if (form.value.modelTypeList && form.value.modelTypeList.length > 0) {
        form.value.modelTypeList.length == 2
          ? (form.value.modelType = '3')
          : (form.value.modelType = form.value.modelTypeList[0])
      }
    } else {
      form.value.modelType = temporaryChangeData.value.modelType
    }
  } else if (hintDialogTitle.value == '变更模特类型会导致已选模特无法满足拍摄需求') {
    if (isMultiSelModel.value) {
      form.value.modelTypeList = oldModelType.value
      if (form.value.modelTypeList && form.value.modelTypeList.length > 0) {
        form.value.modelTypeList.length == 2
          ? (form.value.modelType = '3')
          : (form.value.modelType = form.value.modelTypeList[0])
      }
    } else {
      form.value.modelType = temporaryChangeData.value.modelType
    }
  } else {
    form.value.picCount = undefined
  }
}

function handleDialogConfirm() {
  if (hintDialogTitle.value == '变更平台会导致已选模特无法满足拍摄需求') {
    selectPlatformChange(changeValue.value)
    temporaryChangeData.value.platform = changeValue.value + ''
  } else if (hintDialogTitle.value == '变更拍摄国家会导致已选模特无法满足拍摄需求') {
    selectNationChange(changeValue.value)
    temporaryChangeData.value.shootingCountry = changeValue.value + ''
  } else if (hintDialogTitle.value == '变更模特类型会导致已选模特无法满足拍摄需求') {
    if (isMultiSelModel.value) {
      // selectModelTypeChange(changeValue.value)
      let data = ''
      if (form.value.modelTypeList && form.value.modelTypeList.length > 0) {
        data = form.value.modelTypeList[0]
      } else {
        form.value.selModel = []
      }
      form.value.selModel = form.value.selModel?.filter(item => {
        return item.type == data
      })
    } else {
      temporaryChangeData.value.modelType = changeValue.value + ''
      selectModelTypeChange(changeValue.value)
    }
  } else {
    if (form.value.selModel && form.value.selModel.length > 0) {
      form.value.selModel = form.value.selModel?.filter(item => {
        return item.type != '0'
      })
    }
    form.value.modelTypeList = form.value.modelTypeList?.filter(item => {
      return item != '0'
    })
    form.value.modelType = '1'
  }
  isShowHint.value = false
  changeValue.value = ''
}

//平台切换时临时数据
const temporaryChangeData = ref({
  platform: '0',
  shootingCountry: '7',
  modelType: '1',
  modelTypeList: ['1'],
})

//切换时判断是否有符合条件的模特
function handleModelPlatform(value: string | number | boolean) {
  type PlatformType = 'Tiktok' | 'App' | 'Other' | 'Amazon'
  let temp: PlatformType
  let list: any[] = []
  if (value === Platform['Amazon']) {
    temp = 'Amazon'
    if (props.type == 'add') {
      form.value.shootCount = 5
    } else {
      form.value.shootCount = 1
    }
  }
  if (value === Platform['Tiktok']) {
    temp = 'Tiktok'
    form.value.shootCount = 1
  }
  if (value === Platform['App']) {
    temp = 'App'
    form.value.shootCount = 1
  }
  if (value === Platform['Other']) {
    temp = 'Other'
    form.value.shootCount = 1
  }
  if (form.value.selModel && form.value.selModel.length > 0) {
    list = form.value.selModel.filter(item => {
      item.platform = item.platform + ''
      return item.platform.indexOf(Platform[temp]) != -1
    })
  }
  return list.length == form.value.selModel?.length ? false : true
}

// 表单联动 平台
function selectPlatformChange(value: string | number | boolean) {
  if (value === Platform['Amazon']) {
    form.value.videoFormat = 1
    // form.value.picCount = undefined
    props.type === 'edit' ? (disabled_edit.value = true) : (disabled_edit.value = false)
    // if (props.type === 'edit') {
    //   disabled_edit.value = true
    // }else
    if (form.value.selModel && form.value.selModel.length > 0) {
      // form.value.selModel = []
      form.value.selModel = form.value.selModel.filter(item => {
        return item.platform.indexOf(Platform['Amazon']) != -1
      })
    }
    rules.value.referenceVideoLink[0].required = false
    return
  }
  if (value === Platform['Tiktok']) {
    disabled_edit.value = false
    form.value.videoFormat = 2
    form.value.modelType = '1'
    if (props.type === 'edit') {
      disabled_edit.value = true
    }

    if (form.value.selModel && form.value.selModel.length > 0) {
      // form.value.selModel = []
      form.value.selModel = form.value.selModel.filter(item => {
        return item.platform.indexOf(Platform['Tiktok']) != -1
      })
    }
    rules.value.referenceVideoLink[0].required = false
    return
  }

  if (value === Platform['App']) {
    disabled_edit.value = true
    form.value.videoFormat = 1
    form.value.picCount = undefined
    form.value.modelType = '1'
    if (form.value.selModel && form.value.selModel.length > 0) {
      // form.value.selModel = []
      form.value.selModel = form.value.selModel.filter(item => {
        return item.platform.indexOf(Platform['App']) != -1
      })
    }
    rules.value.referenceVideoLink[0].required = false
    return
  }
  form.value.videoFormat = 1
  form.value.modelType = '1'

  if (value === Platform['Other']) {
    disabled_edit.value = false
    if (props.type === 'edit') {
      disabled_edit.value = true
    }
    if (form.value.selModel && form.value.selModel.length > 0) {
      // form.value.selModel = []
      form.value.selModel = form.value.selModel.filter(item => {
        return item.platform.indexOf(Platform['Other']) != -1
      })
    }
    rules.value.referenceVideoLink[0].required = false
    return
  }
  rules.value.referenceVideoLink[0].required = false
}
// 表单联动 拍摄国家
function selectNationChange(value: string | number | boolean) {
  if (value != '7' && form.value.platform == '0') {
    form.value.modelType = '1'
    disabled_edit.value = false
  }
  if (props.type === 'edit') {
    disabled_edit.value = true
  }
  if (form.value.selModel && form.value.selModel.length > 0) {
    form.value.selModel = form.value.selModel.filter(item => {
      return item.nation == value
    })
  }

  // if(form.value.selModel?.nation != value) {
  //   form.value.selModel = {}
  // }
}
// 表单联动 模特类型
function selectModelTypeChange(value: string | number | boolean) {
  if (form.value.modelType === '1' && form.value.platform == Platform['Amazon']) {
    disabled_edit.value = false
  } else {
    disabled_edit.value = true
    form.value.picCount = undefined
  }

  if (form.value.selModel && form.value.selModel.length > 0) {
    form.value.selModel = form.value.selModel.filter(item => {
      if (item.platform) {
        return item.platform.type == value
      } else {
        return item.type == value
      }
    })
  }
  if (props.type === 'edit') {
    disabled_edit.value = true
  }
  // if(form.value.selModel?.type != value) {
  //   form.value.selModel = {}
  // }
}

function handleModelType(n: any) {
  let biz = biz_model_type.value.find((dict: { value: any }) => dict.value == n)
  return biz ? biz.label : ''
}

let pcVal: boolean = false
// 处理照片数量单选框取消选中 picCountClick->picCountChange
function picCountClick(val: number | string) {
  if (isShowPic.value) return
  if (disabled.value || (disabled_edit.value && form.value.modelType != '1')) return
  pcVal = false

  if (form.value.picCount === val) {
    pcVal = true
    form.value.picCount = undefined
  }
}
function picCountChange() {
  if (isShowPic.value) return
  formRef.value?.clearValidate('modelTypeList')
  if (
    form.value.modelTypeList &&
    form.value.modelTypeList.length == 2 &&
    form.value.selModel &&
    form.value.selModel.length > 0
  ) {
    isShowHint.value = true
    hintDialogTitle.value = '拍摄照片服务仅支持素人创作者'
    hintDialogContent.value = '若选择照片服务，意向模特中已选的亚马逊影响者会被清空哟~'
  } else if (form.value.modelTypeList && form.value.modelTypeList.length == 2) {
    form.value.modelTypeList = form.value.modelTypeList?.filter(item => {
      return item != '0'
    })
    form.value.modelType = '1'
  }
  // 点击取消选中且清空参考图
  if (form.value.picCount == 1) {
    // addPicNumber.value = 2
    addPicNumber.value = 2
    addPicNumber1.value = 2
    if (form.value.referencePic && form.value.referencePic.length > 2) {
      form.value.referencePic = form.value.referencePic.slice(0, 2)
    }
  } else {
    // addPicNumber.value = 5
    addPicNumber.value = 5
    addPicNumber1.value = 5
  }

  if (pcVal) {
    form.value.picCount = undefined
    form.value.referencePic = []
  }
}
// 查看参考照片案例
function lookPhoto() {
  previewImage('https://pstatic.woniu.video/static/photo-example-Ce0FVvR.webp', 2)
}

// 添加 拍摄要求/限制条件 输入框
function handleAddInput(key: 'demands' | 'conditions') {
  // form.value[key]!.push({
  //   value: '',
  // })
}
// 删除 拍摄要求/限制条件 输入框
function handleDelInput(key: 'demands' | 'conditions', i: number) {
  // if (props.type === 'edit') {
  //   const item = form.value[key]
  //   if (item && item[i].id) {
  //     delForm.value[key].push(item)
  //   }
  // }
  // form.value[key]!.splice(i, 1)
}

// 选择意向模特
function openSelect(id?: number, index?: number) {
  if (isMultiSelModel.value && form.value.modelTypeList && form.value.modelTypeList.length == 0) {
    ElMessage.warning('请选择模特类型')
    return
  }
  if (cannotModelList.value && cannotModelList.value.length > 0) {
    form.value.selModel = form.value.selModel?.filter(item => {
      return cannotModelList.value.indexOf(item.id) == -1
    })
    cannotModelList.value = []
    cannotModelNumber.value = 0
  }
  SelectSingleModelRef.value.open(form.value.selModel)
}
// 选择意向模特成功
function handleSelectSuccess(arr: any[]) {
  SelectSingleModelRef.value?.close()
  form.value.selModel = arr
}
// 获取链接图片
function getLink(value: string) {
  // debounce(getLinkApi(value))
}
let curRandomCode = ''
function getLinkApi(url: string) {
  if (url) {
    curRandomCode = Math.random().toString(36).substring(2)
    let RandomCode = curRandomCode
    productLinkLoading.value = true
    form.value.productPicInfo.id = 0
    form.value.productPicInfo.name = ''
    form.value.productPicInfo.picUrl = ''
    form.value.productPicInfo.videoUrl = ''
    uploadAmazonUrl({ url })
      .then(res => {
        if (RandomCode === curRandomCode && res.code == 200) {
          if (res.msg) {
            productPicUrlError.value = true
          } else {
            productPicUrlError.value = false
            form.value.productPicInfo.id = res.data.id
            form.value.productPicInfo.name = res.data.name
            form.value.productPicInfo.picUrl = res.data.picUrl
          }
        }
      })
      .catch(() => {
        if (RandomCode === curRandomCode) {
          productPicUrlError.value = true
        }
      })
      .finally(() => {
        if (RandomCode === curRandomCode) {
          productLinkLoading.value = false
        }
      })
  }
}
function handlePictureCardPreview(file: { url: string }) {
  showViewer([productPicUrl.value])
}

function beforeRemove() {
  form.value.filePicList = []
  form.value.productPic = undefined
  filePicList.value = []
}

//产品图url
const productPicUrl = ref()
function uploadPhotoFile(options: { file: string | Blob }) {
  const formData = new FormData()
  formData.append('file', options.file)
  uploadCloudFile(formData).then((res: any) => {
    if (res.code == 200) {
      // form.value.productPicList=[...res.data]
      form.value.productPic = res.data.id
      productPicUrl.value = res.data.picUrl
      const data = {
        id: res.data.id,
        name: res.data.name,
        url: res.data.picUrl,
      }
      form.value.filePicList = []
      form.value.filePicList?.push(data)
    }
  })
}
const whiteList = 'PNG,png,JPG,jpg,JPEG,jpeg'
function beforeUpload(raw: any) {
  const fileSuffix = raw.name.substring(raw.name.lastIndexOf('.') + 1)
  if (whiteList.indexOf(fileSuffix) === -1) {
    ElMessage({
      message: `请上传格式为png,jpg,jpeg的文件！`,
      type: 'warning',
    })
    return false
  }
  if (raw.size > 1024 * 1024 * 5) {
    ElMessage({
      message: `上传大小不超过5M！`,
      type: 'warning',
    })
    return false
  }
  if (filePicList.value && filePicList.value.length >= 1) {
    ElMessage({
      message: `最多可上传1张图片！`,
      type: 'warning',
    })
    return false
  }
  return true
}

const addCatParams = ref<any>()
const addCatProductPic = ref<any>()

function handleWarningConfirm() {
  let intentionModelIds = replaceDuplicatesWithEmpty(
    addCatParams.value.intentionModelIds || [],
    cannotModelList.value
  )
  if (props.type === 'add') {
    comfirmAddShopCart(addCatParams.value, addCatProductPic.value, intentionModelIds)
  } else if (props.type === 'editCartOrder') {
    ElMessage.success('修改成功！')
    emits('success')
  }
}

// 下单/修改/加入购物车
function addSubmit(toCart: boolean = false) {
  // if (form.value.modelType || form.value.modelType == '0') {
  //   form.value.modelTypeList = [form.value.modelType]
  // }
  formRef.value.validate((valid: any) => {
    if (valid) {
      if (form.value.demands && form.value.demands.length > 5000) {
        ElMessage.warning('拍摄要求不能超过5000字')
        return
      }

      addSubmitCat(toCart)
    }
  })
}

function replaceDuplicatesWithEmpty(arr1: any[], arr2: any[]) {
  // 创建一个集合来跟踪第二个数组中的元素
  const arr2Set = new Set(arr2)

  // 遍历第一个数组，当元素在第二个数组中出现时，将其替换为空值
  const resultArray = arr1.map((item: any) => (arr2Set.has(item) ? '' : item))

  return resultArray || []
}

//确认加入购物车
function comfirmAddShopCart(data: any, productPic: any, intentionModelIds: any) {
  createOrderAddCart({
    ...data,
    productPic,
    intentionModelIds,
  }).then(res => {
    ElMessage.success('添加购物车成功！')
    emits('successCart')
    addToCartAnimation('.addCartBtn')
  })
}

function handleCannotModel(idList: any) {
  const nameList =
    form.value.selModel?.filter(item => idList.indexOf(item.id) != -1).map(item => ` “${item.name}”`) || []
  return nameList.join('，')
}

function removeStartString(originalString: any) {
  const startString = 'https://pstatic.woniu.video/'
  const regex = new RegExp(`^${startString}`)
  return originalString.replace(regex, '')
}
function addSubmitCat(toCart: boolean = false) {
  let { demands, conditions, isLink, selModel, filePicList, ...params } = form.value

  let referencePic = params.referencePic
  params.limitingCondition = []
  // if (demands && demands.length) {
  //   params.limitingCondition.push(
  //     ...demands
  //       .filter(item => item.value)
  //       .map((item, i) => ({
  //         content: item.value,
  //         id: item.id,
  //         sort: i,
  //         type: 1,
  //         operate: item.id && item.content != item.value ? 'update' : 'add',
  //       }))
  //   )
  // }

  if (selModel && selModel.length) {
    if (selModel.length > form.value.shootCount) {
      // let modelNumber = selModel.length - form.value.shootCount
      let newSelModel = selModel.slice(selModel.length - form.value.shootCount)
      let countList: any[] = []
      newSelModel.forEach((item: any) => {
        countList.push(item.id)
      })
      // form.value.intentionModelIds = toRaw(countList)
      params.intentionModelIds = toRaw(countList)
      // ElMessage.warning('1个视频对应1个模特，请留下' + modelNumber + '个模特即可~')
    } else {
      let countList: any[] = []
      selModel.forEach((item: any) => {
        countList.push(item.id)
      })
      params.intentionModelIds = countList
      // form.value.intentionModelIds = countList
    }
  }
  let productPic: any = ''

  if (!isLink) {
    params.productLink = ''
    // params.productPicId = form.value.productPicId
    // params.productPic = {
    //   id: 0,
    //   name: '',
    //   picUrl: '',
    //   videoUrl: '',
    // }
  } else {
    params.productPic = undefined
    productPic = undefined
    params.productLink = form.value.productLink
  }
  if (filePicList && filePicList.length > 0 && !isLink) {
    // params.productPic = filePicList[0].id
    productPic = filePicList[0].picUrl
    // productPic = removeStartString(filePicList[0].url)
  } else {
    params.productPic = undefined
  }
  if (demands && demands.length > 2) {
    let countList = []
    countList = demands.split('\n')
    params.shootRequired = countList.map((item, index) => {
      // const startIndex = index < 9 ? 2 : 3
      // return { content: item.slice(startIndex), sort: index, type: 1 }
      return { content: item, sort: index, type: 1 }
    })
  } else {
    params.shootRequired = []
  }

  if (params.shootRequired?.length && params.shootRequired.length < 2) {
    if (params.shootRequired[0].content == '') params.shootRequired = []
  }

  if (conditions && conditions.length) {
    params.limitingCondition.push(
      ...conditions
        .filter(item => item.value)
        .map((item, i) => ({
          content: item.value,
          id: item.id,
          sort: i,
          type: 2,
          operate: item.id && item.content != item.value ? 'update' : 'add',
        }))
    )
  }
  // 删除记录
  if (props.type === 'edit' || props.type === 'editCartOrder') {
    params.limitingCondition.push(
      ...delForm.value.demands.map((item, i) => ({
        content: item.content,
        id: item.id,
        type: 1,
        operate: 'delete',
      }))
    )
    params.limitingCondition.push(
      ...delForm.value.conditions.map((item, i) => ({
        content: item.content,
        id: item.id,
        type: 2,
        operate: 'delete',
      }))
    )
  }
  if (referencePic && referencePic.length) {
    params.referencePic = referencePic.map(item => {
      if (item.response && item.response.code == 200) {
        return item.response.data.picUrl
        // return {
        //   id: item.response.data.id,
        //   name: item.response.data.name,
        //   picUrl: item.response.data.picUrl,
        //   videoUrl: '',
        // }
      }
      return item.picUrl
      // return {
      //   id: item.id,
      //   name: item.name,
      //   picUrl: item.picUrl,
      //   videoUrl: '',
      // }
    })
  }
  // if (selModel && selModel.id) {
  //   params.intentionModelId = selModel.id
  // }

  // console.log('submit===>', toCart, params)
  if (props.type === 'add') {
    if (toCart) {
      store.realTimeCheckVip().then(() => {
        if (store.isVip()) {
          loadingBtn.value = true
          disabled.value = true

          createOrderAddCart({
            ...params,
            productPic: productPic,
          })
            .then(res => {
              if (res.data.cannotModel && res.data.cannotModel.length > 0) {
                cannotModelNumber.value = res.data.cannotModel.length
                cannotModelList.value = res.data.cannotModel
                addCatParams.value = params
                addCatProductPic.value = productPic
                MessageBox(`有${res.data.cannotModel?.length}位意向模特已无法满足拍摄需求<br/>
                  <span>需更换订单中模特${handleCannotModel(cannotModelList.value)}</span><br/>
                  <span>若不重选，平台也会为您匹配合适的模特</span>`).then(() => {
                  handleWarningConfirm()
                })
                return
              } else {
                cannotModelList.value = []
                cannotModelNumber.value = 0
              }
              ElMessage.success('添加购物车成功！')
              emits('successCart')
              addToCartAnimation('.addCartBtn')
            })
            .finally(() => {
              loadingBtn.value = false
              disabled.value = false
            })
        } else {
          ElMessage.error('请开通会员后再进行下单')
        }
      })
      return
    }

    createOrder([
      {
        ...params,
        productPic: productPic,
      },
    ])
      .then(res => {
        if (res.data.cannotModel?.length) {
          // form.value.selModel = {}
          ElMessageBox.alert('您选择的意向模特无法接单！请更换', '温馨提示')
          return
        }
        ElMessage.success('下单成功！')
        emits('success', res.data)
      })
      .finally(() => {
        loadingBtn.value = false
        disabled.value = false
      })
  } else if (props.type === 'edit') {
    const intentionModelId = form.value.selModel?.length ? form.value.selModel[0].id : ''
    const intentionModelIds: any[] = []
    ElMessageBox.confirm('确认修改！', '提示', {})
      .then(() => {
        loadingBtn.value = true
        disabled.value = true
        updateOrder({
          ...params,
          id: props.orderId,
          intentionModelId,
          intentionModelIds,
          productPic: productPic,
        })
          .then(res => {
            ElMessage.success('修改成功！')
            emits('success')
          })
          .finally(() => {
            loadingBtn.value = false
            disabled.value = false
          })
      })
      .catch(() => {})
  } else {
    ElMessageBox.confirm('确认修改！', '提示', {})
      .then(() => {
        loadingBtn.value = true
        disabled.value = true

        // const intentionModelId = form.value.intentionModelIds?.length ? form.value.intentionModelIds[0] : ''
        const intentionModelId = form.value.selModel?.length ? form.value.selModel[0].id : ''
        const intentionModelIds: any[] = []
        // const intentionModelId = form.value.intentionModel?.id
        // console.log(intentionModelId,7777);
        handleEditCartOrder(
          {
            ...params,
            productPic: productPic,
            intentionModelId,
            id: props.orderId,
            intentionModelIds,
          },
          productPic
        )
      })
      .catch(() => {})
  }
}

function handleEditCartOrder(params: any, productPic: any) {
  orderEditCart(params)
    .then(res => {
      if (res.data.cannotModel && res.data.cannotModel.length > 0) {
        cannotModelNumber.value = res.data.cannotModel.length
        cannotModelList.value = res.data.cannotModel
        addCatParams.value = params
        addCatProductPic.value = productPic
        MessageBox(`有${res.data.cannotModel?.length}位意向模特已无法满足拍摄需求<br/>
          <span>需更换订单中模特${handleCannotModel(cannotModelList.value)}</span><br/>
          <span>若不重选，平台也会为您匹配合适的模特</span>`).then(() => {
          params.cannotModelIds = res.data.cannotModel
          params.intentionModelId = ''
          handleEditCartOrder(params, productPic)
        })
        return
      } else {
        cannotModelList.value = []
        cannotModelNumber.value = 0
      }
      ElMessage.success('修改成功！')
      emits('successCart', props.orderId, 'edit')
    })
    .finally(() => {
      loadingBtn.value = false
      disabled.value = false
    })
}

// 获取订单数据
function getOrderDetails() {
  loading.value = true
  updateOrderDetails(props.orderId)
    .then(res => {
      // let demands = []
      let demands = '1.'
      let conditions = []
      let referencePic = []
      // if (res.data.shootRequired && res.data.shootRequired.length) {
      //   demands = res.data.shootRequired.map((item: { [x: string]: any }) => ({
      //     value: item.content,
      //     content: item.content,
      //     id: item.id,
      //   }))
      // } else {
      //   demands.push({
      //     value: '',
      //   })
      // }
      if (res.data.shootRequired && res.data.shootRequired.length) {
        let countList: any[] = []
        res.data.shootRequired.forEach((item: any, index: any) => {
          // countList.push(`${index + 1}.` + item.content)
          countList.push(item.content)
        })
        demands = countList.join('\n')
        // console.log(countList.join('\n'),777);
      }
      if (res.data.limitingCondition && res.data.limitingCondition.length) {
        conditions = res.data.limitingCondition.map((item: { [x: string]: any }) => ({
          value: item.content,
          content: item.content,
          id: item.id,
        }))
      } else {
        conditions.push({
          value: '',
        })
      }
      if (res.data.referencePic && res.data.referencePic.length) {
        referencePic = res.data.referencePic.map((item: any) => ({
          url: item,
          picUrl: item,
        }))
      }

      let selModel: any[] = []
      if (res.data.intentionModel) {
        selModel.push(res.data.intentionModel)
        selModel[0].picUrl = selModel[0].pic
        // selModel[0].platform = res.data.platform
      }

      const tempData = {
        url: res.data.productPic ? pathHead + res.data.productPic : '',
        name: res.data.productPic ? res.data.productPic : '',
        id: undefined,
        picUrl: res.data.productPic ? res.data.productPic : undefined,
      }
      let filePicList: any[] = []
      if (tempData.url && tempData.url != null) {
        filePicList = [tempData]
      }
      let modelTypeList: any[] = []
      if (res.data.modelType == '3') {
        modelTypeList = ['0', '1']
      } else if (res.data.platform == Platform['Amazon'] && res.data.shootingCountry == '7') {
        modelTypeList[0] = res.data.modelType + ''
      } else {
        modelTypeList[0] = '1'
      }
      form.value = {
        platform: '' + res.data.platform,
        productChinese: res.data.productChinese,
        productEnglish: res.data.productEnglish,
        isLink: res.data.productLink ? 1 : 0,
        productLink: res.data.productLink,
        productPic: res.data.productPic,
        productPicInfo: res.data.productPic,
        isObject: res.data.isObject,
        referenceVideoLink: res.data.referenceVideoLink,
        videoFormat: res.data.videoFormat,
        shootingCountry: res.data.shootingCountry + '',
        modelType: '' + res.data.modelType,
        modelTypeList,
        picCount: res.data.picCount === 0 ? undefined : res.data.picCount,
        referencePic,
        filePicList: filePicList || [],
        selModel,
        demands,
        conditions,
        shootCount: 1,
      }
      addPicNumber.value = form.value.picCount == 2 ? 5 : 2
      productPicUrl.value = res.data.productPic ? res.data.productPic : ''
      temporaryChangeData.value.modelType = form.value.modelType
      temporaryChangeData.value.platform = form.value.platform
      temporaryChangeData.value.shootingCountry = form.value.shootingCountry
    })
    .finally(() => (loading.value = false))
}

//获取购物车订单详情
function getCartDetails() {
  loading.value = true
  updateCartOrderDetails(props.orderId)
    .then(res => {
      if (res.data.modelType == 0 || res.data.platform == 3) {
        disabled_edit.value = true
      }
      // let demands = []
      let demands = '1.'
      let conditions = []
      let referencePic = []
      if (res.data.shootRequired && res.data.shootRequired.length) {
        let countList: any[] = []
        res.data.shootRequired.forEach((item: any, index: any) => {
          // countList.push(`${index + 1}.` + item.content)
          countList.push(item.content)
        })
        demands = countList.join('\n')
        // console.log(countList.join('\n'),777);
      }
      if (res.data.limitingCondition && res.data.limitingCondition.length) {
        conditions = res.data.limitingCondition.map((item: { [x: string]: any }) => ({
          value: item.content,
          content: item.content,
          id: item.id,
        }))
      } else {
        conditions.push({
          value: '',
        })
      }
      if (res.data.referencePic && res.data.referencePic.length) {
        referencePic = res.data.referencePic.map((item: any) => ({
          url: item,
          picUrl: item,
        }))
      }
      let selModel: any[] = []
      if (res.data.intentionModel) {
        selModel.push(res.data.intentionModel)
        selModel[0].picUrl = selModel[0].pic
        // selModel[0].platform = res.data.platform
      }
      const tempData = {
        url: res.data.productPic ? pathHead + res.data.productPic : '',
        name: res.data.productPic ? res.data.productPic : '',
        id: undefined,
        picUrl: res.data.productPic ? res.data.productPic : undefined,
      }
      // if (tempData.url && tempData.url != null) {
      //   form.value.filePicList = [tempData]
      //   console.log(form.value.filePicList,6666);

      //   // filePicList.value.push(tempData)
      // }
      let filePicList: any[] = []
      if (tempData.url && tempData.url != null) {
        filePicList = [tempData]
      }
      let modelTypeList: any[] = []
      if (res.data.modelType == '3') {
        modelTypeList = ['0', '1']
      } else if (res.data.platform == Platform['Amazon'] && res.data.shootingCountry == '7') {
        modelTypeList[0] = res.data.modelType + ''
      } else {
        modelTypeList[0] = '1'
      }

      form.value = {
        platform: '' + res.data.platform,
        productChinese: res.data.productChinese,
        productEnglish: res.data.productEnglish,
        isLink: res.data.productLink ? 1 : 0,
        productLink: res.data.productLink,
        productPicInfo: res.data.productPic,
        isObject: res.data.isObject,
        referenceVideoLink: res.data.referenceVideoLink,
        videoFormat: res.data.videoFormat,
        shootingCountry: res.data.shootingCountry + '',
        modelType: '' + res.data.modelType,
        modelTypeList,
        picCount: res.data.picCount === 0 ? undefined : res.data.picCount,
        referencePic,
        filePicList: filePicList || [],
        selModel,
        demands,
        conditions,
        shootCount: 1,
      }
      addPicNumber.value = form.value.picCount == 2 ? 5 : 2
      productPicUrl.value = res.data.productPic ? res.data.productPic : ''
      temporaryChangeData.value.modelType = form.value.modelType
      temporaryChangeData.value.platform = form.value.platform
      temporaryChangeData.value.shootingCountry = form.value.shootingCountry
    })
    .finally(() => (loading.value = false))
}

init()
</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';
@use '@/styles/customFormDisabled.scss';
:deep(.el-radio-button) {
  .el-radio-button__inner {
    border-radius: 15px;
  }
}
:deep(.el-radio-group) {
  .el-radio-button.is-active {
    .el-radio-button__inner {
      border-left: none;
    }
  }
}
// .el-checkbox-button.is-checked:first-child .el-checkbox-button__inner
:deep(.el-checkbox-button) {
  .el-checkbox-button__inner {
    border-radius: 15px;
  }
}
:deep(.el-checkbox-group) {
  .el-checkbox-button.is-active {
    .el-checkbox-button__inner {
      border-left: none;
      border-left-color: transparent;
    }
  }
  .el-checkbox-button.is-checked:first-child {
    .el-checkbox-button__inner {
      border-left: none;
    }
  }
}

.order-form {
  .loading-icon {
    animation: loadingIcon 2s linear infinite;
  }
  @keyframes loadingIcon {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  //上传样式
  .image-upload {
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px dashed var(--el-border-color-darker);
    width: 70px;
    height: 70px;
    border-radius: 6px;
    background-color: var(--el-fill-color-lighter);
    cursor: pointer;
    // margin-bottom: 10px;
    // margin-top: -10px;
    &:hover {
      border-color: #6eb92b;
    }
  }

  .image-list {
    height: 70px;
    position: relative;
    margin-right: 8px;
    // margin-bottom: 8px;
    .icon {
      height: 17px;
      width: 17px;
      cursor: pointer;
    }
    .image-modal {
      position: absolute;
      top: 4px;
      right: 4px;
      // width: 70px;
      // height: 100%;
      // border-radius: 6px;
      // background-color: #2c2b2b66;
      z-index: 9;
      // opacity: 0;
      // &:hover {
      //   opacity: 1;
      // }
    }
  }

  .image-item {
    border-radius: 6px;
    box-sizing: border-box;
    width: 70px;
    height: 70px;
    // margin-right: 10px;
  }
  // .image-modal {

  // }

  .image-error {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // 非会员头部提示
  .nomember-tips {
    background: #f59a23;
    padding: 5px 20px;
    border-radius: 15px;
    font-size: 12px;
    margin-bottom: 15px;
    &__text {
      color: #fff;
    }
    &__btn {
      cursor: pointer;
      padding: 2px 10px;
      background: #fdebd4;
      border-radius: 15px;
    }
  }

  .item {
    gap: 10px;
    width: 100%;
    cursor: pointer;
    margin-bottom: 8px;
    color: var(--el-color-primary);

    .el-input {
      width: 80%;
    }
  }
  .form-button {
    margin-top: 15px;
  }
  .form-back {
    // height: 100%;
    height: 675px;
    border-radius: 15px 18px;
    padding-right: 18px;
  }
  .prompt-title {
    margin: 0 0 15px 15px;
    padding-top: 15px;
    color: #aaa;
    font-size: 14px;
  }
  .select-number {
    margin-left: 10px;
    font-size: 12px;
    color: var(--el-color-primary);
  }

  .prompt-text {
    color: #aaa;
    font-size: 12px;
  }

  .place-order-button {
    width: 150px;
    height: 44px;
    font-size: 18px;
    font-weight: bold;
    border-radius: 50px;
  }

  .error-text {
    color: var(--el-color-danger);
  }

  :deep(.el-upload-list__item) {
    width: 80px;
    height: 80px;
  }
  :deep(.disabled) {
    .el-upload--picture-card {
      display: none;
    }
  }
  :deep(.hidden) {
    .el-upload--picture-card {
      display: none;
    }
  }
  :deep(.el-upload--picture-card) {
    width: 80px;
    height: 80px;
  }
  :deep(.el-input-number) {
    .el-input-number__decrease,
    .el-input-number__increase {
      background-color: #5695c0;

      color: #fff;
    }
  }
  :deep(.el-image__error) {
    text-align: center;
  }
}
.model-info {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
  // align-items: center;
  margin-top: 10px;
  justify-content: start;
  max-height: 400px;
  overflow: auto;
  :deep(.el-image__inner) {
    height: auto;
  }
  &__item {
    transition: opacity 0.5s ease;
    opacity: 1;
    //
    //margin-right: 8px;
    position: relative;
    cursor: pointer;

    .head-img {
      cursor: pointer;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      border: 1px solid var(--border-gray-color);
      background: var(--border-gray-color);
      line-height: 1;
    }
    .modal-lose {
      color: #fff;
      width: 50px;
      height: 50px;
      position: absolute;
      inset: 0;
      border-radius: 50%;
      background-color: #2c2b2b66;
      z-index: 9;
      opacity: 1;
    }
    // .modal-lose {
    //   width: 40px;
    //   height: 40px;
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   border-radius: 50%;
    //   background-color: #2c2b2b66;
    //   z-index: 9;
    //   opacity: 1;
    // }
    .play-modal {
      width: 50px;
      height: 50px;
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 50%;
      // width: 100%;
      // height: 100%;
      background-color: #2c2b2b66;
      z-index: 9;
      opacity: 0;
    }
    &:hover {
      .play-modal {
        opacity: 1;
      }
    }
  }
  .model-more {
    cursor: pointer;
    &__icon {
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 800;
      color: #fff;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: var(--el-color-primary);
      font-size: 20px;
    }
    &__text {
      margin-top: -7px;
      // line-height: 1;
      font-size: 12px;
      text-align: center;
      color: var(--el-color-primary);
    }
  }
}
.head-name {
  max-width: 50px;
  margin-top: -10px;
  line-height: 1;
  font-size: 12px;
  color: #000;
  text-align: center;
}
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
.demands-count {
  position: absolute;
  bottom: 3px;
  right: 10px;
  font-size: 12px;
  color: #909399;
  background: #fff;
  line-height: 18px;
}
.hint-dialog {
  display: flex;
  &__title {
    font-weight: 400;
  }
  &__content {
    color: #aaa;
  }
}
.hint-line {
  position: absolute;
  left: 0;
  width: 100%;
  height: 1px;
  background: #dad8d8;
  margin-top: 15px;
}
.shootTips {
  color: #bbb;
  font-size: 12px;
  line-height: 16px;
  margin-top: 8px;
  font-weight: 500;
  font-family: Arial;
  transform: scale(0.9);
  position: relative;
  top: -2px;
  left: -13px;
}
</style>
