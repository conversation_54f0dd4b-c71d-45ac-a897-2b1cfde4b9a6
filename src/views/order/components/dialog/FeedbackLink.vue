<template>
  <div class="content-box">
    <el-table
      :data="link"
      style="width: 100%"
      header-row-class-name="table-primary-header-row"
      :row-class-name="tableRowClassName"
      v-loading="loading"
      border
    >
      <template #empty>
        <Empty image-type="order" description="暂无数据" :image-size="120" />
      </template>
      <el-table-column prop="createTime" label="反馈时间" align="center" width="170" />
      <el-table-column prop="type" label="类型" align="center" width="100" />
      <el-table-column prop="url" label="链接" align="center" min-width="190">
        <template v-slot="{ row, $index }">
          <div style="display: flex; align-items: center">
            <div class="one-ell productLink">
              <el-link type="primary" :underline="true" target="_blank" :href="row.url">
                {{ row.url }}
              </el-link>
            </div>
            <template v-if="row.isNew">
              <el-tag style="margin-left: 5px" type="danger" effect="dark" size="small">new</el-tag>
            </template>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" align="center" width="120">
        <template v-slot="{ row }">
          <CopyButton link type="primary" :copy-content="row.url">复制</CopyButton>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { feedbackList } from '@/api/order'
import CopyButton from '@/components/public/button/CopyButton.vue'

const loading = ref(false)
const link = ref<any[]>([])

const props = defineProps({
  videoId: {
    type: [String, Number],
    default: '',
  },
})

function getOrderDetails() {
  loading.value = true
  feedbackList(props.videoId)
    .then(res => {
      if (res.data?.length) {
        res.data.forEach((item: any) => {
          if (item.picUrl) {
            link.value.push({
              createTime: item.createTime,
              type: '图片',
              url: item.picUrl,
              isNew: item.isNew,
            })
          }
          if (item.videoUrl) {
            link.value.push({
              createTime: item.createTime,
              type: '视频',
              url: item.videoUrl,
              isNew: item.isNew,
            })
          }
        })
      }
    })
    .finally(() => (loading.value = false))
}
// 行颜色
const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 !== 0) {
    return 'primary-row'
  }
  return ''
}

getOrderDetails()
</script>

<style scoped lang="scss">
.content-box {
  overflow: hidden auto;
  padding: 0 10px 20px 0;
  font-size: 16px;
  display: flex;

  :deep(.el-table) {
    .table-primary-header-row {
      --el-table-header-bg-color: #f6f6f6;
      // --el-table-header-bg-color: var(--el-color-primary-light-7);

      .el-table__cell {
        // padding: 15px 0;
        color: var(--text-color);
      }
    }
    .primary-row {
      --el-table-tr-bg-color: var(--el-color-primary-light-9);
    }
  }
  .productLink {
    :deep(.el-link) {
      display: inline;

      .el-link__inner {
        display: inline;
      }
    }
  }
}
</style>
