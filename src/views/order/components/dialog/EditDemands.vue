<template>
  <PublicDialog
    ref="DialogRef"
    width="650px"
    title="修改拍摄要求"
    :showFooterButton="false"
    custom-close
    align-center
    :close-on-click-modal="true"
    @close="handleClose"
  >
    <div class="form-box" v-loading="loading">
      <el-form
        ref="formRef"
        :model="form"
        :validate-on-rule-change="false"
        label-width="0"
      >
        <el-form-item label="拍摄国家" prop="shootingCountry" label-width="110px">
          <el-radio-group v-model="form.shootingCountry">
            <el-radio-button
              v-for="item in biz_nation"
              :key="item.value"
              :value="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="模特类型" prop="modelType" label-width="110px">
          <el-radio-group v-model="form.modelType">
            <el-radio-button value="3" label="3">都可以</el-radio-button>
            <el-radio-button
              v-for="item in biz_model_type"
              :key="item.value"
              :value="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
        </el-form-item>
        <div class="flex-start title">
          <div>拍摄要求:</div>
          <el-icon class="add-icon" :size="20" @click="handleAddInput('newDemands')"><CirclePlusFilled /></el-icon>
        </div>
        <template v-for="(item, index) in form.demands" :key="index">
          <el-form-item label="">
            <div class="flex-start item">
              <span class="red">{{ (index+1)+'：原' }}</span>
              <el-input v-model="item.content" placeholder="请输入英文拍摄要求" :disabled="true" maxlength="300" />
            </div>
          </el-form-item>
          <el-form-item label="" :prop="'demands.'+index+'.value'" :rules="demandsRules">
            <div class="flex-start item">
              <span>改</span>
              <el-input v-model="item.value" placeholder="请输入变更后的拍摄要求内容(限英文)" maxlength="300" clearable />
            </div>
          </el-form-item>
        </template>
        <template v-for="(item, index) in form.newDemands" :key="index">
          <el-form-item label="" :prop="'newDemands.'+index+'.content'" :rules="demandsRules">
            <div class="flex-start item">
              <span>{{ (index+1+form.demands.length)+'：加' }}</span>
              <el-input v-model="item.content" placeholder="请输入新拍摄要求(限英文)" maxlength="300" clearable />
              <el-icon :size="20" @click="handleDelInput('newDemands', index)"><Delete /></el-icon>
            </div>
          </el-form-item>
        </template>
        <div class="flex-start title">
          <div>限制条件:</div>
          <el-icon class="add-icon" :size="20" @click="handleAddInput('newConditions')"><CirclePlusFilled /></el-icon>
        </div>
        <template v-for="(item, index) in form.conditions" :key="index">
          <el-form-item label="">
            <div class="flex-start item">
              <span class="red">{{ (index+1)+'：原' }}</span>
              <el-input v-model="item.content" placeholder="请输入限制条件" :disabled="true" maxlength="300" />
            </div>
          </el-form-item>
          <el-form-item label="" :prop="'conditions.'+index+'.value'" :rules="conditionsRules">
            <div class="flex-start item">
              <span>改</span>
              <el-input v-model="item.value" placeholder="请输入变更后的限制条件内容" maxlength="300" clearable />
            </div>
          </el-form-item>
        </template>
        <template v-for="(item, index) in form.newConditions" :key="index">
          <el-form-item label="" :prop="'newConditions.'+index+'.content'" :rules="conditionsRules">
            <div class="flex-start item">
              <span>{{ (index+1+form.conditions.length)+'：加' }}</span>
              <el-input v-model="item.content" placeholder="请输入新限制条件" maxlength="300" clearable />
              <el-icon :size="20" @click="handleDelInput('newConditions', index)"><Delete /></el-icon>
            </div>
          </el-form-item>
        </template>
      </el-form>
    </div>
    <div class="flex-center btn">
      <el-button class="btn-big" plain round @click="DialogRef.close()">取消</el-button>
      <el-button class="btn-big" type="primary" round @click="onConfirm">确认修改</el-button>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { getCurrentInstance, ref } from 'vue'
import { updateOrderDetails, replyCase } from '@/api/order'
import { english_d_reg, chineseCharacter_d_reg } from '@/utils/RegExp'
import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'

const { proxy } = getCurrentInstance() as any

const { biz_nation, biz_model_type } = proxy.useDict(
  'biz_nation',
  'biz_model_type'
)

const DialogRef = ref()
const orderId = ref<string | number>('')
const videoId = ref<string | number>('')
const loading = ref(false)
const form = ref<{
  shootingCountry: string,
  modelType: string,
  demands: {
    [x: string]: any
  }[]
  newDemands: {content: string}[]
  conditions: {
    [x: string]: any
  }[]
  newConditions: {content: string}[]
}>({
  shootingCountry: '',
  modelType: '',
  demands: [],// 拍摄要求
  newDemands: [
    {content: ''}
  ],// 新增拍摄要求
  conditions: [],// 限制条件
  newConditions: [],// 新增限制条件
})

const demandsRules = [
  { required: false, validator: checkDemands, trigger: 'blur'}
]
const conditionsRules = [
    { required: false, validator: checkConditions, trigger: 'change'}
  ]

function checkDemands(rule: any, value: any, callback: any) {
  if(value && !english_d_reg.test(value)) {
    return callback(new Error('*请用英文，简短描述重要的拍摄要求'));
  }
  return callback();
}
function checkConditions(rule: any, value: any, callback: any) {
  if(value && !chineseCharacter_d_reg.test(value)) {
    return callback(new Error('*请用中文，清晰简短描述限制条件'));
  }
  return callback();
}

function handleClose() {
  form.value = {
    shootingCountry: '',
    modelType: '',
    demands: [],
    newDemands: [
      {content: ''}
    ],
    conditions: [],
    newConditions: [],
  }
}


defineExpose({
  open
})

const emits = defineEmits(['success'])

function open(id: string | number, videoid: string | number) {
  orderId.value = id
  videoId.value = videoid
  DialogRef.value.open()
  getList()
}

function getList() {
  loading.value = true
  updateOrderDetails(orderId.value).then(res => {
    form.value.shootingCountry = res.data.shootingCountry + ''
    form.value.modelType = res.data.modelType + ''
    res.data.shootRequired.forEach((item: { [x: string]: any }) => {
      form.value.demands.push({
        ...item,
        value: ''
      })
    })
    res.data.limitingCondition.forEach((item: { [x: string]: any }) => {
      form.value.conditions.push({
        ...item,
        value: ''
      })
    })
  }).finally(() => loading.value = false)
}

function onConfirm() {
  ElMessageBox.confirm(
    '确认修改',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '再想想',
    }
  ).then(() => {
    let data: {
      id: string | number
      replyContent: number
      videoCaseInfoDTO: {
        id: string | number
        shootingCountry: string
        modelType: string
        videoContentHistoryDTO: any[]
      }
    } = {
      id: orderId.value,
      replyContent: 1,
      videoCaseInfoDTO: {
        id: videoId.value,
        shootingCountry: form.value.shootingCountry,
        modelType: form.value.modelType,
        videoContentHistoryDTO: []
      }
    }
    if(form.value.demands.length){
      form.value.demands.forEach((item, i) => {
        if(item.value){
          data.videoCaseInfoDTO.videoContentHistoryDTO.push({
            content: item.value,
            operate: 'update',
            sort: i,
            type: 1,
            videoId: videoId.value,
          })
        }
      })
    }
    if(form.value.newDemands.length){
      form.value.newDemands.forEach((item, i) => {
        if(item.content){
          data.videoCaseInfoDTO.videoContentHistoryDTO.push({
            content: item.content,
            operate: 'add',
            sort: i + form.value.demands.length,
            type: 1,
            videoId: videoId.value,
          })
        }
      })
    }
    if(form.value.conditions.length){
      form.value.conditions.forEach((item, i) => {
        if(item.value){
          data.videoCaseInfoDTO.videoContentHistoryDTO.push({
            content: item.value,
            operate: 'update',
            sort: i,
            type: 2,
            videoId: videoId.value,
          })
        }
      })
    }
    if(form.value.newConditions.length){
      form.value.newConditions.forEach((item, i) => {
        if(item.content){
          data.videoCaseInfoDTO.videoContentHistoryDTO.push({
            content: item.content,
            operate: 'add',
            sort: i + form.value.conditions.length,
            type: 2,
            videoId: videoId.value,
          })
        }
      })
    }
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在提交中，请稍后',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    replyCase(data).then(res => {
      ElMessage.success('修改成功')
      emits('success')
      DialogRef.value.close()
    }).finally(() => el_loading.close())
  }).catch(() => {})
}

// 添加 拍摄要求/限制条件 输入框
function handleAddInput(key: 'newDemands' | 'newConditions') {
  form.value[key]!.push({
    content: ''
  })
}
// 删除 拍摄要求/限制条件 输入框
function handleDelInput(key: 'newDemands' | 'newConditions', i: number) {
  form.value[key]!.splice(i, 1)
}

</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';
.form-box {
  min-height: 300px;
  max-height: 700px;
  overflow-y: auto;

  :deep(.el-form-item__error) {
    margin-left: 111px;
  }
  :deep(.el-form-item__label) {
    font-size: 16px;
  }

  .title {
    gap: 10px;
    width: 100%;
    font-size: 16px;
    margin: 10px 0;

    div {
      width: 100px;
      text-align: right;
    }

    .add-icon {
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }
  .item {
    gap: 10px;
    width: 100%;
    font-size: 16px;

    span {
      width: 100px;
      flex-shrink: 0;
      text-align: right;
      color: var(--el-color-primary)
    }
    .red {
      color: var(--el-color-danger)
    }
    .el-input {
      // flex-grow: 1;
      width: 70%;
    }
    .el-icon {
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }

  .btn {
    margin: 24px 0 20px;
  }
}
</style>