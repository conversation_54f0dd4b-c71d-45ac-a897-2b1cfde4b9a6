<template>
  <PublicDialog
    ref="DialogRef"
    width="500px"
    :title="title"
    :showFooterButton="false"
    custom-close
    align-center
    :close-on-click-modal="true"
    @close="() => formRef.resetFields()"
  >
    <div class="flex-center confirm-box" v-loading="loading">
      <el-form ref="formRef" style="width: 92%" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="退款金额">
          <el-input v-model.trim="form.refundAmount" :disabled="true" />
        </el-form-item>
        <el-form-item label="退款原因">
          <el-input
            v-model="form.refundCause"
            :autosize="{ minRows: 2, maxRows: 4 }"
            type="textarea"
            placeholder="请输入退款原因"
            maxlength="250"
            clearable
          />
        </el-form-item>
      </el-form>
      <div class="tip">退款审核通过后，退款金额将退至您的钱包账户</div>
      <div class="flex-center btn">
        <el-button class="btn-big" plain round @click="close">
          取消
        </el-button>
        <el-button class="btn-big" type="primary" round @click="onConfirm">提交申请</el-button>
      </div>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref } from 'vue'
import { getRefundInfo } from '@/api/order'
import useOrderApi from '@/hooks/useOrderApi'

const {
  handleApplyRefund
} = useOrderApi()

const DialogRef = ref()
const formRef = ref()
const videoId = ref<string | number>('')
const form = ref({
  refundAmount: 0,
  refundCause: ''
})
const loading = ref(false)

const rules = {
  refundCause: [
    { required: true, message: '请输入退款原因', trigger: 'blur' },
  ]
}

const props = defineProps({
  title: {
    type: String,
    default: '申请退款',
  },
  refundType: {
    type: Number,
    default: 1
  }
})

defineExpose({
  open,
})

const emits = defineEmits(['success'])

function open(id: string | number) {
  videoId.value = id
  DialogRef.value.open()
  getInfo()
}
function close() {
  DialogRef.value.close()
}
function getInfo() {
  loading.value = true
  getRefundInfo({
    videoId: videoId.value,
    refundType: props.refundType
  }).then(res => {
    form.value.refundAmount = res.data.refundAmount
  }).finally(() => loading.value = false)
}

function onConfirm() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      handleApplyRefund({
        ...form.value,
        refundType: props.refundType,
        videoId: videoId.value
      }, () => {
        emits('success')
        DialogRef.value.close()
      })
    }
  })

}
</script>

<style scoped lang="scss">
.confirm-box {
  flex-direction: column;
  gap: 20px;
  min-height: 230px;

  .tip {
    text-align: center;
    font-size: 16px;
    color: var(--el-color-warning);
  }
  .btn {
    margin: 24px 0 20px;
  }
}
</style>
