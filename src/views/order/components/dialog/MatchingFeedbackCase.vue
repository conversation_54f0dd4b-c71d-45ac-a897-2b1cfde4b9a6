<template>
  <PublicDialog
    ref="DialogRef"
    width="700px"
    title="匹配反馈情况"
    :showFooterButton="false"
    custom-close
    align-center
    destroy-on-close
    :close-on-click-modal="true"
    @close="() => (steps = [])"
  >
    <div class="case-box" v-loading="loading">
      <el-steps direction="vertical" process-status="wait" finish-status="success">
        <el-step
          v-for="(item, i) in steps"
          :key="item.id"
          :title="item.sendTime"
          :status="item.replyContent == 0 ? 'success' : 'wait'"
        >
          <template #icon>
            <div class="icon-box" :class="{ green: item.replyContent === 0 }"></div>
          </template>
          <template #description>
            <div class="description" v-if="item.replyContent === 0">
              <div class="curColor">{{ item.sendUser.name || '蜗牛运营' }}：{{ item.sendContent }}</div>
              <el-form-item label="" label-width="0px" ref="formInputRef">
                <el-input
                  style="margin-top: 10px"
                  v-model="item.reason"
                  type="textarea"
                  maxlength="500"
                  show-word-limit
                  :rows="4"
                  placeholder="不同意反馈时，请输入理由"
                  @input="handleRemarkChange(i)"
                />
              </el-form-item>
              <div class="flex-end reply">
                <el-button plain round @click="onConfirm(0, item, i)">不同意</el-button>
                <el-button type="primary" round @click="onConfirm(1, item, i)">同意</el-button>
              </div>
            </div>
            <div class="description" v-else>
              <div>{{ item.sendUser.name || '蜗牛运营' }}：{{ item.sendContent }}</div>
              <div class="flex-start reply">
                <span v-if="item?.replyUser?.name">{{ item.replyUser.name }}：</span>
                <span v-else>商家：</span>
                <div>
                  <div
                    class="reply-content"
                    :class="{ success: item.replyContent === 1 }"
                    v-if="item.replyContent !== 0"
                  >
                    {{ item.replyContent === 1 ? '同意' : '不同意' }}
                  </div>
                  <div>{{ item.reason }}</div>
                </div>
              </div>
            </div>
          </template>
        </el-step>
      </el-steps>
      <div v-if="!steps.length" class="flex-center no-data">暂无反馈信息</div>
    </div>
  </PublicDialog>
  <!-- <EditDemands ref="EditDemandsRef" @success="handleSuccess" /> -->
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { replyCaseList, replyCase } from '@/api/order'
import type { OrderUserInfo } from '../../type'
import { ElMessageBox, ElMessage } from 'element-plus'
// import EditDemands from '@/views/order/components/dialog/EditDemands.vue'

const DialogRef = ref()
// const EditDemandsRef = ref<InstanceType<typeof EditDemands>>()
const orderId = ref<string | number>('')
// const active = ref(2)
const steps = ref<
  {
    [x: string]: any
    reply: OrderUserInfo
    sendUser: {
      id: number
      name: string
      phonenumber: string
    }
  }[]
>([])
const loading = ref(false)

defineExpose({
  open,
})

const formInputRef = ref<any>(null)

// const emits = defineEmits(['success'])

function open(id: string | number) {
  orderId.value = id
  DialogRef.value.open()
  getList()
}

function getList() {
  loading.value = true
  replyCaseList(orderId.value)
    .then(res => {
      steps.value = res.data.map((item: any) => ({
        ...item,
        reason: item.reason || '',
      }))
    })
    .finally(() => (loading.value = false))
}

// function handleSuccess() {
//   getList()
// }

function handleRemarkChange(i: number) {
  try {
    formInputRef.value[i].validateState = 'success'
  } catch (error) {}
}

function onConfirm(y: number, item: any, i: number) {
  let msg = '确认同意'
  let replyContent = 1
  // EditDemandsRef.value?.open(item.videoId, orderId.value)
  if (!y) {
    if (!item.reason.trim()) {
      try {
        ElMessage.warning('请输入不同意原因')
        formInputRef.value[i].validateMessage = '不同意反馈时，请输入理由'
        formInputRef.value[i].validateState = 'error'
      } catch (error) {}
      return
    }
    msg = '确认不同意'
    replyContent = 2
  }
  try {
    formInputRef.value[i].validateState = 'success'
  } catch (error) {}
  ElMessageBox.confirm(item.sendContent, msg, {
    confirmButtonText: '确定',
    cancelButtonText: '再想想',
  })
    .then(() => {
      loading.value = true
      replyCase({ id: item.id, videoId: orderId.value, replyContent, reason: item.reason })
        .then(res => {
          ElMessage.success('操作成功')
          getList()
        })
        .catch(() => (loading.value = false))
    })
    .catch(() => {})
}
</script>

<style scoped lang="scss">
.case-box {
  min-height: 400px;
  max-height: 600px;
  overflow-y: auto;

  .icon-box {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--el-text-color-placeholder);
  }
  .green {
    background: var(--el-color-success);
  }
  .curColor {
    color: #000;
  }
  .description {
    font-size: 15px;
  }
  .reply {
    margin-top: 10px;
    align-items: baseline;

    span {
      flex-shrink: 0;
    }

    .reply-content {
      padding: 1px 10px 1px 0;
      font-size: 15px;
      color: var(--el-color-danger);
      // border-radius: var(--el-border-radius-round);
      // background-color: var(--el-color-info);

      &.success {
        color: var(--el-color-success);
      }
    }
  }

  .no-data {
    width: 100%;
    height: 250px;
    font-size: 16px;
    color: var(--text-gray-color);
  }

  :deep(.el-step__main) {
    margin-bottom: 15px;
  }
  :deep(.el-step__description) {
    padding-right: 0;
  }
}
</style>
