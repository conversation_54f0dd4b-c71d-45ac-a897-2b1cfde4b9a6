<template>
  <div>
    <PublicDialog
      ref="DialogRef"
      width="980px"
      :title="title"
      :showFooterButton="false"
      custom-close
      align-center
      destroy-on-close
      style="padding: 0 0 20px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <template #header>
        <div class="flex-between pay-dialog-title">
          <span>{{ title }}</span>
          <div class="close-round-btn" @click="close"></div>
        </div>
      </template>
      <div class="pay-box card">
        <div class="flex-column code-box">
          <!-- <div class="total-box">
            支付金额：￥
            <span>{{ payAmount }}</span>
          </div> -->

          <template
            v-if="
              payWay === PAY_TYPE['对公转账'] ||
              payWay === PAY_TYPE['银行卡转账'] ||
              payWay === PAY_TYPE['全币种支付']
            "
          >
            <div
              class="flex-start payWay-info-box"
              :style="{
                height: payWay === PAY_TYPE['全币种支付'] ? '610px' : '530px',
              }"
            >
              <div style="flex: 1; height: 100%">
                <div class="step-title top">
                  第1步：
                  <span v-if="payWay === PAY_TYPE['全币种支付']">使用全币种支付</span>
                  <span v-else>{{ PAY_TYPE[payWay] }}</span>
                </div>
                <div class="flex-column gap-10 left-box">
                  <div class="bg">
                    <div class="flex-between title">
                      汇款信息：
                      <CopyButton :copy-content="collectionAccInfo" type="primary" icon="Document">
                        复制汇款信息
                      </CopyButton>
                    </div>

                    <div class="flex-start info-item m-0">
                      <div :class="[payWay === PAY_TYPE['全币种支付'] ? 'long-label' : 'label']">
                        转账金额：
                      </div>
                      <div class="content red">
                        <span style="font-size: 20px">
                          {{ payAmount ? payAmount.toFixed(2) : payAmount }}
                        </span>
                        <span style="font-size: 12px">
                          {{ payWay === PAY_TYPE['全币种支付'] ? ' USD' : ' CNY' }}
                        </span>
                        <span v-if="payWay === PAY_TYPE['全币种支付']">
                          <span style="font-weight: normal; font-size: 20px; margin: 0 8px">/</span>
                          <span style="font-size: 20px">
                            {{ cnyAmount ? cnyAmount.toFixed(2) : cnyAmount }}
                          </span>
                          <span style="font-weight: normal; font-size: 12px">&nbsp;CNY</span>
                        </span>
                      </div>
                    </div>

                    <template v-if="payWay === PAY_TYPE['对公转账'] || payWay === PAY_TYPE['银行卡转账']">
                      <div class="flex-start info-item">
                        <div class="label" v-if="payWay === PAY_TYPE['对公转账']">企业全称：</div>
                        <div class="label" v-if="payWay === PAY_TYPE['银行卡转账']">收款人姓名：</div>
                        <div class="content">
                          {{ accInfo.accountName }}
                          <el-icon class="is-loading" v-if="getValidConfigLoading"><Loading /></el-icon>
                        </div>
                      </div>
                      <div class="flex-start info-item">
                        <div class="label">银行账号：</div>
                        <div class="content">
                          {{ accInfo.bankAccount }}
                          <el-icon class="is-loading" v-if="getValidConfigLoading"><Loading /></el-icon>
                        </div>
                      </div>
                      <div class="flex-start info-item">
                        <div class="label">开户行：</div>
                        <div class="content">
                          {{ accInfo.bankName }}
                          <el-icon class="is-loading" v-if="getValidConfigLoading"><Loading /></el-icon>
                        </div>
                      </div>
                      <!-- <div class="flex-start info-item">
                        <div class="label">转账备注：</div>
                        <div class="content">{{ orderNum }}</div>
                      </div>
                      <div style="color: red; font-size: 12px; margin-left: 100px">
                        （*打款时请备注您的订单号，已便加快审批进程）
                      </div> -->
                    </template>
                    <template v-if="payWay === PAY_TYPE['全币种支付']">
                      <div
                        class="flex-start info-item m-0"
                        v-for="(item, index) in accInfoConfigList"
                        :key="index"
                      >
                        <div class="long-label">{{ item.label }}：</div>
                        <div class="content">
                          {{ item.key ? accInfo[item.key] : item.value || '' }}
                          <el-icon class="is-loading" v-if="getValidConfigLoading"><Loading /></el-icon>
                          <div class="tip" v-if="item.key && accInfo[item.key] && item.tip">
                            {{ item.tip }}
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>

                  <!-- <div class="step-title" v-if="payWay === PAY_TYPE['对公转账']">
                    第2步：
                    <span>填写开票信息</span>
                  </div>

                  <div class="bg" v-if="payWay === PAY_TYPE['对公转账']">
                    <div class="flex-between title" style="margin-top: 30px">
                      开票信息：
                      <el-button
                        v-if="!invoiceData.invoiceDutyParagraph || !invoiceData.invoiceTitle"
                        class="primary-btn"
                        size="small"
                        round
                        plain
                        icon="Plus"
                        @click="openInvoice"
                      >
                        增加开票信息
                      </el-button>
                      <el-button v-else @click="openInvoice" class="primary-btn" size="small" round plain>
                        <template #icon>
                          <img src="@/assets/icon/icon_edit.png" style="width: 14px" alt="" />
                        </template>
                        修改
                      </el-button>
                    </div>
                    <div class="flex-start info-item">
                      <div class="label">发票抬头：</div>
                      <div class="content more-ell">{{ invoiceData.invoiceTitle }}</div>
                    </div>
                    <div class="flex-start info-item">
                      <div class="label">税号：</div>
                      <div class="content">{{ invoiceData.invoiceDutyParagraph }}</div>
                    </div>
                  </div> -->
                </div>
              </div>
              <!-- <el-divider direction="vertical" /> -->
              <div style="flex: 1; height: 100%">
                <div class="step-title top">
                  第2步：{{
                    payWay === PAY_TYPE['全币种支付'] ? '填写公司信息及上传付款回执' : '上传付款回执'
                  }}
                  <!-- 第{{ payWay === PAY_TYPE['对公转账'] ? 3 : 2 }}步：填写公司信息及上传付款回执 -->
                </div>
                <div class="right-box">
                  <!-- <div class="title">线下付款成功后，请上传凭证</div> -->
                  <el-form
                    ref="formRef"
                    @submit.prevent
                    :model="form"
                    label-position="top"
                    :rules="rules"
                    label-width="auto"
                    :disabled="disabled"
                  >
                    <el-form-item label="付款账户名称" prop="name" v-if="payWay === PAY_TYPE['全币种支付']">
                      <el-input
                        v-model="form.name"
                        placeholder="请输入付款账户名称"
                        maxlength="30"
                        clearable
                      />
                    </el-form-item>
                    <el-form-item label="上传付款回执" prop="file">
                      <div class="upload-box">
                        <div class="flex-start" style="width: 100%">
                          <!-- <el-button type="primary" icon="Plus" @click="openUpload">上传转账凭证</el-button> -->
                          <PasteUpload v-model="form.file" :limit="5" :size="5" bucket="credit" />
                        </div>
                        <div class="upload-tips">
                          请上传大小不超过
                          <span style="color: #f29c2d">5M</span>
                          ，格式为
                          <span style="color: #f29c2d">png/jpg/jpeg</span>
                          的图片
                          <el-button
                            type="primary"
                            link
                            @click="openImgViewer"
                            style="vertical-align: baseline"
                          >
                            示例参考
                          </el-button>
                        </div>
                        <div style="width: 410px; overflow-x: auto">
                          <ViewerImageList
                            style="margin-top: 10px"
                            :data="form.file"
                            :style="{ 'flex-wrap': 'nowrap', 'overflow-y': 'hidden' }"
                            is-preview-all
                            url-name="picUrl"
                            suffix="fullSize"
                            @delete="handleDelImg"
                          />
                        </div>
                      </div>
                    </el-form-item>
                  </el-form>
                  <div class="tips">
                    <span>温馨提示</span>
                    <br />
                    1. 付款回执需清晰可见付款账号、金额、付款人、时间等信息;
                    <br />
                    2. 汇款信息必须与订单相关信息一致，否则平台将无法确认收款;
                  </div>
                  <div
                    class="flex-center"
                    v-if="
                      payWay === PAY_TYPE['对公转账'] ||
                      payWay === PAY_TYPE['银行卡转账'] ||
                      payWay === PAY_TYPE['全币种支付']
                    "
                  >
                    <el-button class="submit-button" type="primary" @click="onSubmit" :loading="loadingBtn">
                      提交凭证信息
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </PublicDialog>

    <UploadProof ref="UploadProofRef" title="上传支付凭证" @success="upSuccess" />

    <InvoiceManage ref="InvoiceManageRef" :request="invoiceManageConfirm" />
  </div>
</template>

<script setup lang="ts">
import { PAY_TYPE } from '@/utils/order'
import CopyButton from '@/components/public/button/CopyButton.vue'
// import DownloadButton from '@/components/public/button/DownloadButton.vue'
import UploadProof from '@/components/public/dialog/DragUploadDialog.vue'
import PasteUpload from '@/components/public/upload/PasteUpload.vue'
import ViewerImageList from '@/components/public/image/ViewerImageList.vue'
import InvoiceManage from '@/views/center/components/InvoiceManage.vue'
// import proofImage from '@/assets/image/proof.png'
import { ElMessageBox, ElMessage } from 'element-plus'
// import { accountInfo } from '@/utils/data'
import { getValidConfig } from '@/api/order'
import { getAnotherPayValidConfig } from '@/api/anotherPay'
import { type PropType, ref, computed } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { previewImage } from '@/utils/preview'
import { nextTick } from 'vue'

const store = useUserStore()

const props = defineProps({
  payWay: {
    type: Number as PropType<PAY_TYPE>,
    required: true,
  },
  payAmount: {
    type: Number,
    required: true,
  },
  cnyAmount: {
    type: Number,
    required: false,
  },
  taxPointCost: {
    type: Number,
  },
  orderNum: {
    type: String,
    default: '',
  },
  mergeId: {
    type: [String, Number],
    default: '',
  },
  title: {
    type: String,
    default: '对公转账',
  },
  isAnotherPay: {
    type: Boolean,
    default: false,
  },
})

defineExpose({
  open,
  close,
})

const emits = defineEmits(['submit', 'close'])

const getValidConfigLoading = ref(false)
const accInfo = ref<any>({
  accountName: '',
  bankAccount: '',
  bankName: '',
})
// const accInfo = computed(() => {
//   if (props.payWay === PAY_TYPE['对公转账']) {
//     return accountInfo.public_transfer
//   }
//   return accountInfo.bank_transfer
// })
const collectionAccInfo = computed(() => {
  if (props.payWay === PAY_TYPE['对公转账'] || props.payWay === PAY_TYPE['银行卡转账']) {
    let firmName = ''
    let bankAcc = ''
    let bank = ''
    if (props.payWay === PAY_TYPE['对公转账']) {
      firmName = `收款公司名称：${accInfo.value.accountName}`
    }
    if (props.payWay === PAY_TYPE['银行卡转账']) {
      firmName = `收款人姓名：${accInfo.value.accountName}`
    }
    bankAcc = `收款银行账号：${accInfo.value.bankAccount}`
    bank = `开户行名称：${accInfo.value.bankName}`
    return `${firmName}\n${bankAcc}\n${bank}`
  }
  let str = accInfoConfigList.map(item => {
    if (item.key) {
      return `${item.label}：${accInfo.value[item.key] || ''} ${item.tip || ''}`
    }
    return `${item.label}：${item.value || ''}`
  })
  return str.filter(Boolean).join('\n')
})

const accInfoConfigList = [
  { label: '收款账户类型', key: 'companyAccountType' },
  { label: '银行所在地', key: 'bankName' },
  { label: '收款支持币种', value: '离岸人民币/美元/澳元/加币/欧元/英镑/港币/日元/新西兰元/新加坡元' },
  { label: '账户名', key: 'accountName' },
  {
    label: 'SWIFT代码',
    key: 'companyBankSwiftCode',
    tip: '(如果系统提示需要输入11位数，请在最后面加“XXX”凑够即可)',
  },
  { label: '银行账号', key: 'bankAccount' },
  { label: '收款人地址', key: 'companyBankPayeeAddress' },
  { label: '银行地址', key: 'companyBankAddress' },
  { label: '银行代码', key: 'companyBankCode' },
  { label: '分行代码', key: 'companyBankSubCode' },
  { label: '银行名称', key: 'companyBankName' },
]

const DialogRef = ref()

const formRef = ref()
const form = ref<{
  name: string
  file: any[]
}>({
  name: '',
  file: [],
})
const rules = {
  name: [{ required: true, message: '请输入付款账户名称', trigger: 'blur' }],
  file: [{ required: true, message: '请上传转账截图', trigger: 'change' }],
}

const disabled = ref(false)
const loadingBtn = ref(false)

const imgViewerUrl = ref('')

const payeeId = ref(0)
const detailId = ref(0)

const invoiceData = ref({
  invoiceTitle: '',
  invoiceDutyParagraph: '',
})
const UploadProofRef = ref<InstanceType<typeof UploadProof>>()
const InvoiceManageRef = ref<InstanceType<typeof InvoiceManage>>()

function open(info: { payeeId?: number; detailId?: number }) {
  payeeId.value = info.payeeId || 0
  detailId.value = info.detailId || 0
  DialogRef.value.open()
  if (props.isAnotherPay) {
    invoiceData.value.invoiceTitle = ''
    invoiceData.value.invoiceDutyParagraph = ''
  } else {
    invoiceData.value.invoiceTitle = store.userInfo.businessVO?.invoiceTitle || ''
    invoiceData.value.invoiceDutyParagraph = store.userInfo.businessVO?.invoiceDutyParagraph || ''
  }
  nextTick(() => {
    getValidConfigData()
  })
}

function close() {
  DialogRef.value.close()
}
function handleClose() {
  accInfo.value = {
    accountName: '',
    bankAccount: '',
    bankName: '',
  }
  payeeId.value = 0
  emits('close')
}

function openImgViewer() {
  if (props.payWay === PAY_TYPE['对公转账']) {
    previewImage('https://pstatic.woniu.video/static/order-proof-sample2.jpg!fullSize')
  } else if (props.payWay === PAY_TYPE['银行卡转账']) {
    previewImage('https://pstatic.woniu.video/static/817f669d9e6041ed80f1f8c54c6c5cd7.jpg!fullSize')
  } else if (props.payWay === PAY_TYPE['全币种支付']) {
    previewImage('https://pstatic.woniu.video/static/full-type-example.jpg!fullSize')
  }
}
function handleDelImg(_data: any, i: number) {
  form.value.file.splice(i, 1)
}

function getValidConfigData() {
  getValidConfigLoading.value = true
  let fn = props.isAnotherPay ? getAnotherPayValidConfig : getValidConfig
  fn({ type: props.payWay, payeeId: payeeId.value || undefined, detailId: detailId.value || undefined })
    .then(res => {
      if (res.data) {
        accInfo.value = res.data
      }
    })
    .finally(() => {
      getValidConfigLoading.value = false
    })
}
// 发票
function openInvoice() {
  InvoiceManageRef.value?.open(invoiceData.value.invoiceTitle, invoiceData.value.invoiceDutyParagraph)
}
function invoiceManageConfirm(f: any) {
  invoiceData.value.invoiceTitle = f.invoiceTitle
  invoiceData.value.invoiceDutyParagraph = f.invoiceDutyParagraph
  ElMessage.success('修改成功')
  InvoiceManageRef.value?.close()
}
function openUpload() {
  UploadProofRef.value?.open()
}
function upSuccess(data: any) {
  // console.log(data);
  form.value.file.push(...data)
}

function onSubmit() {
  if (form.value.name) {
    form.value.name = form.value.name.trim()
  }
  formRef.value.validate((valid: any) => {
    if (valid) {
      // if (props.payWay === PAY_TYPE['对公转账']) {
      //   if (!invoiceData.value.invoiceDutyParagraph || !invoiceData.value.invoiceTitle) {
      //     ElMessage.error('对公转账开票信息不能为空！')
      //     return
      //   }
      // }
      ElMessageBox.confirm('确认提交！', '提示', {})
        .then(() => {
          loadingBtn.value = true
          disabled.value = true
          // let payDocument = form.value.file.map(item => ({
          //   id: item.id,
          //   name: item.name,
          //   picUrl: item.picUrl,
          // }))
          let payDocument: any[] = []
          form.value.file.map(item => payDocument.push(item.picUrl))
          let params: {
            [x: string]: any
          } = {
            orderNum: !props.mergeId ? props.orderNum : undefined,
            mergeId: props.mergeId || undefined,
            // resource: payDocument,
            objectKeys: payDocument,
            payAmount: props.payAmount,
            payType: props.payWay,
            content: '现代服务推广费',
          }
          if (props.payWay === PAY_TYPE['全币种支付']) {
            params.payAccount = form.value.name
          } else if (props.payWay === PAY_TYPE['对公转账']) {
            // params.dutyParagraph = invoiceData.value.invoiceDutyParagraph
            // params.title = invoiceData.value.invoiceTitle
            params.taxPointCost = props.taxPointCost
          }
          emits('submit', params, () => {
            loadingBtn.value = false
            disabled.value = false
          })
        })
        .catch(() => {})
    }
  })
}
</script>

<style scoped lang="scss">
.title {
  position: relative;
  font-size: 15px;
  font-weight: 500;
  margin: 4px 0 10px;
  padding-left: 10px;
  color: var(--text-color);

  // &::before {
  //   content: '';
  //   position: absolute;
  //   left: 0;
  //   top: 50%;
  //   transform: translateY(-50%);
  //   width: 2px;
  //   height: 60%;
  //   background-color: var(--el-color-primary);
  // }

  &.primary {
    font-size: 25px;
    font-weight: 600;
    color: var(--el-color-primary);
  }
}
.card {
  border-radius: 10px;
  background-color: var(--bg);
  padding-bottom: 14px;
}
.pay-dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-color-primary);
  background: var(--bg);
  padding: 13px 20px;
  border-radius: 12px 12px 0 0;
}
.pay-box {
  padding-top: 0;
  padding-bottom: 0;

  .code-box {
    .total-box {
      text-align: center;
      font-size: 18px;
      color: var(--text-color);
      width: 95%;
      background: #f6f6f6;
      border-radius: 16px;

      span {
        font-size: 30px;
        font-weight: 600;
      }
      .tips {
        font-size: 13px;
      }
    }
    .code-img {
      margin-right: 10px;
      min-width: 180px;
      min-height: 180px;
    }

    .primary-btn {
      color: var(--el-color-primary);
      &:not(.is-link) {
        border-color: var(--el-color-primary);
      }
    }

    .payWay-info-box {
      width: 100%;
      box-sizing: border-box;
      border-radius: 4px;
      // background-color: var(--input-bg);
      padding: 15px 15px 30px;
      align-items: flex-start;
      gap: 10px;
      height: 530px;

      .step-title {
        color: var(--text-color);
        font-size: 16px;

        &.top {
          position: relative;
          top: -10px;
        }
      }

      .left-box {
        min-width: 400px;
        height: 100%;
        overflow-y: auto;
        align-items: flex-start;

        .bg {
          background-color: var(--el-color-info-light-9);
          padding: 20px;
          border-radius: 8px;
          width: 100%;
          box-sizing: border-box;
          flex: 2;
        }

        .label {
          width: 80px;
          text-align: right;
          color: var(--text-gray-color);
        }
        .long-label {
          width: 98px;
          text-align: right;
          color: var(--text-gray-color);
        }

        .info-item {
          margin-bottom: 10px;
          align-items: baseline;
          color: var(--text-color);
          line-height: 32px;
          font-size: 13px;

          .label {
            flex-shrink: 0;
          }

          &.m-0 {
            margin-bottom: 0;
          }

          .content {
            font-weight: 500;
            position: relative;
            line-height: 22px;
            max-width: 310px;

            &.red {
              color: var(--el-color-danger);
              line-height: 32px;
            }

            .copy-btn {
              position: absolute;
              top: 8px;
              right: -45px;
              z-index: 9;
            }

            .tip {
              color: var(--text-gray-color);
              font-size: 12px;
              position: relative;
              left: -1px;
              width: 330px;
            }
          }
        }
      }
      .right-box {
        min-width: 400px;
        padding: 20px;
        background-color: var(--el-color-info-light-9);
        border-radius: 8px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;

        .tips {
          font-size: 13px;
          padding: 10px 20px 10px 0;
          color: var(--text-gray-color);
          margin: 10px 0;

          span {
            color: var(--text-color);
            font-size: 12px;
            font-weight: bold;
          }
        }
      }
      .upload-box {
        position: relative;
        width: 100%;

        .upload-tips {
          color: var(--text-color);
          line-height: 22px;
          font-size: 13px;
          margin-top: 5px;
        }

        // .viewer-btn {
        //   position: absolute;
        //   top: -29px;
        //   left: 100px;
        // }
      }
    }

    .el-divider {
      margin: 45px 22px 0;
      height: 13em;
    }
  }
  .submit-button {
    width: 150px;
    height: 45px;
    font-size: 15px;
    font-weight: bold;
    border-radius: 25px;
    margin-top: 10px;
  }
}
</style>
