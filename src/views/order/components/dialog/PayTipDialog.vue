<template>
  <div class="pay-tip-dialog">
    <el-dialog
      v-model="payTipDialogVisible"
      style="border-radius: var(--dialog-radius)"
      :title="payTipType === 3 ? '支付失败' : ''"
      :width="payTipType === 3 ? '470px' : '600px'"
      align-center
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @open="handleOpen"
      @close="toPage(0)"
    >
      <div class="flex-column gap-5 pay-tip-dialog-content" v-if="payTipType === 3">
        <el-icon :size="70" color="var(--el-color-warning)"><Warning /></el-icon>
        <div class="tips" style="margin: 10px 0 0;">该订单已在合并支付中，无法单笔支付</div>
        <div class="flex-center gap-10">
          <el-button
            type="primary"
            round
            class="pay-tip-dialog-btn"
            @click="toPage(0)"
          >
            返回订单列表
          </el-button>
        </div>
      </div>
      <div
        v-else
        class="flex-column gap-5 pay-tip-dialog-content"
        :class="{
          mb: payTipType === 0,
        }"
      >
        <el-icon :size="50" color="var(--el-color-primary)"><SuccessFilled /></el-icon>
        <div class="title" v-if="payTipType != 1">支付成功</div>
        <div class="title" v-else>提交成功</div>
        <div class="tips" v-if="payTipType === 0">蜗牛运营将快马加鞭的为您匹配模特~</div>
        <div class="tips" v-if="payTipType === 2 && !isRenew">欢迎加入蜗牛会员大家庭</div>
        <div class="tips" v-if="payTipType === 2 && isRenew">您已续费成功</div>
        <div class="tips small" v-if="payTipType === 2 && !isRenew">
          会员编码：{{ memberCode }}
          <el-icon class="is-loading" v-if="loading"><Loading /></el-icon>
        </div>
        <div class="tips small" v-if="payTipType === 2">
          会员到期时间：{{ time }}
          <el-icon class="is-loading" v-if="loading"><Loading /></el-icon>
        </div>
        <div class="tips primary" v-if="payTipType === 2 && !isRenew">请联系蜗牛客服，为您创建专属1v1对接群</div>
        <template v-else-if="payTipType === 1">
          <div class="tips">您已提交支付凭证</div>
          <div class="tips primary">预计审核时间：工作日30分钟内</div>
          <el-divider />
          <div class="flex-start tips order-num-box" v-if="orderNumber">
            <span>订单号：</span>
            <div class="text">{{ orderNumber }}</div>
            <CopyButton :copy-content="orderNumber" link style="vertical-align: baseline">
              <el-icon size="14"><CopyDocument /></el-icon>
            </CopyButton>
          </div>
          <div class="tips gary small" v-if="orderNumber">(如需加急处理，请复制上面的单号联系客服加急处理哟~)</div>
        </template>

        <div class="flex-center gap-10">
          <el-button v-if="payTipType === 0" class="pay-tip-dialog-btn plain" round @click="toPage(1)">
            查看订单
          </el-button>
          <el-button
            v-if="payTipType === 1 || payTipType === 2"
            class="pay-tip-dialog-btn plain"
            round
            @click="toPage(0)"
          >
            我知道啦~
          </el-button>
          <el-button
            v-if="payTipType === 0"
            type="primary"
            round
            class="pay-tip-dialog-btn"
            @click="toPageAgain"
          >
            再下一单
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import CopyButton from '@/components/public/button/CopyButton.vue'
import { usePayTip } from '@/hooks/usePay'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { computed, ref } from 'vue'
import { ORDER_STATUS } from '@/utils/order'

const { payTipDialogVisible, payTipType, orderNum, routerPathUrl } = usePayTip()

const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  }
})

const orderNumber = computed(() => {
  if (orderNum.value) {
    return orderNum.value
  }
  if (props.tableData.length) {
    return props.tableData.map((item: any) => item.orderNum).join('\n')
  }
  return ''
})

const router = useRouter()
const store = useUserStore()

const memberCode = ref('')
const time = ref('')

const loading = ref(false)

const isRenew = ref<boolean | 0>(false)

function handleOpen() {
  if (payTipType.value === 2) {
    loading.value = true
    isRenew.value = store.isViped()
    store
      .getUserInfo()
      .then(res => {
        if (res.businessVO) {
          memberCode.value = res.businessVO.memberCode
          let t = res.businessVO.memberValidity.split('T')[0]
          time.value = handleTime(t)
        }
      })
      .finally(() => {
        loading.value = false
      })
  }
}

function handleTime(time: any) {
  let [year, month, day] = time.split('-')
  return `${year}年${month}月${day}日`
}

function toPage(query: any) {
  if (query) {
    router.replace({
      path: routerPathUrl.value,
      query: {
        tab: ORDER_STATUS['待匹配'],
      },
    })
  } else {
    router.replace(routerPathUrl.value)
  }
}

function toPageAgain() {
  // sessionStorage.setItem('createOrderAgain', String(new Date().getTime()))
  // router.replace(routerPathUrl.value)
  router.replace('/order/create')
}
</script>

<style scoped lang="scss">
.pay-tip-dialog-content {
  font-weight: 500;

  &.mb {
    gap: 10px;

    .title {
      margin-bottom: 25px;
    }
    .tips {
      margin-bottom: 25px;
    }
  }

  .title {
    font-size: 26px;
    font-weight: 600;
    color: var(--text-color);
  }
  .tips {
    font-size: 24px;
    color: var(--text-color);

    &.primary {
      color: var(--el-color-primary);
    }
    &.gary {
      color: var(--text-gray-color);
    }
    &.small {
      font-size: 16px;
    }
  }

  .order-num-box {
    font-size: 20px;
    align-items: baseline;

    .text {
      max-width: 330px;
      max-height: 200px;
      overflow-y: auto;
    }
  }
}
.pay-tip-dialog-btn {
  width: 120px;
  height: 36px;
  font-size: 14px;
  font-weight: 500;
  margin: 30px 5px;

  &.plain {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }
}
</style>
