<template>
  <PublicDialog
    ref="DialogRef"
    width="500px"
    title="更换模特"
    :showFooterButton="false"
    custom-close
    align-center
    :close-on-click-modal="true"
  >
    <div class="flex-center confirm-box">
      <h2 style="text-align: center;">系统检测到模特收货地址有更新</h2>
      <span class="tip-1">建议您先行确认再进行发货</span>
      <span class="tip-2">（模特最新发货地址见发货页）</span>
      <div class="flex-center btn">
        <!-- <el-button class="btn-big" plain round @click="onConfirm(1)">去确认</el-button> -->
        <!-- <el-button class="btn-big" type="primary" round @click="onConfirm(0)">已确认，去发货</el-button> -->
        <el-button class="btn-big" plain round @click="onConfirm(0)">好的~</el-button>
      </div>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const DialogRef = ref()
const orderId = ref<string | number>('')

defineExpose({
  open
})

const emits = defineEmits(['success', 'change'])

function open(id: string | number) {
  orderId.value = id
  DialogRef.value.open()
}

function onConfirm(y: number) {
  DialogRef.value.close()
  emits('change', orderId.value, y)
}

</script>

<style scoped lang="scss">
.confirm-box {
  flex-direction: column;
  gap: 20px;
  height: 230px;

  .tip-1 {
    font-size: 16px;
    color: var(--el-color-warning);
  }
  .tip-2 {
    font-size: 14px;
    color: var(--el-color-info);
  }
  .btn {
    margin: 24px 0 20px;
  }
}
</style>