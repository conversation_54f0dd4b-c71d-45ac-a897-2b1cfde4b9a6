<template>
  <PublicDialog
    ref="DialogRef"
    custom-close
    align-center
    title="历史变更记录"
    width="900px"
    :showFooterButton="false"
    :close-on-click-modal="true"
    @close="handleClose"
  >
    <div
      class="history-box"
      v-loading="loading"
      v-if="historyRecordList.length && historyRecordList.length > 0"
    >
      <div>
        <el-steps style="" :active="activeTab" simple>
          <template v-for="(item, index) in historyRecordList" :key="item.id">
            <el-step>
              <template #icon></template>
              <template #title>
                <div @click="handleClick(index)" style="cursor: pointer">
                  <span>
                    {{
                      item.logType == 3 ? '商家同意后修改' : item.logType == 2 ? '编辑订单时修改' : '初始记录'
                    }}
                  </span>
                  <div style="color: black">{{ item.changeTime }}</div>
                </div>
              </template>
            </el-step>
          </template>
        </el-steps>
      </div>
      <div class="edit-head" v-if="selectRecordInfo.logType != 1">
        <div class="edit-head__title">修改内容：</div>
        <div class="edit-head__content">
          <span>修改人：{{ selectRecordInfo.changeUserName }}({{ selectRecordInfo.changeUserId }})</span>
          <span style="margin-left: 50px">修改时间：{{ selectRecordInfo.changeTime }}</span>
        </div>
      </div>
      <el-divider v-if="selectRecordInfo.logType != 1" style="margin: 0; border-top: 1px solid #f2f2f2" />
      <div class="edit-content" style="max-height: 400px; overflow: auto">
        <el-row>
          <el-col :span="8" v-if="selectRecordInfo.platform || selectRecordInfo.platform == '0'">
            <div class="label">使用平台：</div>
            <div class="content">
              <template v-for="dict in biz_model_platform" :key="dict.value">
                <span v-if="dict.value == selectRecordInfo?.platform">{{ dict.label }}</span>
              </template>
            </div>
          </el-col>
          <el-col
            :span="8"
            v-if="selectRecordInfo.shootingCountry || selectRecordInfo.shootingCountry == '0'"
          >
            <div class="label">拍摄国家：</div>
            <div class="content">
              <template v-for="dict in biz_nation" :key="dict.value">
                <span v-if="dict.value == selectRecordInfo?.shootingCountry">{{ dict.label }}</span>
              </template>
            </div>
          </el-col>
          <el-col :span="8" v-if="selectRecordInfo?.modelType || selectRecordInfo?.modelType == '0'">
            <div class="label">模特类型：</div>
            <div class="content">
              <template v-for="dict in biz_model_type" :key="dict.value">
                <span v-if="dict.value == selectRecordInfo.modelType">{{ dict.label }}</span>
              </template>
              <span v-if="selectRecordInfo.modelType == '3'">亚马逊影响者/素人创作者</span>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" v-if="selectRecordInfo.videoFormat">
            <div class="label">视频格式：</div>
            <div class="content">
              <template v-for="item in videoFormatOptions" :key="item.value">
                <span v-if="item.value == selectRecordInfo?.videoFormat">{{ item.label }}</span>
              </template>
            </div>
          </el-col>
          <el-col :span="8" v-if="selectRecordInfo.intentionModelName">
            <div class="label">意向模特：</div>
            <div class="content">
              {{ selectRecordInfo.intentionModelName }} (ID:{{ selectRecordInfo.intentionModelId }})
            </div>
          </el-col>
          <el-col :span="8" v-if="selectRecordInfo.videoDuration">
            <div class="label">视频时长：</div>
            <div class="content">
              {{ selectRecordInfo?.videoDuration ? selectRecordInfo?.videoDuration + 's' : '' }}
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" v-if="selectRecordInfo.productPic || selectRecordInfo.productPic == null">
            <div class="label">产品图：</div>
            <div class="content">
              <template v-if="selectRecordInfo.productPic != null">
                <ViewerImageList
                  :data="[selectRecordInfo.productPic]"
                  is-preview-all
                  :show-delete-btn="false"
                />
              </template>
              <div v-else>-</div>
            </div>
          </el-col>
          <el-col
            :span="16"
            v-if="
              (selectRecordInfo.referencePic && selectRecordInfo.referencePic.length > 0) ||
              selectRecordInfo.referencePic == null
            "
          >
            <div class="label">参考图片：</div>
            <div class="content">
              <template v-if="selectRecordInfo.referencePic != null">
                <ViewerImageList
                  :data="selectRecordInfo.referencePic"
                  suffix="1x1"
                  is-preview-all
                  :show-delete-btn="false"
                />
              </template>
              <div v-else>-</div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <div class="flex-start col-24" v-if="selectRecordInfo.productCategoryName">
            <div class="label">产品品类：</div>
            <div class="content">{{ selectRecordInfo.productCategoryName }}</div>
          </div>
        </el-row>
        <el-row>
          <div class="flex-start col-24" v-if="selectRecordInfo.productChinese">
            <div class="label">中文名称：</div>
            <div class="content">{{ selectRecordInfo.productChinese }}</div>
          </div>
        </el-row>
        <el-row>
          <div class="flex-start col-24" v-if="selectRecordInfo.productEnglish">
            <div class="label">英文名称：</div>
            <div class="content more-ell">{{ selectRecordInfo.productEnglish }}</div>
          </div>
        </el-row>
        <el-row>
          <div class="flex-start col-24" v-if="selectRecordInfo.productLink">
            <div class="label">产品链接：</div>
            <div class="content more-ell">{{ selectRecordInfo?.productLink || '-' }}</div>
          </div>
        </el-row>
        <el-row>
          <div class="flex-start col-24" v-if="selectRecordInfo.referenceVideoLink">
            <div class="label">参考视频：</div>
            <div class="content more-ell">{{ selectRecordInfo?.referenceVideoLink || '-' }}</div>
          </div>
        </el-row>
        <el-row>
          <div
            class="flex-start col-24"
            v-if="selectRecordInfo.clipsRequired && selectRecordInfo.clipsRequired.length > 0"
          >
            <div class="label">剪辑要求：</div>
            <div class="content">
              <div v-for="(item, index) in selectRecordInfo.clipsRequired" :key="index">
                {{ index + 1 + '.' + item.content }}
              </div>
            </div>
          </div>
        </el-row>
        <el-row>
          <div
            class="flex-start col-24"
            v-if="selectRecordInfo.shootRequired && selectRecordInfo.shootRequired.length > 0"
          >
            <div class="label">拍摄要求：</div>
            <div class="content">
              <div v-for="(item, index) in selectRecordInfo.shootRequired" :key="index">
                {{ item.content }}
                <!-- {{ index + 1 + '.' + item.content }} -->
              </div>
            </div>
          </div>
        </el-row>
        <el-row>
          <div
            class="flex-start col-24"
            v-if="
              (selectRecordInfo?.cautions && selectRecordInfo.cautions.length > 0) ||
              selectRecordInfo.cautions == null
            "
          >
            <div class="label" style="text-align: start">
              匹配模特
              <br />
              注意事项：
            </div>
            <div class="content">
              <template v-if="selectRecordInfo.cautions != null">
                <div v-for="(item, index) in selectRecordInfo.cautions" :key="index">
                  {{ index + 1 + '：' + item.content }}
                </div>
              </template>
              <div v-else>-</div>
            </div>
          </div>
        </el-row>
        <!-- <el-row>
          <div class="flex-start col-24">
            <div class="label">匹配模特注意事项：</div>
            <div class="content more-ell">
            </div>
          </div>
        </el-row> -->
      </div>
    </div>
    <div v-else class="history-box empty flex-center">暂无变更记录</div>
  </PublicDialog>
</template>

<script setup lang="ts">
import ViewerImageList from '@/components/public/image/ViewerImageList.vue'
import { getVideoHistoryChangeRecord } from '@/api/order'
import { videoFormatOptions } from '@/utils/data'
import { getCurrentInstance, ref } from 'vue'

const { proxy } = getCurrentInstance() as any
const { biz_nation, biz_model_type, biz_model_platform } = proxy.useDict(
  'biz_nation',
  'biz_model_type',
  'biz_model_platform'
)
const DialogRef = ref()
const activeTab = ref(0)
const loading = ref(false)

defineExpose({ open, close })

const historyRecordList = ref<any[]>([])
const selectRecordInfo = ref<any>({
  changeUserName: '',
  changeUserId: '',
  changeTime: '',
  platform: '',
  shootingCountry: '',
  modelType: '',
  videoFormat: '',
  intentionModelName: '',
  intentionModelId: '',
  videoDuration: '',
  productChinese: '',
  productEnglish: '',
  productPic: '',
  productLink: '',
  referenceVideoLink: '',
  referencePic: [],
  clipsRequired: [],
  shootRequired: [],
  cautions: [],
  logType: '',
  productCategoryName: '',
})

function open(id: any) {
  if (!id) return
  getHistoryRecord(id)
  DialogRef.value?.open()
}
function handleClose() {
  activeTab.value = 0
  initSelectRecordInfo()
  DialogRef.value?.close()
}

function getHistoryRecord(id: any) {
  loading.value = true
  getVideoHistoryChangeRecord(id)
    .then(res => {
      historyRecordList.value = res.data || []
      if (
        historyRecordList.value &&
        historyRecordList.value.length > 0 &&
        historyRecordList.value[0].changeLogInfoList &&
        historyRecordList.value[0].changeLogInfoList.length > 0
      ) {
        handleData(historyRecordList.value[0].changeLogInfoList)
      }
      selectRecordInfo.value.changeUserName = historyRecordList.value[0].changeUser?.name
      selectRecordInfo.value.changeUserId = historyRecordList.value[0].changeUser?.id
      selectRecordInfo.value.changeTime = historyRecordList.value[0].changeTime
      selectRecordInfo.value.logType = historyRecordList.value[0].logType
    })
    .finally(() => {
      loading.value = false
    })
}

function handleData(list: any[]) {
  for (let key in selectRecordInfo.value) {
    list.forEach(item => {
      item.fieldName == key ? (selectRecordInfo.value[key] = item.value) : ''
    })
  }
}

function close() {
  DialogRef.value?.close()
}

function initSelectRecordInfo() {
  selectRecordInfo.value = {
    platform: '',
    modelType: '',
    changeTime: '',
    videoFormat: '',
    intentionModelName: '',
    intentionModelId: '',
    videoDuration: '',
    productChinese: '',
    shootingCountry: '',
    productEnglish: '',
    productLink: '',
    productPic: '',
    referenceVideoLink: '',
    clipsRequired: [],
    shootRequired: [],
    cautions: [],
    referencePic: [],
    logType: '',
    productCategoryName: '',
  }
}

function handleClick(index: number) {
  initSelectRecordInfo()
  activeTab.value = index
  selectRecordInfo.value.changeUserName = historyRecordList.value[index].changeUser?.name
  selectRecordInfo.value.changeUserId = historyRecordList.value[index].changeUser?.id
  selectRecordInfo.value.changeTime = historyRecordList.value[index].changeTime
  selectRecordInfo.value.logType = historyRecordList.value[index].logType
  if (
    historyRecordList.value[index].changeLogInfoList &&
    historyRecordList.value[index].changeLogInfoList.length > 0
  ) {
    handleData(historyRecordList.value[index].changeLogInfoList)
  }
  // selectRecordInfo.value = historyRecordList.value[index]
}
</script>

<style lang="scss" scoped>
.history-box {
  min-height: 500px;
  :deep(.el-steps--simple) {
    padding: 13px;
    overflow: auto;
    .el-step.is-simple {
      flex-grow: 0;
      flex-shrink: 0;
      flex-basis: auto !important;
      width: 200px;
    }
    .el-step.is-simple.is-flex {
      max-width: 100% !important;
    }
    .el-step__arrow {
      transform: rotateY(180deg);
    }
    .el-step__title {
      max-width: 100%;
    }
    .el-step__icon.is-icon {
      display: none;
    }
    .el-step.is-simple.is-flex {
      max-width: 100%;
    }
    .el-step__title.is-process {
      text-align: center;
      color: var(--el-color-primary);
    }
    .el-step__title.is-finish,
    .is-wait {
      text-align: center;
      color: #333;
    }
  }
  .edit-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 10px 0;
    &__title {
      font-size: 18px;
      font-weight: 700;
      color: #333;
    }
    &__content {
      font-size: 16px;
    }
  }
  .edit-content {
    margin: 10px 0;
    .el-col-8,
    .el-col-16 {
      display: flex;
      margin-bottom: 20px;

      .label {
        flex-shrink: 0;
        width: 70px;
        text-align: end;
      }
      .content {
        flex-shrink: 0;
        width: calc(100% - 95px);
        word-break: break-all;
      }
    }
    .col-24 {
      align-items: flex-start;
      margin-bottom: 20px;
      width: 100%;

      .label {
        flex-shrink: 0;
        width: 70px;
        text-align: end;
      }
      .content {
        flex-shrink: 0;
        width: calc(100% - 95px);
        word-break: break-all;
      }
      .tip {
        flex-shrink: 0;
        color: #7f7f7f;
      }
    }
  }
  .empty {
    height: 250px;
    font-size: 18px;
    color: #7f7f7f;
  }
}
</style>
