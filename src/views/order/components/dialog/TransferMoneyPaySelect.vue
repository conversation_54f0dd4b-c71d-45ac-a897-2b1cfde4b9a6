<template>
  <div>
    <PublicDialog
      ref="DialogRef"
      width="980px"
      title="全币种支付"
      :showFooterButton="false"
      custom-close
      align-center
      destroy-on-close
      style="padding: 0 0 20px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <template #header>
        <div class="flex-between pay-dialog-title">
          <span>
            {{
              guideType == '2' ? '手动支付-操作指引' : guideType == '1' ? '快捷支付-操作指引' : '全币种支付'
            }}
          </span>
          <div class="close-round-btn" v-if="!isTip" @click="close"></div>
          <div class="close-return-btn flex-center" v-if="isTip" @click="handleBack">
            <el-icon><ArrowLeft /></el-icon>
            返回
          </div>
        </div>
      </template>
      <template v-if="isTip">
        <div class="tip-box">
          <template v-if="guideType == '2'">
            <div class="tip-title">第1步:首页-点击转账；</div>
            <img
              class="tip-img"
              src="https://pstatic.woniu.video/static/assets/customer/pay/manual-1.webp"
              alt=""
            />
            <div class="tip-title">第2步:选择服务商快付-搜索合作服务商"蜗牛海拍"；</div>
            <img
              class="tip-img"
              src="https://pstatic.woniu.video/static/assets/customer/pay/manual-2.webp"
              alt=""
            />
            <div class="tip-title">第3步:进入输入金额页面，填写您需要指定货币账户和金额。</div>
            <img
              class="tip-img"
              src="https://pstatic.woniu.video/static/assets/customer/pay/manual-3.webp"
              alt=""
            />
            <div class="tip-title">第4步:下一步，剩余请按照万里汇页面指示，完成支付即可。</div>
          </template>
          <template v-else>
            <div class="tip-title">第1步:进入选择付款人页面，填写您的公司名称；</div>
            <img
              class="tip-img"
              src="https://pstatic.woniu.video/static/assets/customer/pay/smart-1.webp"
              alt=""
            />
            <div class="tip-title">第2步:在输入金额页面，填写您需要指定货币账户和金额。</div>
            <img
              class="tip-img"
              src="https://pstatic.woniu.video/static/assets/customer/pay/smart-2.webp"
              alt=""
            />
            <div class="tip-title">第3步:下一步，剩余请按照万里汇页面指示，完成支付即可。</div>
          </template>
        </div>
      </template>
      <template v-else>
        <div class="select-box" v-if="curTab === 1">
          <div class="box-process flex-center">
            <div class="process-item flex-center">
              <div class="process-item__icon flex-center">1</div>
              <div class="process-item__text">选择支付途径</div>
            </div>
            <div class="process-item__line"></div>
            <div class="process-item flex-center">
              <div class="process-item__no flex-center">2</div>
              <div class="process-item__text" style="color: #777777">填写公司信息及上传凭证</div>
            </div>
          </div>
          <div class="box-info">
            <div class="box-info__label">转账金额：</div>
            <div class="box-info__content">
              <span>{{ payAmount ? payAmount.toFixed(2) : payAmount }}</span>
              <span class="content-unit">USD</span>
              <span class="content-line">/</span>
              <span>{{ cnyAmount ? cnyAmount.toFixed(2) : cnyAmount }}</span>
              <span class="content-unit">CNY</span>
            </div>
          </div>
          <div class="box-tip">请选择一种支付途径进行付款:</div>
          <div class="box-pays">
            <template v-for="item in selectInfoList" :key="item.id">
              <div class="pay-items" :class="{ active: item.select }" @click="handleSelect(item)">
                <div v-if="item.recommend" class="items-left-icon">
                  <div>推荐</div>
                </div>
                <div class="flex-start" style="flex: 1">
                  <div class="items-image">
                    <img class="items-image__box" :src="item.icon" />
                  </div>
                  <div class="items-text">
                    <div class="items-text__top">{{ item.name }}</div>
                    <div class="items-text__bottom">{{ item.content }}</div>
                  </div>
                </div>
                <div class="items-icons">
                  <img :src="item.select ? isSelectIcon : noSelectIcon" alt="" />
                </div>
              </div>
            </template>
          </div>
          <div class="box-btns flex-center">
            <el-button
              type="primary"
              round
              style="font-size: 16px; height: 44px; padding: 11px 75px"
              @click="next"
            >
              下一步
            </el-button>
          </div>
        </div>
        <div class="pay-box card" v-if="curTab === 2">
          <div class="flex-column code-box">
            <div class="flex-center payWay-info-box">
              <div style="flex: 1; height: 100%; margin-right: 14px">
                <div class="step-title top">
                  第1步：
                  <span v-if="curSelectId === '2'">其他平台/银行支付信息</span>
                  <span v-else>使用万里汇支付</span>
                </div>
                <div class="flex-column gap-10 left-box">
                  <div class="bg">
                    <template v-if="curSelectId === '1'">
                      <div class="flex-start info-item m-0">
                        <div class="long-label" style="width: 70px">转账金额：</div>
                        <div class="content red">
                          <span style="font-size: 20px; font-weight: bold">
                            {{ payAmount ? payAmount.toFixed(2) : payAmount }}
                          </span>
                          <span style="font-weight: normal; font-size: 12px">&nbsp;USD</span>
                          <span style="font-weight: normal; font-size: 20px; margin: 0 8px">/</span>
                          <span style="font-size: 20px; font-weight: bold">
                            {{ cnyAmount ? cnyAmount.toFixed(2) : cnyAmount }}
                          </span>
                          <span style="font-weight: normal; font-size: 12px">&nbsp;CNY</span>
                        </div>
                      </div>
                      <div class="flex-start info-item m-0" style="margin-top: 20px">
                        <div class="long-label" style="width: 70px">支付方式：</div>
                        <div class="content">
                          <div class="content-btns">
                            <el-button
                              type="primary"
                              class="pay-btn"
                              round
                              @click="
                                handleOpenLink(
                                  'https://portal.worldfirst.com.cn/payment/transfer?id=2120120255940755&payType=vendor'
                                )
                              "
                            >
                              快捷支付
                            </el-button>
                            <el-button
                              type="primary"
                              link
                              style="font-size: 12px"
                              @click="handleViewGuide('1')"
                            >
                              <img style="width: 12px; height: 12px" src="@/assets/icon/help_circle_v2.png" />
                              查看操作指引
                            </el-button>
                          </div>
                          <div class="content-btns" style="margin-top: 14px">
                            <el-button
                              type="primary"
                              class="pay-btn"
                              round
                              @click="
                                handleOpenLink(
                                  'https://portal.worldfirst.com.cn/payment/transfer?id=2120120255940755&payType=vendor'
                                )
                              "
                            >
                              手动支付
                            </el-button>
                            <el-button
                              type="primary"
                              link
                              style="font-size: 12px"
                              @click="handleViewGuide('2')"
                            >
                              <img style="width: 12px; height: 12px" src="@/assets/icon/help_circle_v2.png" />
                              查看操作指引
                            </el-button>
                          </div>
                        </div>
                      </div>
                    </template>
                    <template v-else>
                      <div class="flex-between title">
                        汇款信息：
                        <CopyButton :copy-content="collectionAccInfo" type="primary" icon="Document">
                          复制汇款信息
                        </CopyButton>
                      </div>
                      <div class="flex-start info-item m-0">
                        <div class="long-label">转账金额：</div>
                        <div class="content red">
                          <span style="font-size: 20px; font-weight: bold">
                            {{ payAmount ? payAmount.toFixed(2) : payAmount }}
                            <span style="font-weight: normal; font-size: 12px">USD</span>
                          </span>
                          <span style="font-weight: normal; font-size: 20px; margin: 0 8px">/</span>
                          <span style="font-size: 20px; font-weight: bold">
                            {{ cnyAmount ? cnyAmount.toFixed(2) : cnyAmount }}
                          </span>
                          <span style="font-weight: normal; font-size: 12px">&nbsp;CNY</span>
                        </div>
                      </div>
                      <div
                        class="flex-start info-item m-0"
                        v-for="(item, index) in accInfoConfigList"
                        :key="index"
                      >
                        <div class="long-label">{{ item.label }}：</div>
                        <div class="content">
                          {{ item.key ? accInfo[item.key] : item.value || '' }}
                          <el-icon class="is-loading" v-if="getValidConfigLoading"><Loading /></el-icon>
                          <div class="tip" v-if="item.key && accInfo[item.key] && item.tip">
                            {{ item.tip }}
                          </div>
                        </div>
                      </div>
                    </template>
                  </div>
                </div>
              </div>
              <div style="flex: 1; height: 100%">
                <div class="step-title top">第2步：填写公司信息及上传付款回执</div>
                <div class="right-box">
                  <el-form
                    ref="formRef"
                    @submit.prevent
                    :model="form"
                    label-position="top"
                    :rules="rules"
                    label-width="auto"
                    :disabled="disabled"
                  >
                    <el-form-item label="付款账户名称" prop="name">
                      <el-input
                        v-model="form.name"
                        placeholder="请输入付款账户名称"
                        maxlength="100"
                        clearable
                      />
                    </el-form-item>
                    <el-form-item label="上传付款回执" prop="file">
                      <div class="upload-box">
                        <div class="flex-start" style="width: 100%">
                          <PasteUpload v-model="form.file" :limit="5" :size="5" bucket="credit" />
                        </div>
                        <div class="upload-tips">
                          请上传大小不超过
                          <span style="color: #f29c2d">5M</span>
                          ，格式为
                          <span style="color: #f29c2d">png/jpg/jpeg</span>
                          的图片
                          <el-button
                            type="primary"
                            link
                            @click="
                              previewImage(
                                'https://pstatic.woniu.video/static/full-type-example.jpg!fullSize'
                              )
                            "
                            style="vertical-align: baseline"
                          >
                            示例参考
                          </el-button>
                        </div>
                        <div style="width: 390px; overflow-x: auto">
                          <ViewerImageList
                            style="margin-top: 10px"
                            :data="form.file"
                            :style="{ 'flex-wrap': 'nowrap', 'overflow-y': 'hidden' }"
                            is-preview-all
                            url-name="picUrl"
                            suffix="fullSize"
                            @delete="handleDelImg"
                          />
                        </div>
                      </div>
                    </el-form-item>
                  </el-form>
                  <div class="tips">
                    <span>温馨提示</span>
                    <br />
                    1. 付款回执需清晰可见付款账号、金额、付款人、时间等信息;
                    <br />
                    2. 汇款信息必须与订单相关信息一致，否则平台将无法确认收款;
                  </div>
                </div>
              </div>
            </div>
            <div class="flex-center" style="gap: 0 12px">
              <el-button
                class="submit-button"
                style="border: 1px solid #5696c0; color: #5996c0"
                @click="handlePre"
                :loading="loadingBtn"
              >
                上一步
              </el-button>
              <el-button class="submit-button" type="primary" @click="onSubmit" :loading="loadingBtn">
                提交凭证信息
              </el-button>
            </div>
          </div>
        </div>
      </template>
    </PublicDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import CopyButton from '@/components/public/button/CopyButton.vue'
import { getValidConfig } from '@/api/order'
import { getAnotherPayValidConfig } from '@/api/anotherPay'
import { previewImage } from '@/utils/preview'
import PasteUpload from '@/components/public/upload/PasteUpload.vue'
import ViewerImageList from '@/components/public/image/ViewerImageList.vue'
import wanlihuiIcon from '@/assets/image/wanlihui_icon.png'
import otherPayIcon from '@/assets/image/other_pay_icon.png'
import isSelectIcon from '@/assets/icon/is_select_icon.png'
import noSelectIcon from '@/assets/icon/no_select_icon.png'

const props = defineProps({
  payAmount: {
    type: Number,
    required: true,
  },
  cnyAmount: {
    type: Number,
    required: false,
  },
  payeeId: {
    type: Number,
    required: false,
  },
  detailId: {
    type: Number,
    required: false,
  },
  orderNum: {
    type: String,
    default: '',
  },
  mergeId: {
    type: [String, Number],
    default: '',
  },
  isAnotherPay: {
    type: Boolean,
    default: false,
  },
})

defineExpose({
  open,
  close,
})

const emits = defineEmits(['submit', 'close'])
const isTip = ref(false)
const curSelectId = ref('1')
const curTab = ref(1)
const accInfoConfigList = [
  { label: '收款账户类型', key: 'companyAccountType' },
  { label: '银行所在地', key: 'bankName' },
  { label: '收款支持币种', value: '离岸人民币/美元/澳元/加币/欧元/英镑/港币/日元/新西兰元/新加坡元' },
  { label: '账户名', key: 'accountName' },
  {
    label: 'SWIFT代码',
    key: 'companyBankSwiftCode',
    tip: '(如果系统提示需要输入11位数，请在最后面加“XXX”凑够即可)',
  },
  { label: '银行账号', key: 'bankAccount' },
  { label: '收款人地址', key: 'companyBankPayeeAddress' },
  { label: '银行地址', key: 'companyBankAddress' },
  { label: '银行代码', key: 'companyBankCode' },
  { label: '分行代码', key: 'companyBankSubCode' },
  { label: '银行名称', key: 'companyBankName' },
]
const selectInfoList = ref([
  {
    name: '万里汇支付',
    content: '0手续费，实时秒到账',
    icon: wanlihuiIcon,
    select: true,
    recommend: true,
    id: '1',
  },
  {
    name: '其他平台/银行支付',
    content: '收款支持币种：离岸人民币/美元/澳元/加币/欧元/英镑/港币/日元/新西兰元/新加坡元',
    icon: otherPayIcon,
    select: false,
    recommend: false,
    id: '2',
  },
])

function getTimestamp(dateString: string) {
  const date = new Date(dateString)
  return date.getTime()
}

const DialogRef = ref()

function open(dataObj: any) {
  DialogRef.value.open()
}
function close() {
  DialogRef.value.close()
}
function handleBack() {
  isTip.value = false
  guideType.value = ''
}
function handleClose() {
  selectInfoList.value = [
    {
      name: '万里汇支付',
      content: '0手续费，实时秒到账',
      icon: wanlihuiIcon,
      select: true,
      recommend: true,
      id: '1',
    },
    {
      name: '其他平台/银行支付',
      content: '收款支持币种：离岸人民币/美元/澳元/加币/欧元/英镑/港币/日元/新西兰元/新加坡元',
      icon: otherPayIcon,
      select: false,
      recommend: false,
      id: '2',
    },
  ]
  accInfo.value = {
    accountName: '',
    bankAccount: '',
    bankName: '',
  }
  curSelectId.value = '1'
  curTab.value = 1
  emits('close')
}

function handleSelect(item: any) {
  selectInfoList.value.forEach((el: any) => {
    el.select = false
  })
  curSelectId.value = item.id
  item.select = true
}

function handlePre() {
  curTab.value = 1
  selectInfoList.value.forEach((item: any) => {
    item.select = false
    if (curSelectId.value === item.id) {
      item.select = true
    }
  })
}

function next() {
  curTab.value = 2
  getValidConfigData()
  //   emits('submit', curSelectId.value)
}
const detailId = ref(0)
function getValidConfigData() {
  getValidConfigLoading.value = true
  let fn = props.isAnotherPay ? getAnotherPayValidConfig : getValidConfig
  fn({ type: 7, payeeId: props.payeeId || undefined, detailId: detailId.value || undefined })
    .then(res => {
      if (res.data) {
        accInfo.value = res.data
      }
    })
    .finally(() => {
      getValidConfigLoading.value = false
    })
}

const getValidConfigLoading = ref(false)
const accInfo = ref<any>({
  accountName: '',
  bankAccount: '',
  bankName: '',
})
const formRef = ref()
const form = ref<{
  name: string
  file: any[]
}>({
  name: '',
  file: [],
})
const rules = {
  name: [{ required: true, message: '请输入付款账户名称', trigger: 'blur' }],
  file: [{ required: true, message: '请上传转账截图', trigger: 'change' }],
}

const disabled = ref(false)
const loadingBtn = ref(false)

const collectionAccInfo = computed(() => {
  let str = accInfoConfigList.map(item => {
    if (item.key) {
      return `${item.label}：${accInfo.value[item.key] || ''} ${item.tip || ''}`
    }
    return `${item.label}：${item.value || ''}`
  })
  return str.filter(Boolean).join('\n')
})
function handleDelImg(_data: any, i: number) {
  form.value.file.splice(i, 1)
}

function onSubmit() {
  if (form.value.name) {
    form.value.name = form.value.name.trim()
  }
  formRef.value.validate((valid: any) => {
    if (valid) {
      ElMessageBox.confirm('确认提交！', '提示', {})
        .then(() => {
          loadingBtn.value = true
          disabled.value = true
          let payDocument: any[] = []
          form.value.file.map(item => payDocument.push(item.picUrl))
          let params: {
            [x: string]: any
          } = {
            orderNum: !props.mergeId ? props.orderNum : undefined,
            mergeId: props.mergeId || undefined,
            objectKeys: payDocument,
            payAmount: props.payAmount,
            payType: 7,
            content: '现代服务推广费',
            payTypeDetail: curSelectId.value == '1' ? 702 : 701,
          }
          params.payAccount = form.value.name
          emits('submit', params, () => {
            loadingBtn.value = false
            disabled.value = false
          })
        })
        .catch(() => {})
    }
  })
}

function handleOpenLink(url: string) {
  window.open(url)
}

const guideType = ref('')
function handleViewGuide(type: string) {
  guideType.value = type
  isTip.value = true
}
</script>

<style lang="scss" scoped>
textarea,
div,
ul {
  scrollbar-width: auto;
  scrollbar-color: auto;
}

:deep(.upload-container) {
  .up-text {
    color: #777;
  }
}

.title {
  position: relative;
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 10px;
  // padding-left: 10px;
  color: var(--text-color);
  &.primary {
    font-size: 25px;
    font-weight: 600;
    color: var(--el-color-primary);
  }
}
.card {
  border-radius: 10px;
  background-color: var(--bg);
  padding-bottom: 14px;
}
.pay-dialog-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--el-color-primary);
  background: var(--bg);
  padding: 13px 20px;
  border-radius: 12px 12px 0 0;
}
.close-return-btn {
  font-size: 16px;
  color: #777777;
  font-weight: 400;
  cursor: pointer;
}

.select-box {
  padding: 24px 92px 52px 92px;
  .box-process {
    font-size: 16px;
    .process-item__icon {
      width: 30px;
      height: 30px;
      background: #5696c0;
      border-radius: 50%;
      color: #fff;
    }
    .process-item__text {
      margin-left: 10px;
      color: #333333;
    }
    .process-item__no {
      box-sizing: border-box;
      width: 30px;
      height: 30px;
      border-radius: 50%;
      color: #aaa;
      border: 2px solid #aaaaaa;
    }
    .process-item__line {
      width: 100px;
      height: 0px;
      border-radius: 0px 0px 0px 0px;
      border: 1px solid #5696c0;
      margin: 0 15px;
    }
  }
  .box-info {
    margin-top: 28px;
    display: flex;
    justify-content: start;
    align-items: baseline;
    &__label {
      font-weight: 500;
      font-size: 18px;
      color: #333333;
    }
    &__content {
      font-family: 'D-DIN', 'D-DIN';
      font-weight: bold;
      font-size: 26px;
      color: #ff5722;
    }
    .content-line {
      font-weight: 400;
      margin: 0 7px;
    }
    .content-unit {
      font-weight: 400;
      font-size: 14px;
      margin-left: 2px;
    }
  }
  .box-tip {
    font-size: 16px;
    color: #777;
    margin-top: 10px;
  }
  .box-pays {
    gap: 16px 0;
    display: grid;
    margin-top: 21px;
    .pay-items {
      cursor: pointer;
      padding: 30px 40px;
      border-radius: 24px 24px 24px 24px;
      border: 1px solid #e7e7e7;
      display: flex;
      align-items: center;
      position: relative;
      overflow: hidden;
      .items-left-icon {
        transform: rotateZ(-45deg);
        width: 75px;
        text-align: center;
        position: absolute;
        background: #ff6565;
        color: #fff;
        top: 5px;
        left: -20px;
      }
    }
    .items-image {
      height: 55px;
      width: 55px;
      flex-shrink: 0;
      &__box {
        width: 100%;
      }
    }
    .items-text {
      margin-left: 16px;
      &__top {
        font-weight: 500;
        font-size: 18px;
        color: #333333;
      }
      &__bottom {
        margin-top: 6px;
        font-size: 14px;
        color: #777777;
      }
    }
    .items-icons {
      width: 16px;
      height: 16px;
      img {
        width: 100%;
      }
    }
    .active {
      background: #e5eef5;
      border: 1px solid #5696c0;
    }
  }
  .box-btns {
    margin-top: 46px;
  }
}
.pay-box {
  padding-top: 0px;
  padding-bottom: 1px;

  .code-box {
    border-radius: 8px;
    .total-box {
      text-align: center;
      font-size: 18px;
      color: var(--text-color);
      width: 95%;
      background: #f6f6f6;
      border-radius: 16px;

      span {
        font-size: 30px;
        font-weight: 600;
      }
      .tips {
        font-size: 13px;
      }
    }
    .code-img {
      margin-right: 10px;
      min-width: 180px;
      min-height: 180px;
    }

    .primary-btn {
      color: var(--el-color-primary);
      &:not(.is-link) {
        border-color: var(--el-color-primary);
      }
    }

    .payWay-info-box {
      width: 100%;
      box-sizing: border-box;
      border-radius: 4px;
      padding: 15px 15px 30px;
      align-items: flex-start;
      gap: 10px;
      height: 510px;
      justify-content: space-between;
      .step-title {
        color: var(--text-color);
        font-size: 16px;

        &.top {
          position: relative;
          top: -11px;
        }
      }

      .left-box {
        min-width: 400px;
        height: 100%;
        overflow-y: auto;
        align-items: flex-start;
        border-radius: 8px;

        .bg {
          background-color: var(--el-color-info-light-9);
          padding: 16px;
          border-radius: 8px;
          width: 100%;
          box-sizing: border-box;
          flex: 2;
        }

        .label {
          width: 80px;
          text-align: right;
          color: var(--text-gray-color);
        }
        .long-label {
          width: 98px;
          text-align: right;
          // color: var(--text-gray-color);
          color: #777;
        }

        .info-item {
          margin-bottom: 10px;
          align-items: baseline;
          color: var(--text-color);
          line-height: 28px;
          font-size: 14px;
          gap: 5px 0;
          .label {
            flex-shrink: 0;
          }

          &.m-0 {
            margin-bottom: 0;
          }

          .content {
            font-weight: 500;
            position: relative;
            line-height: 22px;
            max-width: 310px;

            &.red {
              color: #ff5722;
              font-family: 'D-DIN', 'D-DIN';
              // color: var(--el-color-danger);
              // line-height: 30px;
            }

            .copy-btn {
              position: absolute;
              top: 8px;
              right: -45px;
              z-index: 9;
            }

            .tip {
              // color: var(--text-gray-color);
              color: #777;
              font-size: 12px;
              position: relative;
              left: -1px;
              width: 310px;
            }
            .content-btns {
              display: flex;
              align-items: flex-end;
            }
          }
          .pay-btn {
            padding: 6px 52px;
          }
        }
      }
      .right-box {
        min-width: 400px;
        padding: 20px;
        background-color: var(--el-color-info-light-9);
        border-radius: 8px;
        height: 100%;
        box-sizing: border-box;
        overflow-y: auto;

        .tips {
          font-size: 13px;
          padding: 10px 20px 10px 0;
          // color: var(--text-gray-color);
          color: #777;
          margin: 10px 0;

          span {
            color: var(--text-color);
            // font-size: 12px;
            font-weight: bold;
          }
        }
      }
      .upload-box {
        position: relative;
        width: 100%;
        .upload-tips {
          color: var(--text-color);
          line-height: 22px;
          font-size: 13px;
          margin-top: 5px;
        }
      }
    }

    .el-divider {
      margin: 45px 22px 0;
      height: 13em;
    }
  }
  .submit-button {
    width: 200px;
    height: 44px;
    font-size: 15px;
    font-weight: bold;
    border-radius: 25px;
    margin-top: 21px;
  }
}
.tip-box {
  height: 576px;
  overflow: auto;
  padding: 0 30px;
  .tip-title {
    margin: 16px 0 8px 34px;
    font-weight: 500;
    font-size: 20px;
    color: #333333;
  }
  .tip-img {
    width: 842px;
    height: 483px;
  }
}
</style>
