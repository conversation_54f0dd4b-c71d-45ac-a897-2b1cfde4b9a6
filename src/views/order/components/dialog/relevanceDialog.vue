<template>
  <div>
    <PublicDialog
      v-model="dialogVisible"
      ref="DialogRef"
      width="900px"
      title="亚马逊关联区说明"
      :showFooterButton="false"
      :title-line="false"
      custom-close
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="close"
    >
      <div class="content-box">
        <div>
          这是蜗牛海拍平台提供的一项免费上传服务，仅限
          <span style="color: #fe9400">美国亚马逊</span>
          网站，您填写资料后，我们会将您的视频上传至对应链接下的关联区。
        </div>
        <br />
        <div>
          1、关联区是指产品链接下方的
          <span style="color: #fe9400">Videos区</span>
          （目前仅限美国亚马逊网站），用于展示与该产品相关的视频，上传视频可以帮助卖家提高商品页面的质量和吸引力，从而增加商品的曝光和销售。
        </div>
        <br />
        <div>
          2、您查看素材时填写信息：标题和封面，提交后，预计一个工作日左右会有专员给您处理上传，亚马逊平台审核预计48小时左右会显示到视频区。由于平台审核规则，不能保证100%通过，您也可以使用自己的账号进行上传。
        </div>
        <br />
        <div>3、每个视频仅提供1次上传服务。</div>
        <br />
        <div>
          <img class="img" :src="$picUrl + 'static/assets/customer/order/relevance.webp'" alt="">
        </div>
      </div>
    </PublicDialog>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const dialogVisible = ref(false)
const DialogRef = ref()
defineExpose({
  open,
  close,
})

function open() {
  dialogVisible.value = true
}

function close() {
  dialogVisible.value = false
}
</script>

<style scoped lang="scss">
.content-box {
  padding: 0 10px;
}
.img {
  box-shadow: 0 0px 10px rgba(0, 0, 0, 0.2); /* 水平偏移量、垂直偏移量、模糊半径、颜色 */
  border-radius: 8px; /* 可选：给图片添加圆角 */
  width: 100%;
  height: 100%;
  object-fit: cover;
}
</style>
