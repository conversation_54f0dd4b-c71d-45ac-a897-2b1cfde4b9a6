<template>
  <PublicDialog
    ref="DialogRef"
    width="750px"
    title="确认素材"
    :showFooterButton="false"
    custom-close
    align-center
    :titleCenter="false"
    :close-on-click-modal="false"
    @close="close"
  >
    <div class="confirm-box">
      <div style="position: absolute; top: -54px; left: 80px">
        <h4 v-if="disabled" style="margin-top: 0; color: #d67c5a">
          {{ form.isUpload == 1 ? '素材上传中' : form.isUpload == 0 ? '素材已上传' : '' }}
        </h4>
      </div>

      <template v-if="!disabled">
        <!-- <div class="step-title" style="font-size: 15px; font-weight: bold">验收流程：</div> -->
        <div class="step-title">
          <span v-if="selectLinkBtn.length > 1">第一步：</span>
          查看模特反馈的拍摄素材
        </div>
        <FeedbackLink v-if="videoId" :videoId="videoId" />
      </template>

      <!-- <div class="step-title" v-if="!disabled && selectLinkBtn.length == 1">
        第二步：验收素材无误后，请确认验收
      </div> -->
      <div class="step-title" v-if="!disabled && selectLinkBtn.length > 1">
        第二步：确认是否上传关联区
        <el-tag style="margin-left: 8px; cursor: pointer" effect="dark" @click="openHintDialog">
          关联区是什么？
        </el-tag>
      </div>

      <el-form :model="form" :rules="rules" ref="formRef" label-width="auto">
        <div
          class="confirm-info"
          :style="{ paddingBottom: form.needUpload == '1' ? '1px' : '' }"
          v-if="(!disabled && selectLinkBtn.length > 1) || form.isUpload == 1"
        >
          <div v-if="form.isUpload != 1">
            <el-form-item label="请选择" prop="needUpload">
              <el-radio-group v-model="form.needUpload" @change="changeIsLink">
                <el-radio-button
                  v-for="item in selectLinkBtn"
                  :key="item.value"
                  :value="item.value"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </div>
          <template v-if="form.needUpload == '0'">
            <div>
              <el-form-item label="上传链接" prop="needUploadLink">
                <el-input
                  :disabled="disabled"
                  placeholder="请输入上传的链接"
                  :rows="3"
                  type="textarea"
                  resize="none"
                  v-model.trim="form.needUploadLink"
                ></el-input>
                <div class="tips" v-if="isShowLinkHint && !disabled">*若有新的产品链接，直接更换即可</div>
              </el-form-item>
              <el-form-item label="视频标题" prop="videoTitle">
                <el-input
                  :disabled="disabled"
                  placeholder="请输入视频标题"
                  v-model="form.videoTitle"
                  type="text"
                  minlength="10"
                ></el-input>
                <div
                  class="tips"
                  style="line-height: 12px; margin-top: 10px"
                  v-if="isShowTitleHint && orderStatus != '8'"
                >
                  请输入全英文字母，3个单词以上，60个字符以内。请勿使用特殊字符、表情符号（例如$、℃、℉）、产品Asin、全大写或全数字标题。
                </div>
              </el-form-item>
              <el-form-item label="视频封面">
                <!-- v-if="filePicList && filePicList.length > 0" -->
                <template v-if="(filePicList && filePicList.length > 0) || orderStatus == '7'">
                  <el-upload
                    v-model:file-list="filePicList"
                    ref="uploadRef"
                    action=""
                    :accept="isMobileDevice() ? 'image/*' : ''"
                    :capture="isMobileDevice() ? 'user' : ''"
                    :disabled="disabled"
                    list-type="picture-card"
                    :limit="1"
                    :on-preview="handlePictureCardPreview"
                    :http-request="uploadPhotoFile"
                    :before-upload="beforeUpload"
                    :before-remove="beforeRemove"
                    class="show-gif"
                    :class="{
                      disabled,
                      hidden: filePicList && filePicList.length >= 1,
                    }"
                  >
                    <el-icon><Plus /></el-icon>
                  </el-upload>
                  <div class="upload-tips" v-if="!disabled">
                    1. 请上传1张大小不超过
                    <span class="upload-tips__hint">5M</span>
                    ，格式为
                    <span class="upload-tips__hint">png/jpg、jpeg/bmp/gif</span>
                    的文件;
                    <br />
                    2.分辨率推荐上传
                    <span class="upload-tips__hint">1920*1080px</span>
                    （最小上传640*360px/最大上传3840*2160px）
                  </div>
                </template>
                <template v-else>无</template>
              </el-form-item>
            </div>
          </template>
        </div>
      </el-form>
      <div class="flex-column btn" v-if="!disabled">
        <div class="btn-tip-red">
          点击【确认素材无误{{ form.needUpload == '0' ? '并上传至关联区' : '' }}】后，订单将进入"已完成"状态
        </div>
        <template v-if="form.needUpload == '0' && selectLinkBtn.length > 1">
          <div class="btn-tip">并且在预计1个工作日内会安排上传，操作上传后，亚马逊的审核需要48小时</div>
          <div class="btn-tip">请先确认是否需要安排上传，再提交上传需求哦~</div>
        </template>
        <el-button class="btn-big" type="primary" style="margin-top: 12px" round @click="onConfirm">
          确认素材无误{{ form.needUpload == '0' ? '并上传至关联区' : '' }}
        </el-button>
      </div>
    </div>
    <RelevanceDialog ref="RelevanceDialogRef" />
  </PublicDialog>
</template>

<script setup lang="ts">
import FeedbackLink from '@/views/order/components/dialog/FeedbackLink.vue'
import RelevanceDialog from '@/views/order/components/dialog/relevanceDialog.vue'
import { ref, getCurrentInstance, computed } from 'vue'
import { englishCharacter_d_reg } from '@/utils/RegExp'
import { checkImgResolutionRatio, isMobileDevice } from '@/utils/public'
import { ElMessage } from 'element-plus'
import { getUploadMaterial } from '@/api/order'
import useOrderApi from '@/hooks/useOrderApi'
import { uploadCloudFile } from '@/api/index'
import { useViewer } from '@/hooks/useViewer'
// import { MessageBox } from '@/utils/message'

const { showViewer } = useViewer()

const { handleConfirmProduct } = useOrderApi()

const { proxy } = getCurrentInstance() as any
const pathHead = proxy.$picUrl

const DialogRef = ref()
const uploadRef = ref()
const formRef = ref()
const disabled = computed(() => {
  return orderStatus.value == '8'
})
const selectLinkBtn = ref([
  { label: '上传至关联区', value: 0 },
  { label: '无需上传', value: 1 },
])

type FormType = {
  needUpload: string | number
  needUploadLink: string | null
  videoTitle: string | null
  isUpload: null | number | string
  videoId: any
}

const form = ref<FormType>({
  needUpload: 1,
  needUploadLink: null,
  isUpload: null,
  videoTitle: null,
  videoId: '',
})

const filePicList = ref<{ url: any }[]>([])

// defineProps
const emits = defineEmits(['success'])

defineExpose({ open })

const rules = {
  needUpload: [{ required: true, message: '请选择', trigger: 'blur' }],
  needUploadLink: [{ required: true, validator: validateLink, trigger: 'change' }],
  videoTitle: [
    { required: true, message: '请输入视频标题', trigger: 'blur' },
    { validator: validateTitle, trigger: ['blur', 'change'] },
  ],
}

//视频链接校验规则
const isShowLinkHint = ref(true)
function validateLink(rule: any, value: any, callback: any) {
  const regLink = 'https://www.amazon.com'
  if (value && value.startsWith(regLink)) {
    isShowLinkHint.value = true
    return callback()
  } else {
    isShowLinkHint.value = false
    return callback(new Error('视频链接有格式错误'))
  }
}

//视频标题校验规则
// const regexAscll = /[^\x00-\x7F]|[A-Z0-9]{10}/
const regexAscll = /^(?!.*\\b(?=[A-Z0-9]{10}\\b)(?!(?:[A-Z]{10}|\\d{10})\\b)[A-Z0-9]{10}\\b).*$/
const regexNum = /^\d+$/
// const regexS = /S/ &&
// !regexS.test(value)
const isShowTitleHint = ref(true)
function validateTitle(rule: any, value: any, callback: any) {
  if (!value) {
    return callback(new Error('请输入视频标题'))
  }
  if (value.trim().length > 60) {
    return callback(new Error('视频标题不能超过60个字符'))
  }
  if (
    englishCharacter_d_reg.test(value) &&
    value != value.toUpperCase() &&
    !regexNum.test(value) &&
    regexAscll.test(value)
  ) {
    isShowTitleHint.value = true
    let temp = value.split(' ').filter(Boolean)
    if (temp.length >= 3 && value.length >= 10) {
      isShowTitleHint.value = true
      return callback()
    } else {
      isShowTitleHint.value = false
      return callback(new Error('请最少输入3个单词,10个字符'))
    }
  } else {
    isShowTitleHint.value = false
    return callback(new Error('请勿输入: 表情符号、"$"、非英文字符、产品 ASIN、全大写或全数字标题'))
  }
}

function changeIsLink(value: string) {
  productPicUrl.value = ''
  filePicList.value = []
  form.value = {
    needUpload: value,
    needUploadLink: needUploadLink.value ? needUploadLink.value : null,
    videoTitle: null,
    isUpload: null,
    videoId: '',
  }
}

function handlePictureCardPreview(file: { url: string }) {
  showViewer([productPicUrl.value])
}

const productPicUrl = ref()
async function uploadPhotoFile(options: { file: string | Blob }) {
  let res = await checkImgResolutionRatio(options.file)
  if (res) {
    if (res.width < 640 || res.width > 3840 || res.height < 360 || res.height > 2160) {
      beforeRemove()
      uploadRef.value?.clearFiles()
      return ElMessage({
        message: `图片分辨率超过限制!`,
        type: 'warning',
      })
    }
  }
  const formData = new FormData()
  formData.append('file', options.file)
  uploadCloudFile(formData)
    .then((res: any) => {
      if (res.code === 200) {
        if (res.data.picUrl) {
          res.data.picUrl.slice(-3) == 'gif'
            ? (filePicList.value[0].url = pathHead + res.data.picUrl)
            : (filePicList.value[0].url = pathHead + res.data.picUrl + '!1x1')
        }
        productPicUrl.value = res.data.picUrl
      }
    })
    .catch(() => {
      beforeRemove()
      uploadRef.value?.clearFiles()
    })
}
const whiteList = 'PNG,png,JPG,jpg,JPEG,jpeg,bmp,BMP,gif,GIF'
function beforeUpload(raw: any) {
  const fileSuffix = raw.name.substring(raw.name.lastIndexOf('.') + 1)
  if (whiteList.indexOf(fileSuffix) === -1) {
    ElMessage({
      message: `请上传格式为png,jpg,jpeg,bmp,gif的文件！`,
      type: 'warning',
    })
    return false
  }
  if (raw.size > 1024 * 1024 * 5) {
    ElMessage({
      message: `上传大小不超过5M！`,
      type: 'warning',
    })
    return false
  }
  if (filePicList.value && filePicList.value.length >= 1) {
    ElMessage({
      message: `最多可上传1张图片！`,
      type: 'warning',
    })
    return false
  }
  return true
}

function beforeRemove() {
  productPicUrl.value = ''
}

function getDetail() {
  getUploadMaterial(videoId.value).then(res => {
    if (res.code === 200) {
      form.value.needUpload = selectLinkBtn.value.length > 1 ? 0 : 1
      form.value.needUploadLink = res.data.needUploadLink
      form.value.videoTitle = res.data.videoTitle
      form.value.isUpload = res.data.status
      productPicUrl.value = res.data.videoCover
      const data = {
        url: proxy.$picUrl + res.data.videoCover + '!1x1',
      }
      res.data.videoCover ? filePicList.value.push(data) : ''
      // filePicList.value.push(data)
    }
  })
}
//弹窗打开
const videoId = ref('')
const orderStatus = ref('')
const needUploadLink = ref('')
function open(
  id: any,
  status: any,
  item: {
    link?: string
    platform: number
    modelType: number
    nation: number
    shootModelType: number
  }
) {
  orderStatus.value = status
  videoId.value = id
  if (item.link) {
    needUploadLink.value = item.link
  }
  // 订单满足“美国”“素人”“亚马逊”时
  if (
    item.nation == 7 &&
    item.platform == 0 &&
    (item.modelType == 1 || item.modelType == 3) &&
    item.shootModelType != 0
  ) {
    selectLinkBtn.value = [
      { label: '上传至关联区', value: 0 },
      { label: '无需上传', value: 1 },
    ]
    form.value.needUpload = 0
    form.value.needUploadLink = needUploadLink.value
  } else {
    selectLinkBtn.value = [
      { label: '无需上传', value: 1 },
      // { label: '上传至关联区', value: 0 },
    ]
  }

  if (status == 8) {
    getDetail()
  }

  DialogRef.value.open()
}
function onConfirm() {
  if (form.value.videoTitle) {
    form.value.videoTitle = form.value.videoTitle.trim()
  }
  formRef.value.validate((valid: any) => {
    if (valid) {
      // MessageBox(`素材确认提醒<br><span>请查看模特反馈的拍摄素材无误后确认~</span>`)
      //   .then(() => {
      let videoCover = productPicUrl.value
      let data = {}
      if (form.value.needUpload == 1) {
        data = {
          needUpload: form.value.needUpload,
          videoId: videoId.value,
        }
      } else {
        data = {
          ...form.value,
          videoId: videoId.value,
          videoCover,
        }
      }

      handleConfirmProduct(data, () => {
        close()
        emits('success')
      })
      // })
    }
  })
}
function close() {
  productPicUrl.value = ''
  filePicList.value = []
  needUploadLink.value = ''
  form.value = {
    needUpload: 1,
    needUploadLink: null,
    videoTitle: null,
    isUpload: null,
    videoId: '',
  }
  videoId.value = ''
  isShowTitleHint.value = true
  formRef.value.resetFields()
  DialogRef.value.close()
}
const RelevanceDialogRef = ref<InstanceType<typeof RelevanceDialog>>()
function openHintDialog() {
  RelevanceDialogRef.value?.open()
}
</script>

<style lang="scss" scoped>
@use '@/styles/customForm.scss';
@use '@/styles/customFormDisabled.scss';

:deep(.el-radio-button) {
  .el-radio-button__inner {
    border-radius: 15px;
  }
}
:deep(.el-radio-group) {
  .el-radio-button.is-active {
    .el-radio-button__inner {
      border-left: none;
    }
  }
}
:deep(.el-radio-button:first-child) {
  .el-radio-button__inner {
    // border-left: none;
    border-radius: 15px;
  }
}

.confirm-box {
  position: relative;

  .step-title {
    display: flex;
    align-items: baseline;
    margin: 0 0 10px 0;
    color: var(--el-color-primary);
    font-size: 14px;
  }
  .confirm-info {
    padding: 20px;
    border-radius: 10px;
    background: #f7fbfa;
  }
  .tips {
    color: #aaa;
    font-size: 12px;
  }
  .upload-tips {
    margin-top: 7px;
    width: 100%;
    line-height: 15px;
    // margin: 0 0 -50px 10px;
    color: #9ba6ba;
    font-size: 12px;
    &__hint {
      color: #d67c5a;
    }
  }
  .confirm-remark {
    margin-top: 10px;
    background: #f7f8fb;
    border-radius: 12px;
    text-align: center;
    padding: 20px;
    &__title {
      color: #aaa;
      font-size: 16px;
      margin: 15px 0;
    }
    &__btn {
      margin-top: 20px;
      // :deep(.el-form-item__content) {
      // justify-content: center;
      // }
    }
    // &__info {
    // padding: 0 20px;
    // }
  }
  .btn {
    margin: 12px 0 0;

    &-tip {
      color: var(--el-color-primary);
    }
    &-tip-red {
      color: red;
    }
  }
}
:deep(.show-gif) {
  .el-upload-list__item-thumbnail {
    object-fit: cover;
  }
}
// :deep(.el-upload-list__item-thumbnail) {
//   object-fit: cover;
// }

:deep(.disabled) {
  .el-upload--picture-card {
    display: none;
  }
}
:deep(.hidden) {
  .el-upload--picture-card {
    display: none;
  }
}
</style>
