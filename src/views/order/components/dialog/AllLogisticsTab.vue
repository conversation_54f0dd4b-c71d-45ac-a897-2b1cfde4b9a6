<template>
  <PublicDialog
    ref="DialogRef"
    width="700px"
    :title="title"
    :showFooterButton="false"
    custom-close
    :titleCenter="false"
    align-center
    :close-on-click-modal="true"
  >
    <div class="content-box" v-loading="loading">
      <Empty v-if="!logisticsList?.length" image-type="order" description="暂无数据" :image-size="120" />
      <el-tabs v-else tab-position="left" type="border-card">
        <el-tab-pane v-for="(item, index) in logisticsList" :key="index">
          <template #label>
            <div class="tab-label">
              <div class="flex-start">
                <div class="label">物流单号：</div>
                <div class="text">{{ item.number }}</div>
              </div>
              <div class="flex-start">
                <div class="label">物流状态：</div>
                <div class="text">{{ item.mainStatus }}</div>
              </div>
            </div>
          </template>
          <template #default>
            <div class="tab-content">
              <div class="remark" v-if="item.remark">备注：{{ item.remark }}</div>
              <DotSteps
                :active="-1"
                :steps="item.steps"
                is-border
                direction="vertical"
                width="245px"
                class="logistics-steps"
              >
                <template #title="{ step }">
                  <div class="logistics-step-title">
                    <span>{{ step.title }}</span>
                    <div class="time">
                      {{ step.time }}
                    </div>
                  </div>
                </template>
                <template #description="{ step }">
                  <div class="logistics-step-description">
                    {{ step.description }}
                  </div>
                </template>
              </DotSteps>
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import DotSteps from '@/components/public/steps/DotSteps.vue'
import { ref } from 'vue'

const DialogRef = ref()
const loading = ref(false)

defineProps({
  title: {
    type: String,
    default: '所有物流信息',
  },
})

defineExpose({
  open,
})

const logisticsList = ref<any[]>([])

function open(data: any[]) {
  loading.value = true
  logisticsList.value = data?.length ? data.filter(item => item.steps?.length) : []
  DialogRef.value.open()
  setTimeout(() => {
    loading.value = false
  }, 500)
}
</script>

<style scoped lang="scss">
.content-box {
  height: 535px;

  .tab-label {
    width: 100%;
    text-align: left;
    padding: 10px 0;

    .flex-start {
      width: 200px;
      align-items: flex-start;
    }

    .label {
      flex-shrink: 0;
      width: 70px;
      font-size: 14px;
      font-weight: 400;
      color: var(--text-gray-color);
    }
    .text {
      font-size: 14px;
      flex-shrink: 0;
      width: calc(100% - 60px);
      word-break: break-all;
      white-space: initial;
      text-align: left;
      color: var(--text-gray-color);
    }
  }

  .tab-content {
    // padding-left: 30px;
    height: 500px;
    padding: 15px 0 20px;
    overflow: hidden auto;

    .remark {
      font-size: 14px;
      font-weight: 400;
      color: var(--text-gray-color);
      margin-bottom: 10px;
    }

    .logistics-steps {
      padding-left: 75px;
    }
    .logistics-step-title {
      position: relative;

      span {
        color: var(--text-color);
      }

      .time {
        position: absolute;
        top: 0;
        left: -112px;
        width: 75px;
        color: var(--text-gray-color);
        font-size: 12px;
        text-align: center;
      }
    }
    .logistics-step-description {
      color: var(--text-gray-color);
      font-size: 12px;
    }
  }
}
:deep(.el-tabs) {
  height: 100%;

  &.el-tabs--left {
    .el-tabs__item.is-left {
      padding-left: 10px;
      height: auto;
    }
  }
  .el-tabs__content {
    padding: 0 0 0 15px;
  }
}
</style>
