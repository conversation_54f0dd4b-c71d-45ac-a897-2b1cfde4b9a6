<template>
  <PublicDialog
    ref="DialogRef"
    width="814px"
    :title="title"
    :showFooterButton="false"
    custom-close
    align-center
    :titleCenter="false"
    :close-on-click-modal="true"
    @close="() => formRef.resetFields()"
  >
    <div class="flex-column gap-10 scroll-box confirm-box">
      <div class="info-box product-box" style="background: #f7fbfa">
        <!-- <div class="title">产品信息</div> -->
        <Title>产品信息</Title>
        <div class="flex-start gap-10" style="align-items: flex-start; margin-top: 10px">
          <el-image
            class="product-img"
            :src="orderData?.productPic ? $picUrl + orderData.productPic + '!thumbnail200' : ''"
            @click="orderData?.productPic ? showViewer([orderData?.productPic]) : ''"
            fit="scale-down"
          >
            <template #error>
              <svg class="icon" aria-hidden="true" style="width: 102px; height: 102px">
                <use xlink:href="#icon-zanwutupian"></use>
              </svg>
            </template>
          </el-image>
          <div style="line-height: 30px">
            <div>视频编码：{{ orderData?.videoCode }}</div>
            <div class="flex-start" style="align-items: baseline">
              <span style="flex-shrink: 0">产品名称：</span>
              <div style="line-height: 20px">
                {{ orderData?.productChinese }}
                {{ `（${orderData?.productEnglish}）` }}
              </div>
            </div>
            <div class="flex-start one-ell productLink">
              <span style="flex-shrink: 0">产品链接：</span>
              <el-link
                class="one-ell"
                v-if="orderData?.productLink && http_reg.test(orderData.productLink)"
                :underline="false"
                target="_blank"
                type="primary"
                :href="orderData?.productLink"
              >
                {{ orderData?.productLink }}
              </el-link>
              <span v-else>{{ orderData?.productLink }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="info-box model-box" style="background: #f7f8fb">
        <!-- <div class="title">模特信息</div> -->
        <Title>模特信息</Title>
        <div class="text" v-if="orderData.change">
          <el-icon style="margin: -1px 3px 0 0" size="13"><WarningFilled /></el-icon>
          模特地址有更新，请确认后发货
        </div>
        <CopyButton class="all-copy-btn" :copy-content="handleCopy">
          <el-icon style="margin-right: 5px"><CopyDocument /></el-icon>
          复制全部
        </CopyButton>
        <div class="flex-start gap-10" style="margin-top: 10px">
          <PercentageImg
            style="flex-shrink: 0; margin: 0"
            width="100px"
            :src="orderData?.modelPic"
            radius="8px"
          />
          <div class="model-description">
            <div class="flex-start" v-for="(item, index) in shippingInfo" :key="index">
              <div class="label">{{ item.label }}：</div>
              {{ item.value }}
              <CopyButton v-if="item.value" :copy-content="item.value" link>
                <el-icon><CopyDocument /></el-icon>
              </CopyButton>
            </div>
            <div class="flex-start" v-if="orderData.phone">
              <div class="label">TEL：</div>
              {{ orderData.phone }}
              <CopyButton :copy-content="orderData.phone" link>
                <el-icon><CopyDocument /></el-icon>
              </CopyButton>
            </div>
          </div>
        </div>
      </div>
      <div
        class="info-box model-box"
        style="background: #f7f8fb"
        v-if="orderData?.shippingRemark || orderData?.shippingPics?.length"
      >
        <Title>发货备注</Title>
        <div style="margin-top: 10px">
          <div class="flex-start" style="align-items: baseline">
            <span style="flex-shrink: 0">备注：</span>
            <div class="text-n-all">
              {{ orderData?.shippingRemark || '-' }}
            </div>
          </div>
          <div class="flex-start" v-if="orderData?.shippingPics?.length">
            <span>图片：</span>
            <ViewerImageList
              :data="orderData.shippingPics"
              is-preview-all
              show-download-btn
              :show-delete-btn="false"
              suffix="1x1"
              style="--image-width: 60px; --image-height: 60px"
              :imgItemStyle="{ 'border-radius': '8px', overflow: 'hidden' }"
            />
          </div>
        </div>
      </div>
      <div class="info-box model-box" style="background: #f7f8fb" v-if="orderData?.logisticFlagTime">
        <Title>发货记录</Title>
        <div style="margin-top: 10px">
          <div>标记发货&emsp;{{ orderData?.logisticFlagTime }}</div>
          <div class="flex-start" style="align-items: baseline">
            <span style="flex-shrink: 0">&emsp;&emsp;备注&emsp;</span>
            <div class="text-n-all">
              {{ orderData?.logisticFlagRemark || '-' }}
            </div>
          </div>
        </div>
      </div>
      <el-form ref="formRef" style="width: 92%" :model="form" label-width="auto" @submit.prevent>
        <div class="form-box">
          <!-- <el-form-item label="收件人：" style="margin-bottom: 0;">
            <span>{{ orderData?.recipient }}</span>
          </el-form-item>
          <el-form-item label="收货地址：" style="margin-bottom: 0;">
            <span>{{ orderData?.detailAddress }}</span>
          </el-form-item> -->
          <h3>发货信息</h3>
          <el-form-item label="发货方式">
            <el-radio-group v-model="isFlag">
              <el-radio label="物流发货" :value="false" />
              <el-radio :disabled="flag" :label="flag ? '已标记发货' : '标记发货'" :value="true" />
            </el-radio-group>
          </el-form-item>
          <div class="tip" v-if="first">
            如果您已经创建发货单，但还没有物流单号，支持先标记发货，后续补充物流单号后再确认发货
          </div>
          <el-form-item v-if="!first" label="发货类型">
            <el-radio-group v-model="reissue">
              <el-radio-button label="新增" :value="1" :disabled="reissueDisabled" />
              <el-radio-button label="补发" :value="0" />
            </el-radio-group>
          </el-form-item>
          <template v-for="(item, index) in form.Logistics" :key="index">
            <el-form-item
              v-show="!isFlag"
              label="物流单号"
              :prop="'Logistics.' + index + '.orderNum'"
              :rules="orderNumRules"
            >
              <div class="flex-start form-item">
                <el-input
                  v-model.trim="item.orderNum"
                  placeholder="请输入物流单号"
                  maxlength="100"
                  clearable
                  style="width: 100%"
                  @input="(val: string) => (item.orderNum = val.replace(/\s+/g, ''))"
                />
                <!-- <el-icon
                  v-if="form.Logistics && form.Logistics.length - 1 === index"
                  :size="20"
                  @click="handleAddInput"
                >
                  <CirclePlusFilled />
                </el-icon>
                <el-icon v-if="form.Logistics.length > 1" :size="20" @click="handleDelInput(index)">
                  <Delete />
                </el-icon> -->
              </div>
            </el-form-item>
          </template>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="form.remark"
              placeholder="请输入备注"
              maxlength="150"
              show-word-limit
              :rows="3"
              type="textarea"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </div>
      </el-form>
      <div class="flex-center btn">
        <!-- <el-button class="btn-big" v-if="first" plain round :disabled="flag" @click="onConfirm(0)">
          {{ flag ? '已标记发货' : '标记发货' }}
        </el-button> -->
        <el-button class="btn-big" type="primary" round @click="onConfirm">提交</el-button>
      </div>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import Title from '@/components/public/Title.vue'
import PercentageImg from '@/components/public/image/PercentageImg.vue'
import ViewerImageList from '@/components/public/image/ViewerImageList.vue'
import CopyButton from '@/components/public/button/CopyButton.vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { ref, getCurrentInstance, computed, nextTick } from 'vue'
import { orderShipping, orderShippingFlag, orderShippingInfo } from '@/api/order'
import { english_d_reg, http_reg } from '@/utils/RegExp'
import { useViewer } from '@/hooks/useViewer'
import { addressInfoMap } from '@/views/order/details/data'

const { showViewer } = useViewer()
const { proxy } = getCurrentInstance() as any

const { biz_nation_e } = proxy.useDict('biz_nation_e')

type Reissue = 0 | 1

const DialogRef = ref()
const formRef = ref()
const orderId = ref<string | number>('')
const reissue = ref<Reissue>(1)
const isFlag = ref(false)
const form = ref({
  Logistics: [
    {
      orderNum: '',
    },
  ],
  remark: '',
})
const loading = ref(false)

const shippingInfo = ref<any[]>([])
const orderData = ref<any>({})

const orderNumRules = [
  { required: true, message: '请输入物流单号', trigger: 'blur' },
  { pattern: english_d_reg, message: '请输入字母或数字', trigger: 'change' },
]

const bizNation = computed(() => {
  let n = biz_nation_e.value.find((item: any) => item.value == orderData.value?.nation)
  return n ? n.label : ''
})
function bizNationMap(val: string) {
  let n = biz_nation_e.value.find((item: any) => item.value == val)
  return n ? n.label : ''
}

defineProps({
  title: {
    type: String,
    default: '确认发货',
  },
  flag: {
    type: Boolean,
    default: false,
  },
  first: {
    type: Boolean,
    default: true,
  },
  reissueDisabled: {
    type: Boolean,
    default: false,
  },
})

defineExpose({
  open,
})

const emits = defineEmits(['success'])

function open(id: string | number, r: Reissue = 1) {
  orderId.value = id
  reissue.value = r
  isFlag.value = false
  getOrderDetails()
  DialogRef.value.open()
}

function getOrderDetails() {
  loading.value = true
  orderShippingInfo(orderId.value)
    .then(res => {
      // console.log(res);
      orderData.value = res.data
      handleDetails(res.data)
      // nextTick(() => {
      //   const scrollBox = document.querySelector('.scroll-box')
      //   if (
      //     scrollBox?.lastElementChild &&
      //     (orderData.value.shippingRemark ||
      //       orderData.value.shippingPics?.length ||
      //       orderData.value.logisticFlagTime)
      //   ) {
      //     // scrollBox.scrollTop = scrollBox.scrollHeight
      //     scrollBox.lastElementChild.scrollIntoView({ behavior: 'smooth' })
      //   }
      // })
    })
    .finally(() => (loading.value = false))
}

type NationCode = '1' | '2' | '3' | '4' | '5' | '6' | '7'
function handleDetails(data: any) {
  let n: NationCode = (data.nation + '') as NationCode
  if (n) {
    shippingInfo.value = addressInfoMap[n].map((item: any) => {
      let value = data[item.key]
      if (item.key === 'nation') {
        value = bizNationMap(n)
      }
      return { label: item.label, key: item.key, value }
    })
  }
}

const handleCopy = computed(() => {
  let str = ''
  shippingInfo.value.forEach((item: any) => {
    str += `${item.label}：${item.value}\n`
  })
  if (orderData.value.phone) {
    str += `TEL：${orderData.value.phone}`
  }
  return str
})

function handleAddInput() {
  form.value.Logistics.push({
    orderNum: '',
  })
}
function handleDelInput(i: number) {
  form.value.Logistics.splice(i, 1)
}

function onConfirm() {
  if (!isFlag.value) {
    formRef.value.validate((valid: any) => {
      if (valid) {
        const el_loading = ElLoading.service({
          lock: true,
          text: '正在提交中，请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        orderShipping({
          videoId: orderId.value,
          reissue: reissue.value,
          number: form.value.Logistics[0].orderNum,
          // number: form.value.Logistics.map(item => item.orderNum).filter(Boolean),
          remark: form.value.remark,
          recipient: orderData.value.recipient,
          detailAddress: orderData.value.detailAddress,
          city: orderData.value.city,
          state: orderData.value.state,
          zipcode: orderData.value.zipcode,
          nation: orderData.value.nation,
          phone: orderData.value.phone,
        })
          .then(res => {
            if (res.code === 200) {
              if (res.data) {
                ElMessageBox.alert(res.data, '提示', {
                  confirmButtonText: '好的',
                  callback: () => {
                    getOrderDetails()
                  },
                })
              } else {
                ElMessage.success('操作成功')
                emits('success')
                DialogRef.value.close()
              }
            }
          })
          .finally(() => el_loading.close())
      }
    })
  } else {
    ElMessageBox.confirm('确认标记发货', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
      .then(() => {
        const el_loading = ElLoading.service({
          lock: true,
          text: '正在提交中，请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        orderShippingFlag({
          videoId: orderId.value,
          remark: form.value.remark,
        })
          .then(res => {
            ElMessage.success('操作成功')
            emits('success')
            DialogRef.value.close()
          })
          .finally(() => el_loading.close())
      })
      .catch(() => {})
  }
}
</script>

<style scoped lang="scss">
.scroll-box {
  overflow-y: overlay;
  min-height: 400px;
  max-height: 82vh;
  padding: 0 10px 0 0;
}
.confirm-box {
  flex-direction: column;
  min-height: 230px;

  .info-box {
    position: relative;
    color: #09172f;

    width: 690px;
    font-size: 15px;
    padding: 10px 20px 10px;
    border-radius: 12px;
    // box-shadow: 0px 0px 4px var(--el-color-warning-light-3);

    // .title {
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   padding: 3px 6px;
    //   border-radius: 6px 0 6px;
    //   font-size: 13px;
    //   color: #fff;
    //   background: var(--el-color-warning-light-3);
    // }
    .text {
      display: flex;
      align-items: center;
      position: absolute;
      top: 11px;
      left: 125px;
      padding: 3px 6px;
      border-radius: 6px 0 6px;
      font-size: 13px;
      color: #d67c5a;
    }

    .productLink {
      width: 550px;

      :deep(.el-link) {
        display: inline;

        .el-link__inner {
          display: inline;
        }
      }
    }

    .model-description {
      .label {
        width: 150px;
        line-height: 18px;
      }
    }
  }
  .product-box {
    .product-img {
      margin-top: 10px;
      border-radius: 4px;
      width: 102px;
      height: 102px;
      cursor: pointer;
      flex-shrink: 0;
    }
    // box-shadow: 0px 0px 4px var(--el-color-warning-light-3);
  }
  .model-box {
    // box-shadow: 0px 0px 4px var(--el-color-warning-light-3);

    .all-copy-btn {
      position: absolute;
      bottom: 43%;
      right: 12px;
    }
  }

  .form-box {
    max-height: 500px;
    overflow-y: auto;

    .form-item {
      width: 100%;
      gap: 6px;
      color: var(--el-color-primary);

      .el-icon {
        cursor: pointer;
      }
    }
  }

  .tip {
    font-size: 13px;
    color: var(--el-color-info);
    padding-left: 76px;
    margin-bottom: -10px;
    position: relative;
    top: -20px;
  }
  // .btn {
  //   // margin: 15px 0 20px;
  // }
}
</style>
