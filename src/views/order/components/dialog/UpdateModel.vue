<template>
  <PublicDialog
    ref="DialogRef"
    width="500px"
    title="更换模特"
    :showFooterButton="false"
    custom-close
    align-center
    :close-on-click-modal="true"
    @close="handleClose"
  >
    <div class="flex-center confirm-box">
      <div class="flex-start tips">
        <el-icon color="#b8741a"><WarningFilled /></el-icon>
        <div>
          <div>平台已精心筛选过模特，确保其符合产品特性</div>
          <div>建议保留当前的拍摄模特</div>
        </div>
      </div>
      <div class="input-box">
        <div class="tip">*请告诉我们更换原因<span>（以便后续我们能为您匹配更为合适的模特）</span></div>
        <el-form :model="form">
          <el-form-item label="" label-width="0px" ref="formRef" prop="remark">
            <el-input
              v-model="form.remark"
              type="textarea"
              maxlength="150"
              show-word-limit
              :rows="4"
              placeholder="请输入更换原因"
              @input="remarksChange"
            />
          </el-form-item>
        </el-form>
      </div>
      <div class="flex-center btn">
        <el-button class="btn-big" plain round @click="onConfirm(1)">继续更换</el-button>
        <el-button v-if="isObject" class="btn-big" type="primary" round @click="DialogRef.close()">
          不换了
        </el-button>
        <el-button v-else class="btn-big" type="primary" round @click="onConfirm(0)">
          不换了，去发货
        </el-button>
      </div>
    </div>
  </PublicDialog>
  <SelectSingleModel
    :limit="1"
    ref="SelectSingleModelRef"
    :nation="shootingCountry"
    :modelType="modelType"
    :platform="platform"
    :noIntentionModelBtn="true"
    @success="selectSuccess"
  />
</template>

<script setup lang="ts">
import SelectSingleModel from '@/views/model/components/SelectSingleModel.vue'
import { ref } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { orderChangeModel } from '@/api/order'

const DialogRef = ref()
const SelectSingleModelRef = ref<InstanceType<typeof SelectSingleModel>>()
const orderId = ref<string | number>('')
const shootModels = ref<any[]>([])
const intentionModel = ref<any>({})
const isObject = ref(0)
const shootingCountry = ref('')
const modelType = ref('')
const platform = ref('')

const formRef = ref<any>(null)
const form = ref({
  remark: '',
})

defineExpose({
  open,
})

const emits = defineEmits(['success', 'change'])

function open(row: any) {
  orderId.value = row.id
  shootModels.value = [row.shootModel]
  intentionModel.value = row.intentionModel
  isObject.value = row.isObject
  shootingCountry.value = row.shootingCountry + ''
  modelType.value = row.modelType + ''
  platform.value = row.platform + ''
  DialogRef.value.open()
}

function handleClose() {
  form.value.remark = ''
  formRef.value.validateState = 'success'
}

function remarksChange() {
  formRef.value.validateState = 'success'
}

function onConfirm(y: number) {
  if (y) {
    if (!form.value.remark.trim()) {
      formRef.value.validateMessage = '继续更换需填写更换原因'
      formRef.value.validateState = 'error'
      return
    }
    SelectSingleModelRef.value?.open(shootModels.value, intentionModel.value?.account,'clearSelModel')
  } else {
    DialogRef.value.close()
    emits('change', orderId.value)
  }
}

function selectSuccess(rows: any) {
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在提交中，请稍后',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  orderChangeModel({
    id: orderId.value,
    modelId: rows?.length ? rows[0].id : '',
    reason: form.value.remark,
  })
    .then(res => {
      if (res.data?.cannotModel?.length) {
        SelectSingleModelRef.value?.clearSelectModels()
        ElMessage.warning('模特暂时无法接单，请重新选择')
        return
      }
      ElMessage.success('更换成功')
      emits('success')
      SelectSingleModelRef.value?.close()
      DialogRef.value.close()
    })
    .finally(() => el_loading.close())
}
</script>

<style scoped lang="scss">
.confirm-box {
  flex-direction: column;
  gap: 8px;
  height: 230px;

  .tips {
    position: absolute;
    top: 60px;
    left: 0;
    width: 100%;
    background-color: var(--el-color-warning-light-9);
    padding: 5px 10px;
    box-sizing: border-box;
    align-items: baseline;
    gap: 5px;
    font-size: 15px;
    font-weight: 400;
    color: var(--text-color);

    div {
      div:last-child {
        font-size: 14px;
        color: #b8741a;
      }
    }
  }

  .input-box {
    margin-top: 75px;
    font-size: 15px;
    font-weight: 400;
    color: #7f7f7f;
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;

    .tip {
      margin-bottom: 8px;
      span {
        font-size: 12px;
      }
    }
  }

  .btn {
    margin: 0 0 20px;
  }
}
</style>
