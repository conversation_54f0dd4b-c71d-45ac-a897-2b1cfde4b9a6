<template>
  <PublicDialog
    ref="DialogRef"
    width="450px"
    title=""
    :showFooterButton="false"
    custom-close
    align-center
    :close-on-click-modal="true"
  >
    <div class="flex-center confirm-box">
      <h2>{{ isCancel ? '确认取消上传？' : '确认可以上传？' }}</h2>
      <span v-if="!isCancel" class="tip">确认后，运营将会把视频上传至平台展示</span>
      <div class="flex-center btn">
        <el-button class="btn-big" plain round @click="DialogRef.close()">取消</el-button>
        <el-button class="btn-big" type="primary" round @click="onConfirm">确认</el-button>
      </div>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { allowUpload } from '@/api/order'
import { ElLoading, ElMessage } from 'element-plus'

const DialogRef = ref()
const isCancel = ref(false)
const upId = ref<string | number>('')

defineExpose({
  open
})

const emits = defineEmits(['success'])

function open(id: string | number, iscancel: boolean) {
  isCancel.value = iscancel
  upId.value = id
  DialogRef.value.open()
}

function onConfirm() {
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在执行中，请稍后',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  allowUpload({id: upId.value}).then(res => {
    ElMessage.success('操作成功')
    emits('success')
    DialogRef.value.close()
  }).finally(() => el_loading.close())
}

</script>

<style scoped lang="scss">
.confirm-box {
  flex-direction: column;
  gap: 20px;
  height: 230px;

  .tip {
    font-size: 16px;
    color: var(--el-color-warning);
  }
  .btn {
    margin: 24px 0 20px;
  }
}
</style>