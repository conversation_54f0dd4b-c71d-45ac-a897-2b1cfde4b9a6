<template>
  <PublicDialog ref="DialogRef" width="800px" custom-close align-center center :close-on-click-modal="true">
    <div class="container" v-loading="loading">
      <div class="header">
        <h1>{{ info.company }}</h1>
        <p>(订单生成凭据)</p>
      </div>
      <div class="content">
        <h3>
          交款单位：
          <span style="font-weight: 500">{{ info.unit }}</span>
        </h3>
        <table>
          <tr>
            <th>账单日期</th>
            <th>项目名称</th>
            <th>金额</th>
          </tr>
          <tr>
            <td>{{ info.date }}</td>
            <td>{{ info.name }}</td>
            <td>¥{{ info.amount }}</td>
          </tr>
        </table>
      </div>
      <div class="content">
        <h3>交易信息:</h3>
        <div class="transaction-info">
          <p>收款公司名称：{{ info.receiptCompany }}</p>
          <p class="unit">
            <span>开户行名称：{{ info.openingBank }}</span>
            <span>收款单位（章）</span>
            <!-- <img src="/src/assets/image/signet.png" alt=""> -->
          </p>
          <p>收款银行账号：{{ info.proceedsAccount }}</p>
        </div>
      </div>
    </div>
    <template #footerButton>
      <DownloadButton
        class="btn-big"
        round
        type="primary"
        :disabled="loading"
        text="下载凭据"
        loadingText="下载中"
        fileName="订单凭据"
        url="/order/order/get-document-pdf"
        :params="{
          orderNum: orderId
        }"
      />
    </template>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getDocumentInfo } from '@/api/order'
import DownloadButton from '@/components/public/button/DownloadButton.vue'

defineExpose({
  open,
  close,
})

const DialogRef = ref()
const orderId = ref<number | string>('')
const loading = ref(false)
const info = ref<{
  amount: number,
  company: string,
  date: string,
  name: string,
  openingBank: string,
  proceedsAccount: string,
  receiptCompany: string,
  unit: string
}>({
  amount: 0,
  company: '',
  date: '',
  name: '',
  openingBank: '',
  proceedsAccount: '',
  receiptCompany: '',
  unit: ''
})

function open(orderNum: number | string) {
  orderId.value = orderNum
  DialogRef.value.open()
  getInfo()
}
function close() {
  DialogRef.value.close()
}

function getInfo() {
  loading.value = true
  getDocumentInfo({orderNum: orderId.value}).then(res => {
    info.value = res.data
  })
  .catch(() => close())
  .finally(() => loading.value = false)
}
</script>

<style scoped lang="scss">
.container {
  // width: 100%;
  margin: 0 auto;
  padding: 20px;
  // border: 1px solid #ccc;
  color: var(--text-color);
}
.header {
  text-align: center;
  margin-bottom: 20px;

  h1 {
    margin: 0;
    font-size: 24px;
  }

  p {
    margin: 0;
    color: #888;
  }
}

.content {
  margin-bottom: 20px;

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th,
  td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
  }
  th {
    background-color: #f4f4f4;
  }
}

.transaction-info {
  p {
    margin-left: 20px;
    margin-right: 20px;
  }

  .unit {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: baseline;

    img {
      width: 140px;
      height: 140px;
      position: absolute;
      right: -12px;
      top: -60px;
    }
  }
}
</style>
