<template>
  <PublicDialog
    ref="DialogRef"
    width="700px"
    title="吐槽一下"
    :showFooterButton="false"
    custom-close
    align-center
    :titleCenter="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="content-box">
      <el-form :model="form" :rules="rules" ref="formRef" label-width="auto">
        <!-- <el-divider content-position="center">本次订单体验如何? 来吐槽一下~</el-divider> -->
        <Title v-if="videoId">本次订单体验如何? 来吐槽一下</Title>
        <div class="confirm-remark__btn">
          <el-form-item prop="object" label="吐槽对象">
            <el-radio-group v-model="form.object" @change="roastObjectChange" :disabled="loading">
              <el-radio-button
                v-for="item in [
                  { label: '视频', value: 1 },
                  { label: '客服', value: 2 },
                  { label: '其他', value: 3 },
                ]"
                :key="item.value"
                :value="item.value"
                :label="item.value"
                @click="roastObjectClick(item.value)"
              >
                {{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="confirm-remark__info">
          <el-form-item label="吐槽内容">
            <el-input
              type="textarea"
              placeholder="请输入吐槽内容"
              :rows="5"
              v-model="form.content"
              :disabled="loading"
              :maxlength="300"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div class="flex-center btn">
        <el-button class="btn-big" type="primary" round @click="submit" :loading="loading">提交</el-button>
      </div>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import Title from '@/components/public/Title.vue'
import { ref } from 'vue'
import { doRoast, systemRoast } from '@/api/order'
import { ElMessage } from 'element-plus'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['success'])

const DialogRef = ref()
const videoId = ref('')
const formRef = ref()
const form = ref({
  object: '',
  content: '',
})
const loading = ref(false)
const rules = {
  object: [
    { required: true, message: '请选择吐槽对象', trigger: 'change' },
  ],
}

function open(id?: any) {
  videoId.value = id || ''
  DialogRef.value.open()
}

function close() {
  DialogRef.value.close()
}
function handleClose() {
  form.value = {
    object: '',
    content: '',
  }
  videoId.value = ''
  loading.value = false
  formRef.value.resetFields()
}

let clearRoastObject = false
function roastObjectClick(value: any) {
  clearRoastObject = false
  if (form.value.object == value) {
    clearRoastObject = true
    form.value.object = ''
  }
}

function roastObjectChange() {
  if (clearRoastObject) {
    form.value.object = ''
  }
}

function submit() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      loading.value = true
      if (videoId.value) {
        doRoast({
          content: form.value.content || null,
          object: form.value.object || null,
          videoId: videoId.value,
        }).then(res => {
          ElMessage.success('提交成功')
          close()
          emits('success')
        }).finally(() => loading.value = false)
      } else {
        systemRoast({
          content: form.value.content || null,
          object: form.value.object || null,
        }).then(res => {
          ElMessage.success('提交成功')
          close()
        }).finally(() => loading.value = false)
      }
    }
  })
}

</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';

.content-box {
  position: relative;

  .confirm-remark {
    margin-top: 10px;
    // background: #f7f8fb;
    border-radius: 12px;
    text-align: center;
    padding: 20px;

    &__title {
      color: #aaa;
      font-size: 16px;
      margin: 15px 0;
    }
    &__btn {
      margin-top: 20px;
    }

  }
  .btn {
    margin: 24px 0 0;
  }
}
</style>
