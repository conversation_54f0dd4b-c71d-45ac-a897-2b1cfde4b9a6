<template>
  <div class="order-info-box">
    <div class="flex-between head-box">
      <div class="flex-around left-box">
        <label class="flex-start gap-10">
          <el-checkbox v-if="isCheckbox" :value="data.orderNum" label="" />
          <div class="head-label">
            <span>订单号：</span>
            {{ data.orderNum }}
          </div>
        </label>
        <div class="head-label">
          <span>下单时间：</span>
          {{ data.orderTime }}
        </div>
        <div class="head-label" v-if="data.auditTime && handleShowAuditTime()">
          <span>审核时间：</span>
          {{ data.auditTime }}
        </div>
        <div class="head-label" v-else-if="data.payTime">
          <span>支付时间：</span>
          {{ data.payTime }}
        </div>
      </div>
      <!-- && (store.isOwnerAcc() || checkCompanyUserIds) -->
      <div class="flex-start" v-if="headActionIds && data.isShowReopenOrder == 0">
        <div v-if="data.orderTime && !data.mergeId" style="margin-right: 8px">
          订单将在
          <CountDown :endTime="handleOrderTime(data.orderTimeSign)" showDays @end="handleTimeEnd" />
          后自动关闭
        </div>
        <div class="head-label" style="margin-right: 8px">
          <span>订单总价:</span>
          ￥{{ data.defaultExchangeRate ? '-' : toFixed2(data.payAmount) }}
        </div>
        <div class="head-label" style="margin-right: 8px" v-if="data.mergeId">合并支付中</div>
        <!-- <DownloadButton
          v-if="data.payType"
          link
          type="primary"
          size="large"
          text="下载凭证单据"
          loadingText="下载中"
          :beforeConfirm="false"
          fileName="订单凭据"
          url="/order/order/get-document-pdf"
          :params="{
            orderNum: data.orderNum
          }"
        /> -->
        <el-button
          v-if="!headActionDisabled && !data.mergeId && (store.isOwnerAcc() || checkCompanyUserIds)"
          round
          size="small"
          plain
          color="var(--text-gray-color)"
          class="head-cancel-btn"
          @click="handleHeadAction('取消订单')"
        >
          取消订单
        </el-button>
        <!-- 当前时间 小于 订单首次关闭时间 + 30天 -->
        <!-- <el-button
          v-else-if="
            data.orderTime == data.orderTimeSign &&
            new Date().getTime() < handleOrderTime(data.orderTime) + 30 * 24 * 60 * 60 * 1000
          "
          type="primary"
          round
          size="small"
          style="width: 72px"
          @click="handleHeadAction('开启订单')"
        >
          开启订单
        </el-button> -->
        <el-button
          v-if="!data.defaultExchangeRate && !headActionDisabled"
          type="warning"
          round
          size="small"
          :style="{
            width: data.mergeId ? '95px' : '72px',
          }"
          @click="handleHeadAction('去支付')"
        >
          {{ data.mergeId ? '支付合并订单' : '去支付' }}
        </el-button>
        <el-tooltip v-else :visible="true" placement="top" effect="custom-danger" append-to=".pages">
          <template #content>
            <div class="flex-start gap-5" style="color: red">
              <el-icon><WarnTriangleFilled /></el-icon>
              汇率获取失败，请联系客服
            </div>
          </template>
          <el-button
            type="warning"
            round
            size="small"
            :disabled="headActionDisabled"
            @click="handleHeadAction('去支付')"
          >
            去支付
          </el-button>
        </el-tooltip>
      </div>
      <div class="flex-start gap-10 head-label" v-else-if="data.payType">
        <div class="head-label" style="margin-right: 8px">
          <span>订单总价:</span>
          ￥{{ data.defaultExchangeRate ? '-' : toFixed2(data.payAmount) }}
        </div>
        <el-button
          v-if="data?.orderInvoice?.status === 3"
          round
          size="small"
          type="primary"
          @click="handleDownloadInvoice(data.orderInvoice.id)"
        >
          下载发票
        </el-button>
        <span v-if="data.payTime">
          {{ payType(data.payType) }}
          {{ data.payType == 7 ? '- ' + payMoneyTypeStatus(data.payTypeDetail) : '' }}
        </span>
      </div>
      <div class="flex-start gap-10 head-label" v-else>
        <div class="head-label">
          <span>订单总价:</span>
          ￥{{ data.defaultExchangeRate ? '-' : toFixed2(data.payAmount) }}
        </div>
        <!-- <el-button
          v-if="data.isShowReopenOrder"
          type="primary"
          round
          size="small"
          style="width: 72px"
          @click="handleHeadAction('开启订单')"
        >
          开启订单
        </el-button> -->
      </div>
    </div>
    <div class="table-box">
      <el-table
        ref="tableRef"
        class="custom-table-border radius-bottom"
        :data="tableData"
        style="width: 100%"
        :header-cell-style="{
          'background-color': '#F6F6F6',
          color: 'var(--text-color)',
          'font-size': '13px',
        }"
        border
        default-expand-all
        row-key="id"
        :expand-row-keys="expandRowKeys"
      >
        <template #empty>
          <Empty image-type="order" description="暂无数据" :image-size="120" />
        </template>

        <!-- 物流 -->
        <LogisticInfo @action="handleAction" />
        <!-- 产品图 -->
        <ProductPic />
        <!-- 产品信息 -->
        <ProductInfo />
        <!-- 照片数量 -->
        <PicCount />

        <el-table-column prop="user" label="订单运营" align="center" width="110">
          <template v-slot="{ row }">
            <div v-if="row.createOrderUserName">{{ row.createOrderUserName }}</div>
            <div v-else-if="row.createOrderUserNickName">{{ row.createOrderUserNickName }}</div>
            <div v-else>-</div>
          </template>
        </el-table-column>

        <IntentionModelInfo @hover="handleModelHover">
          <template #header>
            <div class="flex-center gap-5">
              意向模特
              <el-icon
                color="rgb(155, 166, 186)"
                style="margin: 1px 0 0 -2px"
                @mouseenter="showTooltips($event, 'order-list-intentionModel')"
              >
                <QuestionFilled />
              </el-icon>
            </div>
          </template>
        </IntentionModelInfo>

        <ShootModelInfo @hover="handleModelHover">
          <template #header>
            <div class="flex-center gap-5">
              <span>拍摄模特</span>
              <el-icon
                color="rgb(155, 166, 186)"
                style="margin: 1px 0 0 -2px"
                @mouseenter="showTooltips($event, 'order-list-shootModel')"
              >
                <QuestionFilled />
              </el-icon>
            </div>
          </template>
        </ShootModelInfo>

        <el-table-column prop="status" label="订单状态" align="center" width="120">
          <template v-slot="{ row }">
            <el-tag v-if="row.hasNewFeedBack" type="danger" round size="small">有新素材更新</el-tag>
            <el-tag
              v-if="row.orderVideoRefund && row.orderVideoRefund.refundStatus == REFUND_STATUS['退款成功']"
              type="danger"
              round
              size="small"
            >
              退款成功
            </el-tag>
            <div>
              {{ orderStatus(row.status) }}
            </div>
            <div class="status-btn-box">
              <el-button link type="primary" @click="handleAction('查看详情', row)">订单详情</el-button>
              <!-- <template v-if="row.status === ORDER_STATUS['需支付']">
                <el-button link type="primary" @click="handleAction('修改订单', row)">修改订单</el-button>
              </template> -->
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" align="center" width="150">
          <template v-slot="{ row }">
            <div
              class="action-box"
              v-if="store.isOwnerAcc() || store.userInfo.account == row.createOrderUserAccount"
            >
              <template v-if="row.status === ORDER_STATUS['需支付']">
                <!-- <el-button link type="primary" @click="handleAction('取消订单', row)">取消订单</el-button> -->
                <el-button
                  link
                  type="primary"
                  :disabled="headActionDisabled"
                  @click="handleAction('修改订单', row)"
                >
                  修改订单
                </el-button>
              </template>
              <!-- <template v-else-if="row.status === ORDER_STATUS['待上传凭证']">
                <el-button link type="primary" @click="handleAction('上传凭证', row)">上传凭证</el-button>
                <el-button link type="primary" @click="handleAction('取消订单', row)">取消订单</el-button>
              </template> -->
              <!-- <template v-else-if="row.status === ORDER_STATUS['待审核']"> -->
              <!-- <el-button link type="primary" @click="handleAction('重传凭证', row)">重传凭证</el-button> -->
              <!-- <el-button link type="primary" @click="handleAction('取消订单', row)">取消订单</el-button> -->
              <!-- </template> -->
              <template
                v-else-if="
                  row.status === ORDER_STATUS['待匹配 '] ||
                  row.status === ORDER_STATUS['待匹配'] ||
                  row.status === ORDER_STATUS['待审核']
                "
              >
                <!-- <el-button
                  v-if="row.unConfirmTime && checkTime(row.unConfirmTime)"
                  :disabled="isRefund(row.orderVideoRefund)"
                  link
                  type="primary"
                  @click="handleAction('申请退款', row)"
                >
                  申请退款
                </el-button> -->
                <el-button
                  v-if="row.hasCase"
                  type="primary"
                  link
                  class="hint-btn"
                  @click="handleAction('匹配情况反馈', row)"
                >
                  匹配情况反馈
                </el-button>
                <!-- <el-button v-if="row.isObject === 1 && row.shootModel?.account" round icon="CircleCheckFilled" type="primary" @click="handleAction('确认模特', row)">确认模特</el-button> -->
                <!-- <el-button v-if="row.status === ORDER_STATUS['待匹配'] && row.unMatchFlag === 1 && row.shootModel?.account" link type="primary" @click="handleAction('更换模特', row)">更换模特</el-button> -->
              </template>
              <template v-else-if="row.status === ORDER_STATUS['需发货']">
                <el-button
                  v-if="row.isObject === 0 && row.logisticFlag"
                  type="primary"
                  link
                  @click="handleAction('发货', row)"
                >
                  填入物流单号
                </el-button>
                <el-button
                  v-if="row.isObject === 0 && !row.logisticFlag"
                  type="primary"
                  link
                  @click="handleAction('发货', row)"
                >
                  去发货
                </el-button>
                <el-button
                  v-if="row.isObject === 1 && row.shootModel?.account"
                  round
                  icon="CircleCheckFilled"
                  type="primary"
                  @click="handleAction(row.platform == 3 ? '确认模特2' : '确认模特', row)"
                >
                  确认模特
                </el-button>
                <el-button
                  v-if="
                    row.shootModel.account != row.intentionModel?.account ||
                    (!row.shootModel && !row.intentionModel)
                  "
                  link
                  type="primary"
                  @click="handleAction('更换模特', row)"
                >
                  更换模特
                </el-button>
              </template>
              <template v-else-if="row.status === ORDER_STATUS['需确认']">
                <!-- <el-button link type="primary" @click="handleAction('验收素材', row)">查看素材</el-button> -->
              </template>
              <template v-else-if="row.status === ORDER_STATUS['待完成']">
                <el-button
                  v-if="isShowReminder(row.unFinishedTime)"
                  link
                  type="primary"
                  @click="handleAction('催一催', row)"
                >
                  催一催
                </el-button>
                <!--                <el-button :class="[`add-cart-btn-${row.id}`]" link type="primary" @click="againAddCart(row)">-->
                <!--                  重新加入购物车-->
                <!--                </el-button>-->
              </template>
              <!-- <template v-else-if="row.status === ORDER_STATUS['已完成']">
                <el-button link type="primary" v-if="row.allowUpload === 0 && !row.uploadPlatformUrl" @click="handleAction('取消上传', row)">取消上传</el-button>
                <el-button link type="primary" v-else-if="row.allowUpload === 1" @click="handleAction('可以上传', row)">可以上传</el-button>
                <el-button link type="primary" v-if="row.uploadPlatformUrl" @click="handleAction('查看链接', row)">查看链接</el-button>
              </template> -->
              <template v-else-if="row.status === ORDER_STATUS['交易关闭']">
                <!--                <el-button :class="[`add-cart-btn-${row.id}`]" link type="primary" @click="againAddCart(row)">-->
                <!--                  重新加入购物车-->
                <!--                </el-button>-->
              </template>
              <template v-else-if="row.status === ORDER_STATUS['已完成']">
                <el-button
                  v-if="row.isRoast == false"
                  link
                  type="primary"
                  @click="handleAction('吐槽一下', row)"
                >
                  吐槽一下
                </el-button>
                <el-button
                  link
                  type="primary"
                  v-if="row.uploadStatus == 0 && row.uploadLink"
                  @click="openWin(row.uploadLink)"
                >
                  视频已上传
                </el-button>
                <el-button
                  link
                  type="primary"
                  v-if="row.uploadStatus == 1"
                  @click="handleAction('验收素材', row)"
                >
                  视频上传中...
                </el-button>
                <!--                <el-button :class="[`add-cart-btn-${row.id}`]" link type="primary" @click="againAddCart(row)">-->
                <!--                  重新加入购物车-->
                <!--                </el-button>-->
              </template>
              <!-- <template v-if="row.isUpload">
                <div style="color: #6eb92b;font-size: 12px;">{{ row.isUpload == 1? '视频上传中...': '视频已上传' }}</div>
              </template> -->
              <!-- <el-button link type="primary" @click="handleAction('查看详情', row)">查看详情</el-button> -->
            </div>
            <div class="action-box" v-else-if="row.status === ORDER_STATUS['需发货']">
              <el-button
                v-if="row.isObject === 0 && row.logisticFlag"
                type="primary"
                link
                @click="handleAction('发货', row)"
              >
                填入物流单号
              </el-button>
              <el-button
                v-if="row.isObject === 0 && !row.logisticFlag"
                type="primary"
                link
                @click="handleAction('发货', row)"
              >
                去发货
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
// import PercentageImg from '@/components/public/image/PercentageImg.vue'
// import DownloadButton from '@/components/public/button/DownloadButton.vue'
// import CopyButton from '@/components/public/button/CopyButton.vue'
import CountDown from '@/components/public/CountDown.vue'

import LogisticInfo from '@/views/order/components/tableColumn/LogisticInfo.vue'
import ProductPic from '@/views/order/components/tableColumn/ProductPic.vue'
import ProductInfo from '@/views/order/components/tableColumn/ProductInfo.vue'
import PicCount from '@/views/order/components/tableColumn/PicCount.vue'
import IntentionModelInfo from '@/views/order/components/tableColumn/IntentionModelInfo.vue'
import ShootModelInfo from '@/views/order/components/tableColumn/ShootModelInfo.vue'

import { type PropType, computed, getCurrentInstance, ref } from 'vue'
import { PAY_TYPE, ORDER_STATUS, orderStatus, REFUND_STATUS, payType, payMoneyTypeStatus } from '@/utils/order'
import { picCountOptions } from '@/utils/data'
import { useViewer } from '@/hooks/useViewer'
import { rejoinCart } from '@/api/order'
import { viewInvoice } from '@/api/vip'
import { ElMessage, ElLoading } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { useRouter } from 'vue-router'
import { useTooltips } from '@/hooks/useTooltips'
import { addToCartAnimation } from '@/hooks/addToCartAnimation'

const { proxy } = getCurrentInstance() as any

const { showViewer } = useViewer()

const { showTooltips } = useTooltips()

const router = useRouter()

const store = useUserStore()

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  isCheckbox: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['action', 'headAction', 'hover'])

const tableData = computed(() => {
  return props.data.orderVideoVOS || []
})
const headActionIds = computed(() => {
  return props.data.orderVideoVOS?.every((item: any) => item.status === ORDER_STATUS['需支付'])
})
const headActionDisabled = ref(false)

const checkCompanyUserIds = computed(() => {
  return props.data.checkCompanyUserIds?.includes(store.userInfo.id)
})

const expandRowKeys = computed(() => {
  let arr: any[] = []
  if (props.data.orderVideoVOS) {
    props.data.orderVideoVOS.forEach((item: any) => {
      if (item.status === ORDER_STATUS['待完成'] && item.logisticInfo) {
        arr.push(item.id)
      }
      if (item.status === ORDER_STATUS['需确认']) {
        arr.push(item.id)
      }
    })
  }
  return arr
})

function isRefund(orderVideoRefund: any) {
  return (
    orderVideoRefund != null &&
    (orderVideoRefund.refundStatus == 0 ||
      orderVideoRefund.refundStatus == 1 ||
      orderVideoRefund.refundStatus == 4)
  )
}

function handleShowAuditTime() {
  if (props.data.payType) {
    if ([
      PAY_TYPE['银行卡转账'],
      PAY_TYPE['对公转账'],
      PAY_TYPE['全币种支付'],
      PAY_TYPE['银行卡转账'] + PAY_TYPE['余额支付'],
      PAY_TYPE['对公转账'] + PAY_TYPE['余额支付'],
      PAY_TYPE['全币种支付'] + PAY_TYPE['余额支付'],
    ].includes(props.data.payType)) {
      return true
    }
  }
  return false
}

const isShowReminder = (() => {
  return import.meta.env.VITE_APP_ENV === 'production'
    ? (time: string) => {
        let unTime = new Date(time).getTime() + 1000 * 60 * 60 * 24 * 10 // 10天
        return new Date().getTime() > unTime
      }
    : (time: string) => {
        let unTime = new Date(time).getTime() + 1000 * 60 * 60 * 1 // 1小时
        return new Date().getTime() > unTime
      }
})()

function handleOrderTime(time: string) {
  let date = time.split(' ')[0] + ' 00:00:00'
  return new Date(date).getTime() + 1000 * 60 * 60 * 24 * 31 - 1000 // 加30天 到最后一天 23:59:59
}

function toFixed2(val: any) {
  if (val) {
    return val.toFixed(2)
  }
  return val == 0 ? 0 : '-'
}

function handleTimeEnd() {
  headActionDisabled.value = true
}

function openWin(url: string) {
  if (url) window.open(url)
}

function handleSelectiveAssembly(val: string | number) {
  let str = picCountOptions.find(item => item.value == val)
  return str ? str.label.substring(0, 2) : '-'
}

// const checkTime = (() => {
//   return import.meta.env.VITE_APP_ENV === 'production'
//     ? (time: string) => {
//         let unTime = new Date(time).getTime() + 1000 * 60 * 60 * 24 * 15 // 15天
//         return new Date().getTime() > unTime
//       }
//     : (time: string) => {
//         let unTime = new Date(time).getTime() + 1000 * 60 * 60 * 1 // 1小时
//         return new Date().getTime() > unTime
//       }
// })()

function handleViewer(arr: any[]) {
  let urls = arr.map(item => item)
  showViewer(urls)
}

// 重新加入购物车
function againAddCart(row: any) {
  // let orderParams = {
  //   platform: row.platform + '',
  //   videoFormat: row.videoFormat,
  //   shootingCountry: row.shootingCountry + '',
  //   modelType: row.modelType + '',
  //   productChinese: row.productChinese,
  //   productEnglish: row.productEnglish,
  //   productLink: row.productLink || undefined,
  //   productPic: !row.productLink ? row.productPic : undefined,
  //   shootCount: 1,
  //   intentionModelIds: row.intentionModel ? [row.intentionModel.id] : [],
  //   shootRequired: Array.isArray(row.shootRequired)
  //     ? row.shootRequired.map((item: any) => ({
  //         content: item.content,
  //         type: item.type,
  //         sort: item.sort,
  //       }))
  //     : [],
  //   referenceVideoLink: row.referenceVideoLink,
  //   picCount: row.picCount,
  //   referencePic: row.referencePic || [],
  // }
  // console.log(orderParams)
  store.realTimeCheckVip().then(() => {
    if (store.isVip()) {
      const el_loading = ElLoading.service({
        lock: true,
        text: '添加中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      rejoinCart({ videoId: row.id })
        .then(() => {
          ElMessage.success('添加购物车成功！')
          store.getShoppingCartCount()
          store.getShoppingCartList()
          addToCartAnimation(`.add-cart-btn-${row.id}`)
        })
        .finally(() => el_loading.close())
    } else {
      ElMessage.error('请开通会员后再进行下单')
    }
  })

  // createOrderAddCart(orderParams)
  //   .then(() => {
  //     ElMessage.success('添加购物车成功！')
  //     store.getShoppingCartCount()
  //     store.getShoppingCartList()
  //   })
  //   .finally(() => el_loading.close())
}

function handleDownloadInvoice(id: any) {
  if (!id) {
    return
  }
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在下载中',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  viewInvoice(id)
    .then(res => {
      if (typeof res.data === 'string') {
        fetch(proxy.$picUrl + res.data).then(async response => {
          const blob = await response.blob()
          const a = document.createElement('a')
          a.href = URL.createObjectURL(blob)
          a.download = res.data.substring(res.data.lastIndexOf('/') + 1)
          a.click()
        })
      }
    })
    .finally(() => el_loading.close())
}

function handleAction(btn: string, row: any) {
  emits('action', btn, row)
}

function handleHeadAction(btn: string) {
  if (btn === '去支付') {
    store.realTimeCheckVip().then(() => {
      if (store.isVip()) {
        router.push({
          name: 'order-pay',
          state: {
            orderNum: !props.data.mergeId ? tableData.value[0].orderNum : '',
            payType: props.data.payType,
            mergeId: props.data.mergeId || undefined,
          },
        })
      } else {
        ElMessage.error('请先开通会员！')
      }
    })
    return
  }
  emits('headAction', btn, tableData.value)
}

function handleModelHover(el: EventTarget, id: any, show: any) {
  emits('hover', el, id, show)
}
</script>

<style scoped lang="scss">
.hint-btn {
  &::after {
    content: '';
    display: inline-block;
    width: 7px;
    height: 7px;
    margin-left: 4px;
    border-radius: 50%;
    background-color: #f73827;
  }
}
.order-info-box {
  margin-bottom: 10px;

  .head-box {
    font-size: 14px;
    padding: 0 15px;
    height: 40px;
    background: var(--table-head-bg);
    color: var(--el-text-color-regular);
    border-radius: var(--table-border-radius) var(--table-border-radius) 0 0;

    .el-button {
      font-weight: 500;
    }

    .left-box {
      gap: 10px;

      :deep(.el-checkbox) {
        .el-checkbox__label {
          padding-left: 0;
        }
      }
    }

    .head-label {
      span {
        font-weight: bold;
      }
    }

    .head-cancel-btn {
      width: 72px;

      &:hover {
        background-color: #f0f1f3;
        border-color: var(--text-color);
        color: var(--text-color);
      }
    }
  }
  .el-button {
    font-weight: bold;
  }
  .table-box {
    :deep(.el-table) {
      color: var(--text-gray-color);

      &.custom-table-border.el-table--border .el-table__cell:first-child {
        border-right: 0;
      }

      td {
        &.el-table__cell {
          &.intention-model-column,
          &.shoot-model-column {
            padding: 0 0 12px 0;
            overflow: hidden;

            .cell {
              overflow: visible;
            }
          }
        }
      }

      th {
        text-align: center;
      }
      .expand-column {
        padding: 0;
        width: 0px !important;

        .cell {
          width: 0;
          padding: 0;

          .el-table__expand-icon {
            display: none;
          }
        }
      }
    }
    .img-error {
      width: 100px;
      height: 100px;
      flex-shrink: 0;
      font-size: 12px;
      background-color: #f5f7fa;
      margin-top: 0;
      text-align: center;
      line-height: 100px;
      color: var(--text-gray-color);
    }

    .action-box {
      .el-button.is-round {
        padding: 2px 8px;
        height: 22px;
        font-weight: 500;
      }
      .el-button + .el-button {
        margin-left: 0;
      }
    }
  }

  .status-btn-box {
    .el-button + .el-button {
      margin-left: 0;
    }
  }
}
// 解决 ResizeObserver loop limit exceeded 问题
:deep(.el-table__header) {
  width: 100% !important;
}
:deep(.el-table__body) {
  width: 100% !important;
}
</style>
