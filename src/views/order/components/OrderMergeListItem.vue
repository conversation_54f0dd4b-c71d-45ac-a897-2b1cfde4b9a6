<template>
  <div class="order-merge-list-item">
    <div class="flex-between merge-info-head">
      <div class="flex-around gap-10">
        <div class="head-label">
          <span>合并人：</span>
          {{ dataList.mergeBy || dataList.mergeNickBy || '' }}
        </div>
        <div class="head-label">
          <span>合并时间：</span>
          {{ dataList.mergeTime }}
        </div>
      </div>
      <!-- v-if="store.isOwnerAcc() || checkCompanyUserIds" -->
      <div class="flex-start">
        <div v-if="dataList.mergeTime" style="margin-right: 8px">
          订单将在
          <CountDown :endTime="handleOrderTime(dataList.mergeTime)" showDays @end="handleTimeEnd" />
          后自动关闭
        </div>
        <div class="head-label" style="margin-right: 8px">
          <span>合计总价:</span>
          ￥{{ defaultExchangeRate ? '-' : toFixed2(dataList.mergePayAmount) }}
        </div>
        <el-button
          v-if="!headActionDisabled && (store.isOwnerAcc() || store.userInfo.id === dataList.mergeById)"
          round
          size="small"
          plain
          color="var(--text-gray-color)"
          class="cancel-btn"
          @click="handleHeadAction('取消合并')"
        >
          取消合并
        </el-button>
        <el-button
          v-if="!defaultExchangeRate && !headActionDisabled"
          type="warning"
          round
          size="small"
          style="width: 72px"
          @click="handleHeadAction('去支付')"
        >
          去支付
        </el-button>
        <el-tooltip v-else :visible="true" placement="top" effect="custom-danger" append-to=".pages">
          <template #content>
            <div class="flex-start gap-5" style="color: red">
              <el-icon><WarnTriangleFilled /></el-icon>
              汇率获取失败，请联系客服
            </div>
          </template>
          <el-button
            type="warning"
            round
            size="small"
            :disabled="headActionDisabled"
            @click="handleHeadAction('去支付')"
          >
            去支付
          </el-button>
        </el-tooltip>
      </div>
    </div>
    <el-collapse class="collapse-box" v-model="activeCollapse">
      <template v-for="(data, index) in list" :key="index">
        <el-collapse-item :name="index + ''">
          <template #title>
            <div class="flex-start gap-10" style="padding-left: 10px; border-bottom: none">
              <div class="head-label">
                <span>订单号：</span>
                {{ data.orderNum }}
              </div>
              <div class="head-label">
                <span>下单时间：</span>
                {{ data.orderTime }}
              </div>
            </div>
          </template>
          <template #icon="{ isActive }">
            <div class="flex-end gap-5">
              <div class="head-label" style="margin-right: 8px">
                <span>订单总价:</span>
                ￥{{ defaultExchangeRate ? '-' : toFixed2(data.payAmount) }}
              </div>
              <el-icon
                :style="{
                  transform: `rotate(${isActive ? '180deg' : '0deg'})`,
                }"
                style="transition: 0.3s"
              >
                <ArrowDownBold />
              </el-icon>
            </div>
          </template>
          <div class="table-box">
            <el-table
              ref="tableRef"
              class="merge-table-box"
              :data="data.orderVideoVOS"
              style="width: 100%"
              :header-cell-style="{
                'background-color': '#fff',
                color: 'var(--text-color)',
                'font-size': '13px',
              }"
              border
              default-expand-all
              row-key="id"
            >
              <template #empty>
                <Empty image-type="order" description="暂无数据" :image-size="120" />
              </template>

              <!-- 产品图 -->
              <ProductPic />
              <!-- 产品信息 -->
              <ProductInfo />
              <!-- 照片数量 -->
              <PicCount />

              <el-table-column prop="user" label="订单运营" align="center" width="110">
                <template v-slot="{ row }">
                  <div v-if="row.createOrderUserName">{{ row.createOrderUserName }}</div>
                  <div v-else-if="row.createOrderUserNickName">{{ row.createOrderUserNickName }}</div>
                  <div v-else>-</div>
                </template>
              </el-table-column>

              <IntentionModelInfo @hover="handleModelHover">
                <template #header>
                  <div class="flex-center gap-5">
                    意向模特
                    <el-icon
                      color="rgb(155, 166, 186)"
                      style="margin: 1px 0 0 -2px"
                      @mouseenter="showTooltips($event, 'order-list-intentionModel')"
                    >
                      <QuestionFilled />
                    </el-icon>
                  </div>
                </template>
              </IntentionModelInfo>

              <ShootModelInfo @hover="handleModelHover">
                <template #header>
                  <div class="flex-center gap-5">
                    <span>拍摄模特</span>
                    <el-icon
                      color="rgb(155, 166, 186)"
                      style="margin: 1px 0 0 -2px"
                      @mouseenter="showTooltips($event, 'order-list-shootModel')"
                    >
                      <QuestionFilled />
                    </el-icon>
                  </div>
                </template>
              </ShootModelInfo>

              <el-table-column prop="status" label="订单状态" align="center" width="110">
                <template v-slot="{ row }">
                  <div>
                    {{ orderStatus(row.status) }}
                  </div>
                  <div class="status-btn-box">
                    <el-button link type="primary" @click="toDetails(row)">订单详情</el-button>
                    <!-- <template v-if="row.status === ORDER_STATUS['需支付']">
                      <el-button link type="primary" @click="handleAction('修改订单', row)">修改订单</el-button>
                    </template> -->
                  </div>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" align="center" width="150">
                <template v-slot="{ row }">
                  <div
                    class="action-box"
                    v-if="store.isOwnerAcc() || store.userInfo.account == row.createOrderUserAccount"
                  >
                    <el-button
                      link
                      type="primary"
                      :disabled="headActionDisabled"
                      @click="handleEditOrder(row)"
                    >
                      修改订单
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
      </template>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import CountDown from '@/components/public/CountDown.vue'
import ProductPic from '@/views/order/components/tableColumn/ProductPic.vue'
import ProductInfo from '@/views/order/components/tableColumn/ProductInfo.vue'
import PicCount from '@/views/order/components/tableColumn/PicCount.vue'
import IntentionModelInfo from '@/views/order/components/tableColumn/IntentionModelInfo.vue'
import ShootModelInfo from '@/views/order/components/tableColumn/ShootModelInfo.vue'

import { computed, ref, type PropType } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { orderStatus } from '@/utils/order'
import { useTooltips } from '@/hooks/useTooltips'
import { ElMessage } from 'element-plus'

const { showTooltips } = useTooltips()

const props = defineProps({
  dataList: {
    type: Object,
    default: () => ({}),
  },
})
const emits = defineEmits(['hover', 'action', 'headAction'])
const router = useRouter()
const store = useUserStore()

const list = computed(() => {
  if (props.dataList.orderListVOS?.length) {
    return props.dataList.orderListVOS
  }
  return []
})
const activeCollapse = ref([])
const headActionDisabled = ref(false)

// 订单支付权限-子账号
// const checkCompanyUserIds = computed(() => {
//   let flag = false
//   if (props.dataList.orderListVOS?.length) {
//     props.dataList.orderListVOS.forEach((item: any) => {
//       if (item.checkCompanyUserIds?.includes(store.userInfo.id)) {
//         flag = true
//       }
//     })
//   }
//   return flag
// })
const defaultExchangeRate = computed(() => {
  let flag = false
  if (props.dataList.orderListVOS?.length) {
    props.dataList.orderListVOS.forEach((item: any) => {
      if (item.defaultExchangeRate) {
        flag = true
      }
    })
  }
  return flag
})

function handleHeadAction(btn: string) {
  if (btn === '去支付') {
    store.realTimeCheckVip().then(() => {
      if (store.isVip()) {
        router.push({
          name: 'order-pay',
          state: { orderNum: '', mergeId: props.dataList.mergeId },
        })
      } else {
        ElMessage.error('请先开通会员！')
      }
    })
    return
  }
  emits('headAction', btn, props.dataList)
}

function handleOrderTime(time: string) {
  let date = time.split(' ')[0] + ' 00:00:00'
  return new Date(date).getTime() + 1000 * 60 * 60 * 24 * 31 - 1000 // 加30天 到最后一天 23:59:59
}

function handleTimeEnd() {
  headActionDisabled.value = true
}

function toFixed2(val: any) {
  if (val) {
    return val.toFixed(2)
  }
  return val == 0 ? 0 : '-'
}

// 修改订单
function handleEditOrder(row: any) {
  if (!row.id) return
  if (!store.isVip()) {
    ElMessage({
      type: 'warning',
      message: store.isViped() ? '您的会员已过期，请开通会员后再进行操作！' : '请先开通会员！',
      duration: 2000,
    })
    return
  }
  // emits('action', '修改订单', row)
  routerNewWindow('/order/edit/' + row.id)
}

function toDetails(row: any) {
  routerNewWindow('/order/details/' + row.id)
}

function routerNewWindow(path: string) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

function handleModelHover(el: EventTarget, id: any, show: any) {
  emits('hover', el, id, show)
}
</script>

<style scoped lang="scss">
.order-merge-list-item {
  margin-bottom: 20px;

  .merge-info-head {
    font-size: 14px;
    color: var(--el-text-color-regular);
    height: 48px;
    background-color: var(--table-head-bg);
    padding: 0 11px;
    border-radius: var(--table-border-radius) var(--table-border-radius) 0 0;

    .el-button {
      font-weight: 500;
    }

    .head-label {
      font-size: 14px;
      color: var(--el-text-color-regular);
      font-family:
        'wn-font',
        'PingFang SC',
        -apple-system,
        system-ui,
        Arial;
      span {
        font-weight: bold;
      }
    }

    .cancel-btn {
      width: 72px;

      &:hover {
        background-color: #f0f1f3;
        border-color: var(--text-color);
        color: var(--text-color);
      }
    }
  }

  :deep(.el-collapse) {
    border-radius: 0 0 var(--table-border-radius) var(--table-border-radius);
    overflow: hidden;
    border: 1px solid var(--el-collapse-border-color);

    .el-collapse-item {
      .el-collapse-item__header {
        background-color: #fcfcfc;
        // border-left: 1px solid #ebeef5;
        // border-right: 1px solid #ebeef5;
        padding-right: 10px;
        width: 100%;
        height: 40px;
        justify-content: space-between;
        font-size: 14px;
        color: var(--text-gray-color);
        font-family:
          'wn-font',
          'PingFang SC',
          -apple-system,
          system-ui,
          Arial;

        .head-label {
          span {
            font-weight: bold;
          }
        }
      }
      &__content {
        padding: 0;
      }
    }
  }

  .table-box {
    :deep(.el-table) {
      td {
        &.el-table__cell {
          &.intention-model-column,
          &.shoot-model-column {
            padding: 0 0 12px 0;
            overflow: hidden;

            .cell {
              overflow: visible;
            }
          }

          border-bottom: 1px solid transparent;
          border-right: none;
          border-left: none;
        }
      }

      .el-table__inner-wrapper {
        &:before {
          height: 0;
        }
        .el-table__border-left-patch {
          width: 0;
        }
      }
    }
    .el-button {
      font-weight: bold;
    }
  }
}
</style>

<style lang="scss">
.merge-table-box {
  &.el-table--border {
    &:before,
    &:after {
      width: 0;
      height: 0;
    }

    .el-table__cell {
      border-right: none;
    }
  }
}
</style>
