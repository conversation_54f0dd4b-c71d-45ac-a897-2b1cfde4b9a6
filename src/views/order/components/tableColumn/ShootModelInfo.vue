<template>
  <el-table-column prop="shootModel" label="拍摄模特" align="center" width="130" class-name="shoot-model-column">
    <template #header>
      <slot name="header"></slot>
    </template>
    <template v-slot="{ row }">
      <div v-if="row.shootModel" class="shoot-model-box">
        <div
          class="hint-box"
          v-if="
            row.shootModel?.account &&
            row.intentionModel?.account &&
            row.shootModel.account != row.intentionModel.account
          "
        >
          <div class="exchange">
            <span @mouseenter="showTooltips($event, 'order-list-huan')">换</span>
          </div>
        </div>
        <div
          @mouseenter="handleMouseEnter($event, row.shootModel.id)"
          @mouseleave="handleMouseLeave($event, row.shootModel.id)"
        >
          <el-avatar
            class="model-avatar"
            icon="UserFilled"
            :src="$picUrl + row.shootModel.modelPic + '!3x4'"
          />
          <div>{{ row.shootModel.name }}</div>
          <div>{{ row.shootModel?.account ? `(ID:${row.shootModel.account})` : '' }}</div>
          <template v-for="dict in biz_model_type" :key="dict.value">
            <el-tag v-if="dict.value == row.shootModel.type" type="info" size="small" round>
              {{ dict.label }}
            </el-tag>
          </template>
        </div>
        <!-- <el-button
            v-if="row.status === ORDER_STATUS['需发货']"
            link
            type="info"
            style="font-weight: 500; display: block; width: 100%"
            @click="handleAction('更换模特', row)"
          >
            更换
          </el-button> -->
      </div>
      <div v-else>-</div>
    </template>
  </el-table-column>
</template>

<script setup lang="ts">
import { getCurrentInstance } from 'vue'
import { useTooltips } from '@/hooks/useTooltips'
const { showTooltips } = useTooltips()

const { proxy } = getCurrentInstance() as any

const { biz_model_type } = proxy.useDict('biz_model_type')

const emits = defineEmits(['hover'])

function handleMouseEnter(e: Event, id: any) {
  emits('hover', e.target, id, true)
}
function handleMouseLeave(e: Event, id: any) {
  emits('hover', e.target, id, false)
}
</script>

<style scoped lang="scss">
.shoot-model-box {
  padding-top: 8px;

  .hint-box {
    position: absolute;
    right: -20px;
    top: 0px;
    z-index: 99;
    overflow: hidden;
    width: 50px;
    height: 20px;
    background: var(--el-color-info-light-5);
    transform: rotateZ(45deg);
    pointer-events: auto;

    .exchange {
      color: #fff;
      font-size: 13px;
      position: absolute;
      left: 14px;
      bottom: 4px;
      transform: rotateZ(-45deg);
      font-weight: 500;
      line-height: 13px;
      // transform: translate(-17px, -3px) scale(0.8) rotateZ(-45deg);

      // span {
      //   display: block;
      //   transform: rotateZ(45deg);
      // }
    }
  }
}
.model-avatar {
  --el-avatar-size: 40px;
  --el-avatar-icon-size: 22px;
}
:deep(.el-avatar) {
  position: relative;

  &>img {
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>
