<template>
  <el-table-column prop="picCount" label="照片数量" align="center" width="110">
    <template v-slot="{ row }">
      <div>{{ handleSelectiveAssembly(row.picCount) }}</div>
      <el-button v-if="row.referencePic?.length" link type="primary" @click="handleViewer(row.referencePic)">
        已选参考图
      </el-button>
    </template>
  </el-table-column>
</template>

<script setup lang="ts">
import { picCountOptions } from '@/utils/data'
import { useViewer } from '@/hooks/useViewer'

const { showViewer } = useViewer()

function handleSelectiveAssembly(val: string | number) {
  let str = picCountOptions.find(item => item.value == val)
  return str ? str.label.substring(0, 2) : '-'
}

function handleViewer(arr: any[]) {
  let urls = arr.map(item => item)
  showViewer(urls)
}
</script>

<style scoped lang="scss"></style>
