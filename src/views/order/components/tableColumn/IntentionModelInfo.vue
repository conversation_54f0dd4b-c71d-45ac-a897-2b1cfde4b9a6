<template>
  <el-table-column
    prop="intentionModel"
    label="意向模特"
    align="center"
    width="130"
    class-name="intention-model-column"
    v-bind="$attrs"
  >
    <template #header>
      <slot name="header"></slot>
    </template>
    <template v-slot="{ row }">
      <div
        v-if="row.intentionModel"
        @mouseenter="handleMouseEnter($event, row.intentionModel.id)"
        @mouseleave="handleMouseLeave($event, row.intentionModel.id)"
        class="intention-model-box"
      >
        <el-avatar
          class="model-avatar"
          icon="UserFilled"
          :src="$picUrl + row.intentionModel.modelPic + '!3x4'"
        />
        <div>{{ row.intentionModel.name }}</div>
        <div>{{ row.intentionModel?.account ? `(ID:${row.intentionModel.account})` : '' }}</div>
        <template v-for="dict in biz_model_type" :key="dict.value">
          <el-tag v-if="dict.value == row.intentionModel.type" type="info" size="small" round>
            {{ dict.label }}
          </el-tag>
        </template>
      </div>
      <div v-else>-</div>
    </template>
  </el-table-column>
</template>

<script setup lang="ts">
import { getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance() as any

const { biz_model_type } = proxy.useDict('biz_model_type')

const emits = defineEmits(['hover'])

function handleMouseEnter(e: Event, id: any) {
  emits('hover', e.target, id, true)
}
function handleMouseLeave(e: Event, id: any) {
  emits('hover', e.target, id, false)
}
</script>

<style scoped lang="scss">
.intention-model-box {
  padding-top: 8px;
}
.model-avatar {
  --el-avatar-size: 40px;
  --el-avatar-icon-size: 22px;
}
:deep(.el-avatar) {
  position: relative;

  &>img {
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>
