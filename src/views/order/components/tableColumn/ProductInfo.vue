<template>
  <el-table-column prop="productInfo" label="产品信息" minWidth="250">
    <template v-slot="{ row }">
      <div class="product-info-box">
        <CopyButton class="btn" :copy-content="handleCopy(row)" link />
        <div>视频编码：{{ row.videoCode }}</div>
        <div class="one-ell">产品名称：{{ row.productChinese }}</div>
        <!-- <div>英文名称：{{ row.productEnglish }}</div> -->
        <div class="one-ell productLink">
          产品链接：
          <el-link
            v-if="row?.productLink && http_reg.test(row.productLink)"
            :underline="false"
            target="_blank"
            type="primary"
            :href="row.productLink"
          >
            {{ row.productLink }}
          </el-link>
          <span v-else>{{ row.productLink }}</span>
        </div>
        <div class="flex-start gap-5" style="margin-top: 5px; flex-wrap: wrap">
          <template v-for="dict in biz_model_platform" :key="dict.value">
            <el-tag v-if="dict.value == row.platform" type="info" size="small" round>
              {{ dict.label }}
            </el-tag>
          </template>
          <template v-for="dict in biz_model_type" :key="dict.value">
            <el-tag v-if="dict.value == row.modelType" type="info" size="small" round>
              {{ dict.label }}
            </el-tag>
          </template>
          <template v-if="row.modelType == '3'">
            <el-tag type="info" size="small" round>影/素都可以</el-tag>
          </template>
          <template v-for="dict in biz_nation" :key="dict.value">
            <el-tag v-if="dict.value == row.shootingCountry" type="info" size="small" round>
              {{ dict.label }}
            </el-tag>
          </template>
          <template v-for="op in videoFormatOptions" :key="op.value">
            <el-tag class="tag" v-if="op.value == row.videoFormat" type="info" size="small" round>
              {{ op.label }}
            </el-tag>
          </template>
        </div>
      </div>
    </template>
  </el-table-column>
</template>

<script setup lang="ts">
import CopyButton from '@/components/public/button/CopyButton.vue'
import { getCurrentInstance } from 'vue'
import { videoFormatOptions } from '@/utils/data'
import { http_reg } from '@/utils/RegExp'

const { proxy } = getCurrentInstance() as any

const { biz_nation, biz_model_type, biz_model_platform } = proxy.useDict(
  'biz_nation',
  'biz_model_type',
  'biz_model_platform'
)

function handleCopy(row: any) {
  let str = ''
  if (row.videoCode) {
    str += '视频编码：' + row.videoCode + '\n'
  }
  if (row.productChinese) {
    str += '产品名称：' + row.productChinese + '\n'
  }
  if (row.productEnglish) {
    str += '英文名称：' + row.productEnglish + '\n'
  }
  if (row.productLink) {
    str += '产品链接：' + row.productLink
  }
  return str
}
</script>

<style scoped lang="scss">
.product-info-box {
  position: relative;

  .btn {
    position: absolute;
    top: 0;
    right: -12px;
    padding: 3px 8px;
    height: auto;
    z-index: 9;
    font-size: 14px;
    font-weight: bold;
  }
}
.productLink {
  position: relative;
  padding-right: 50px;
  width: fit-content;
  max-width: 100%;

  :deep(.el-link) {
    display: contents;

    .el-link__inner {
      display: inline;
    }
  }
}
</style>
