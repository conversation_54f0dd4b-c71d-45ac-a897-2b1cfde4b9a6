<template>
  <el-table-column
    prop="productPicUrl"
    class-name="product-pic-column"
    label="产品图"
    align="center"
    width="150"
  >
    <template v-slot="{ row }">
      <!-- <PercentageImg width="90px" :src="scope.row.productPic?.picUrl" radius="8px" /> -->
      <svg v-if="!row.productPic" class="icon" aria-hidden="true" style="width: 125px; height: 125px">
        <use xlink:href="#icon-zanwutupian"></use>
      </svg>
      <el-image
        v-else
        style="width: 125px; height: 125px; display: flex; justify-content: center; align-items: center"
        :src="$picUrl + row.productPic + '!thumbnail200'"
        fit="scale-down"
        @click="showViewer([row.productPic], { scale: 'thumbnail200' })"
      >
        <template #error>
          <!-- <div v-if="!item.productPic" class="img-error">暂无产品图</div> -->
          <div class="img-error">加载失败</div>
        </template>
      </el-image>
    </template>
  </el-table-column>
</template>

<script setup lang="ts">
// import PercentageImg from '@/components/public/image/PercentageImg.vue'
import { useViewer } from '@/hooks/useViewer'
const { showViewer } = useViewer()
</script>

<style scoped lang="scss">
:deep(.el-table) {
  td.el-table__cell.product-pic-column {
    padding: 0;

    .cell {
      padding: 0;
    }
  }
}
.img-error {
  width: 100px;
  height: 100px;
  flex-shrink: 0;
  font-size: 12px;
  background-color: #f5f7fa;
  margin-top: 0;
  text-align: center;
  line-height: 100px;
  color: var(--text-gray-color);
}
</style>
