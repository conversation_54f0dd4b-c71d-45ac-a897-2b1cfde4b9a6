<template>
  <el-table-column type="expand" :width="1" class-name="expand-column">
    <template #default="{ row }">
      <div
        class="flex-end gap-10"
        style="padding: 0 20px"
        v-if="row.logisticInfo && row.status === ORDER_STATUS['待完成']"
      >
        <div class="flex-start gap-10 logistic-info">
          <img src="@/assets/icon/icon_truck.png" alt="" />
          <span>
            物流状态：{{ row.logisticInfo.mainStatusSketch || row.logisticInfo.subStatusSketch || '-' }}
          </span>
          <span>{{ row.logisticInfo.description || '-' }}</span>
          <span>{{ row.logisticInfo.curTime || '-' }}</span>
        </div>
        <el-button link type="primary" @click="toViewDetail(row)">查看物流</el-button>
      </div>
      <div class="flex-between gap-10" style="padding: 0 20px" v-if="row.status === ORDER_STATUS['需确认']">
        <div>
          <div
            class="one-ell productLink"
            style="max-width: 900px"
            v-if="row.latestFeedBack && row.latestFeedBack.videoUrl"
          >
            视频素材链接：
            <el-link type="primary" :underline="true" target="_blank" :href="row.latestFeedBack.videoUrl">
              {{ row.latestFeedBack.videoUrl }}
            </el-link>
          </div>
          <div
            class="one-ell productLink"
            style="max-width: 900px"
            v-if="row.latestFeedBack && row.latestFeedBack.picUrl"
          >
            照片素材链接：
            <el-link type="primary" :underline="true" target="_blank" :href="row.latestFeedBack.picUrl">
              {{ row.latestFeedBack.picUrl }}
            </el-link>
          </div>
        </div>
        <el-button
          v-if="store.isOwnerAcc() || store.userInfo.account == row.createOrderUserAccount"
          link
          type="primary"
          @click="handleAction('验收素材', row)"
        >
          确认素材
        </el-button>
      </div>
    </template>
  </el-table-column>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { ORDER_STATUS } from '@/utils/order'

const store = useUserStore()
const router = useRouter()
const emits = defineEmits(['action'])
function toViewDetail(row: any) {
  router.push('/order/details/' + row.id)
}

function handleAction(btn: string, row: any) {
  emits('action', btn, row)
}
</script>

<style scoped lang="scss">
:deep(.el-table) {
  .expand-column {
    padding: 0;
    width: 0px !important;

    .cell {
      width: 0;
      padding: 0;

      .el-table__expand-icon {
        display: none;
      }
    }
  }
}
.logistic-info {
  color: var(--text-gray-color);
  font-size: 13px;
  line-height: 14px;
  background-color: #f6f6f6;
  border-radius: 4px;
  padding: 4px 15px;

  img {
    width: 16px;
    height: 14px;
  }

  :nth-child(2) {
    flex-shrink: 0;
  }
}
.productLink {
  :deep(.el-link) {
    display: inline;

    .el-link__inner {
      display: inline;
    }
  }
}
</style>
