<template>
  <!-- 步骤条 -->
  <div class="box-top" v-for="(item, index) in dataArray" :key="index">
    <!-- 左边 -->
    <!-- <div class="left-box-top">{{ item.curTime }}</div> -->
    <div class="line" :class="{ none: index == dataArray.length - 1 }">
      <!-- 中线 -->

      <div class="dot-one flex-center" v-if="index === 0">
        <img class="icon" src="@/assets/icon/icon_express_fff.png" alt="" />
        <!-- <img class="icon" src="@/assets/icon/icon_on_order.png" alt="" />
        <img class="icon" src="@/assets/icon/icon_job_bought.png" alt="" /> -->
      </div>
      <div class="dot" v-else></div>
      <div class="dot-one flex-center" style="background: #fff;border: 1px solid #C2CAD9;" v-if="index === dataArray.length - 1 && dataArray.length > 2">
        <img class="icon-two" src="@/assets/icon/icon_on_order.png" alt="" />
      </div>
      <div class="dot-one flex-center" style="background: #fff;border: 1px solid #C2CAD9;" v-if="index === dataArray.length - 2 && dataArray.length > 3">
        <img class="icon-two" src="@/assets/icon/icon_job_bought.png" alt="" />
      </div>
      <!-- 圆点 -->
    </div>
    <div class="right-box-top" style="color: #9ba6ba" :class="{ 'content-active': index === 0 }">
      <!-- 右边 -->
      <slot name="content" :data="item">
        <div>
          <div :class="{ title: index === 0 }">{{ item.mainStatus }} {{ item.curTime }}</div>
          <div>
            {{ item.city ? '[' + item.city + '] ' : '' }} {{ item.description ? item.description : '-' }}
          </div>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Logistics } from '../type'

const props = defineProps({
  dataArray: {
    type: Array<Logistics>,
    default: () => [],
  },
})
</script>

<style lang="scss" scoped>
$dot: #5695c0;
.box-top {
  width: 100%;
  min-height: 60px;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;

  .left-box-top {
    width: 75px;
    text-align: center;
    color: var(--text-gray-color);
    font-size: 13px;
  }

  .line {
    width: 2px;
    background-color: #e4e8eb;
    margin: 0 10px 0 10px;
    transform: translateY(13px);

    .dot {
      width: 10px;
      height: 10px;
      // border: 1px solid #979797;
      background-color: #c9cfd9;
      border-radius: 50%;
      position: relative;
      top: -6px;
      left: -4px;
    }
    .dot-one {
      width: 30px;
      height: 30px;
      background: $dot;
      border-radius: 50%;
      text-align: center;
      position: relative;
      top: -18px;
      left: -14px;
      .icon {
        width: 18px;
        height: 18px;
      }
      .icon-two {
        width: 20px;
        height: 20px;
      }
    }
  }

  .right-box-top {
    flex: 1;
    padding: 0 0 10px 15px;
    font-size: 13px;
    color: var(--text-gray-color);

    .title {
      color: #5695c0;
      font-weight: 600;
    }
  }
  .content-active {
    color: var(--text-color);
  }
}

//激活元素
.active {
  background: $dot !important;

  &.dot {
    border-color: $dot !important;
  }
}

// 隐藏元素
.none {
  background-color: rgba(0, 0, 0, 0) !important;
}
</style>
