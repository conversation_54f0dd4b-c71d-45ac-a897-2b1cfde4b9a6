<template>
  <div class="head-box">
    <!-- <div class="head-box__title">
      <div class="head-title">
        <el-icon color="#5695c0"><CaretRight /></el-icon>
        <span style="margin-left: 5px;">我的订单</span>
        <span style="color: #e4e8eb; margin: 0 10px">/</span>
        <span style="font-weight: 800;">订单详情</span>
      </div>
    </div> -->
    <div class="flex-start">
      <div class="flex-column status-box">
        <img v-if="orderVideoVO?.status === ORDER_STATUS['需支付']" src="@/assets/icon/icon_order_pay.png" />
        <!-- <img src="@/assets/icon/icon_order_express.png" /> -->
        <img
          v-else-if="orderVideoVO?.status === ORDER_STATUS['交易关闭']"
          src="@/assets/icon/icon_order_closed.png"
        />
        <img
          v-else-if="orderVideoVO?.status === ORDER_STATUS['待审核']"
          src="@/assets/icon/icon_order_audit.png"
        />
        <img
          v-else-if="
            orderVideoVO?.status === ORDER_STATUS['待匹配 '] ||
            orderVideoVO?.status === ORDER_STATUS['待匹配']
          "
          src="@/assets/icon/icon_order_mate.png"
        />
        <img
          v-else-if="orderVideoVO?.status === ORDER_STATUS['需发货']"
          src="@/assets/icon/icon_order_deliver.png"
        />
        <img
          v-else-if="
            orderVideoVO?.status === ORDER_STATUS['待完成'] ||
            orderVideoVO?.status === ORDER_STATUS['已完成'] ||
            orderVideoVO?.status === ORDER_STATUS['需确认']
          "
          src="@/assets/icon/icon_order_completed.png"
        />
        <!-- <img src="@/assets/icon/money_icon.svg" />
        <span v-if="orderVideoVO?.status">{{ orderStatus(orderVideoVO.status) }}</span> -->
      </div>
      <div class="content">
        <!-- 暂时不展示 -->
        <template v-if="false">
          <div class="one-ell" v-if="orderVideoVO?.status === ORDER_STATUS['需支付']">
            <!-- <span>订单将在 </span>
            <CountDown :endTime="orderVO.overTime" h="时" m="分" s="秒" @end="$emit('countDownEnd')" />
            <span> 后自动关闭，请尽快支付。</span> -->
            订单已生成，请尽快支付。
          </div>
          <div class="one-ell" v-else-if="orderVideoVO?.status === ORDER_STATUS['交易关闭']">
            订单取消了。
          </div>
          <!-- <div class="red one-ell" v-else-if="orderVideoVO?.status === ORDER_STATUS['待上传凭证']">
              您选择的是银行卡转账/对公转账，请尽快上传转账凭证。
            </div> -->
          <div class="one-ell" v-else-if="orderVideoVO?.status === ORDER_STATUS['待审核']">
            您已提交转账凭证，平台财务审核中，请耐心等待。
          </div>
          <!-- 待确认目前改成待匹配用空格暂时显示 -->
          <div class="one-ell" v-else-if="orderVideoVO?.status === ORDER_STATUS['待匹配 ']">
            您的订单匹配中。
          </div>
          <div class="one-ell" v-else-if="orderVideoVO?.status === ORDER_STATUS['待匹配']">
            您的订单匹配中。
          </div>
          <div class="one-ell" v-else-if="orderVideoVO?.status === ORDER_STATUS['需发货']">
            请尽快将产品寄出。
          </div>
          <div class="one-ell" v-else-if="orderVideoVO?.status === ORDER_STATUS['待完成']">
            <template v-if="orderVideoVO.hasFeedBack">
              <span>成品已完成，可下载查看，如有问题，请及时联系我们~</span>
              <!-- <CountDown :endTime="orderVideoVO.orderFeedBacks[0].overTime" h="时" m="分" s="秒" @end="$emit('countDownEnd')" /> -->
              <!-- <span> 后系统将自动确认成品。</span> -->
            </template>
            <span v-else>订单处理中，请耐心等候。</span>
          </div>
          <div class="one-ell" v-else-if="orderVideoVO?.status === ORDER_STATUS['已完成']">
            <span v-if="orderVideoVO?.uploadPlatformUrl">视频成品已为您上传至相关平台。</span>
            <span v-else-if="orderVideoVO?.allowUpload === 0">视频成品已完成，正在为您上传至相关平台。</span>
            <span v-else>交易已完成。</span>
          </div>
        </template>

        <div class="flex-start gap-10 status-text-box">
          <span>{{ ORDER_STATUS[orderVideoVO?.status] }}</span>
        </div>
        <div class="content-info">
          <template v-if="orderVideoVO?.status === ORDER_STATUS['已完成']">
            <div>视频编码&emsp;{{ orderVideoVO?.videoCode || '-' }}</div>
            <div>
              <el-tooltip
                effect="dark"
                content="已确认无误并上传完成的视频地址"
                placement="top-start"
                :offset="5"
              >
                <span>视频地址&emsp;</span>
              </el-tooltip>
              <el-button
                v-if="data?.orderVideoUploadLinkSimpleVO?.uploadLink"
                link
                type="primary"
                size="small"
                style="position: relative; bottom: 2px; padding: 0"
                @click="openLink"
              >
                查看
              </el-button>
              <span v-else>-</span>
            </div>
            <div>完成时间&emsp;{{ orderVideoVO?.statusTime || '-' }}</div>
            <div>订单运营&emsp;{{ orderVideoVO?.createOrderUserName || '-' }}</div>
          </template>
          <template v-else>
            <div>视频编码&emsp;{{ orderVideoVO?.videoCode || '-' }}</div>
            <div>商品总额&emsp;￥{{ orderVideoVO?.amount ? orderVideoVO.amount.toFixed(2) : '-' }}</div>
            <div v-if="orderVideoVO?.status === ORDER_STATUS['交易关闭']">
              取消时间&emsp;{{ orderVideoVO?.statusTime || '-' }}
            </div>
            <div v-else>下单时间&emsp;{{ orderVO?.orderTime }}</div>
            <div>
              订单运营&emsp;{{
                orderVideoVO?.createOrderUserName || orderVideoVO?.createOrderUserNickName || '-'
              }}
            </div>
          </template>
        </div>
      </div>

      <DotSteps :active="active" :steps="steps">
        <template #title="{ step }">
          <el-tooltip v-if="step.tooltip" effect="dark" :content="step.tooltip" placement="top" :offset="30">
            <div class="tooltip-label">
              <div class="pop-box"></div>
              {{ step.title }}
            </div>
          </el-tooltip>
          <div v-else>{{ step.title }}</div>
        </template>
      </DotSteps>

      <div
        class="head-btn-box flex-start"
        v-if="store.isOwnerAcc() || orderVideoVO?.createOrderUserAccount == store.userInfo.account"
      >
        <template v-if="orderVideoVO?.status === ORDER_STATUS['需支付']">
          <!-- <el-button plain round @click="handleAction('取消订单')">取消订单</el-button>
            <el-button type="primary" round @click="handleAction('立即支付')">立即支付</el-button> -->
        </template>
        <!-- <template v-else-if="orderVideoVO?.status === ORDER_STATUS['待上传凭证']">
              <el-button type="primary" round>上传凭证</el-button>
            </template> -->
        <template
          v-else-if="
            orderVideoVO?.status === ORDER_STATUS['待匹配 '] ||
            orderVideoVO?.status === ORDER_STATUS['待匹配']
          "
        >
          <el-button
            v-if="orderVideoVO?.unConfirmTime && checkTime(orderVideoVO?.unConfirmTime)"
            round
            type="primary"
            @click="handleAction('申请退款')"
          >
            申请退款
          </el-button>
        </template>
        <template v-else-if="orderVideoVO?.status === ORDER_STATUS['需发货'] && orderVideoVO?.isObject === 0">
          <el-button v-if="orderVideoVO?.logisticFlag" type="primary" round @click="handleAction('发货')">
            填入物流单号
          </el-button>
          <el-button v-else type="primary" round @click="handleAction('发货')">去发货</el-button>
        </template>
        <template v-else-if="orderVideoVO?.status === ORDER_STATUS['需发货'] && orderVideoVO?.isObject === 1">
          <el-button type="primary" round @click="handleAction('确认模特')">确认模特</el-button>
        </template>
        <!-- <template v-else-if="orderVideoVO?.status === ORDER_STATUS['待完成'] && orderVideoVO.hasFeedBack">
            <el-button type="primary" round @click="handleAction('验收素材')">
              验收素材
            </el-button>
            <el-button type="primary" round @click="handleAction('确认成品')">
              确认成品
            </el-button>
          </template> -->
        <template v-else-if="orderVideoVO?.status === ORDER_STATUS['已完成']">
          <div class="flex-end up-url" v-if="orderVideoVO?.uploadPlatformUrl">
            <div class="one-ell">
              上传地址：
              <a target="_blank" :href="orderVideoVO.uploadPlatformUrl">
                {{ orderVideoVO.uploadPlatformUrl }}
              </a>
            </div>
            <CopyButton size="small" :copy-content="orderVideoVO.uploadPlatformUrl" />
          </div>
          <el-button
            type="primary"
            round
            v-else-if="orderVideoVO?.allowUpload === 0"
            @click="handleAction('取消上传')"
          >
            取消上传
          </el-button>
          <!-- <el-button type="primary" round v-else @click="handleAction('可以上传')">可以上传</el-button> -->
        </template>
      </div>
    </div>

    <ApplyRefund ref="ApplyRefundRef" :refundType="2" @success="$emit('countDownEnd')" />
  </div>
</template>

<script setup lang="ts">
import { ORDER_STATUS, ORDER_STEPS_STATUS } from '@/utils/order'
import { computed, ref, type PropType } from 'vue'
// import CountDown from '@/components/public/CountDown.vue'
import CopyButton from '@/components/public/button/CopyButton.vue'
import DotSteps from '@/components/public/steps/DotSteps.vue'
import ApplyRefund from '@/views/order/components/dialog/ApplyRefund.vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'

const props = defineProps({
  data: {
    type: Object as PropType<any>,
    default: () => ({}),
  },
})

const emits = defineEmits(['action', 'countDownEnd'])

const store = useUserStore()

const ApplyRefundRef = ref<InstanceType<typeof ApplyRefund>>()

const orderVO = computed(() => props.data.orderSimpleVO)
const orderVideoVO = computed(() => props.data.orderVideoSimpleVO)
const orderVideoFlowNodeDiagramVOS = computed(() => props.data.orderVideoFlowNodeDiagramVOS || [])

// 当前步骤
const active = computed(() => {
  if (orderVideoFlowNodeDiagramVOS.value.length) {
    if (orderVideoFlowNodeDiagramVOS.value.every((item: any) => item.time)) {
      return orderVideoFlowNodeDiagramVOS.value.length
    }
    let i = orderVideoFlowNodeDiagramVOS.value.findIndex((item: any) => !item.time)
    return i !== -1 ? i : 0
  }
  return 0
})
// 步骤节点
const steps = computed(() => {
  let arr = []
  if (orderVideoFlowNodeDiagramVOS.value.length) {
    arr = orderVideoFlowNodeDiagramVOS.value.map((item: any) => ({
      title: handleStepTitle(item),
      description: item.time,
      tooltip: ORDER_STEPS_STATUS['商家确认'] === item.node ? getEndTime() : '',
    }))
  }
  return arr
})

function handleStepTitle(item: any) {
  if (ORDER_STEPS_STATUS['完成拍摄'] === item.node) {
    if (!item.time) {
      return '拍摄中'
    }
  }
  return ORDER_STEPS_STATUS[item.node]
}

function getEndTime() {
  if (orderVideoVO.value?.autoCompleteTime) {
    let t = orderVideoVO.value?.autoCompleteTime.split(' ')[0]
    let date = new Date(t).getTime()
    let now = new Date().getTime()
    let day = Math.floor((date - now) / (1000 * 60 * 60 * 24))
    return (day > 0 ? day : 0) + '天后将会自动确认'
  }
  return ''
}

const checkTime = (() => {
  return import.meta.env.VITE_APP_ENV === 'production'
    ? (time: string) => {
        let unTime = new Date(time).getTime() + 1000 * 60 * 60 * 24 * 15 // 20天
        return new Date().getTime() > unTime
      }
    : (time: string) => {
        let unTime = new Date(time).getTime() + 1000 * 60 * 60 * 1 // 1小时
        return new Date().getTime() > unTime
      }
})()

function openLink() {
  if (props.data?.orderVideoUploadLinkSimpleVO?.uploadLink) {
    window.open(props.data?.orderVideoUploadLinkSimpleVO?.uploadLink)
  } else {
    ElMessage.error('暂无已上传的视频地址')
  }
}

function handleAction(btn: string) {
  if (btn === '申请退款' && orderVideoVO.value?.id) {
    ApplyRefundRef.value?.open(orderVideoVO.value.id)
    return
  }
  emits('action', btn)
}
</script>

<style scoped lang="scss">
.head-box {
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.24) 0%, #ffffff 100%);
  padding: 13px;
  border: 1px solid #fff;
  border-radius: 8px;
  gap: 10px;
  position: relative;
  box-shadow: 3px 3px 4px 0px var(--shadow-gray);
  &__title {
    font-size: 14px;
    .head-title {
      display: flex;
      align-items: center;
    }
  }
  &__title::after {
    content: '';
    display: block;
    height: 1px;
    background-color: #e4e8eb;
    margin-top: 8px;
  }

  .status-box {
    padding: 8px 20px 8px 10px;
    border-radius: 4px;
    font-size: 16px;
    // background-color: #f2f2f2;

    img {
      width: 80px;
      height: 80px;
    }

    span {
      color: #000;
    }
  }
  .content {
    font-size: 20px;
    font-weight: 600;
    width: 260px;
    border-right: 1px solid #e4e8eb;
    margin-right: 20px;

    @include mediaTo('notebook') {
      width: 170px;
    }

    &:first-child {
      margin-bottom: 10px;
    }

    .red {
      color: var(--el-color-danger);
    }

    .status-text-box {
      .el-button {
        margin-top: 4px;
      }
    }
    .content-info {
      margin-top: 5px;
      font-size: 13px;
      font-weight: 500;
      color: var(--text-color);
    }
  }

  .up-url {
    width: 180px;
    font-size: 12px;
    gap: 10px;
    color: var(--el-color-primary);

    div {
      max-width: 500px;
    }
  }

  .head-btn-box {
    flex-wrap: wrap;
    height: 117px;
    padding-left: 30px;
    margin-left: 30px;
    border-left: 1px solid #e4e8eb;

    @include mediaTo('notebook') {
      height: 137px;
      padding-left: 30px;
      margin-left: 10px;
    }
    // @include mediaTo('pc') {
    //   margin-right: 100px;
    // }

    .el-button {
      width: 100px;
    }
  }
}

.tooltip-label {
  position: relative;

  // .pop-box {
  //   display: block;
  //   position: absolute;
  //   left: 50%;
  //   bottom: 100%;
  //   z-index: 99;
  //   transform: translateX(-20px);
  //   width: 40px;
  //   height: 25px;
  //   background-color: transparent;
  // }
  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 50%;
    bottom: 100%;
    z-index: 99;
    transform: translateX(-20px);
    width: 40px;
    height: 25px;
    background-color: transparent;
  }
}
</style>
