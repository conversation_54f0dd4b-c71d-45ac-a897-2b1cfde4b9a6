<template>
  <div class="pages">
    <div class="search-box" v-if="curTab === '' && !orderTotal">
      <el-tabs model-value="" class="tabs" @tab-change="handleTabChange">
        <el-tab-pane v-for="(tab, i) in tabList" :key="tab.value" :name="tab.value">
          <template #label>
            <div v-if="i" class="flex-center gap-5" style="align-content: center">
              {{ tab.label }}
              (
              <span style="color: red">{{ tab.number }}</span>
              )
              <el-icon
                v-if="tab.tip"
                color="rgb(155, 166, 186)"
                class="tooltip-icon"
                @mouseenter="showTooltips($event, tab.tip)"
              >
                <QuestionFilled />
              </el-icon>
            </div>
            <div v-else>{{ tab.label }}</div>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="flex-center w-bg" v-if="curTab === '' && !orderTotal">
      <div class="flex-column" v-loading="loading" style="width: 100%; justify-content: center; gap: 20px">
        <Empty image-type="order" description="暂无订单信息" :image-size="180" v-once>
          <template #description>
            <div>暂无订单信息</div>
            <el-button type="primary" round class="no-data-button" @click="handleAddOrder" v-once>
              去创建订单
            </el-button>
          </template>
        </Empty>
      </div>
    </div>

    <template v-else>
      <div class="search-box">
        <el-tabs v-model="curTab" class="tabs" @tab-change="handleTabChange">
          <el-tab-pane v-for="(tab, i) in tabList" :key="tab.value" :name="tab.value">
            <template #label>
              <div v-if="i" class="flex-center gap-5" style="align-content: center">
                {{ tab.label }}
                (
                <span style="color: red">{{ tab.number }}</span>
                )
                <el-icon
                  v-if="tab.tip"
                  color="rgb(155, 166, 186)"
                  class="tooltip-icon"
                  @mouseenter="showTooltips($event, tab.tip)"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
              <div v-else>{{ tab.label }}</div>
            </template>
          </el-tab-pane>
        </el-tabs>
        <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
          <el-form-item label="" prop="keyword">
            <el-input
              class="custom-radius"
              v-model="queryParams.val"
              clearable
              style="width: 180px"
              placeholder="请输入关键字搜索"
            >
              <!-- <template #prepend>
                <el-select
                  v-model="queryParams.select"
                  placeholder="请选择"
                  clearable
                  style="width: 100px"
                >
                  <el-option
                      v-for="item in searchSelectList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  />
                </el-select>
              </template> -->
            </el-input>
          </el-form-item>
          <el-form-item label="">
            <SelectV2
              class="custom-radius"
              v-model="queryParams.createOrderUserName"
              :request="orderMerchantList"
              :requestCallback="
                (res: any) => res.code === 200 && res.data.map((item: any) => ({ label: item, value: item }))
              "
              placeholder="请选择订单运营"
              style="width: 180px"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
            />
          </el-form-item>
          <el-form-item label="" prop="platform">
            <el-select
              class="custom-radius"
              v-model="queryParams.platform"
              placeholder="请选择平台"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              style="width: 180px"
            >
              <el-option
                v-for="item in biz_model_platform"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" prop="status" v-if="curTab === ''">
            <el-select
              class="custom-radius"
              v-model="queryParams.status"
              placeholder="请选择订单状态"
              multiple
              collapse-tags
              collapse-tags-tooltip
              clearable
              style="width: 180px"
            >
              <el-option
                v-for="item in orderStatusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="" style="width: 320px">
            <el-date-picker
              class="custom-radius"
              v-model="queryParams.placeOrderTime"
              format="YYYY/M/D HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="-"
              unlink-panels
              start-placeholder="下单开始日期"
              end-placeholder="下单结束日期"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="" style="width: 320px">
            <el-date-picker
              class="custom-radius"
              v-model="queryParams.playTime"
              format="YYYY/M/D HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetimerange"
              range-separator="-"
              unlink-panels
              start-placeholder="支付开始日期"
              end-placeholder="支付结束日期"
              :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
            ></el-date-picker>
          </el-form-item>
          <!-- <el-form-item label="支付方式">
              <el-select
                v-model="queryParams.payType"
                placeholder="请选择"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                style="width: 220px"
              >
                <el-option
                  v-for="item in payTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
          </el-form-item> -->
          <el-form-item>
            <el-button
              class="custom-radius"
              type="primary"
              icon="Search"
              native-type="submit"
              @click="onQuery"
            >
              搜索
            </el-button>
            <el-button class="custom-radius" icon="Refresh" plain @click="resetQuery">重置</el-button>
            <DownloadButton
              v-if="store.isOwnerAcc()"
              class="custom-radius"
              icon="Download"
              plain
              text="导出数据"
              url="/order/order/company-export"
              :params="handleParams()"
              :disabled="!tableData.length"
            />
          </el-form-item>
        </el-form>
      </div>

      <template v-if="tableData.length">
        <div
          class="w-bg"
          :style="{
            'border-radius': curTab === ORDER_STATUS['需支付'] ? '14px 14px 0 0' : '14px',
          }"
        >
          <div v-loading="loading" style="min-height: 500px" v-if="curTab === ORDER_STATUS['需支付']">
            <el-checkbox-group v-model="checkedOrders" @change="handleCheckedOrdersChange">
              <template v-for="(item, i) in tableData" :key="i">
                <OrderMergeListItem
                  v-if="item.mergeId && defer(i / 2)"
                  :dataList="item"
                  @action="handleButtonAction"
                  @headAction="handleHeadButtonAction"
                  @hover="handleModelHover"
                />
                <OrderListItem
                  v-else-if="item.orderListVOS?.length && defer(i / 2)"
                  is-checkbox
                  :data="item.orderListVOS[0]"
                  :key="item.orderNum"
                  @action="handleButtonAction"
                  @headAction="handleHeadButtonAction"
                  @hover="handleModelHover"
                />
              </template>
            </el-checkbox-group>
          </div>
          <div v-loading="loading" style="min-height: 500px" v-else>
            <template v-for="(item, i) in tableData" :key="i">
              <OrderListItem
                v-if="defer(i / 2)"
                :data="item"
                :key="item.orderNum"
                @action="handleButtonAction"
                @headAction="handleHeadButtonAction"
                @hover="handleModelHover"
              />
            </template>
          </div>
          <div class="pagination-box" style="margin-top: 20px" v-if="curTab != ORDER_STATUS['需支付']">
            <el-pagination
              class="custom-pagination-radius"
              background
              @size-change="pageChange({ pageNum: 1, pageSize: $event })"
              @current-change="pageChange({ pageNum: $event, pageSize })"
              :current-page="pageNum"
              :page-size="pageSize"
              :page-sizes="pageSizes"
              layout="total, prev, pager, next, jumper"
              :total="total"
            />
          </div>
        </div>
      </template>
      <div
        class="w-bg"
        :style="{
          'border-radius': curTab === ORDER_STATUS['需支付'] ? '14px 14px 0 0' : '14px',
        }"
        v-else
      >
        <div class="flex-center" v-if="noOrderDate" v-loading="loading">
          <Empty image-type="order" description="该状态下暂无相关订单" :image-size="180" v-once />
        </div>
        <div class="flex-center" v-else v-loading="loading">
          <Empty image-type="order" description="暂无数据" :image-size="180" v-once />
        </div>
      </div>

      <div class="flex-between submit-box" v-if="curTab === ORDER_STATUS['需支付']">
        <div class="flex-start" style="margin: 0 20px 0 2px">
          <el-checkbox
            v-model="checkAll"
            :indeterminate="isIndeterminate"
            :disabled="submitLoading || loading || allCheckedDisabled"
            @change="handleCheckAllChange"
          >
            全选
          </el-checkbox>
        </div>
        <div class="flex-end gap bottom-right">
          <div style="margin-right: 20px">
            <div class="flex-end text">
              <span style="margin-right: 15px">
                已选订单数：
                <span style="color: #ff5722">{{ checkedOrders.length }}</span>
                ,
              </span>
              <span>当前总计&nbsp;</span>
              <div class="money">
                <span style="color: #ff5722; font-size: 22px; margin-right: 5px">{{ totalAmount }}</span>
                <span style="color: #ff5722">CNY</span>
              </div>
            </div>
            <!-- <div class="tip">*此处价格展示为人民币价格，最终结算价格请以结算页为准。</div> -->
          </div>
          <el-button
            class="submit-button"
            type="primary"
            :disabled="!checkedOrders.length || loading"
            :loading="submitLoading"
            @click="onSubmit()"
          >
            立即支付
          </el-button>
        </div>
      </div>
    </template>

    <!--    <el-popover-->
    <!--      placement="left-end"-->
    <!--      title="购物车清单"-->
    <!--      popper-style="border-radius: 10px;text-align: center;color:#09172F"-->
    <!--      :width="220"-->
    <!--      trigger="hover"-->
    <!--    >-->
    <!--      <template #reference>-->
    <!--        <el-badge-->
    <!--          @click.stop="routerNewWindow('/order/shoppingCart')"-->
    <!--          :value="store.shoppingCartCount"-->
    <!--          :max="99"-->
    <!--          :offset="[-10, 8]"-->
    <!--          :show-zero="false"-->
    <!--          class="shopping-cart-badge"-->
    <!--        >-->
    <!--          <div-->
    <!--            class="flex-center shopping-cart-flaot"-->
    <!--            v-if="store.shoppingCartCount"-->
    <!--            @click.stop="routerNewWindow('/order/shoppingCart')"-->
    <!--          >-->
    <!--            <el-icon :size="20"><ShoppingCart /></el-icon>-->
    <!--            <span>购物车</span>-->
    <!--          </div>-->
    <!--        </el-badge>-->
    <!--      </template>-->
    <!--      <template #default>-->
    <!--        <div class="shopping-cart-list" v-if="store.shoppingCartList && store.shoppingCartList.length > 0">-->
    <!--          <div class="list-items" v-for="(item, index) in store.shoppingCartList" :key="item.id">-->
    <!--            <template v-if="index < 10">-->
    <!--              <div class="list-items__icon"></div>-->
    <!--              <div class="one-ell" style="max-width: 160px">{{ item.productChinese }}</div>-->
    <!--            </template>-->
    <!--            <template v-if="index > 9">-->
    <!--              <div class="one-ell" style="max-width: 160px; margin-left: 15px">.....</div>-->
    <!--            </template>-->
    <!--          </div>-->
    <!--        </div>-->
    <!--        <div class="shopping-cart-list" v-else>这里啥也没有~</div>-->
    <!--      </template>-->
    <!--    </el-popover>-->
    <el-popover placement="left" title="" popper-class="roast-popover" :width="225" trigger="hover">
      <template #default>
        <div>如有不满意，可以随时吐槽一下~</div>
      </template>
      <template #reference>
        <div class="flex-column gap-5 roast-flaot" @click.stop="openRoastDialog">
          <IconPen style="flex-shrink: 0" />
          <div class="text-n-no">吐槽一下</div>
        </div>
      </template>
    </el-popover>

    <!-- <PublicDialog
      ref="orderFormDialogRef"
      width="1100px"
      :dialogStyle="{ width: '1100px' }"
      :title="orderFormDialogTitle"
      :showFooterButton="false"
      custom-close
      align-center
      :titleCenter="false"
      append-to-body
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="beforeClose"
    >
      <OrderForm
        ref="orderFormRef"
        :type="orderFormType"
        :orderId="orderFormId"
        @success="createOrderSuccess"
        @error="orderFormDialogRef?.close()"
      />
    </PublicDialog> -->

    <ConfirmUpload ref="ConfirmUploadRef" @success="handleQuery" />
    <ConfrimDeliverGoods
      ref="ConfrimDeliverGoodsRef"
      :title="ConfrimDeliverGoodsTitle"
      :flag="logisticFlag"
      @success="handleQuery"
    />
    <ModelAddressUpdateTips ref="ModelAddressUpdateTipsRef" @change="addressUpdateTipsChange" />
    <UpdateModel ref="UpdateModelRef" @success="handleQuery" @change="checkModelAddressChange" />
    <MatchingFeedbackCase ref="MatchingFeedbackCaseRef" />
    <ApplyRefund ref="ApplyRefundRef" :refundType="2" @success="handleQuery" />
    <!-- <Voucher ref="VoucherRef" /> -->
    <MessageBoxDialog
      ref="MessageBoxDialogRef"
      title=""
      :content="MsgBoxDialogContent"
      :warningText="MsgBoxDialogWarning"
      confirmText="确认"
      @confirm="msgBoxConfirm"
    >
      <div class="flex-column gap-10" v-if="MsgBoxDialogType === '确认模特2'">
        <div class="flex-center gap-5 message-box-model">
          <el-avatar
            class="model-avatar"
            icon="UserFilled"
            :src="$picUrl + MsgBoxDialogOrderData?.shootModel?.modelPic + '!3x4'"
          />
          <div>
            <div>
              <span>{{ MsgBoxDialogOrderData.shootModel.name }}</span>
              <span>
                {{
                  MsgBoxDialogOrderData.shootModel?.account
                    ? `(ID:${MsgBoxDialogOrderData.shootModel.account})`
                    : ''
                }}
              </span>
            </div>
            <div class="flex-start gap-10" style="margin-bottom: 3px">
              <NationTag :type="MsgBoxDialogOrderData.shootModel?.nation" isIcon color="var(--text-color)" />
              <ModelTypeTag :type="MsgBoxDialogOrderData.shootModel?.type" isIcon color="var(--text-color)" />
            </div>
          </div>
        </div>
        <div style="padding: 0px 55px">
          请确认最终选定的视频拍摄模特，订单将流转至待完成状态，以便我们安排后续拍摄事宜~
        </div>
      </div>
    </MessageBoxDialog>
    <MessageBoxDialog ref="MessageHintDialogRef" :title="MessageHintDialogTitle" content="">
      <h3>{{ MessageHintDialogContent }}</h3>
      <template #button>
        <el-button class="btn-big" type="primary" round @click="msgHintConfirm">确认</el-button>
      </template>
    </MessageBoxDialog>

    <ModelInfoPopover ref="ModelInfoPopoverRef" @details="handleOpenModelDetails" />
    <ModelDetailDialog ref="ModelDetailDialogRef" />
    <CheckAndAcceptMaterial ref="CheckAndAcceptMaterialRef" @success="handleQuery" />
    <Roast ref="RoastRef" @success="handleQuery" />

    <el-dialog
      v-if="orderRestart"
      :model-value="true"
      title="订单开启"
      width="400px"
      show-close
      align-center
      :close-on-click-modal="false"
      @close="onQuery"
    >
      <div class="flex-column gap-10">
        <el-icon :size="55" color="var(--el-color-success)"><CircleCheckFilled /></el-icon>
        <div style="font-size: 18px; color: var(--text-color); margin: 8px 0 15px">订单开启成功~</div>
      </div>
      <template #footer>
        <div class="flex-center">
          <el-button @click="onQuery">好的</el-button>
          <el-button type="primary" @click="submitOrderRestart">立即支付</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-if="orderStatusUpdateTip"
      :model-value="true"
      title=""
      width="400px"
      show-close
      align-center
      :close-on-click-modal="false"
      @close="onQuery"
    >
      <div class="flex-column gap-10">
        <el-icon :size="65" color="var(--el-color-warning)"><Warning /></el-icon>
        <div style="font-size: 18px; color: var(--text-color); margin: 8px 0 15px">
          {{ orderStatusUpdateTip }}
        </div>
      </div>
      <template #footer>
        <div class="flex-center">
          <el-button type="primary" round @click="onQuery">刷新列表</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import OrderMergeListItem from '@/views/order/components/OrderMergeListItem.vue'
import OrderListItem from '@/views/order/components/OrderListItem.vue'
// import OrderForm from '@/views/order/components/OrderForm.vue'
import ConfirmUpload from '@/views/order/components/dialog/ConfirmUpload.vue'
import UpdateModel from '@/views/order/components/dialog/UpdateModel.vue'
import ConfrimDeliverGoods from '@/views/order/components/dialog/ConfirmDeliverGoods.vue'
import ModelAddressUpdateTips from '@/views/order/components/dialog/ModelAddressUpdateTips.vue'
import MatchingFeedbackCase from '@/views/order/components/dialog/MatchingFeedbackCase.vue'
import CheckAndAcceptMaterial from '@/views/order/components/dialog/CheckAndAcceptMaterial.vue'
import Roast from '@/views/order/components/dialog/Roast.vue'
import ApplyRefund from '@/views/order/components/dialog/ApplyRefund.vue'
// import Voucher from '@/views/order/components/dialog/Voucher.vue'
import MessageBoxDialog from '@/components/public/dialog/MessageBoxDialog.vue'
import ModelInfoPopover from '@/components/public/ModelInfoPopover.vue'
import ModelDetailDialog from '@/views/model/components/ModelDetailsDialog.vue'
import SelectV2 from '@/components/public/select/SelectV2.vue'
import DownloadButton from '@/components/public/button/DownloadButton.vue'
import IconPen from '@/components/icons/IconPen.vue'
import NationTag from '@/components/public/tag/NationTag.vue'
import ModelTypeTag from '@/components/public/tag/ModelTypeTag.vue'
import { ref, getCurrentInstance, computed, onMounted } from 'vue'
import { ORDER_STATUS } from '@/utils/order'
import type { QueryParams, FormCompType } from '../type'
import {
  needPayOrderList,
  orderList,
  orderListCount,
  mergeOrder,
  checkCancelMergeOrder,
  cancelMergeOrder,
  orderStatusCount,
  doRemind,
  orderMerchantList,
} from '@/api/order'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import useOrderApi from '@/hooks/useOrderApi'
import { useDefer } from '@/hooks/useDefer'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { MessageBox } from '@/utils/message'
import copy from '@/utils/copy'
import { useTooltips } from '@/hooks/useTooltips'
import currency from 'currency.js'

const { defer, updateDefer } = useDefer(30)

const { handleCancelOrder, handleConfirmModel, handleConfirmProduct, checkModelAddress, handleReopenOrder } =
  useOrderApi()

const { showTooltips } = useTooltips()

const { proxy } = getCurrentInstance() as any

const router = useRouter()

const store = useUserStore()

const { biz_model_platform } = proxy.useDict('biz_model_platform')

const tabList = ref<
  {
    label: string
    value: ORDER_STATUS | ''
    number?: number
    tip?: string
  }[]
>([
  { label: '所有订单', value: '' },
  { label: '需支付', value: ORDER_STATUS['需支付'], number: 0, tip: `order-tab-${ORDER_STATUS['需支付']}` },
  { label: '待审核', value: ORDER_STATUS['待审核'], number: 0, tip: `order-tab-${ORDER_STATUS['待审核']}` },
  { label: '待匹配', value: ORDER_STATUS['待匹配'], number: 0, tip: `order-tab-${ORDER_STATUS['待匹配']}` },
  { label: '需发货', value: ORDER_STATUS['需发货'], number: 0, tip: `order-tab-${ORDER_STATUS['需发货']}` },
  { label: '待完成', value: ORDER_STATUS['待完成'], number: 0, tip: `order-tab-${ORDER_STATUS['待完成']}` },
  { label: '需确认', value: ORDER_STATUS['需确认'], number: 0, tip: `order-tab-${ORDER_STATUS['需确认']}` },
  { label: '已完成', value: ORDER_STATUS['已完成'], number: 0 },
])
const curTab = ref<ORDER_STATUS | ''>('')

// const searchSelectList: {
//   label: string
//   value: SearchSelect
// }[] = [
//   { label: '订单号', value: 'orderNum' },
//   { label: '视频编码', value: 'videoCode' },
//   { label: '产品名称', value: 'productName' },
//   { label: '模特姓名', value: 'modelName' },
//   { label: '模特ID', value: 'modelAccount' },
// ]

const orderStatusList = ref<
  {
    label: string
    value: number
  }[]
>([])

const checkAll = ref(false)
const isIndeterminate = ref(false)
const checkedOrders = ref<any[]>([])
const checkedOrderList = ref<any[]>([])
const submitLoading = ref(false)
const totalAmount = computed(() => {
  let amount = 0
  if (checkedOrderList.value.length) {
    checkedOrderList.value.forEach(item => {
      if (item.mergePayAmount) amount = currency(amount).add(item.mergePayAmount).value
    })
  }
  return amount.toFixed(2)
})
const allCheckedDisabled = computed(() => {
  return tableData.value.filter(item => !item.mergeId)?.length === 0
})

const handleCheckAllChange = (val: boolean) => {
  checkedOrders.value = []
  checkedOrderList.value = []
  if (val) {
    tableData.value.forEach(item => {
      if (!item.mergeId && item.orderListVOS?.length) {
        checkedOrders.value.push(item.orderListVOS[0].orderNum)
        checkedOrderList.value.push(item)
      }
    })
  }
  isIndeterminate.value = false
}
const handleCheckedOrdersChange = (value: string[]) => {
  let isCheckArr = tableData.value.filter(item => !item.mergeId)
  if (checkedOrders.value.length != checkedOrderList.value.length) {
    checkedOrderList.value = isCheckArr.filter(item => {
      if (item.orderListVOS?.length) {
        return value.includes(item.orderListVOS[0].orderNum)
      }
      return false
    })
  }
  const checkedCount = value.length
  checkAll.value = checkedCount === isCheckArr.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < isCheckArr.length
}
function resetChecked() {
  checkedOrders.value = []
  checkedOrderList.value = []
  checkAll.value = false
  isIndeterminate.value = false
  submitLoading.value = false
}
// 合并支付
function onSubmit() {
  if (!checkedOrders.value.length) return
  if (checkedOrders.value.length === 1) {
    toPayPage(checkedOrders.value[0], null, null)
    return
  }
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在提交中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  submitLoading.value = true
  mergeOrder(checkedOrders.value)
    .then(res => {
      if (res.code === 200 && res.data) {
        toPayPage('', res.data, null)
      }
    })
    .catch(err => {
      if (err.data?.msg?.indexOf('订单') > -1) {
        orderStatusUpdateTip.value = '订单状态变更'
      }
    })
    .finally(() => {
      el_loading.close()
      submitLoading.value = false
    })
}

// const payTypeOptions = ref<
//   {
//     label: string
//     value: PAY_TYPE
//   }[]
// >([
//   { label: '微信支付', value: PAY_TYPE['微信支付'] },
//   { label: '支付宝支付', value: PAY_TYPE['支付宝支付'] },
//   { label: '对公转账', value: PAY_TYPE['对公转账'] },
//   { label: '银行卡转账', value: PAY_TYPE['银行卡转账'] },
// ])

function init() {
  // 关键字搜索
  if (router.currentRoute.value.query.keyword) {
    queryParams.value.val = router.currentRoute.value.query.keyword as string
  }
  // 订单状态搜索
  if (router.currentRoute.value.query.tab) {
    let tab = tabList.value.find(item => item.value == (router.currentRoute.value.query.tab as string))
    if (tab) {
      curTab.value = tab.value
    }
  } else if (router.currentRoute.value.query.tabs) {
    let tabs: any = (router.currentRoute.value.query.tabs as string).split(',')
    queryParams.value.status = tabs.map((item: any) => parseInt(item))
  }
  // 带入下单运营
  if (router.currentRoute.value.query.u && !store.isOwnerAcc() && curTab.value != ORDER_STATUS['需支付']) {
    queryParams.value.createOrderUserName.push(store.userInfo.name)
  }
  Object.entries(ORDER_STATUS).forEach(([key, value]) => {
    if (typeof value === 'number') {
      if (value != 3) {
        orderStatusList.value.push({
          label: key,
          value,
        })
      }
    }
  })

  // Object.entries(AFTER_SALE_STATUS).forEach(([key, value]) => {
  //   if(typeof value === 'number'){
  //     orderStatusList.value.push({
  //       label: key,
  //       value
  //     })
  //   }
  // })

  // Object.entries(PAY_TYPE).forEach(([key, value]) => {
  //   if(typeof value === 'number'){
  //     payTypeOptions.value.push({
  //       label: key,
  //       value
  //     })
  //   }
  // })

  orderListCount().then(res => {
    if (res.data) {
      orderTotal.value = res.data
    }
  })
  onQuery()
  getTabsCount()
  getCartCount()
  getOrderCartList()
}

const queryParams = ref<QueryParams>({
  val: '',
  select: 'videoCode',
  createOrderUserName: [],
  platform: [],
  orderUserId: [],
  status: [],
  payType: [],
  placeOrderTime: [],
  playTime: [],
})

function resetQuery() {
  queryParams.value = {
    val: '',
    select: 'videoCode',
    createOrderUserName: [],
    platform: [],
    orderUserId: [],
    status: [],
    payType: [],
    placeOrderTime: [],
    playTime: [],
  }
  if (!store.isOwnerAcc() && curTab.value != '' && curTab.value != ORDER_STATUS['需支付']) {
    queryParams.value.createOrderUserName.push(store.userInfo.name)
  }
  onQuery()
}

const tableData = ref<any[]>([])
const loading = ref(true)
const pageNum = ref(1)
const pageSize = ref(50)
const pageSizes = [10, 20, 30, 50]
const total = ref(0)
const orderTotal = ref(0)
let randomStr = ''

// const orderFormDialogRef = ref()
// const orderFormDialogTitle = ref('创建订单')
// const orderFormType = ref<FormCompType>('add')
// const orderFormId = ref('')
const ConfirmUploadRef = ref<InstanceType<typeof ConfirmUpload>>()
const ConfrimDeliverGoodsRef = ref<InstanceType<typeof ConfrimDeliverGoods>>()
const ConfrimDeliverGoodsTitle = ref('确认发货')
const ModelAddressUpdateTipsRef = ref<InstanceType<typeof ModelAddressUpdateTips>>()
const UpdateModelRef = ref<InstanceType<typeof UpdateModel>>()
const logisticFlag = ref(false)
const MatchingFeedbackCaseRef = ref<InstanceType<typeof MatchingFeedbackCase>>()
const ApplyRefundRef = ref<InstanceType<typeof ApplyRefund>>()
// const VoucherRef = ref<InstanceType<typeof Voucher>>()
const MessageBoxDialogRef = ref<InstanceType<typeof MessageBoxDialog>>()
const MessageHintDialogRef = ref<InstanceType<typeof MessageBoxDialog>>()
const MsgBoxDialogContent = ref('确认模特？')
const MessageHintDialogTitle = ref()
const MessageHintDialogContent = ref()
// const MsgBoxDialogTitle = ref()
const MsgBoxDialogWarning = ref('')
const MsgBoxDialogType = ref('确认模特')
const MsgBoxDialogOrderId = ref(0)
const MsgBoxDialogOrderData = ref<any>({})
const ModelInfoPopoverRef = ref<InstanceType<typeof ModelInfoPopover>>()
const ModelDetailDialogRef = ref<InstanceType<typeof ModelDetailDialog>>()
// 验收素材
const CheckAndAcceptMaterialRef = ref<InstanceType<typeof CheckAndAcceptMaterial>>()
// 吐槽
const RoastRef = ref<InstanceType<typeof Roast>>()
// 开启订单
const orderRestart = ref('')
// 订单状态变更提示
const orderStatusUpdateTip = ref('')

// tab切换
function handleTabChange(name: ORDER_STATUS | '') {
  curTab.value = name
  if (name == ORDER_STATUS['需支付']) {
    tableData.value = []
  }
  resetQuery()
}

const noOrderDate = computed(() => {
  if (curTab.value != '') {
    let t = tabList.value.find(item => item.value == curTab.value)
    if (t && !t.number) {
      return true
    }
  }
  return false
})

// 获取订单各个状态数量
function getTabsCount() {
  orderStatusCount().then(res => {
    if (res.data) {
      tabList.value[1].number = res.data.unPayCount
      tabList.value[2].number = res.data.unCheckCount
      tabList.value[3].number = res.data.unMatchCount
      tabList.value[4].number = res.data.unFilledCount
      tabList.value[5].number = res.data.unFinishedCount
      tabList.value[6].number = res.data.needConfirmCount
      tabList.value[7].number = res.data.finishedCount
    }
  })
}

// const orderFormRef = ref<InstanceType<typeof OrderForm>>()
//创建订单关闭前的回调
// function beforeClose(close: () => void) {
//   orderFormRef.value?.saveOrderForm()
//   close()
// }

function getCartCount() {
  store.getShoppingCartCount()
}

//获取购物车列表
function getOrderCartList() {
  store.getShoppingCartList()
}

// 搜索
function onQuery() {
  pageNum.value = 1
  orderStatusUpdateTip.value = ''
  if (curTab.value == ORDER_STATUS['需支付']) {
    resetChecked()
  }
  getMyOrderList(handleParams(), true)
}
function handleQuery(isUpdate: any = false) {
  getMyOrderList(handleParams(), isUpdate)
}
function handleParams() {
  let { val, select, placeOrderTime, playTime, ...params } = queryParams.value
  if (curTab.value !== '') {
    params.status = [curTab.value]
  }
  Object.keys(params).forEach(key => {
    if (Array.isArray(params[key])) {
      params[key] = params[key].join(',')
    }
  })
  // if(select){
  //   params[select] = val
  // }
  params.keyword = val ? val.trim() : ''
  if (placeOrderTime && placeOrderTime.length) {
    params.orderTimeBegin = placeOrderTime[0]
    params.orderTimeEnd = placeOrderTime[1]
  }
  if (playTime && playTime.length) {
    params.payTimeBegin = playTime[0]
    params.payTimeEnd = playTime[1]
  }
  return params
}
// 分页跳转
function pageChange(page: { pageNum: number; pageSize: number }) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  getMyOrderList(handleParams())
}
function getMyOrderList(params: QueryParams = {}, isUpdate: any = false) {
  params.pageNum = pageNum.value
  params.pageSize = pageSize.value

  if (!loading.value) {
    getTabsCount()
    getCartCount()
    getOrderCartList()
  }
  loading.value = true
  // 只加载最近一次请求
  randomStr = Math.random().toString(36).substring(2)
  let checkStr = randomStr

  orderRestart.value = ''
  if (curTab.value == ORDER_STATUS['需支付']) {
    params.pageSize = 300
    needPayOrderList(params)
      .then(res => {
        if (checkStr === randomStr) {
          tableData.value = res.data.rows
          total.value = res.data.total
          if (isUpdate) updateDefer()
        }
      })
      .finally(() => {
        if (checkStr === randomStr) loading.value = false
      })
    return
  }
  orderList(params)
    .then(res => {
      if (checkStr === randomStr) {
        tableData.value = res.data.rows
        total.value = res.data.total
        if (isUpdate) updateDefer()
      }
    })
    .finally(() => {
      if (checkStr === randomStr) loading.value = false
    })
}
// 创建订单
function handleAddOrder() {
  store.realTimeCheckVip().then(() => {
    // orderFormDialogTitle.value = '创建订单'
    // orderFormType.value = 'add'
    // orderFormId.value = ''
    // orderFormDialogRef.value.open()
    router.push('/order/create')
  })
}

// function createOrderSuccess(res: any) {
//   // console.log(orderFormDialogRef.value);

//   orderFormDialogRef.value.close()

//   if (res) {
//     router.push({ name: 'order-pay', state: { orderNum: res.orderNum } })
//   } else {
//     onQuery()
//   }
// }
// 表格按钮处理
function handleButtonAction(btn: string, row: any) {
  // console.log(btn)
  if (btn == '查看详情') {
    // router.push({ path: '/order/details/' + row.id })
    routerNewWindow('/order/details/' + row.id)
    return
  }
  switch (btn) {
    case '取消订单':
      handleCancelOrder(row.orderNum, () => {
        if (curTab.value == ORDER_STATUS['需支付']) {
          resetChecked()
        }
        handleQuery(true)
      })
      break
    case '继续支付':
      if (!store.isVip()) {
        ElMessage({
          type: 'warning',
          message: store.isViped() ? '您的会员已过期，请开通会员后再进行操作！' : '请先开通会员！',
          duration: 2000,
        })
      } else {
        router.push({ name: 'order-pay', state: { orderNum: row.orderNum, payType: row.payType } })
      }
      break
    case '修改订单':
      // orderFormDialogTitle.value = '修改订单'
      // orderFormType.value = 'edit'
      // orderFormId.value = row.id
      // orderFormDialogRef.value.open()
      if (!store.isVip()) {
        ElMessage({
          type: 'warning',
          message: store.isViped() ? '您的会员已过期，请开通会员后再进行操作！' : '请先开通会员！',
          duration: 2000,
        })
      } else {
        routerNewWindow('/order/edit/' + row.id)
      }
      break
    case '申请退款':
      ApplyRefundRef.value?.open(row.id)
      break
    case '匹配情况反馈':
      MatchingFeedbackCaseRef.value?.open(row.id)
      break
    case '发货':
      ConfrimDeliverGoodsTitle.value = '订单发货信息'
      logisticFlag.value = row.logisticFlag === 1
      // 检测模特收货地址是否更新
      checkModelAddressChange(row.id, 1)
      break
    // case '补发':
    //   ConfrimDeliverGoodsTitle.value = '确认补发'
    //   logisticFlag.value = row.logisticFlag === 1
    //   // 检测模特收货地址是否更新
    //   checkModelAddressChange(row.id, 0)
    //   break
    case '更换模特':
      logisticFlag.value = row.logisticFlag === 1
      UpdateModelRef.value?.open(row)
      // UpdateModelRef.value?.open(row.id, row.shootModel.account, row.isObject)
      break
    case '确认模特':
      MsgBoxDialogOrderId.value = row.id
      MsgBoxDialogContent.value = '确认模特？'
      MsgBoxDialogWarning.value = ''
      MsgBoxDialogType.value = '确认模特'
      MessageBoxDialogRef.value?.open()
      break
    case '确认模特2':
      MsgBoxDialogOrderId.value = row.id
      MsgBoxDialogOrderData.value = row
      MsgBoxDialogType.value = '确认模特2'
      MessageBoxDialogRef.value?.open()
      break
    case '催一催':
      reminderOrderDialog(row)
      break
    case '验收素材':
      CheckAndAcceptMaterialRef.value?.open(row.id, row.status, {
        link: row.productLink,
        shootModelType: row.shootModel?.type || '',
        platform: row.platform,
        modelType: row.modelType,
        nation: row.shootingCountry,
      })
      break
    case '可以上传':
      ConfirmUploadRef.value?.open(row.id, false)
      break
    case '取消上传':
      ConfirmUploadRef.value?.open(row.id, true)
      break
    case '查看链接':
      ElMessageBox.confirm(row.uploadPlatformUrl, '素材链接', {
        confirmButtonText: '复制链接',
        center: true,
      }).then(() => {
        copy(row.uploadPlatformUrl)
      })
      break
    case '吐槽一下':
      RoastRef.value?.open(row.id)
      break
    default:
      break
  }
}

// 订单重启去支付
function submitOrderRestart() {
  toPayPage(orderRestart.value, null, null)
}

function openRoastDialog() {
  RoastRef.value?.open()
}

function routerNewWindow(path: string) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}
function reminderOrderDialog(row: any) {
  doRemind({ videoId: row.id })
    .then(res => {
      if (res.code == 200) {
        MessageHintDialogContent.value = res.data
          ? '已催促模特加速完成任务，请您耐心等待~'
          : '您今天已经催过啦，请明天再来 ~'
        MessageHintDialogTitle.value = '催一催'
        MessageHintDialogRef.value?.open()
        // const content = res.data ? '已催促模特加速完成任务，请您耐心等待~' : '您今天已经催过啦，请明天再来 ~'
        // ElMessageBox.alert(`${content}`, '催一催', {
        //   confirmButtonText: '确认',
        //   showClose: false,
        //   roundButton: true,
        // })
      }
    })
    .catch(err => {})
}
// 表格表头按钮
function handleHeadButtonAction(btn: string, data: any) {
  // console.log(btn, data);
  // if(btn === '凭证单') {
  // VoucherRef.value?.open(data[0].orderNum)
  // } else
  if (btn === '取消订单') {
    handleCancelOrder(
      data[0].orderNum,
      () => {
        if (curTab.value == ORDER_STATUS['需支付']) {
          resetChecked()
        }
        handleQuery(true)
      },
      e => {
        if (e.data?.msg?.indexOf('订单') > -1) {
          orderStatusUpdateTip.value = '订单状态变更'
        }
      }
    )
  } else if (btn === '开启订单') {
    handleReopenOrder(data[0].orderNum, () => {
      orderRestart.value = data[0].orderNum
    })
  } else if (btn === '取消合并') {
    handleCancelMerge(data.mergeId)
  } else if (btn === '订单状态变更') {
    orderStatusUpdateTip.value = '订单状态变更'
  }
}
// 模特浮窗
function handleModelHover(el: EventTarget, id: any, show: any) {
  if (show) {
    ModelInfoPopoverRef.value?.open(el, id)
  } else {
    ModelInfoPopoverRef.value?.close()
  }
}
// 确认模特/确认成品
function msgBoxConfirm(close: () => void) {
  if (MsgBoxDialogType.value == '确认模特' || MsgBoxDialogType.value == '确认模特2') {
    handleConfirmModel(MsgBoxDialogOrderId.value, () => {
      close()
      handleQuery()
    })
  } else if (MsgBoxDialogType.value == '确认成品') {
    handleConfirmProduct(MsgBoxDialogOrderId.value, () => {
      close()
      handleQuery()
    })
  }
}
function msgHintConfirm() {
  MessageHintDialogRef.value?.close()
}

// 模特收货地址更新确认
function addressUpdateTipsChange(id: any, y: number) {
  if (y === 0) {
    ConfrimDeliverGoodsTitle.value = '订单发货信息'
    ConfrimDeliverGoodsRef.value?.open(id)
  }
}
// 检测模特收货地址是否更新
function checkModelAddressChange(id: any, r: 0 | 1) {
  ConfrimDeliverGoodsRef.value?.open(id, r)
  // checkModelAddress(id, (res: any) => {
  //   if (res.data.code == 200) {
  //     ConfrimDeliverGoodsRef.value?.open(id, r)
  //   }
  //   if (res.data.code == 500) {
  //     ModelAddressUpdateTipsRef.value?.open(id)
  //   }
  // })
}
// 模特详情
function handleOpenModelDetails(id: any) {
  ModelDetailDialogRef.value?.open(id)
}
// 取消合并
function handleCancelMerge(mergeId: any) {
  const el_loading = ElLoading.service({
    lock: true,
    text: '执行中...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  checkCancelMergeOrder({ mergeId })
    .then(res => {
      if (res.code == 200) {
        let msg =
          '取消合并支付后，可直接选择订单支付，或重新勾选需合并支付的订单并合并支付，是否确认取消合并支付？'
        if (res.data?.length) {
          let dom = ''
          res.data.forEach((item: any) => {
            dom += `<div>${item}</div>`
          })
          msg = `取消合并支付后，订单：
            <div style="max-height: 200px;width: 368px;overflow-y: auto;">${dom}</div>
            因到期将会自动关闭，是否确认取消合并支付？`
        }
        MessageBox(msg).then(() => {
          const el_loading = ElLoading.service({
            lock: true,
            text: '正在取消合并...',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          cancelMergeOrder({ mergeId })
            .then(res => {
              ElMessage.success('取消成功')
              handleQuery(true)
            })
            .catch(err => {
              if (err.data?.msg?.indexOf('订单') > -1) {
                orderStatusUpdateTip.value = '订单状态变更'
              }
            })
            .finally(() => {
              el_loading.close()
            })
        })
      }
    })
    .finally(() => el_loading.close())
}

// 跳支付页
function toPayPage(orderNum: any, mergeId: any, payType: any) {
  if (!orderNum && !mergeId) {
    ElMessage.error('参数错误！')
    throw new Error('Missing key field')
  }
  store.realTimeCheckVip().then(() => {
    if (store.isVip()) {
      router.push({
        name: 'order-pay',
        state: { orderNum, mergeId, payType },
      })
    } else {
      ElMessage.error('请先开通会员！')
    }
  })
}

onMounted(() => {
  // if (sessionStorage.getItem('createOrderModelInfo')) {
  //   handleAddOrder()
  // } else if (sessionStorage.getItem('createOrderAgain')) {
  //   sessionStorage.removeItem('createOrderAgain')
  //   handleAddOrder()
  // }
})

init()
</script>

<style scoped lang="scss">
.no-drag {
  -webkit-user-select: none; /* Chrome 和 Safari */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* IE10+ */
  user-select: none; /* Non-prefixed version, currently supported by Chrome, Opera, Edge, Firefox */
}
.w-bg {
  margin-top: 20px;
}
.tooltip-icon {
  margin-right: -5px;
  -moz-transform: translateY(1px);
}
.search-box {
  padding: 4px 20px;
  position: relative;

  // :deep(.el-form) {
  //   .el-form-item {
  //     margin: ;
  //   }
  // }
}
.submit-box {
  width: 100%;
  height: 60px;
  background: #fff;
  padding: 0 20px;
  box-sizing: border-box;
  position: sticky;
  left: 0;
  bottom: -10px;
  z-index: 9;
  border-top: 1px solid #ebeef5;
  border-radius: 0 0 14px 14px;

  :deep(.el-checkbox) {
    margin: 0 10px 0 0;
    --el-checkbox-input-width: 16px;
    --el-checkbox-input-height: 16px;
    --el-checkbox-border-radius: 4px;
    --el-checkbox-font-size: 16px;

    .el-checkbox__input {
      &.is-checked {
        .el-checkbox__inner {
          &::after {
            left: 5px;
            top: 2px;
          }
        }
      }
      &.is-indeterminate {
        .el-checkbox__inner {
          &::before {
            top: 6px;
          }
        }
      }
    }
  }

  .bottom-right {
    align-items: baseline;

    .text {
      color: var(--text-color);
      font-size: 14px;
      align-items: baseline;
    }

    .submit-button {
      padding: 18px 30px;
      font-size: 15px;
      font-weight: 500;
    }
  }

  .tip {
    font-size: 12px;
    color: #0006;
    transform: translateY(-4px);
  }
}
.no-data-button {
  width: 120px;
  height: 40px;
  border-radius: 20px;
  font-size: 16px;
  margin-top: 25px;
}
.roast-flaot {
  position: fixed;
  right: 33px;
  bottom: 100px;
  z-index: 3000;
  width: 44px;
  height: 44px;
  background-color: #fff;
  color: var(--el-color-primary);
  padding: 10px;
  border-radius: 50%;
  border: 1px solid #efefef;
  cursor: pointer;
  font-size: 13px;
  box-shadow: 0px 0px 8px 0px #0002;
}
.message-box-model {
  padding: 12px 16px;
  border-radius: 8px;
  background-color: #f1f4fb;
  color: var(--text-color);
}
.shopping-cart-badge {
  position: fixed;
  right: 33px;
  bottom: 175px;
  z-index: 3000;
}
.shopping-cart-flaot {
  cursor: pointer;
  flex-direction: column;
  width: 45px;
  height: 45px;
  background-color: var(--el-color-primary);
  color: #fff;
  padding: 10px;
  border-radius: 50%;
  box-shadow: 0 0 6px var(--el-color-primary);

  span {
    font-size: 13px;
  }

  &:hover {
    box-shadow: 0 0 10px #fff;
  }
}
.shopping-cart-list {
  padding: 0 8px;
  .list-items {
    margin-bottom: 5px;
    display: flex;
    align-items: baseline;
    &__icon {
      width: 8px;
      height: 8px;
      background-color: #5695c0;
      margin-right: 5px;
      border-radius: 50%;
    }
  }
}
.cart-list {
  height: 338px;
  width: 250px;
}
</style>

<style lang="scss">
.el-popover.el-popper.roast-popover {
  border-radius: 25px;
  text-align: center;
  color: var(--text-gray-color);

  .el-popper__arrow {
    display: none;
  }
}
</style>
