<template>
  <div class="order-form-box">
    <div class="flex-between form-header">
      <div class="flex-start gap-10" style="align-items: baseline">
        <Title>
          <!-- <span style="font-weight: 500" v-if="orderNum">{{ orderNum }}</span> -->
          <span style="font-weight: 600">&nbsp;订单&nbsp;{{ index + 1 }}</span>
        </Title>
        <el-tooltip
          v-if="index === 0"
          :visible="guideVisible"
          :offset="2"
          effect="light"
          content=""
          placement="top-start"
        >
          <div class="flex-start gap-5">
            填写指引
            <el-switch v-model="guideSwitch" size="small" @change="switchChange" />
          </div>
          <template #content>
            <div class="guide-tip-box">
              <div>您如果已知订单填写规则，可手动关闭填写指引哦~</div>
              <div class="flex-end btn">
                <el-button type="primary" link @click="guideVisible = false">我知道了</el-button>
              </div>
            </div>
          </template>
        </el-tooltip>
      </div>
      <div class="flex-start" v-if="!isEditing">
        <el-popover
          v-model:visible="popoverResetVisible"
          ref="popoverResetRef"
          placement="bottom"
          :width="207"
          trigger="click"
        >
          <template #reference>
            <el-button type="primary" link>清空</el-button>
          </template>
          <div>
            <div class="flex-start popconfirm-box">
              <el-icon color="var(--el-color-primary)"><WarningFilled /></el-icon>
              <span>
                是否清空
                <strong>订单{{ index + 1 }}</strong>
                中全部填写项？
              </span>
            </div>
            <div class="flex-end">
              <el-button size="small" @click="popoverResetVisible = false">取消</el-button>
              <el-button type="primary" size="small" @click="handleReset">确定</el-button>
            </div>
          </div>
        </el-popover>
        <el-popover
          v-if="isShowDelete"
          v-model:visible="popoverDeleteVisible"
          ref="popoverResetRef"
          placement="bottom"
          :width="207"
          trigger="click"
        >
          <template #reference>
            <el-button type="danger" link>删除</el-button>
          </template>
          <div>
            <div class="flex-start popconfirm-box">
              <el-icon color="var(--el-color-danger)"><WarningFilled /></el-icon>
              <span>
                删除后无法恢复，是否确认删除
                <strong>订单{{ index + 1 }}</strong>
                ？
              </span>
            </div>
            <div class="flex-end">
              <el-button size="small" @click="popoverDeleteVisible = false">取消</el-button>
              <el-button type="primary" size="small" @click="handleDeleteOrder(index)">确定</el-button>
            </div>
          </div>
        </el-popover>
      </div>
    </div>

    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :disabled="formDisabled"
      :validate-on-rule-change="false"
      label-width="85px"
      class="order-form"
    >
      <el-row :gutter="20" style="margin-right: 0">
        <el-col :span="12">
          <div class="form-back">
            <el-form-item label="产品链接" prop="isLink" v-if="form.isLink == 1">
              <template #label>
                <div @mouseenter="handleShowTooltips($event, 'order-create-productLink')">产品链接</div>
              </template>
              <div class="flex-start gap-10" style="width: 100%">
                <el-input
                  maxlength="1000"
                  v-model.trim="form.productLink"
                  placeholder="请输入产品链接"
                  clearable
                  :disabled="isPromotionDisabled"
                  @input="productLinkButtonError = false"
                />
                <el-button
                  v-if="!productLinkButtonDisabled"
                  @mouseenter="handleMouseEnterLinkButton"
                  type="primary"
                  :disabled="productLinkButtonDisabled"
                  @click="getLinkApi"
                >
                  获取产品信息
                </el-button>
                <div
                  class="el-button el-button--primary el-button--default is-disabled product-disabled-button"
                  @mouseenter="handleMouseEnterLinkButton"
                  v-else-if="!isEditing"
                >
                  获取产品信息
                </div>
              </div>
              <div class="guide-text" v-if="!isSelectLinkDisabled">
                <span>暂无产品链接？你也可以</span>
                <el-button type="primary" link :disabled="isSelectLinkDisabled" @click="changeIsLink(0)">
                  手动上传图片
                </el-button>
              </div>
              <div class="guide-text" v-if="form.modelType != '1'">请填入产品链接，以便模特上传视频</div>
            </el-form-item>
            <el-form-item v-if="!form.isLink" label="产品图" prop="productPicInfo">
              <div class="image-list" v-for="(item, i) in form.filePicList" :key="item.id">
                <div
                  v-if="!isPromotionDisabled"
                  @click.stop="doDeleteProductUrl(i)"
                  class="image-modal flex-around"
                >
                  <!-- v-if="props.type === 'add' || props.type === 'editCartOrder'" -->
                  <img class="icon" src="@/assets/icon/icon_close.png" alt="" />
                </div>
                <el-image
                  ref="imgViewRef"
                  preview-teleported
                  :src="picUrlPath + item.picUrl + '!thumbnail200'"
                  fit="scale-down"
                  class="image-item"
                  :preview-src-list="item.picUrl ? [picUrlPath + item.picUrl] : []"
                />
              </div>
              <div
                class="text-n-no image-upload"
                @click="doShowSelectImage('product')"
                v-if="form.filePicList && form.filePicList.length < 1 && !isPromotionDisabled"
              >
                <el-icon size="18" color="#909399"><Plus /></el-icon>
                点击上传图片
              </div>
              <div class="guide-text" v-if="!isPromotionDisabled">
                <span>我有产品链接，我要</span>
                <el-button type="primary" link :disabled="isSelectLinkDisabled" @click="changeIsLink(1)">
                  填写产品链接
                </el-button>
              </div>
            </el-form-item>
            <el-form-item label="使用平台" prop="platform">
              <template #label>
                <div @mouseenter="handleShowTooltips($event, 'order-create-platform')">使用平台</div>
              </template>
              <el-radio-group v-model="form.platform" @change="changeFormValue($event, 'platform')">
                <el-radio-button
                  class="radio-button-w-small pd"
                  v-for="item in model_platform"
                  :key="item.value"
                  :value="item.value"
                  :label="item.value"
                  :disabled="(item.value == '3' && platformDisabled) || isPromotionDisabled"
                >
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
              <div class="guide-text" v-if="isGuideSwitch">
                请选择视频发布的平台，不同平台需提供的订单信息不同
              </div>
            </el-form-item>
            <el-form-item label="拍摄国家" prop="shootingCountry">
              <template #label>
                <div @mouseenter="handleShowTooltips($event, 'order-create-nation')">拍摄国家</div>
              </template>
              <el-radio-group
                v-model="form.shootingCountry"
                @change="changeFormValue($event, 'shootingCountry')"
                :disabled="isPromotionDisabled"
              >
                <el-radio-button
                  class="radio-button-w-small pd"
                  v-for="item in biz_nation"
                  :key="item.value"
                  :value="item.value"
                  :label="item.value"
                >
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
              <div class="guide-text" v-if="isGuideSwitch">我们将根据拍摄国家为您匹配当地模特</div>
            </el-form-item>
            <el-form-item label="产品名称" prop="productEnglish">
              <el-input
                v-model="form.productEnglish"
                placeholder="例如：women tank top"
                maxlength="225"
                clearable
              />
              <div class="guide-text" v-if="isGuideSwitch">请填写产品英文名，我们将会直接同步给拍摄模特</div>
            </el-form-item>
            <el-form-item label="中文名称" prop="productChinese">
              <el-input
                v-model="form.productChinese"
                placeholder="例如：女士背心"
                maxlength="225"
                clearable
              />
              <div class="guide-text" v-if="isGuideSwitch">请填写您要拍摄的产品中文名称</div>
            </el-form-item>
            <el-form-item label="产品卖点" prop="sellingPointProduct">
              <el-input
                v-model="form.sellingPointProduct"
                type="textarea"
                :placeholder="`请填写产品卖点(英文)提供给模特参考。\n注意：拍摄建议请勿填入此处`"
                :rows="6"
                maxlength="8000"
                style="font-family: none"
              />
              <!-- <span class="textarea-tips">*请用英文</span> -->
            </el-form-item>
          </div>
        </el-col>

        <el-col :span="12">
          <div class="form-back right-form-box" style="padding-right: 10px">
            <el-form-item label="视频数量" prop="shootCount" style="margin-bottom: 20px">
              <div>
                <el-input-number
                  style="width: 130px"
                  :precision="0"
                  v-model="form.shootCount"
                  :min="1"
                  :max="99"
                  :disabled="isEditing"
                  @change="changeShootCount"
                />

                <div v-if="!isEditing" class="coupon-box">
                  <img v-if="form.shootCount < 5" src="@/assets/image/un_coupon.png" alt="" />
                  <img v-else src="@/assets/image/coupon.png" alt="" />
                  <div class="flex-center coupon-amount">
                    <div class="del" v-if="form.shootCount < 5">
                      <span>￥</span>
                      <span class="num">100</span>
                    </div>
                    <span v-if="form.shootCount >= 5">￥</span>
                    <span class="num" v-if="form.shootCount >= 5">{{ form.shootCount * 20 }}</span>
                  </div>
                  <div class="flex-column coupon-text" :class="{ gray: form.shootCount < 5 }">
                    <div>同产品5单</div>
                    <div>立减100元</div>
                  </div>
                </div>
              </div>
              <div
                class="guide-text"
                style="margin-top: 8px"
                v-if="isGuideSwitch && form.platform == Platform['Amazon']"
              >
                亚马逊Listing中，Videos区域可上传10个产品视频,拍摄5个视频可抢占videos前5的核心位置,拍摄10个竞品视频将无法展示
              </div>
            </el-form-item>
            <el-form-item label="视频格式" prop="videoFormat">
              <template #label>
                <div @mouseenter="handleShowTooltips($event, 'order-create-videoFormat')">视频格式</div>
              </template>
              <el-radio-group v-model="form.videoFormat">
                <el-radio-button
                  class="radio-button-w-large"
                  v-for="item in videoFormatOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.value"
                  :disabled="Platform['Tiktok'] == form.platform"
                >
                  <div class="flex-center gap-5">
                    <img
                      v-if="item.value == form.videoFormat"
                      :style="{
                        transform: `rotate(${item.value == 1 ? 0 : 90}deg)`,
                      }"
                      style="height: 13px; width: 13px"
                      src="@/assets/icon/smartphone_sel.png"
                    />
                    <img
                      v-else
                      :style="{
                        transform: `rotate(${item.value == 1 ? -90 : 0}deg)`,
                      }"
                      style="height: 13px; width: 13px"
                      src="@/assets/icon/smartphone.png"
                    />
                    {{ item.label.slice(0, 4) }}
                  </div>
                </el-radio-button>
              </el-radio-group>
              <div class="guide-text" v-if="isGuideSwitch">
                <span v-if="form.platform == Platform['Amazon']">亚马逊可发布横/竖屏视频</span>
                <span v-else-if="form.platform == Platform['Tiktok']">TikTok仅可发布竖屏拍摄</span>
                <span v-else>可发布横/竖屏视频</span>
              </div>
            </el-form-item>
            <el-form-item label="模特类型" prop="modelType">
              <el-radio-group v-model="form.modelType" @change="changeFormValue($event, 'modelType')">
                <el-radio-button
                  class="radio-button-w-large"
                  v-for="item in biz_model_type"
                  :key="item.value"
                  :value="item.value"
                  :label="item.value"
                  :disabled="isSelModel"
                  @mouseenter="handleMouseEnterModelType($event, item.value)"
                >
                  {{ item.label }}
                </el-radio-button>
                <el-radio-button
                  class="radio-button-w-large"
                  value="3"
                  label="影/素都可以"
                  :disabled="isSelModel"
                />
              </el-radio-group>
              <div
                class="guide-text"
                v-if="isGuideSwitch && form.shootingCountry != nationOptionsMap['美国']"
              >
                <span>亚马逊影响者仅在</span>
                <span class="light">美国</span>
                <span>可提供服务</span>
              </div>
            </el-form-item>
            <el-form-item label="意向模特" prop="selModel">
              <template #label>
                <div @mouseenter="handleShowTooltips($event, 'order-create-intentionModel')">意向模特</div>
              </template>
              <div class="flex-start gap-10">
                <el-radio-group v-model="form.selModelType" style="min-width: 190px">
                  <el-radio-button
                    class="radio-button-w-large"
                    :value="0"
                    label="帮我匹配"
                    @click="handleSelModelType"
                  />
                  <el-radio-button
                    class="radio-button-w-large"
                    :value="1"
                    label="我要自选"
                    @click="openSelect"
                  />
                </el-radio-group>
              </div>
              <div class="model-info" v-if="form.selModel && form.selModel.length > 0">
                <TransitionGroup name="sel-model-list">
                  <template v-for="(item, i) in form.selModel" :key="item.id">
                    <div class="model-info__item" v-if="i < 11" :key="item.id">
                      <div
                        class="modal-lose flex-around"
                        v-if="cannotModelList && cannotModelList.includes(item.id)"
                        @click="openSelect()"
                      >
                        失效
                      </div>
                      <div class="play-modal flex-around" v-else>
                        <img
                          style="height: 24px; width: 24px"
                          @click="doDeleteModel(index, i)"
                          src="@/assets/icon/icon_delete_fff.png"
                          alt=""
                        />
                      </div>
                      <el-image class="head-img" :src="$picUrl + item.modelPic + '!3x4'" fit="fill">
                        <template #error>
                          <el-icon size="50"><Avatar /></el-icon>
                        </template>
                      </el-image>
                      <div
                        class="head-name more-ell"
                        :style="{ color: cannotModelList.includes(item.id) ? '#aaa' : '#000' }"
                      >
                        {{ item.name }}
                      </div>
                    </div>
                  </template>
                </TransitionGroup>

                <!-- 15 -->
                <div @click="doShowSelModelList" class="model-more" v-if="form.selModel?.length > 11">
                  <div class="model-more__icon"><span style="padding-bottom: 2px">· · ·</span></div>
                  <div class="model-more__text">更多</div>
                </div>

                <div class="flex-column" style="justify-content: center; align-items: flex-start">
                  <el-button type="primary" @click="openSelect">
                    {{ selectModelNumber > 0 ? '选择模特' : '重新选择' }}
                  </el-button>
                  <div
                    v-if="cannotModelNumber > 0"
                    style="color: #0006; line-height: 14px; margin: 5px 0; font-size: 12px"
                  >
                    有{{ cannotModelNumber }} 位模特不满足拍摄需求，请更换
                  </div>
                  <div
                    v-else-if="selectModelNumber > 0 && form.selModel && form.selModel.length > 0"
                    style="color: #0006; line-height: 14px; margin: 5px 0; font-size: 12px"
                  >
                    还可以选择{{ selectModelNumber }}位模特
                  </div>
                </div>
              </div>
              <div class="guide-text" v-if="isGuideSwitch">
                拍摄模特会优先匹配您的意向模特，若匹配失败，我们将会为您匹配更适合拍摄该产品的模特
              </div>
            </el-form-item>

            <el-form-item label="选配照片" prop="picCount" v-if="isEditing">
              <template #label>
                <div @mouseenter="handleShowTooltips($event, 'order-create-picCount')">选配照片</div>
              </template>
              <el-radio-group v-model="form.picCount" :disabled="isDisabledPicCount">
                <el-radio-button
                  v-if="form.picCount == -1"
                  class="radio-button-w-large"
                  :model-value="form.picCount"
                  :value="-1"
                  label="无需拍摄"
                />
                <template v-for="item in picCountOptions" :key="item.value">
                  <el-radio-button
                    class="radio-button-w-large"
                    v-if="form.picCount == item.value"
                    :model-value="form.picCount"
                    :value="item.value"
                    :label="item.value"
                  >
                    <span>
                      <span>{{ item.value == 1 ? '2张' : '5张' }}</span>
                      <span style="font-size: 12px; line-height: 14px">
                        /${{ item.value == 1 ? '10' : '20' }}
                      </span>
                    </span>
                  </el-radio-button>
                </template>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="选配照片" prop="picCount" v-else>
              <template #label>
                <div @mouseenter="handleShowTooltips($event, 'order-create-picCount')">选配照片</div>
              </template>
              <el-radio-group v-model="form.picCount" :disabled="isDisabledPicCount" @change="picCountChange">
                <el-radio-button class="radio-button-w-large" :value="-1" label="无需拍摄" />
                <el-radio-button
                  class="radio-button-w-large"
                  v-for="item in picCountOptions"
                  :key="item.value"
                  :value="item.value"
                  :label="item.value"
                  @mouseenter="picCountTooltips"
                >
                  <span>
                    <span>{{ item.value == 1 ? '2张' : '5张' }}</span>
                    <span style="font-size: 12px; line-height: 14px">
                      /${{ item.value == 1 ? '10' : '20' }}
                    </span>
                  </span>
                </el-radio-button>
              </el-radio-group>
              <div class="flex-start guide-text" v-if="isGuideSwitch">
                <span>可额外加购图片拍摄服务，查看</span>
                <el-button type="primary" link @click="lookPhoto">照片案例</el-button>
              </div>
            </el-form-item>
            <el-form-item
              label=""
              prop="referencePic"
              v-if="form.picCount != undefined && form.picCount > -1"
            >
              <div class="flex-start" style="flex-wrap: wrap; gap: 10px 0">
                <div class="image-list" v-for="(item, i) in form.referencePic" :key="item.id">
                  <div @click.stop="doDeletePicUrl(i)" class="image-modal flex-around">
                    <img class="icon" src="@/assets/icon/icon_close.png" alt="" />
                  </div>
                  <el-image
                    ref="imgViewRef"
                    preview-teleported
                    :src="picUrlPath + item.picUrl + '!thumbnail200'"
                    fit="scale-down"
                    class="image-item"
                    :preview-src-list="item.picUrl ? [picUrlPath + item.picUrl] : []"
                  >
                    <!-- <template #error>
                      <div class="image-error">
                        <el-icon :size="25"><Picture /></el-icon>
                      </div>
                    </template> -->
                  </el-image>
                </div>
                <div
                  class="text-n-no image-upload"
                  @click="doShowSelectImage('picCount')"
                  v-if="form.referencePic && form.referencePic.length < addPicNumber"
                >
                  <el-icon size="18" color="#909399"><Plus /></el-icon>
                  点击上传图片
                </div>
                <div class="prompt-text" style="width: 100%" v-if="form.picCount">
                  您可以上传{{ addPicNumber }}张拍照参考图，供拍摄模特参考（也可不上传）
                </div>
              </div>
            </el-form-item>
            <el-form-item label="拍摄建议" prop="shootSuggestType" style="margin-bottom: 10px">
              <el-radio-group
                v-model="form.shootSuggestType"
                @change="changeFormValue($event, 'shootSuggestType')"
              >
                <el-radio-button class="radio-button-w-large" value="null" label="无" />
                <el-radio-button class="radio-button-w-large" value="demands" label="拍摄建议" />
                <el-radio-button class="radio-button-w-large" value="link" label="参考视频" />
              </el-radio-group>
            </el-form-item>
            <el-form-item label="" prop="demands" v-if="form.shootSuggestType == 'demands'">
              <el-input
                v-model="form.demands"
                type="textarea"
                :placeholder="`请填写您期望模特拍摄的内容(英文)，我们将提供给模特作为参考，模特将结合自己的拍摄风格完成视频拍摄。\n注意：若有重要的拍摄要求，请尽可能的详细表达，避免后续的重复沟通`"
                :rows="6"
                maxlength="8000"
                style="font-family: none"
              />
              <!-- <span class="textarea-tips">*请用英文</span> -->
            </el-form-item>
            <el-form-item label="" prop="referenceVideoLink" v-else-if="form.shootSuggestType == 'link'">
              <el-input
                maxlength="1000"
                v-model="form.referenceVideoLink"
                placeholder="请输入参考视频的链接供模特拍摄参考"
                clearable
              />
            </el-form-item>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <el-dialog
      :close-on-click-modal="false"
      v-model="showHintDialog"
      :close-on-press-escape="false"
      :show-close="false"
      width="450"
      align-center
    >
      <div class="hint-dialog">
        <div>
          <svg class="svg-icon" aria-hidden="true" style="width: 20px; height: 20px; margin-right: 10px">
            <use xlink:href="#icon-tishi"></use>
          </svg>
        </div>
        <div>
          <div class="hint-dialog__title">{{ hintDialogTitle }}</div>
          <div class="hint-dialog__content">{{ hintDialogContent }}</div>
        </div>
      </div>
      <div class="hint-line"></div>

      <template #footer>
        <div class="footer-btn" style="padding-top: 15px">
          <el-button @click="handleDialogCancel">取消</el-button>
          <el-button type="primary" @click="handleDialogConfirm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Title from '@/components/public/Title.vue'
import { computed, getCurrentInstance, ref, toRef, watch } from 'vue'
import { chinese_c_reg } from '@/utils/RegExp'
import { picCountOptions, videoFormatOptions, nationOptionsMap } from '@/utils/data'
import { Platform } from '@/utils/dictType'
import { previewImage } from '@/utils/preview'
import { ElMessage, ElLoading } from 'element-plus'
import { crossTagSendMsg, CrossTagMsgType } from '@/hooks/crossTagMsg'
import { useTooltips } from '@/hooks/useTooltips'
import { getProductInfo } from '@/api/order'
import {
  model_platform,
  guideSwitch,
  guideVisible,
  orderFormList,
  formDisabled,
  reset,
  handleDeleteOrder,
  doDeleteModel,
} from '@/views/order/create/hooks/index'

const { tooltipKey, tooltipsTriggerRef, showTooltips } = useTooltips()

enum HintDialogType {
  PLATFORM_CHANGE_HINT = '变更平台会导致已选模特无法满足拍摄需求',
  MODEL_CHANGE_HINT = '变更模特类型会导致已选模特无法满足拍摄需求',
  NATION_CHANGE_HINT = '变更拍摄国家会导致已选模特无法满足拍摄需求',
  SHOOT_DEMANDS_CHANGE_HINT = '您已填写拍摄建议，若切换将会清空拍摄建议，是否确认切换选项？',
  SHOOT_LINK_CHANGE_HINT = '您已填写参考视频，若切换将会清空参考视频，是否确认切换选项？',
}

const props = defineProps({
  index: {
    type: Number,
    default: 0,
  },
  orderNum: {
    type: String,
    default: '',
  },
  videoPromotionAmount: {
    type: Boolean,
    default: false,
  },
})

const isGuideSwitch = computed(() => {
  return guideSwitch.value && props.index === 0
})

function switchChange(val: boolean) {
  guideVisible.value = false
  if (val) {
    tooltipsTriggerRef.value = null
    tooltipKey.value++
  }
}

const popoverResetVisible = ref(false)
const popoverDeleteVisible = ref(false)

// 鼠标移入label提示
function handleShowTooltips(e: any, type: string) {
  if (!isGuideSwitch.value) {
    showTooltips(e, type)
  }
}

const isEditing = computed(() => {
  return props.orderNum ? true : false
})

const isShowDelete = computed(() => {
  return orderFormList.value.length > 1
})

const emits = defineEmits(['showSelModelList', 'openSelect', 'onUpload', 'action'])

const { proxy } = getCurrentInstance() as any
const picUrlPath = proxy.$picUrl
const { biz_nation, biz_model_type } = proxy.useDict('biz_nation', 'biz_model_type')

const formRef = ref()
const form = toRef(orderFormList.value[props.index], 'form')
const filePicList = toRef(orderFormList.value[props.index], 'filePicList')

const showHintDialog = ref(false)
const hintDialogTitle = ref('')
const hintDialogContent = ref('')

//需要更换的模特数量
const cannotModelNumber = computed(() => {
  return orderFormList.value[props.index].cannotModelNumber
})
const cannotModelList = computed(() => {
  return orderFormList.value[props.index].cannotModelList
})
//平台切换时临时数据
const temporaryChangeData = toRef(orderFormList.value[props.index], 'temporaryChangeData')

//计算可选模特数量
const selectModelNumber = computed(() => {
  return form.value.shootCount - (form.value.selModel?.length || 0) > 0
    ? form.value.shootCount - (form.value.selModel?.length || 0)
    : 0
})

//模特类型是否可选
const isSelModel = computed(() => {
  if (isEditing.value && form.value.picCount && form.value.picCount > -1) {
    return true
  }
  if (form.value.shootingCountry == nationOptionsMap['美国'] && form.value.platform == Platform['Amazon']) {
    return false
  }
  return true
  // if (form.value.picCount != undefined && form.value.picCount > -1) {
  //   if (form.value.shootingCountry == '7' && form.value.platform == Platform['Amazon']) {
  //     return true
  //   }
  //   if (isEditing.value) return true
  // }
  // return false
})

const addPicNumber = toRef(orderFormList.value[props.index], 'addPicNumber')

//判断是否禁用照片数
const isDisabledPicCount = computed(() => {
  if (isEditing.value || form.value.modelType == '0' || form.value.platform == '3') {
    return true
  }
  return false
})
// 选配照片提示
function picCountTooltips(e: any) {
  if (isDisabledPicCount.value) {
    let text =
      form.value.platform == '0'
        ? '亚马逊影响者不支持选配照片'
        : form.value.platform == '3'
          ? 'APP/解说类视频不支持选配照片'
          : '亚马逊影响者、APP/解说类视频不支持选配照片'
    showTooltips(e, text, true)
  }
}

// 使用平台
const platformDisabled = computed(() => {
  if (isEditing.value && form.value.picCount && form.value.picCount > -1) {
    return true
  }
  return false
})

// 已享优惠的订单
const isPromotionDisabled = computed(() => {
  if (isEditing.value && props.videoPromotionAmount) {
    return true
  }
  return false
})

// 正确链接地址
const productPath = [
  {
    link: 'shop.tiktok.com',
    value: 'tiktok',
  },
  {
    link: 'www.amazon.com',
    value: nationOptionsMap['美国'], // 澳大利亚通用 www.amazon.com.au
  },
  {
    link: 'www.amazon.co.uk',
    value: nationOptionsMap['英国'],
  },
  {
    link: 'www.amazon.fr',
    value: nationOptionsMap['法国'],
  },
  {
    link: 'www.amazon.de',
    value: nationOptionsMap['德国'],
  },
  {
    link: 'www.amazon.it',
    value: nationOptionsMap['意大利'],
  },
  {
    link: 'www.amazon.ca',
    value: nationOptionsMap['加拿大'],
  },
  {
    link: 'www.amazon.es',
    value: nationOptionsMap['西班牙'],
  },
]
// 判断是否正确产品链接
function checkCorrectProductLink(link: string) {
  return productPath.some(sub => link.includes(sub.link))
}
// 获取链接对应国家
function getLinkNation(link: string) {
  let nation = productPath.find(sub => link.includes(sub.link))
  if (nation) {
    return nation.value + ''
  }
  return ''
}

// 产品链接 获取
const productLinkTip = computed(() => {
  if (form.value.isLink == 1 && !isEditing.value) {
    if (form.value.productLink) {
      if (!checkCorrectProductLink(form.value.productLink) || productLinkButtonError.value) {
        return '该链接暂时无法获取到产品信息，请手动填写'
      }
      return '点击即可一键填写产品信息<br/>* 该功能目前仅支持亚马逊、TikTok'
    }
    return '请先粘贴产品链接'
  }
  return ''
})
const productLinkButtonError = ref(false)
const productLinkButtonDisabled = computed(() => {
  if (!form.value.productLink || productLinkButtonError.value || isEditing.value) {
    return true
  }
  if (form.value.productLink && !checkCorrectProductLink(form.value.productLink)) {
    return true
  }
  return false
})
// 获取产品链接信息
function getLinkApi() {
  if (form.value.productLink) {
    if (checkCorrectProductLink(form.value.productLink)) {
      const el_loading = ElLoading.service({
        lock: true,
        text: '信息获取中',
        background: 'rgba(0, 0, 0, 0.5)',
      })
      getProductInfo({ url: form.value.productLink })
        .then((res: any) => {
          if (res.data && (res.data.name || res.data.productChineseName || res.data.specInfo)) {
            form.value.productEnglish = res.data.name || ''
            form.value.productChinese = res.data.productChineseName || ''
            if (res.data.objectKey) {
              orderFormList.value[props.index].crawlProductPic = {
                link: form.value.productLink,
                objectKey: res.data.objectKey,
              }
              //   form.value.isLink = 0
              //   let picNames = res.data.objectKey.split('/')
              //   form.value.filePicList = [
              //     {
              //       id: res.data.objectKey,
              //       name: picNames[picNames.length - 1] || res.data.objectKey,
              //       picUrl: res.data.objectKey,
              //     },
              //   ]
            }
            form.value.sellingPointProduct = res.data.specInfo || ''
            let natioin = getLinkNation(form.value.productLink)
            if (natioin == 'tiktok') {
              form.value.shootingCountry = nationOptionsMap['美国']
              // 默认平台 tiktok
              form.value.platform = Platform['Tiktok']
              // 1.视频格式：竖屏拍摄
              // 2.模特类型：素人创作者
              form.value.videoFormat = 2
              form.value.modelType = '1'
            } else if (natioin) {
              form.value.shootingCountry = natioin
              // 默认平台 amazon
              form.value.platform = Platform['Amazon']
              // 1.视频格式：横屏拍摄
              // 2.模特类型：美国-影/素都可以 其他国家-素人
              form.value.videoFormat = 1
              form.value.modelType = natioin == nationOptionsMap['美国'] ? '3' : '1'
              form.value.picCount = -1
              form.value.referencePic = []
            }
            ElMessage({
              message: '产品信息获取成功',
              type: 'success',
              plain: true,
            })
            return
          }
          ElMessage({
            message: '获取失败，请手动填写',
            type: 'error',
            plain: true,
          })
          productLinkButtonError.value = true
        })
        .catch(() => {
          productLinkButtonError.value = true
        })
        .finally(() => {
          el_loading.close()
        })
      return
    }
    ElMessage({
      message: '该功能目前仅支持亚马逊、TikTok链接',
      type: 'error',
      plain: true,
    })
  }
}
function handleMouseEnterLinkButton(e: any) {
  if (productLinkTip.value) {
    return showTooltips(e, productLinkTip.value, true)
  }
}

// 意向模特清空时自动跳帮我匹配
watch(
  () => form.value.selModel,
  (newVal: any, oldVal: any) => {
    if (!newVal || (newVal && newVal.length == 0)) {
      form.value.selModelType = 0
    }
  }
)
// 照片选配 无需拍摄选配的照片清空
// watch(
//   () => form.value.picCount,
//   (newVal: any, oldVal: any) => {
//     if (newVal && newVal == -1) {
//       form.value.referencePic = []
//     }
//   }
// )

const rules = ref({
  platform: [{ required: true, message: '请选择平台', trigger: 'blur' }],
  productChinese: [
    { required: true, message: '请输入产品中文名', trigger: 'blur' },
    // { pattern: chineseCharacter_d_reg, message: '请输入中文', trigger: 'change' },
  ],
  productEnglish: [{ required: true, validator: checkProductEnglish, trigger: 'change' }],
  shootingCountry: [{ required: true, message: '请选择国家', trigger: 'blur' }],
  // 产品链接
  isLink: [{ required: true, validator: checkIsLink, trigger: 'change' }],
  // 产品图
  productPicInfo: [{ required: true, validator: selectProductPic, trigger: 'blur' }],

  videoFormat: [{ required: true, message: '请选择视频格式', trigger: 'blur' }],
  modelType: [{ required: true, message: '请选择模特类型', trigger: 'change' }],
  // 拍摄建议
  demands: [{ required: true, validator: checkDemands, trigger: 'change' }],
  shootCount: [{ required: true, message: '请选择视频数量', trigger: 'blur' }],
  sellingPointProduct: [{ required: true, validator: checkSellingPoint, trigger: 'change' }],
  referenceVideoLink: [{ required: true, message: '请输入参考视频的链接', trigger: 'blur' }],
})
function checkProductEnglish(rule: any, value: any, callback: any) {
  if (value) {
    if (chinese_c_reg.test(value)) {
      return callback(new Error('请输入英文'))
    }
    return callback()
  }
  return callback(new Error('请输入产品英文名'))
}
function checkIsLink(rule: any, value: any, callback: any) {
  if (!form.value.isLink) return callback()
  if (value && !form.value.productLink) {
    return callback(new Error('请输入产品链接'))
  }
  return callback()
}
function checkDemands(rule: any, value: any, callback: any) {
  if (value && value.trim()) {
    if (value.length > 8000) {
      return callback(new Error('拍摄建议不能超过8000字'))
    }
    if (chinese_c_reg.test(value)) {
      return callback(new Error('*请用英文'))
    }
  }
  return callback()
}
function checkSellingPoint(rule: any, value: any, callback: any) {
  if (value && value.trim()) {
    if (value.length > 8000) {
      return callback(new Error('产品卖点不能超过8000字'))
    }
    if (chinese_c_reg.test(value)) {
      return callback(new Error('*请用英文输入产品卖点'))
    }
  }
  return callback()
}
function selectProductPic(rule: any, value: any, callback: any) {
  if ((value && !form.value.filePicList) || (form.value.filePicList && form.value.filePicList.length === 0)) {
    return callback(new Error('请上传产品图'))
  }
  return callback()
}

// 表单联动 平台
function selectPlatformChange(value: string | number | boolean) {
  if (value === Platform['Amazon']) {
    form.value.videoFormat = 1

    if (form.value.selModel && form.value.selModel.length > 0) {
      // form.value.selModel = []
      form.value.selModel = form.value.selModel.filter((item: any) => {
        return item.platform.indexOf(Platform['Amazon']) != -1
      })
    }
    // 平台为tiktok时，拍摄建议默认【无】
    if (!form.value.demands && !form.value.referenceVideoLink) {
      form.value.shootSuggestType = 'null'
    }
    return
  }
  if (value === Platform['Tiktok']) {
    form.value.videoFormat = 2
    form.value.modelType = '1'

    if (form.value.selModel && form.value.selModel.length > 0) {
      // form.value.selModel = []
      form.value.selModel = form.value.selModel.filter((item: any) => {
        return item.platform.indexOf(Platform['Tiktok']) != -1
      })
    }
    // 平台为tiktok时，拍摄建议默认【参考视频】
    if (!form.value.demands) {
      form.value.shootSuggestType = 'link'
    }
    return
  }

  if (value === Platform['App']) {
    form.value.videoFormat = 1
    form.value.picCount = -1
    form.value.modelType = '1'
    if (form.value.selModel && form.value.selModel.length > 0) {
      // form.value.selModel = []
      form.value.selModel = form.value.selModel.filter((item: any) => {
        return item.platform.indexOf(Platform['App']) != -1
      })
    }
    return
  }
  form.value.videoFormat = 1
  form.value.modelType = '1'

  if (value === Platform['Other']) {
    if (form.value.selModel && form.value.selModel.length > 0) {
      // form.value.selModel = []
      form.value.selModel = form.value.selModel.filter((item: any) => {
        return item.platform.indexOf(Platform['Other']) != -1
      })
    }
    return
  }
}
// 表单联动 拍摄国家
function selectNationChange(value: string | number | boolean) {
  if (value != nationOptionsMap['美国'] && form.value.platform == '0') {
    form.value.modelType = '1'
  }
  if (form.value.selModel && form.value.selModel.length > 0) {
    form.value.selModel = form.value.selModel.filter((item: any) => {
      return item.nation == value
    })
  }
}
// 表单联动 模特类型
function selectModelTypeChange(value: string | number | boolean) {
  if (!(form.value.modelType === '1' && form.value.platform == Platform['Amazon'])) {
    form.value.picCount = -1
  }

  if (value != '3' && form.value.selModel && form.value.selModel.length > 0) {
    form.value.selModel = form.value.selModel.filter((item: any) => item.type == value)
  }
}

// 表单联动 产品链接
const isSelectLinkDisabled = computed(() => {
  if (
    (form.value.platform == '0' && form.value.modelType == '0') ||
    form.value.modelType == '3' ||
    isPromotionDisabled.value
  ) {
    return true
  }
  return false
})

// 模特类型提示
function handleMouseEnterModelType(e: any, val: any) {
  // if (isGuideSwitch.value) return
  if (val == '0') {
    // if (isSelModel.value) {
    //   return showTooltips(e, 'order-create-model-yxz')
    // }
    return showTooltips(e, 'order-create-model-yxz')
  }
  if (val == '1') {
    return showTooltips(e, 'order-create-model-sr')
  }
}

//重置表单数据
function handleReset() {
  if (orderFormList.value[props.index].key) {
    orderFormList.value[props.index] = reset(orderFormList.value[props.index].key)
    form.value = orderFormList.value[props.index].form
    crossTagSendMsg(CrossTagMsgType.ORDER_FORM_UPDATE, 'update')
  }
}

//删除产品图
function doDeleteProductUrl(index: number) {
  form.value.filePicList?.splice(index, 1)
}
//删除参考图
function doDeletePicUrl(index: number) {
  form.value.referencePic?.splice(index, 1)
}

//查看更多模特
function doShowSelModelList() {
  emits('showSelModelList', props.index)
}

//拍摄数量与意向模特联动效果
function changeShootCount(val: number) {
  if (form.value.selModel?.length && form.value.selModel.length > form.value.shootCount) {
    form.value.selModel = form.value.selModel?.slice(0, val)
  }
}

const changeValue = ref('')
function changeFormValue(value: any, type: string) {
  // 拍摄建议切换
  if (type == 'shootSuggestType') {
    if (value != 'demands' && form.value.demands) {
      hintDialogContent.value = ''
      hintDialogTitle.value = HintDialogType.SHOOT_DEMANDS_CHANGE_HINT
      showHintDialog.value = true
    }
    if (value != 'link' && form.value.referenceVideoLink) {
      hintDialogContent.value = ''
      hintDialogTitle.value = HintDialogType.SHOOT_LINK_CHANGE_HINT
      showHintDialog.value = true
    }
    return
  }

  // 平台、模特类型、国家切换
  let isConfirm = false
  changeValue.value = value
  hintDialogContent.value = '需重新选择模特'
  if (type == 'platform') {
    isConfirm = handleModelPlatform(value)
    hintDialogTitle.value = HintDialogType.PLATFORM_CHANGE_HINT
  } else if (type == 'modelType') {
    if (value != '1') {
      form.value.isLink = 1
    }
    if (value != '3') {
      let m = form.value.selModel?.find((item: any) => item.type != value)
      if (m) {
        isConfirm = true
      }
    }
    hintDialogTitle.value = HintDialogType.MODEL_CHANGE_HINT
  } else if (type == 'shootingCountry') {
    isConfirm = true
    hintDialogTitle.value = HintDialogType.NATION_CHANGE_HINT
  }
  if (form.value.selModel && form.value.selModel.length > 0 && isConfirm) {
    showHintDialog.value = true
    // emits('action', props.index)
  } else {
    if (type == 'platform') {
      selectPlatformChange(value)
      temporaryChangeData.value.platform = value + ''
    } else if (type == 'shootingCountry') {
      selectNationChange(value)
      temporaryChangeData.value.shootingCountry = value + ''
    } else {
      temporaryChangeData.value.modelType = value + ''
      selectModelTypeChange(value)
    }
  }
}
//切换时判断是否有符合条件的模特
function handleModelPlatform(value: string | number | boolean) {
  type PlatformType = 'Tiktok' | 'App' | 'Other' | 'Amazon'
  let temp: PlatformType
  let list: any[] = []
  if (value === Platform['Amazon']) {
    temp = 'Amazon'
    form.value.shootCount = isEditing.value ? 1 : 5
  }
  if (value === Platform['Tiktok']) {
    temp = 'Tiktok'
    form.value.shootCount = 1
  }
  if (value === Platform['App']) {
    temp = 'App'
    form.value.shootCount = 1
  }
  if (value === Platform['Other']) {
    temp = 'Other'
    form.value.shootCount = 1
  }
  if (form.value.selModel && form.value.selModel.length > 0) {
    list = form.value.selModel.filter((item: any) => {
      item.platform = item.platform + ''
      return item.platform.indexOf(Platform[temp]) != -1
    })
  }
  return list.length == form.value.selModel?.length ? false : true
}

//选择链接后清空之前内容
function changeIsLink(val: number) {
  form.value.isLink = val
  filePicList.value = []
}

//展示选择图片弹窗
function doShowSelectImage(type: string) {
  emits('onUpload', type, props.index)
}

// 帮我匹配处理
function handleSelModelType() {
  if (form.value.selModel && form.value.selModel.length > 0) {
    form.value.selModel = []
  }
}
// 选择意向模特
function openSelect() {
  emits('openSelect', props.index)
}

// 照片数量选择
function picCountChange() {
  if (isDisabledPicCount.value) return

  if (form.value.modelType == '3') {
    if (form.value.selModel && form.value.selModel.length > 0) {
      showHintDialog.value = true
      hintDialogTitle.value = '拍摄照片服务仅支持素人创作者'
      hintDialogContent.value = '若选择照片服务，意向模特中已选的亚马逊影响者会被清空哟~'
      emits('action', props.index)
    } else {
      form.value.modelType = '1'
    }
  }
  if (form.value.picCount == 1) {
    addPicNumber.value = 2
    if (form.value.referencePic && form.value.referencePic.length > 2) {
      form.value.referencePic = form.value.referencePic.slice(0, 2)
    }
  } else {
    addPicNumber.value = 5
  }
}
// 查看参考照片案例
function lookPhoto() {
  previewImage('https://pstatic.woniu.video/static/photo-example-Ce0FVvR.webp', 2)
}

function handleDialogCancel() {
  showHintDialog.value = false
  if (hintDialogTitle.value == HintDialogType.SHOOT_DEMANDS_CHANGE_HINT) {
    // 拍摄建议切换
    form.value.shootSuggestType = 'demands'
  } else if (hintDialogTitle.value == HintDialogType.SHOOT_LINK_CHANGE_HINT) {
    // 参考视频切换
    form.value.shootSuggestType = 'link'
  } else if (hintDialogTitle.value == HintDialogType.PLATFORM_CHANGE_HINT) {
    // 平台切换
    form.value.platform = temporaryChangeData.value.platform
    if (form.value.platform != Platform['Amazon']) {
      form.value.modelType = '1'
    } else {
      form.value.modelType = temporaryChangeData.value.modelType
    }
  } else if (hintDialogTitle.value == HintDialogType.NATION_CHANGE_HINT) {
    // 国家切换
    form.value.shootingCountry = temporaryChangeData.value.shootingCountry
    form.value.modelType = temporaryChangeData.value.modelType
  } else if (hintDialogTitle.value == HintDialogType.MODEL_CHANGE_HINT) {
    // 模特类型切换
    form.value.modelType = temporaryChangeData.value.modelType
  } else {
    form.value.picCount = -1
  }
}

function handleDialogConfirm() {
  if (hintDialogTitle.value == HintDialogType.SHOOT_DEMANDS_CHANGE_HINT) {
    // 拍摄建议切换
    form.value.demands = ''
  } else if (hintDialogTitle.value == HintDialogType.SHOOT_LINK_CHANGE_HINT) {
    // 参考视频切换
    form.value.referenceVideoLink = ''
  } else if (hintDialogTitle.value == HintDialogType.PLATFORM_CHANGE_HINT) {
    // 平台切换
    selectPlatformChange(changeValue.value)
    temporaryChangeData.value.platform = changeValue.value + ''
  } else if (hintDialogTitle.value == HintDialogType.NATION_CHANGE_HINT) {
    // 国家切换
    selectNationChange(changeValue.value)
    temporaryChangeData.value.shootingCountry = changeValue.value + ''
  } else if (hintDialogTitle.value == HintDialogType.MODEL_CHANGE_HINT) {
    // 模特类型切换
    temporaryChangeData.value.modelType = changeValue.value + ''
    selectModelTypeChange(changeValue.value)
  } else {
    if (form.value.selModel && form.value.selModel.length > 0) {
      form.value.selModel = form.value.selModel?.filter((item: any) => {
        return item.type != '0'
      })
    }

    form.value.modelType = '1'
  }
  showHintDialog.value = false
  changeValue.value = ''
}

defineExpose({
  // 表单校验
  validate: () => {
    return new Promise((resolve, reject) => {
      try {
        formRef.value?.validate((valid: boolean) => {
          if (valid) {
            resolve(true)
          } else {
            reject({
              key: orderFormList.value[props.index].form.key,
              index: props.index,
            })
          }
        })
      } catch (error) {
        reject(error)
      }
    })
  },
})
</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';
@use '@/styles/customFormDisabled.scss';
:deep(.el-radio-button) {
  .el-radio-button__inner {
    color: #0006;
    // border-radius: 15px;
    box-sizing: border-box;
    padding: 8px 0px;
    font-size: 13px;

    &:hover {
      color: var(--el-color-primary);
    }
  }
  &.pd {
    .el-radio-button__inner {
      padding: 8px;
    }
  }
  &.radio-button-w-large {
    .el-radio-button__inner {
      min-width: 90px;
    }
  }
  &.radio-button-w-small {
    .el-radio-button__inner {
      min-width: 80px;
    }
  }
  &.is-disabled {
    .el-radio-button__inner {
      background-color: #f5f5f5;
      &:hover {
        color: #0006;
      }
    }
    &.radio-button-w-large {
      .el-radio-button__inner {
        min-width: 90px;
      }
    }
    &.radio-button-w-small {
      .el-radio-button__inner {
        min-width: 80px;
      }
    }
  }
}
:deep(.el-radio-group) {
  gap: 6px 8px;

  .el-radio-button {
    &.is-active,
    &.is-disabled.is-active {
      .el-radio-button__inner {
        background-color: var(--el-color-primary-light-9);
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
      }
    }
  }
}
// .el-checkbox-button.is-checked:first-child .el-checkbox-button__inner
:deep(.el-checkbox-button) {
  &.checkbox-button-90 {
    .el-checkbox-button__inner {
      min-width: 90px;
    }
  }
  .el-checkbox-button__inner {
    box-sizing: border-box;
    // border-radius: 15px;
    padding: 8px 18px;
    vertical-align: baseline;
  }
  &.is-checked {
    &.checkbox-button-90 {
      .el-checkbox-button__inner {
        min-width: 90px;
      }
    }
    .el-checkbox-button__inner {
      // border-left: none;
      background-color: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }
  }
}
:deep(.el-checkbox-group) {
  .el-checkbox-button.is-active {
    .el-checkbox-button__inner {
      border-left: none;
      border-left-color: transparent;
    }
  }
  .el-checkbox-button.is-checked {
    .el-checkbox-button__inner {
      // border-left: none;
      background-color: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }
  }
}
:deep(.el-form) {
  .el-form-item {
    // .el-form-item--default {
    //   margin-bottom: 16px;
    // }
    .el-form-item__label {
      color: #333;

      &::before {
        display: none;
      }
    }

    .el-form-item__content {
      line-height: 1;
    }
  }
  .right-form-box {
    .el-form-item {
      margin-bottom: 19px;
    }
  }
}
:deep(.el-input) {
  --el-input-placeholder-color: #0005;
  input::placeholder {
    font-size: 13px;
  }
}
:deep(.el-textarea) {
  --el-input-placeholder-color: #0005;
  textarea::placeholder {
    font-size: 13px;
  }
}

.popconfirm-box {
  margin-bottom: 10px;
  font-family: Arial;
  align-items: baseline;
  gap: 6px;

  .el-icon {
    transform: translateY(2px);
  }
}

.order-form-box {
  // margin-bottom: 15px;
  // border-radius: 6px;
  // border: 1px solid transparent;

  .form-header {
    background-color: #fff;
    height: 40px;
    padding: 10px 20px 0;
    border-radius: 6px 6px 0 0;

    // .title {
    //   display: flex;
    //   align-items: center;
    //   gap: 10px;

    //   &::before {
    //     content: '';
    //     display: block;
    //     height: 20px;
    //     width: 2px;
    //     background-color: #fff;
    //     transform: translateY(1px);
    //   }
    // }
  }

  .guide-text {
    width: 100%;
    margin-top: 5px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.5);
    line-height: 14px;

    .light {
      color: #f59a23b6;
    }

    .el-button {
      font-size: 12px;
      padding: 0;
      vertical-align: baseline;
    }
  }

  .order-form {
    padding-top: 10px;
    background-color: #fff;
    border-radius: 0 0 6px 6px;
    position: relative;

    &::before {
      content: '';
      display: block;
      height: 92%;
      width: 1px;
      background-color: #f5f5f5;
      position: absolute;
      top: 10px;
      left: calc(50% + 5px);
    }

    //上传样式
    .image-upload {
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      gap: 10px;
      color: rgba(0, 0, 0, 0.4);
      font-size: 12px;
      line-height: 12px;
      border: 1px dashed var(--el-border-color-darker);
      width: 80px;
      height: 80px;
      border-radius: 6px;
      background-color: var(--el-fill-color-lighter);
      cursor: pointer;
      // margin-bottom: 10px;
      // margin-top: -10px;
      &:hover {
        border-color: var(--el-color-primary);
      }
    }

    .image-list {
      width: fit-content;
      height: 80px;
      position: relative;
      margin-right: 8px;
      // margin-bottom: 8px;
      .icon {
        height: 17px;
        width: 17px;
        cursor: pointer;
      }
      .image-modal {
        position: absolute;
        top: 4px;
        right: 4px;
        // width: 70px;
        // height: 100%;
        // border-radius: 6px;
        // background-color: #2c2b2b66;
        z-index: 9;
        // opacity: 0;
        // &:hover {
        //   opacity: 1;
        // }
      }
    }

    .image-item {
      border-radius: 6px;
      box-sizing: border-box;
      width: 80px;
      height: 80px;
      // margin-right: 10px;
    }
    // .image-modal {

    // }

    .image-error {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    // 非会员头部提示
    .nomember-tips {
      background: #f59a23;
      padding: 5px 20px;
      border-radius: 15px;
      font-size: 12px;
      margin-bottom: 15px;
      &__text {
        color: #fff;
      }
      &__btn {
        cursor: pointer;
        padding: 2px 10px;
        background: #fdebd4;
        border-radius: 15px;
      }
    }

    .coupon-box {
      width: 125px;
      height: 32px;
      position: absolute;
      left: 145px;
      top: 16px;
      transform: translateY(-50%);

      img {
        width: 100%;
        height: 100%;
      }

      .coupon-amount {
        align-items: baseline;
        position: absolute;
        top: 0;
        left: 0;
        width: 46%;
        height: 100%;
        color: #fff;
        font-size: 12px;
        text-align: center;
        line-height: 32px;

        .num {
          font-size: 18px;
          font-weight: bold;
        }

        .del {
          position: relative;
          color: #a7a7a7;

          .num {
            color: #a7a7a7;
          }

          &::after {
            content: '';
            display: block;
            width: 78%;
            height: 1px;
            background-color: #a7a7a7;
            position: absolute;
            top: calc(50% - 1px);
            left: 13%;
          }
        }
      }
      .coupon-text {
        width: 54%;
        height: 32px;
        font-size: 12px;
        color: #fff;
        position: absolute;
        right: 0;
        top: 0;
        justify-content: center;
        gap: 2px;
        transform: scale(0.8);
        font-weight: 600;

        &.gray {
          color: #a7a7a7;
        }
      }
    }

    .item {
      gap: 10px;
      width: 100%;
      cursor: pointer;
      margin-bottom: 8px;
      color: var(--el-color-primary);

      .el-input {
        width: 80%;
      }
    }
    .form-button {
      margin-top: 15px;
    }
    .form-back {
      border-radius: 15px 18px;
    }
    .product-disabled-button {
      font-family: Arial;
    }
    .prompt-title {
      margin: 0 0 15px 15px;
      padding-top: 15px;
      color: #aaa;
      font-size: 14px;
    }
    .select-number {
      margin-left: 10px;
      font-size: 12px;
      color: var(--el-color-primary);
    }

    .prompt-text {
      color: #aaa;
      font-size: 12px;
    }

    .place-order-button {
      width: 150px;
      height: 44px;
      font-size: 18px;
      font-weight: bold;
      border-radius: 50px;
    }

    .textarea-tips {
      position: absolute;
      bottom: 0;
      right: 20px;
      color: var(--el-color-primary);
      font-size: 12px;
    }

    .error-text {
      color: var(--el-color-danger);
    }

    :deep(.el-upload-list__item) {
      width: 80px;
      height: 80px;
    }
    :deep(.disabled) {
      .el-upload--picture-card {
        display: none;
      }
    }
    :deep(.hidden) {
      .el-upload--picture-card {
        display: none;
      }
    }
    :deep(.el-upload--picture-card) {
      width: 80px;
      height: 80px;
    }
    :deep(.el-input-number) {
      .el-input-number__decrease,
      .el-input-number__increase {
        background-color: var(--el-color-primary);
        color: #fff;
      }
      &.is-disabled {
        .el-input-number__decrease,
        .el-input-number__increase {
          background-color: var(--el-color-primary-light-5);
        }
      }
    }
    :deep(.el-image__error) {
      text-align: center;
    }
  }
}
.model-info {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
  // align-items: center;
  margin-top: 10px;
  justify-content: start;
  max-height: 400px;
  overflow: auto;
  :deep(.el-image__inner) {
    height: auto;
  }
  &__item {
    transition: opacity 0.5s ease;
    opacity: 1;
    position: relative;
    cursor: pointer;

    .head-img {
      cursor: pointer;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 1px solid var(--border-gray-color);
      background: var(--border-gray-color);
      line-height: 1;
    }
    .modal-lose {
      color: #fff;
      width: 40px;
      height: 40px;
      position: absolute;
      inset: 0;
      border-radius: 50%;
      background-color: #2c2b2b66;
      z-index: 9;
      opacity: 1;
    }
    .play-modal {
      width: 40px;
      height: 40px;
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 50%;
      // width: 100%;
      // height: 100%;
      background-color: #2c2b2b66;
      z-index: 9;
      opacity: 0;
    }
    &:hover {
      .play-modal {
        opacity: 1;
      }
    }
  }
  .model-more {
    cursor: pointer;
    &__icon {
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 800;
      color: #fff;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--el-color-primary);
      font-size: 20px;
    }
    &__text {
      margin-top: -7px;
      // line-height: 1;
      font-size: 12px;
      text-align: center;
      color: var(--el-color-primary);
    }
  }
}

.sel-model-list-enter-active,
.sel-model-list-leave-active {
  transition: all 0.5s ease;
}
.sel-model-list-enter-from,
.sel-model-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.head-name {
  max-width: 50px;
  // margin-top: -10px;
  line-height: 1;
  font-size: 12px;
  color: #000;
  text-align: center;
}
.hint-dialog {
  display: flex;
  &__title {
    font-weight: 400;
  }
  &__content {
    color: #aaa;
  }
}
.hint-line {
  position: absolute;
  left: 0;
  width: 100%;
  height: 1px;
  background: #dad8d8;
  margin-top: 15px;
}
</style>
