<template>
  <div class="flex-between header-box" v-if="store.isVip()">
    <span class="text">{{ title }}</span>
    <div class="flex-start gap-10">
      <span class="text2">填写指引</span>
      <el-tooltip :visible="visible" :offset="2" effect="light" content="" placement="bottom-end">
        <el-switch v-model="guideSwitch" @click="visible = false" />
        <template #content>
          <div class="guide-tip-box">
            <div>您如果已知订单填写规则，可手动关闭填写指引哦~</div>
            <div class="flex-end btn">
              <el-button type="primary" link @click="visible = false">我知道了</el-button>
            </div>
          </div>
        </template>
      </el-tooltip>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { createOrderCount } from '@/api/order'
import { guideSwitch } from '@/views/order/create/hooks/index'

const props = defineProps({
  title: {
    type: String,
    default: '创建订单',
  },
})

const store = useUserStore()

const visible = ref(false)

onMounted(() => {
  if (store.isVip()) {
    createOrderCount().then(res => {
      if (res.data < 3) {
        nextTick(() => {
          guideSwitch.value = true
          visible.value = true
        })
      }
    })
  }
})
</script>

<style scoped lang="scss">
.header-box {
  padding: 0 0 0 5px;
  .text {
    font-size: 16px;
  }
  .text2 {
    font-size: 14px;
  }
}
.guide-tip-box {
  width: 180px;

  .btn {
    .el-button {
      font-size: 12px;
    }
  }
}
</style>
