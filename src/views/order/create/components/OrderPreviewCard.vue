<template>
  <div class="order-preview-card" ref="cardRef" @click="handleView">
    <div class="card-corner">订单{{ index + 1 }}</div>

    <div class="flex-between card-title">
      <div class="more-ell text-n-all">{{ form.productChinese || '未填写' }}</div>
      <el-tag type="info" round>×{{ form.shootCount }}</el-tag>
    </div>
    <div class="card-info">
      <div class="info-item">{{ platform }}</div>
      <div class="info-item">{{ shootingCountry }}</div>
      <div class="info-item">{{ modelType }}</div>
      <div class="info-item">{{ picCount }}</div>
    </div>

    <div class="flex-between card-footer">
      <div class="flex-start btn-box">
        <el-button type="primary" link :disabled="!store.isVip()" @click.stop="handleView">查看</el-button>
        <el-button type="primary" link :disabled="formDisabled || !store.isVip()" @click.stop="handleCopy">复制</el-button>
        <el-popover
          v-if="isShowDelete"
          v-model:visible="popoverDeleteVisible"
          ref="popoverResetRef"
          placement="bottom"
          :width="207"
          trigger="click"
        >
          <template #reference>
            <el-button type="danger" link :disabled="formDisabled || !store.isVip()" @click.stop>删除</el-button>
          </template>
          <div>
            <div class="flex-start popconfirm-box">
              <el-icon color="var(--el-color-danger)"><WarningFilled /></el-icon>
              <span>
                删除后无法恢复，是否确认删除
                <strong>订单{{ index + 1 }}</strong>
                ？
              </span>
            </div>
            <div class="flex-end">
              <el-button size="small" @click="popoverDeleteVisible = false">取消</el-button>
              <el-button type="primary" size="small" @click="handleDeleteOrder(index)">确定</el-button>
            </div>
          </div>
        </el-popover>
      </div>
      <div class="price-box">
        <span>{{ totalPrice }}</span>
        USD
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, getCurrentInstance, ref, toRef } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { picCountOptions } from '@/utils/data'
import {
  model_platform,
  orderFormList,
  formDisabled,
  handleDeleteOrder,
  handleCopyOrder,
  video_price,
  pic_price,
} from '@/views/order/create/hooks/index'
import currency from 'currency.js'

const store = useUserStore()

const emits = defineEmits(['view'])

const cardRef = ref()

const props = defineProps({
  index: {
    type: Number,
    default: 0,
  },
})
const { proxy } = getCurrentInstance() as any
const { biz_nation, biz_model_type } = proxy.useDict('biz_nation', 'biz_model_type')

const form = toRef(orderFormList.value[props.index].form)

const popoverDeleteVisible = ref(false)

const isShowDelete = computed(() => {
  return orderFormList.value.length > 1
})

const platform = computed(() => {
  if (form.value.platform) {
    return model_platform.find((item: any) => item.value == form.value.platform)?.label || '-'
  }
  return '-'
})
const shootingCountry = computed(() => {
  if (form.value.shootingCountry) {
    return biz_nation.value.find((item: any) => item.value == form.value.shootingCountry)?.label || '-'
  }
  return '-'
})
const modelType = computed(() => {
  if (form.value.modelType) {
    if (form.value.modelType == '3') {
      return '影/素都可以'
    }
    return biz_model_type.value.find((item: any) => item.value == form.value.modelType)?.label || '-'
  }
  return '-'
})
const picCount = computed(() => {
  if (form.value.picCount && form.value.picCount > -1) {
    return picCountOptions.find((item: any) => item.value == form.value.picCount)?.label || '-'
  }
  return '无照片选配'
})
const totalPrice = computed(() => {
  let total = 0
  if (form.value.shootCount) {
    total = currency(currency(video_price).add(pic_price(form.value.picCount)).value).multiply(
      form.value.shootCount
    ).value
  }
  return total
})

function handleView() {
  if (!store.isVip()) return
  emits('view', props.index)
}

function handleCopy() {
  handleCopyOrder(props.index)
  emits('view', props.index + 1)
}

defineExpose({
  cardKey: () => form.value.key,
  errorHint: () => {
    if (cardRef.value) {
      cardRef.value.addEventListener('animationend', function () {
        cardRef.value.classList.remove('order-preview-card-hint-err')
      })
      cardRef.value.classList.add('order-preview-card-hint-err')
      cardRef.value.scrollIntoView({ behavior: 'smooth' })
    }
  },
})
</script>

<style lang="scss" scoped>
.order-preview-card-hint-err {
  animation: order-card-hint-err-animate 3s linear;

  &.order-preview-card {
    .card-corner {
      animation: order-card-hint-bg-animate 3s linear;
    }
  }
}
@keyframes order-card-hint-err-animate {
  0% {
    border-color: #50adebcc;
  }
  25% {
    border-color: #f56c6c;
  }
  50% {
    border-color: #50adebcc;
  }
  75% {
    border-color: #f56c6c;
  }
  100% {
    border-color: #50adebcc;
  }
}
@keyframes order-card-hint-bg-animate {
  0% {
    background-color: #50adebcc;
  }
  25% {
    background-color: #f56c6c;
  }
  50% {
    background-color: #50adebcc;
  }
  75% {
    background-color: #f56c6c;
  }
  100% {
    background-color: #50adebcc;
  }
}
.popconfirm-box {
  margin-bottom: 10px;
  font-family: Arial;
  align-items: baseline;
  gap: 6px;

  .el-icon {
    transform: translateY(2px);
  }
}
.order-preview-card {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #50adebcc;
  padding: 30px 12px 12px;
  margin-bottom: 10px;
  cursor: pointer;
  overflow: hidden;

  .card-corner {
    background-color: #64acdb;
    border-radius: 0 0 6px 0;
    color: #fff;
    font-size: 12px;
    line-height: 24px;
    width: 50px;
    text-align: center;
    position: absolute;
    top: 0;
    left: 0;
  }

  .card-title {
    font-size: 14px;
    color: #333;
    line-height: 22px;

    div {
      max-width: 85%;
    }

    .el-tag {
      height: 17px;
      font-family:
        Source Han Sans SC,
        Source Han Sans SC;
    }
  }

  .card-info {
    display: flex;
    flex-wrap: wrap;
    gap: 3px 0;
    margin: 8px 0;

    .info-item {
      width: 50%;
      flex-shrink: 0;
      font-size: 12px;
      color: #777;
      line-height: 16px;
    }
  }

  .card-footer {
    align-items: baseline;

    .btn-box {
      .el-button {
        font-size: 13px;
        & + .el-button {
          margin-left: 5px;
        }
      }
    }
    .price-box {
      font-size: 12px;
      color: #777;

      span {
        font-weight: bold;
        font-size: 18px;
        color: #ff5722;
      }
    }
  }
}
</style>
