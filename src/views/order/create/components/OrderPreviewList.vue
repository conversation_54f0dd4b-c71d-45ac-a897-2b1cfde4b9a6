<template>
  <div class="order-preview-list">
    <div class="title-box">
      <div class="flex-between">
        <Title>
          <span style="font-weight: 500">&nbsp;订单信息</span>
        </Title>
        <el-button
          type="primary"
          size="small"
          plain
          round
          icon="Plus"
          :disabled="formDisabled || orderFormList.length >= 30 || !store.isVip()"
          :loading="formAddLoading"
          @click="handleAddOrder"
        >
          添加订单
        </el-button>
      </div>
    </div>

    <TransitionGroup
      tag="div"
      name="o-f-l-r"
      class="list-box"
      :style="{
        '--h':
          couponAmount && oneOrderInfo.amount ? '60px' : couponAmount || oneOrderInfo.amount ? '50px' : '0px',
      }"
    >
      <div
        v-for="(item, index) in orderFormList"
        :key="item.key"
        ref="orderPreviewCardRef"
        :data-index="index"
      >
        <OrderPreviewCard
          :key="item.form.key"
          :index="index"
          ref="orderPreviewCardItemRef"
          @view="handleView"
        />
      </div>
    </TransitionGroup>

    <div class="total-box">
      <div class="flex-between total-price">
        <div>订单金额</div>
        <div>
          <span>{{ totalInfo.total }}</span>
          USD
        </div>
      </div>

      <div class="flex-between total-item">
        <span>&emsp;视频佣金</span>
        <span>${{ totalInfo.videoPrice.value }}×{{ totalInfo.videoPrice.count }}</span>
      </div>
      <div class="flex-between total-item">
        <span>&emsp;照片佣金</span>
        <span>${{ totalInfo.picPrice }}</span>
      </div>
      <div class="flex-between total-item">
        <el-tooltip
          placement="top"
          raw-content
          :content="handleShowTooltips('order-pay-service')"
          :hide-after="200"
          :offset="2"
          popper-class="public-tooltips"
        >
          <span class="flex-start" style="gap: 2px">
            &emsp;服务费
            <el-icon color="rgb(155, 166, 186)">
              <QuestionFilled />
            </el-icon>
          </span>
        </el-tooltip>
        <span>
          <del v-if="oldPrice != totalInfo.servicePrice">${{ oldPrice }}</del>
          <span>${{ totalInfo.servicePrice }}×{{ totalInfo.videoPrice.count }}</span>
        </span>
      </div>
      <div class="flex-between total-item">
        <el-tooltip
          placement="top"
          raw-content
          :content="handleShowTooltips('order-pay-taxes')"
          :hide-after="200"
          :offset="2"
          popper-class="public-tooltips"
        >
          <span class="flex-start" style="gap: 2px">
            &emsp;佣金代缴税费
            <el-icon color="rgb(155, 166, 186)">
              <QuestionFilled />
            </el-icon>
          </span>
        </el-tooltip>
        <span>${{ totalInfo.commissionPaysTaxes }}</span>
      </div>
      <div class="flex-between total-item">
        <el-tooltip
          placement="top"
          raw-content
          :content="handleShowTooltips('order-pay-PayPal')"
          :hide-after="200"
          :offset="2"
          popper-class="public-tooltips"
        >
          <span class="flex-start" style="gap: 2px">
            &emsp;PayPal代付手续费
            <el-icon color="rgb(155, 166, 186)">
              <QuestionFilled />
            </el-icon>
          </span>
        </el-tooltip>
        <span>${{ totalInfo.exchangePrice }}</span>
      </div>

      <div
        class="total-coupon"
        :style="{
          height:
            couponAmount && oneOrderInfo.amount
              ? '62px'
              : couponAmount || oneOrderInfo.amount
                ? '42px'
                : '0px',
          'padding-top': couponAmount ? '6px' : '0px',
        }"
      >
        <div class="coupon-title">优惠信息</div>
        <div class="flex-between coupon-box" v-if="couponAmount">
          <span class="red">&emsp;满5减100元优惠活动</span>
          <span>
            <span class="red">-{{ couponAmount }}</span>
            CNY
          </span>
        </div>
        <div class="flex-between coupon-box" v-if="oneOrderInfo.amount">
          <span class="red">&emsp;每月首单返还会员费</span>
          <span>
            <span class="red">-{{ oneOrderInfo.amount }}</span>
            USD
          </span>
        </div>
      </div>

      <!-- <div class="tip">*此处价格展示为美金价格,最终结算价格请以结算页为准</div> -->

      <div class="flex-between btn-box" v-if="store.isVip()">
        <el-button round :disabled="formDisabled" @click="handleSaveOrder">保存订单</el-button>
        <el-button round type="primary" :disabled="formDisabled" @click="handleSubmit">提交订单</el-button>
      </div>
      <div class="flex-between btn-box" v-else>
        <div
          class="el-button el-button--default is-round is-disabled"
          @mouseenter="handleShowTips($event, '非会员无法保存订单')"
        >
          保存订单
        </div>
        <div
          class="el-button el-button--primary el-button--default is-round is-disabled"
          @mouseenter="handleShowTips($event, '非会员无法提交订单')"
        >
          提交订单
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import Title from '@/components/public/Title.vue'
import OrderPreviewCard from './OrderPreviewCard.vue'

import {
  type TotalOrderAmount,
  orderFormList,
  formDisabled,
  oldPrice,
  formAddLoading,
  handleAddOrderForm,
  computeAllOrderAmount,
  setCacheOrderFormList,
} from '@/views/order/create/hooks/index'
import { computed, nextTick, ref } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'
import { useTooltips } from '@/hooks/useTooltips'
import aegis from '@/utils/aegis'
import Aegis from 'aegis-web-sdk'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { oneOrderInfo } = useShowRenewDialog()

const store = useUserStore()

const emits = defineEmits(['view', 'submit'])

const { getTipContent, showTooltips } = useTooltips()

function handleShowTooltips(type: string) {
  let content = getTipContent(type)
  if (content) {
    return `<div style="max-width: 300px;">${content}</div>`
  }
}

function handleShowTips(e: any, tips: string) {
  showTooltips(e, tips, true)
}

const orderPreviewCardRef = ref()
const orderPreviewCardItemRef = ref()

const totalInfo = computed(() => {
  let total: TotalOrderAmount = {
    videoPrice: {
      value: 0,
      count: 0,
    },
    picPrice: 0,
    servicePrice: 0,
    commissionPaysTaxes: 0,
    exchangePrice: 0,
    total: 0,
  }
  if (orderFormList.value.length) {
    total = computeAllOrderAmount(orderFormList.value)
  }
  return total
})

const couponAmount = computed(() => {
  let amount = 0
  orderFormList.value.forEach(item => {
    if (item.form.shootCount >= 5) {
      amount += item.form.shootCount * 20
    }
  })
  return amount
})

function handleView(i: number) {
  nextTick(() => {
    handleScrollIntoView(i)
    setTimeout(() => {
      emits('view', i)
    })
  })
}

// 滚动到指定订单卡片
function handleScrollIntoView(i: number) {
  let dom = orderPreviewCardRef.value.find((item: any) => item.dataset.index == i)
  if (dom) {
    dom.scrollIntoView()
  }
}

// 添加订单
function handleAddOrder() {
  let add = handleAddOrderForm()
  if (add) {
    setCacheOrderFormList()
    nextTick(() => {
      handleScrollIntoView(orderFormList.value.length - 1)
      setTimeout(() => {
        emits('view', orderFormList.value.length - 1)
      })
    })
  }
}

function handleSaveOrder() {
  setCacheOrderFormList()
  try {
    aegis?.report({
      msg: '登录用户点击保存订单',
      level: Aegis.logType.REPORT,
    })
  } catch (error) {
    console.log('日志上报失败')
  }
  ElMessage.success('已保存')
}

function handleSubmit() {
  emits('submit')
}

defineExpose({
  errorHint: (key: string, index: number) => {
    if (key && orderPreviewCardItemRef.value) {
      handleScrollIntoView(index)
      let i = orderPreviewCardItemRef.value.findIndex((item: any) => item.cardKey() == key)
      if (i > -1) {
        orderPreviewCardItemRef.value[i]?.errorHint()
      }
    }
  },
  handleScrollIntoView,
})
</script>

<style scoped lang="scss">
.o-f-l-r-move,
.o-f-l-r-enter-active,
.o-f-l-r-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}
.o-f-l-r-enter-from {
  opacity: 0;
}
.o-f-l-r-leave-to {
  opacity: 0;
  transform: translate(30px, 0);
}
.o-f-l-r-leave-active {
  position: absolute;
  z-index: 2;
}

.order-preview-list {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 20px;
  position: relative;

  .title-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    box-sizing: border-box;
    padding: 20px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.06);
  }

  .list-box {
    --h: 0px;
    width: 100%;
    height: calc(100% - 235px - var(--h));
    margin-top: 40px;
    padding: 10px 20px 20px 0;
    overflow-y: overlay;
  }

  .total-box {
    box-sizing: border-box;
    width: 100%;
    height: auto;
    transition: 0.3s;
    padding: 15px 20px;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 2;
    box-shadow: 0px -6px 6px 0px rgba(0, 0, 0, 0.06);
    background-color: #fff;
    border-radius: 0 0 6px 6px;

    .total-item {
      font-size: 12px;
      color: var(--text-color);
      line-height: 19px;

      span:first-child {
        font-size: 12px;
        color: #0009;
      }

      del {
        color: #999;
        margin-right: 1px;
      }
    }

    .total-price {
      font-size: 14px;
      color: var(--text-color);
      line-height: 19px;
      margin: 0;
      position: relative;

      div:last-child {
        font-size: 12px;

        span {
          font-weight: bold;
          font-size: 16px;
        }
      }
    }

    .total-coupon {
      margin-top: 6px;
      border-top: 1px solid #eee;
      transition: 0.3s;
      overflow: hidden;

      .coupon-title {
        font-size: 14px;
        color: var(--text-color);
      }
      .coupon-box {
        width: 100%;
        color: #333c;
        font-size: 12px;

        .red {
          color: #ff5722;
        }
      }
    }

    .tip {
      font-size: 13px;
      color: rgba(0, 0, 0, 0.4);
      margin-bottom: 10px;
    }

    .btn-box {
      margin-top: 12px;

      .el-button {
        width: 48%;
        font-size: var(--el-font-size-base);
        font-weight: 500;
        font-family: Arial;
      }
    }
  }
}
</style>
