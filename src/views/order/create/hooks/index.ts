import { onMounted, onUnmounted, ref } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { crossTagSendMsg, crossTagListenMsg, CrossTagMsgType } from '@/hooks/crossTagMsg'
import type { OrderForm } from '@/views/order/type'
import currency from 'currency.js'
import { deepClone } from '@/utils/public'
import { ElMessageBox } from 'element-plus'
import { ElMessage } from 'element-plus'

const CACHE_ORDER_FORM_LIST_KEY = 'c_o_f_list_'

type OrderFormItem = {
  key: string
  orderNum: string
  form: OrderForm & { key: string }
  crawlProductPic: {
    link: string
    objectKey: string
  }
  oldModelTypeList: any[]
  filePicList: UploadPicFile
  cannotModelNumber: number
  cannotModelList: any[]
  temporaryChangeData: TemporaryChangeData
  addPicNumber: number
}
type UploadPicFile = {
  name?: string
  url?: string
  id?: number
}[]
type TemporaryChangeData = {
  platform: string
  shootingCountry: string
  modelType: string
}

const model_platform = [
  { label: '亚马逊', value: '0' },
  { label: 'TikTok', value: '1' },
  { label: '其他', value: '2' },
  { label: 'APP/解说', value: '3' },
]

/**
 * 订单表单列表
 */
const orderFormList = ref<OrderFormItem[]>([])
const formAddLoading = ref(false)
const formLoading = ref(false)
const formDisabled = ref(false)
const isUpdate = ref(false)

const guideSwitch = ref(false)
const guideVisible = ref(false)

// const showHintDialog = ref(false)
// const hintDialogTitle = ref('')
// const hintDialogContent = ref('')

/**
 * 获取缓存订单表单列表
 * @returns {number} 缓存订单表单列表数量
 */
function getCacheOrderFormList() {
  const cacheOrderFormList = localStorage.getItem(CACHE_ORDER_FORM_LIST_KEY + useUserStore().userInfo.account)
  if (cacheOrderFormList) {
    let arr = JSON.parse(cacheOrderFormList)
    if (Array.isArray(arr)) {
      arr.forEach((item: any, i: number) => {
        item.form.key = (new Date().getTime() + i).toString()
        if (item.form.selModel?.length) {
          item.form.selModelType = 1
        } else {
          item.form.selModelType = 0
        }
      })
      orderFormList.value = arr
      return arr.length
    }
  }
  orderFormList.value.length = 0
  handleAddOrderForm()
  return 0
}
/**
 * 缓存订单表单列表
 */
function setCacheOrderFormList() {
  localStorage.setItem(
    CACHE_ORDER_FORM_LIST_KEY + useUserStore().userInfo.account,
    JSON.stringify(orderFormList.value)
  )
  crossTagSendMsg(CrossTagMsgType.ORDER_FORM_UPDATE, 'update')
}
/**
 * 清除缓存订单表单列表
 */
function removeCacheOrderFormList() {
  localStorage.removeItem(CACHE_ORDER_FORM_LIST_KEY + useUserStore().userInfo.account)
  crossTagSendMsg(CrossTagMsgType.ORDER_FORM_UPDATE, 'remove')
}

/**
 * 创建表单
 */
function createForm(): OrderForm & { key: string } {
  return {
    key: new Date().getTime().toString(),
    platform: '0',
    productChinese: '',
    productEnglish: '',
    isLink: 1,
    productLink: '',
    productPic: undefined,
    productPicInfo: {
      id: 0,
      name: '',
      picUrl: '',
      videoUrl: '',
    },
    isObject: 0,
    referenceVideoLink: '',
    videoFormat: 1,
    shootingCountry: '7',
    modelType: '1',
    picCount: -1,
    filePicList: [],
    selModelType: 0,
    selModel: [],
    referencePic: [],
    intentionModelIds: [],
    demands: '',
    shootRequired: [
      {
        content: '',
      },
    ],
    // 限制条件
    conditions: [
      {
        value: '',
      },
    ],
    shootCount: 5,
    shootSuggestType: 'null',
    sellingPointProduct: '',
  }
}
/**
 * 重置表单数据
 */
function reset(key?: string): OrderFormItem {
  return {
    key: key || Math.random().toString(36).substring(2), // 随机key
    orderNum: '', // 订单号
    form: createForm(), // 表单数据
    crawlProductPic: {
      link: '',
      objectKey: '',
    }, // 爬取到的产品图
    oldModelTypeList: [], // 旧的模特类型列表
    filePicList: [], // 上传文件列表
    cannotModelNumber: 0, // 需要更换的模特数量
    cannotModelList: [], // 需要更换的模特数据
    temporaryChangeData: {
      //平台切换时临时数据
      platform: '0',
      shootingCountry: '7',
      modelType: '1',
    },
    addPicNumber: 2, // 可添加图片数量
  }
}

/**
 * 增加订单表单
 */
function handleAddOrderForm(data?: any) {
  if (orderFormList.value.length >= 30) {
    ElMessage.warning('最多只能一次创建30个订单')
    return false
  }
  formAddLoading.value = true
  let item = reset()
  if (data) {
    if (data.form) {
      item.form = data.form
      item.form.key = new Date().getTime().toString()
      item.temporaryChangeData = {
        modelType: data.form.modelType,
        platform: data.form.platform,
        shootingCountry: data.form.shootingCountry,
      }
    }
    if (data.addPicNumber) item.addPicNumber = data.addPicNumber
    item.orderNum = data.orderNum
  }
  orderFormList.value.push(item)
  setTimeout(() => {
    formAddLoading.value = false
  }, 500)
  return true
}

/**
 * 复制订单表单
 */
function handleCopyOrder(i: number) {
  if (orderFormList.value.length >= 30) {
    ElMessage.warning('最多只能一次创建30个订单')
    return
  }
  if (orderFormList.value[i]) {
    formAddLoading.value = true
    // let item = JSON.parse(JSON.stringify(orderFormList.value[i]))
    let item = deepClone(orderFormList.value[i])
    item.key = Math.random().toString(36).substring(2)
    item.form.key = new Date().getTime().toString()
    orderFormList.value.splice(i + 1, 0, item)
    setCacheOrderFormList()
    setTimeout(() => {
      formAddLoading.value = false
    }, 500)
  }
}

/**
 * 删除订单
 * @param i
 */
function handleDeleteOrder(i: number) {
  if (orderFormList.value[i]) {
    orderFormList.value.splice(i, 1)
    setCacheOrderFormList()
  }
}

/**
 * 删除选择模特
 * @param index
 */
function doDeleteModel(i: number, index: number) {
  if (orderFormList.value[i]?.form?.selModel) {
    orderFormList.value[i].form.selModel.splice(index, 1)
    if (orderFormList.value[i].form.selModel.length === 0) {
      orderFormList.value[i].form.selModelType = 0
    }
  }
}

/**
 * 提交订单
 */
function handleSubmitOrder() {
  formDisabled.value = true
  try {
    return orderFormList.value.map((item, index) => {
      let {
        key,
        demands,
        isLink,
        selModelType,
        selModel,
        filePicList,
        referencePic,
        shootSuggestType,
        ...params
      } = item.form

      // 意向模特
      if (selModelType == 1 && selModel && selModel.length) {
        params.intentionModelIds = selModel.filter((m, i) => i < item.form.shootCount).map((m: any) => m.id)
      }
      // 产品链接/产品图
      if (!isLink) {
        params.productLink = ''
        if (filePicList && filePicList.length > 0) {
          params.productPic = filePicList[0].picUrl
        }
      } else {
        params.productPic = undefined
      }
      // 照片选配
      if (params.picCount === -1) {
        params.picCount = undefined
      }
      // 选配图片
      if (params.picCount && params.picCount != -1 && referencePic && referencePic.length) {
        referencePic = referencePic.map(item => {
          if (item.response && item.response.code == 200) {
            return item.response.data.picUrl
          }
          return item.picUrl
        })
      } else {
        referencePic = []
      }

      // 拍摄建议
      if (shootSuggestType == 'demands' && demands) {
        let countList = []
        countList = demands.split('\n')
        params.shootRequired = countList.map((item, index) => {
          return { content: item, sort: index, type: 1 }
        })
        if (
          params.shootRequired &&
          params.shootRequired.length < 2 &&
          params.shootRequired[0].content == ''
        ) {
          params.shootRequired = []
        }
      } else {
        params.shootRequired = []
      }
      // 参考视频
      if (shootSuggestType != 'link') {
        params.referenceVideoLink = ''
      }

      // 抓取的产品图
      let objectKey: any
      if (item.crawlProductPic?.objectKey && item.crawlProductPic.link == params.productLink) {
        objectKey = item.crawlProductPic.objectKey
      }

      return {
        ...params,
        crawlProductPic: objectKey,
        referencePic,
        serialNumber: index + 1,
      }
    })
  } catch (error) {
    throw error
  }
}

/**
 * 视频佣金 USD
 */
const video_price = 29.9
/**
 * 照片佣金 USD
 * @param val
 * @returns
 */
const pic_price = (val?: number) => {
  if (val === 1) {
    return 10
  }
  if (val === 2) {
    return 20
  }
  return 0
}
/**
 * 蜗牛服务费 USD
 */
const service_price = 3
const servicePrice = ref(3)
const oldPrice = ref(6)
/**
 * PayPal代付手续费 （videoPrice+picPrice）*0.044+0.3  保留两位小数 1.616 -> 1.61
 * @param picCount 照片选配
 * @returns USD
 */
const exchange_price = (picCount?: number) => {
  let num = currency(0.044, { precision: 3 })
    .multiply(currency(video_price).add(pic_price(picCount)).value)
    .add(0.3).value
  return Math.floor(num * 100) / 100
}
/**
 * 佣金代缴税费（videoPrice+picPrice）*0.06
 * @param picCount 照片选配
 * @returns USD
 */
const commission_pays_taxes = (picCount?: number) => {
  return currency(0.06).multiply(currency(video_price).add(pic_price(picCount)).value).value
}
/**
 * 计算单个订单金额 USD
 * 视频佣金 + 照片佣金 + 蜗牛服务费 + PayPal代付手续费 + 佣金代缴税费
 */
function computeOrderAmount(picCount?: number) {
  return currency(video_price)
    .add(pic_price(picCount))
    .add(exchange_price(picCount))
    .add(commission_pays_taxes(picCount))
    .add(servicePrice.value).value
}
type TotalOrderAmount = {
  videoPrice: {
    value: number
    count: number
  }
  picPrice: number
  servicePrice: number
  commissionPaysTaxes: number
  exchangePrice: number
  total: number
}
/**
 * 计算所有订单金额 USD
 */
function computeAllOrderAmount(list: OrderFormItem[]): TotalOrderAmount {
  let obj: TotalOrderAmount = {
    videoPrice: {
      value: video_price,
      count: 0,
    },
    picPrice: 0,
    servicePrice: servicePrice.value,
    commissionPaysTaxes: 0,
    exchangePrice: 0,
    total: 0,
  }
  if (list && list.length) {
    list.forEach((item: OrderFormItem) => {
      // 所有订单-视频数量
      obj.videoPrice.count += item.form.shootCount

      // 单个视频价格
      const oneVideoPrice = computeOrderAmount(item.form.picCount)

      // 此订单视频总价格 = 视频数量 * 单个视频价格
      const allVideoPrice = currency(oneVideoPrice).multiply(item.form.shootCount).value

      // 此订单视频佣金 = 视频数量 * 单个视频佣金
      const allPicPrice = currency(pic_price(item.form.picCount)).multiply(item.form.shootCount).value

      // 此订单佣金代缴税费 = 视频数量 * 单个视频佣金代缴税费
      const allCommissionPaysTaxes = currency(item.form.shootCount).multiply(
        commission_pays_taxes(item.form.picCount)
      ).value

      // 此订单PayPal代付手续费 = 视频数量 * 单个视频PayPal代付手续费
      const allExchangePrice = currency(item.form.shootCount).multiply(
        exchange_price(item.form.picCount)
      ).value

      // 所有订单-总视频佣金
      obj.picPrice = currency(obj.picPrice).add(allPicPrice).value

      // 所有订单-总佣金代缴税费
      obj.commissionPaysTaxes = currency(obj.commissionPaysTaxes).add(allCommissionPaysTaxes).value

      // 所有订单-总PayPal代付手续费
      obj.exchangePrice = currency(obj.exchangePrice).add(allExchangePrice).value

      // 所有订单-总价
      obj.total = currency(obj.total).add(allVideoPrice).value
    })
  }
  return obj
}

let timer: any = null

/**
 * 定时器-保存表单
 */
function setSaveFormTimer() {
  clearTimer()
  timer = setInterval(
    () => {
      setCacheOrderFormList()
      console.log('save form')
    },
    1000 * 60 * 3
  )
}
function clearTimer() {
  if (timer) {
    clearInterval(timer)
  }
}

const visibilityChangeEvent = (() => {
  if (typeof document.hidden !== 'undefined') {
    return 'visibilitychange'
  }
  if (typeof (document as any).msHidden !== 'undefined') {
    return 'msvisibilitychange'
  }
  if (typeof (document as any).webkitHidden !== 'undefined') {
    return 'webkitvisibilitychange'
  }
})()

let msgBox: any = null
function handleVisibilityChange(callback?: any) {
  if (isUpdate.value && !msgBox) {
    // 页面切换后，订单表单有更新
    formAddLoading.value = true
    ElMessageBox.close()
    if (callback) callback()
    msgBox = ElMessageBox.alert('表单内容已更新，请刷新', '温馨提示', {
      confirmButtonText: '更新',
      showClose: false,
      callback: () => {
        getCacheOrderFormList()
        setTimeout(() => {
          formDisabled.value = false
          formAddLoading.value = false
        }, 500)
        if (visibilityChangeEvent) setSaveFormTimer()
        isUpdate.value = false
        msgBox = null
      },
    })
  }
}

/**
 * 订单表单初始化
 */
function useOrderFormInit() {
  let cacheListLength = getCacheOrderFormList()
  // 模特选ta
  const model = sessionStorage.getItem('createOrderModelInfo')
  if (model) {
    if (!cacheListLength) {
      orderFormList.value.length = 0
    }

    let modelObj = JSON.parse(model)

    let data = reset()
    data.form.selModel = [modelObj]
    //默认平台中的第一个
    data.form.platform = modelObj.platform.split(',')[0]
    data.form.shootingCountry = modelObj.nation + ''
    data.form.modelType = modelObj.type + ''

    data.temporaryChangeData.modelType = modelObj.type + ''
    data.temporaryChangeData.platform = modelObj.platform.split(',')[0]
    data.temporaryChangeData.shootingCountry = modelObj.nation + ''

    if (data.form.platform == '1') {
      data.form.videoFormat = 2
    }
    data.form.selModelType = 1

    handleAddOrderForm(data)
    sessionStorage.removeItem('createOrderModelInfo')
    setCacheOrderFormList()
    // 滚动到底部
    setTimeout(() => {
      let box = document.querySelectorAll('.order-list-box')
      if (box && box[0].children.length) {
        let dom = box[0].children[box[0].children.length - 1]
        if (dom) {
          let className = 'order-list-item-hint'
          dom.addEventListener('animationend', function () {
            dom.classList.remove(className)
          })
          dom.classList.add(className)
          dom.scrollIntoView({ behavior: 'smooth' })
        }
      }
    }, 100)
  }

  const SelectSingleModelRef = ref()
  const ModelsDialogRef = ref()
  const UploadProofRef = ref()

  function visibilityChange() {
    if (document.visibilityState === 'visible') {
      // console.log('页面处于当前标签页');

      handleVisibilityChange(() => {
        // 弹窗表单关闭
        if (SelectSingleModelRef.value) {
          SelectSingleModelRef.value.close()
        }
        if (ModelsDialogRef.value) {
          ModelsDialogRef.value.close()
        }
        if (UploadProofRef.value) {
          UploadProofRef.value.close()
        }
      })
    } else {
      // console.log('页面不在当前标签页');
      // setCacheOrderFormList()
      clearTimer()
    }
  }

  onMounted(() => {
    if (useUserStore().isVip()) {
      // 跨标签监听消息
      crossTagListenMsg(data => {
        if (data.type === CrossTagMsgType.ORDER_FORM_UPDATE) {
          // getCacheOrderFormList()
          isUpdate.value = true
        }
      })
      // 当前标签页页面切换监听
      if (visibilityChangeEvent) {
        setSaveFormTimer()
        document.addEventListener(visibilityChangeEvent, visibilityChange)
      } else {
        // 降级方案：使用 blur/focus 事件（不推荐，准确性较低）
        throw new Error('浏览器不支持 visibilitychange 事件')
      }
    }
  })
  onUnmounted(() => {
    clearTimer()
    if (visibilityChangeEvent) {
      document.removeEventListener(visibilityChangeEvent, visibilityChange)
    }
  })

  return {
    SelectSingleModelRef,
    ModelsDialogRef,
    UploadProofRef,
  }
}

export {
  type OrderFormItem,
  type UploadPicFile,
  type TemporaryChangeData,
  type TotalOrderAmount,
  model_platform,
  guideSwitch,
  servicePrice,
  oldPrice,
  guideVisible,
  orderFormList,
  formAddLoading,
  formLoading,
  formDisabled,
  // showHintDialog,
  // hintDialogTitle,
  // hintDialogContent,
  reset,
  useOrderFormInit,
  createForm,
  handleAddOrderForm,
  handleCopyOrder,
  handleDeleteOrder,
  doDeleteModel,
  handleSubmitOrder,
  getCacheOrderFormList,
  setCacheOrderFormList,
  removeCacheOrderFormList,
  // 金额相关
  video_price,
  pic_price,
  service_price,
  exchange_price,
  computeOrderAmount,
  computeAllOrderAmount,
}
