<template>
  <div class="pages create-order-page">
    <!-- <HeaderBox /> -->
    <div class="content-tip-img" @click="goOpenMember" v-if="!store.isVip()">
      <img class="img" src="@/assets/image/create_order_vip.png" alt="" />

      <div class="content-img">
        <img class="content-img-bg" src="@/assets/image/order_banner_bg.png" alt="" />
        <div class="content-img-text flex-between">
          <div class="flex-center" style="justify-content: normal">
            <div class="text-title">开通会员</div>
            <div class="text-tip" v-if="twoOrderInfo.type && twoOrderInfo.type === 4">
              <div>·会员费折合每月$19.9</div>
              <div>·享每月首单立减${{ twoOrderInfo.amount }}</div>
            </div>
          </div>
          <div class="text-right">
            <div class="text-right-name">马上开通</div>
            <img style="width: 24px; height: 24px" src="@/assets/image/order_right_icon.png" alt="" />
          </div>
        </div>
      </div>
    </div>
    <div class="content-box" v-loading="formLoading">
      <!-- <div class="flex-between content-tip-box" v-if="!store.isVip()">
        <div>开通蜗牛会员，即可创建订单</div>
        <el-button type="primary" round size="small" @click="goOpenMember">立即开通</el-button>
      </div> -->

      <TransitionGroup tag="div" name="o-f-l" class="order-list-box" :class="{ 'min-h': !store.isVip() }">
        <div
          class="order-list-item"
          v-for="(item, index) in orderFormList"
          :key="item.key"
          ref="OrderListItemRef"
          :data-index="index"
        >
          <OrderForm
            ref="OrderListItemFormRef"
            :index="index"
            :key="item.form.key"
            @showSelModelList="doShowSelModelList"
            @openSelect="openSelect"
            @onUpload="handleUploadProof"
            @action="handleAction"
          />
        </div>

        <div class="flex-center" key="btn">
          <el-button
            class="create-add-order-btn"
            type="primary"
            plain
            round
            icon="Plus"
            :disabled="formDisabled || orderFormList.length >= 30 || !store.isVip()"
            :loading="formAddLoading"
            @click="handleAddOrder"
          >
            添加订单
          </el-button>
        </div>
      </TransitionGroup>

      <div class="order-info-box" :class="{ 'info-box-height': !store.isVip() }">
        <OrderPreviewList ref="OrderPreviewListRef" @view="handleViewOrder" @submit="onSubmit" />
      </div>
    </div>

    <PublicDialog
      ref="ModelsDialogRef"
      width="500px"
      :title="`已选的意向模特(${orderFormList[actionIndex]?.form?.selModel?.length})`"
      :showFooterButton="false"
      :titleCenter="false"
      custom-close
      align-center
      destroy-on-close
      :close-on-click-modal="true"
    >
      <div class="model-info" style="padding-bottom: 20px; gap: 16px">
        <div
          class="model-info__item"
          v-for="(item, i) in orderFormList[actionIndex].form.selModel"
          :key="item.id"
        >
          <div>
            <div
              class="modal-lose flex-around"
              v-if="
                orderFormList[actionIndex].cannotModelList &&
                orderFormList[actionIndex].cannotModelList.includes(item.id)
              "
              @click="openSelect(-1)"
            >
              失效
            </div>
            <div class="play-modal flex-around">
              <img
                style="height: 24px; width: 24px"
                @click="doDeleteModel(actionIndex, i)"
                src="@/assets/icon/icon_delete_fff.png"
                alt=""
              />
            </div>
            <el-image class="head-img" :src="$picUrl + item.modelPic + '!3x4'" fit="fill">
              <template #error>
                <el-icon size="50"><Avatar /></el-icon>
              </template>
            </el-image>
            <div class="more-ell" style="width: 50px; text-align: center">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </PublicDialog>
    <SelectSingleModel
      v-if="orderFormList.length > 0"
      :limit="orderFormList[actionIndex]?.form?.shootCount || 1"
      :nation="orderFormList[actionIndex]?.form?.shootingCountry || '7'"
      :modelType="orderFormList[actionIndex]?.form?.modelType || ''"
      :platform="orderFormList[actionIndex]?.form?.platform || '0'"
      ref="SelectSingleModelRef"
      @close="handleCloseSelectModel"
      @success="handleSelectSuccess"
    />
    <UploadProof
      ref="UploadProofRef"
      :title="uploadProofTitle"
      :fileType="['jpg', 'jpeg', 'png', 'bmp']"
      @success="upSuccess"
      :limit="upLimit"
    >
      <template #tip>
        <div class="el-upload__tip">
          请上传大小不超过
          <span style="color: #f29c2d">5M</span>
          ，格式为
          <span style="color: #f29c2d">png/jpg/jpeg/bmp</span>
          的图片
        </div>
      </template>
    </UploadProof>
  </div>
</template>

<script setup lang="ts">
import OrderForm from '@/views/order/create/components/OrderForm.vue'
import OrderPreviewList from '@/views/order/create/components/OrderPreviewList.vue'
// import HeaderBox from '@/views/order/create/components/HeaderBox.vue'
import SelectSingleModel from '@/views/model/components/SelectSingleModel.vue'
import UploadProof from '@/components/public/dialog/DragUploadDialog.vue'
import { nextTick, onMounted, ref } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { useRouter } from 'vue-router'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { MessageBox } from '@/utils/message'
import { createOrder, createOrderCount, getServiceFeeConfig } from '@/api/order'

import {
  oldPrice,
  servicePrice,
  guideSwitch,
  guideVisible,
  orderFormList,
  formAddLoading,
  formLoading,
  formDisabled,
  useOrderFormInit,
  handleAddOrderForm,
  doDeleteModel,
  handleSubmitOrder,
  setCacheOrderFormList,
  removeCacheOrderFormList,
} from '@/views/order/create/hooks/index'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { oneOrderInfo, handelRenewDialog,twoOrderInfo } = useShowRenewDialog()

const store = useUserStore()
const router = useRouter()

const { SelectSingleModelRef, ModelsDialogRef, UploadProofRef } = useOrderFormInit()

// const SelectSingleModelRef = ref()
// const ModelsDialogRef = ref()
const actionIndex = ref(0)

const uploadProofTitle = ref('')
const upLimit = ref(1)
// const UploadProofRef = ref()

const OrderListItemRef = ref()
const OrderListItemFormRef = ref()
const OrderPreviewListRef = ref()

function handleViewOrder(i: number, isErr: boolean = false) {
  let dom = OrderListItemRef.value.find((item: any) => item.dataset.index == i)
  if (dom) {
    let className = isErr ? 'order-list-item-hint-err' : 'order-list-item-hint'
    dom.addEventListener('animationend', function () {
      dom.classList.remove(className)
    })
    dom.classList.add(className)
    dom.scrollIntoView({ behavior: 'smooth' })
  }
}

// 添加订单
function handleAddOrder() {
  let add = handleAddOrderForm()
  if (add) {
    setCacheOrderFormList()
    nextTick(() => {
      OrderPreviewListRef.value.handleScrollIntoView(orderFormList.value.length - 1)
      nextTick(() => {
        handleViewOrder(orderFormList.value.length - 1)
      })
    })
  }
}

// 当前操作的表单索引
function handleAction(i: number) {
  actionIndex.value = i
}

//查看更多模特
function doShowSelModelList(i: number) {
  actionIndex.value = i
  ModelsDialogRef.value?.open()
}
// 选择意向模特
function openSelect(index: number) {
  if (index >= 0) {
    actionIndex.value = index
  }
  if (!orderFormList.value[actionIndex.value].form.modelType) {
    ElMessage.warning('请选择模特类型')
    return
  }
  if (
    orderFormList.value[actionIndex.value].cannotModelList &&
    orderFormList.value[actionIndex.value].cannotModelList.length > 0
  ) {
    orderFormList.value[actionIndex.value].form.selModel = orderFormList.value[
      actionIndex.value
    ].form.selModel?.filter((item: any) => {
      return orderFormList.value[actionIndex.value].cannotModelList.indexOf(item.id) == -1
    })
    orderFormList.value[actionIndex.value].cannotModelList = []
    orderFormList.value[actionIndex.value].cannotModelNumber = 0
  }
  nextTick(() => {
    SelectSingleModelRef.value.open(orderFormList.value[actionIndex.value].form.selModel)
  })
}

// 关闭选择意向模特
function handleCloseSelectModel() {
  if (!orderFormList.value[actionIndex.value]?.form?.selModel?.length) {
    orderFormList.value[actionIndex.value].form.selModelType = 0
  }
}
// 选择意向模特成功
function handleSelectSuccess(arr: any[]) {
  orderFormList.value[actionIndex.value].form.selModel = arr
  orderFormList.value[actionIndex.value].form.selModelType = 1
  SelectSingleModelRef.value?.close()
}
// 上传
function handleUploadProof(type: string, index: number) {
  actionIndex.value = index
  if (type === 'picCount') {
    uploadProofTitle.value = '上传参考图片'
    if (orderFormList.value[actionIndex.value].form.picCount) {
      upLimit.value =
        orderFormList.value[actionIndex.value].addPicNumber -
        (orderFormList.value[actionIndex.value].form.referencePic?.length || 0)
      UploadProofRef.value?.open()
    }
  } else {
    uploadProofTitle.value = '上传产品图片'
    upLimit.value = 1
    UploadProofRef.value?.open()
  }
}
function upSuccess(data: any) {
  if (uploadProofTitle.value === '上传参考图片') {
    orderFormList.value[actionIndex.value].form.referencePic?.push(...data)
  } else {
    orderFormList.value[actionIndex.value].form.filePicList.push(...data)
  }
}

//去开通会员
function goOpenMember() {
  router.push('/vip')
}

const createParams = ref<any>()

// 提交订单
function onSubmit() {
  if (!OrderListItemFormRef.value) {
    throw new Error('OrderListItemFormRef is error')
  }
  formDisabled.value = true
  const all_promise: any[] = []
  OrderListItemFormRef.value?.forEach((item: any) => {
    all_promise.push(item.validate())
  })
  Promise.allSettled(all_promise)
    .then((res: any) => {
      res = res.filter((item: any) => item.status === 'rejected')
      // 表单校验失败
      if (res.length) {
        res = res.sort((a: any, b: any) => a.reason.index - b.reason.index)
        OrderPreviewListRef.value?.errorHint(res[0].reason.key, res[0].reason.index)
        nextTick(() => {
          let i = orderFormList.value.findIndex(item => item.form?.key == res[0].reason)
          handleViewOrder(res[0].reason.index, true)
        })
        ElMessage.error('有内容未填写！')
        formDisabled.value = false
        return
      }
      store
        .realTimeCheckVip()
        .then(() => {
          if (store.isVip()) {
            ElMessageBox.confirm(`<div>是否确认提交订单？</div>`, '提示', {
              dangerouslyUseHTMLString: true,
            })
              .then(() => {
                let params = handleSubmitOrder()
                createOrder(params)
                  .then((res: any) => {
                    orderFormList.value.forEach((item: any) => {
                      item.cannotModelNumber = 0
                      item.cannotModelList.length = 0
                    })
                    if (res.data?.unfulfilledOrderModeSerialNumberMap) {
                      createParams.value = params
                      let models = []
                      for (let k of Object.keys(res.data.unfulfilledOrderModeSerialNumberMap)) {
                        let i = +k - 1
                        orderFormList.value[i].cannotModelNumber =
                          res.data.unfulfilledOrderModeSerialNumberMap[k].length
                        orderFormList.value[i].cannotModelList =
                          res.data.unfulfilledOrderModeSerialNumberMap[k]
                        if (orderFormList.value[i].form.selModel) {
                          models.push(
                            ...(orderFormList.value[i].form.selModel
                              .filter(
                                item => res.data.unfulfilledOrderModeSerialNumberMap[k].indexOf(item.id) != -1
                              )
                              .map(item => `“${item.name}”`) || [])
                          )
                        }
                      }
                      MessageBox(
                        `有${models.length}位意向模特已无法满足拍摄需求，是否继续提交订单？<br/>
                      <span>需更换订单中模特${models.join('， ')}</span><br/>
                      <span>若不重选，平台也会为您匹配合适的模特</span>`,
                        {
                          cancelButtonText: '重选模特',
                          confirmButtonText: '继续提交',
                        }
                      ).then(() => {
                        handleWarningConfirm()
                      })
                      return
                    }
                    ElMessage.success('订单创建成功')
                    removeCacheOrderFormList()
                    handelRenewDialog()
                    if (res.data.orderNum) {
                      router.replace({ name: 'order-pay', state: { orderNum: res.data.orderNum } })
                    } else {
                      router.replace('/order/list')
                    }
                  })
                  .finally(() => {
                    formDisabled.value = false
                  })
              })
              .catch(() => (formDisabled.value = false))
          } else {
            ElMessage.error('请开通会员后再进行下单')
          }
        })
        .catch(() => (formDisabled.value = false))
    })
    .catch(error => {
      console.error(error)
      formDisabled.value = false
    })
}
// 二次确认提交处理
function handleWarningConfirm() {
  if (createParams.value?.length) {
    createParams.value.forEach((item: any, i: number) => {
      item.intentionModelIds = replaceDuplicatesWithEmpty(
        item.intentionModelIds || [],
        orderFormList.value[i].cannotModelList
      )
    })
    store.realTimeCheckVip().then(() => {
      if (store.isVip()) {
        formDisabled.value = true
        const el_loading = ElLoading.service({
          lock: true,
          text: '正在提交中...',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        createOrder(createParams.value)
          .then((res: any) => {
            ElMessage.success('订单创建成功')
            removeCacheOrderFormList()
            handelRenewDialog()
            if (res.data.orderNum) {
              router.replace({ name: 'order-pay', state: { orderNum: res.data.orderNum } })
            } else {
              router.replace('/order/list')
            }
          })
          .finally(() => {
            el_loading.close()
            formDisabled.value = false
          })
      } else {
        ElMessage.error('请开通会员后再进行下单')
      }
    })
  }
}
// 替换不符合模特
function replaceDuplicatesWithEmpty(arr1: any[], arr2: any[]) {
  // 创建一个集合来跟踪第二个数组中的元素
  const arr2Set = new Set(arr2)

  // 遍历第一个数组，当元素在第二个数组中出现时，将其替换为空值
  const resultArray = arr1.map((item: any) => (arr2Set.has(item) ? '' : item))

  return resultArray || []
}

onMounted(() => {
  if (store.isVip()) {
    createOrderCount().then(res => {
      if (res.data < 3) {
        nextTick(() => {
          guideSwitch.value = true
          guideVisible.value = true
        })
      }
    })
    getServiceFeeConfig().then(res => {
      if (store.isProxy()) {
        servicePrice.value = res.data.currentPriceProxy
        oldPrice.value = res.data.originPriceProxy

      } else {
        servicePrice.value = res.data.currentPrice
        oldPrice.value = res.data.originPrice
      }
    })
  }
  getServiceFeeConfig().then(res => {
    if (store.isProxy() && store.isVip()) {
      servicePrice.value = res.data.currentPriceProxy
      oldPrice.value = res.data.originPriceProxy
    } else {
      servicePrice.value = res.data.currentPrice
      oldPrice.value = res.data.originPrice
    }
  })
})
</script>

<style scoped lang="scss">
.o-f-l-move,
.o-f-l-enter-active,
.o-f-l-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}
.o-f-l-enter-from {
  opacity: 0;
}
.o-f-l-leave-to {
  opacity: 0;
  transform: translate(30px, 0);
}
.o-f-l-leave-active {
  position: absolute;
}

.order-list-item-hint {
  animation: order-list-item-hint-animate 3s linear;
}
.order-list-item-hint-err {
  animation: order-list-item-hint-err-animate 3s linear;
}
@keyframes order-list-item-hint-animate {
  0% {
    border-color: #fff;
  }
  25% {
    border-color: #5695c0;
  }
  50% {
    border-color: #fff;
  }
  75% {
    border-color: #5695c0;
  }
  100% {
    border-color: #fff;
  }
}
@keyframes order-list-item-hint-err-animate {
  0% {
    border-color: #fff;
  }
  25% {
    border-color: #f56c6c;
  }
  50% {
    border-color: #fff;
  }
  75% {
    border-color: #f56c6c;
  }
  100% {
    border-color: #fff;
  }
}

.guide-tip-box {
  width: 175px;
  font-size: 12px;

  .btn {
    margin-top: -13px;

    .el-button {
      font-size: 12px;
    }
  }
}
.create-order-page {
  // padding: 0 28px 0 18px;
  padding: 0 28px 0 23px;
  overflow: hidden;

  .content-tip-box {
    width: calc(100% - 300px - 15px);
    height: 40px;
    font-size: 14px;
    color: #333;
    margin: 0 auto 10px 5px;
    border-radius: 4px;
    background-color: #fff;
    padding: 5px 10px;
    box-sizing: border-box;
    box-shadow: 0px 0 10px 0px #9292925c;
  }
  .content-tip-img {
    position: relative;
    width: 100%;
    height: 52px;
    display: flex;
    cursor: pointer;

    .img {
      width: 106px;
      height: 52px;
    }
    .img-right {
      height: 52px;
      background: linear-gradient(180deg, #fffcf8 0%, #ffeacf 99%);
      border-top-right-radius: 10px;
      border-bottom-right-radius: 10px;
      box-sizing: border-box;
    }
    .content-img {
      width: calc(100% - 106px);
      position: relative;
      height: 52px;
      &-bg {
        width: 100%;
        height: 52px;
      }
      &-text {
        display: flex;
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
        // padding: 0 30px 0 32px;
      }
      .text-title {
        font-size: 20px;
        color: #382929;
        font-weight: 500;
        margin-left: 32px;
      }
      .text-tip {
        font-size: 13px;
        color: #d96e26;
        margin-left: 30px;
      }
      .text-right {
        display: flex;
        align-items: center;
        margin-right: 30px;
        &-name {
          font-size: 16px;
          color: #392a29;
          font-weight: 500;
          margin-right: 13px;
        }
      }
    }
  }
  .content-box {
    margin-top: 16px;
    position: relative;
    width: 100%;
    height: 100%;
    align-items: flex-start;

    .order-list-box {
      position: relative;
      width: calc(100% - 300px - 15px);
      height: 100%;
      // padding: 0px 0px 0 5px;
      overflow: hidden;
      overflow-y: auto;

      &.min-h {
        height: calc(100% - 50px);
      }

      .order-list-item {
        width: 100%;
        border-radius: 6px;
        border: 1px solid #fff;
        margin-bottom: 15px;
      }

      .create-add-order-btn {
        width: 200px;
        margin: 0 0 30px;
        background-color: #fff;

        &:hover {
          background-color: var(--el-color-primary-light-9);
          border-color: var(--el-color-primary);
          color: var(--el-color-primary);
        }
        &.is-disabled:hover {
          background-color: #fff;
          border-color: var(--el-color-primary-light-8);
          color: var(--el-color-primary-light-5);
        }
      }
    }
    .order-info-box {
      position: absolute;
      top: 0;
      right: 0;
      width: 300px;
      height: calc(100% - 10px);
      background-color: #fff;
      border-radius: 6px;
    }
    .info-box-height {
      height: calc(100% - 62px);
    }
  }
}
.model-info {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 8px 16px;
  // align-items: center;
  margin-top: 10px;
  justify-content: start;
  max-height: 400px;
  overflow: auto;
  :deep(.el-image__inner) {
    height: auto;
  }
  &__item {
    transition: opacity 0.5s ease;
    opacity: 1;
    //
    //margin-right: 8px;
    position: relative;
    cursor: pointer;

    .head-img {
      cursor: pointer;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      border: 1px solid var(--border-gray-color);
      background: var(--border-gray-color);
      line-height: 1;
    }
    .modal-lose {
      color: #fff;
      width: 50px;
      height: 50px;
      position: absolute;
      inset: 0;
      border-radius: 50%;
      background-color: #2c2b2b66;
      z-index: 9;
      opacity: 1;
    }
    // .modal-lose {
    //   width: 40px;
    //   height: 40px;
    //   position: absolute;
    //   top: 0;
    //   left: 0;
    //   border-radius: 50%;
    //   background-color: #2c2b2b66;
    //   z-index: 9;
    //   opacity: 1;
    // }
    .play-modal {
      width: 50px;
      height: 50px;
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 50%;
      // width: 100%;
      // height: 100%;
      background-color: #2c2b2b66;
      z-index: 9;
      opacity: 0;
    }
    &:hover {
      .play-modal {
        opacity: 1;
      }
    }
  }
  .model-more {
    cursor: pointer;
    &__icon {
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 800;
      color: #fff;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: var(--el-color-primary);
      font-size: 20px;
    }
    &__text {
      margin-top: -7px;
      // line-height: 1;
      font-size: 12px;
      text-align: center;
      color: var(--el-color-primary);
    }
  }
}
</style>
