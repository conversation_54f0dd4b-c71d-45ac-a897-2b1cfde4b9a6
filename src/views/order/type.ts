import { ORDER_STATUS, AUDIT_STATUS, REFUND_STATUS, PAY_TYPE } from '@/utils/order'
export type SearchSelect =
  | 'modelAccount'
  | 'modelName'
  | 'productName'
  | 'videoCode'
  | 'orderNum'
  | 'refundNum'
  | 'shootModelId'
  | 'shootModelName'
export type FormCompType = 'add' | 'edit' | 'editCartOrder' | 'readOnly'
// 请求参数
export type QueryParams = {
  val?: string // 自定义参数
  select?: SearchSelect // 自定义参数
  modelId?: string
  modelName?: string
  productName?: string
  videoCode?: string
  orderNum?: string
  refundNum?: string
  shootModelId?: string
  shootModelName?: string
  platform?: string[]
  orderUserId?: number[]
  status?: ORDER_STATUS[]
  placeOrderTime?: string[] // 自定义参数
  orderTimeBegin?: string
  orderTimeEnd?: string
  playTime?: string[] // 自定义参数
  payTimeBegin?: string
  payTimeEnd?: string
  payType?: PAY_TYPE[]
  pageNum?: number
  pageSize?: number
  [x: string]: any
}
//购物车列表请求参数
export type QueryCarParams = {
  createOrderUserIds?: number[]
  keyword?: string
  platforms?: (number[] | string)[]
}
// 限制条件
export type LimitingCondition = {
  content: string
  id?: number
  sort?: number
  type: number
  createBy?: string
  createTime?: string
  updateBy?: string
  updateTime?: string
  videoId?: number
}
export type UrlItem = {
  id: number
  name: string
  picUrl: string
  videoUrl: string
}
export type ReferencePic = UrlItem & {
  response?: {
    [x: string]: any
  }
}
export type UploadPicFile = {
  name?: string
  url?: string
  id?: number
  picUrl?: string
}
// 订单表单
export type OrderForm = {
  modelType: string
  modelTypeList?: string[]
  platform: string
  productChinese: string
  productEnglish: string
  isLink?: number
  productLink: string
  // productPic: UrlItem
  productPicInfo: UrlItem
  filePicList: UploadPicFile[]
  productPic?: number | string
  isObject: number
  referenceVideoLink: string
  videoFormat: number
  shootingCountry: string
  picCount?: number
  shootCount: number
  referencePic?: ReferencePic[]
  intentionModelIds?: number[]
  selModelType?: number
  selModel?: {
    id?: number
    picUrl?: string
    name?: string
    type?: string
    [x: string]: any
  }[]
  intentionModelId?: number
  limitingCondition?: LimitingCondition[]
  demands: string
  shootRequired?: {
    content: string
    [x: string]: any
  }[]
  // demands?: {
  //   value: string
  //   [x: string]: any
  // }[]
  conditions?: {
    value: string
    [x: string]: any
  }[]
  shootSuggestType?: 'null' | 'link' |  'demands'
  sellingPointProduct?: string
}
// 订单发票信息对象
export type OrderInvoice = {
  amount: number
  businessInfo: null
  dutyParagraph: string
  id: number
  invoicingTime: string
  number: string
  operator: {
    id: number
    name: string
    phonenumber: string
  }
  operatorTime: string
  orderNum: string
  payTime: string
  status: number
  title: string
  type: number
}

// 商家信息对象VO
export type OrderUserInfo = {
  account: string
  balance: number
  businessId: number
  businessName: string
  businessStatus: number
  customerType: number
  externalUserId: string
  id: number
  invoiceContent: string
  invoiceDutyParagraph: string
  invoiceTitle: string
  invoiceTitleType: number
  isBalanceLock: number
  isOwnerAccount: number
  isProxy: number
  lastLoginTime: string
  memberCode: string
  memberFirstTime: string
  memberFirstType: number
  memberLastTime: string
  memberPackageType: number
  memberStatus: number
  memberType: number
  memberValidity: string
  name: string
  nickName: string
  ownerAccount: string
  pic: string
  status: number
  unionid: string
  waiterId: number
}
export type merchantVO = {
  merchantCode: string
  merchantName: string
  merchantId: number
}
// 模特信息
export type ModelInfo = {
  acceptability: number
  account: string
  city: string
  commission: number
  commissionUnit: string
  detailAddress: string
  id: number
  lastLoginTime: string
  name: string
  persons: {
    id: number
    name: string
    phonenumber: string
  }[]
  pic: string
  picId: number
  recipient: string
  state: string
  status: number
  type: string
  zipcode: string
}
// 订单_对象VO
export type OrderVO = {
  id: number
  auditStatus: AUDIT_STATUS
  orderNum: string
  orderTime: string
  overTime: string
  orderAmount: number
  payAmount: number
  payDocument: string
  payTime: string
  orderUserInfo?: OrderUserInfo
  payUserId?: number
  useBalance?: number
  videoCount?: number
}
// 子订单_视频对象VO
export type OrderVideoItem = {
  id: number
  amount?: number
  allowUpload?: number
  contactId?: number
  createOrderUserInfo?: OrderUserInfo
  feedbackMaterialUrl?: string
  // intentionModelId?: number
  intentionModel?: ModelInfo
  isObject: number
  issueId?: number
  limitingCondition?: LimitingCondition[]
  logisticFlag?: number
  logisticInfo?: Logistics
  modelType: string
  orderNum: string
  picCount?: number
  platform?: string
  productChinese: string
  productEnglish: string
  productLink?: string
  productPic?: UrlItem
  referencePic?: UrlItem
  referenceVideoLink?: string
  // selectedModelId?: number
  selectedModel?: ModelInfo
  // shootModelId?: number
  shootModel?: ModelInfo
  shootRequired?: LimitingCondition[]
  shootingCountry?: number
  status?: ORDER_STATUS
  videoCode?: string
  videoFormat?: number
  videoId?: number
}

// 退款请求参数
export type RefundQueryParams = {
  val?: string // 自定义参数
  select?: SearchSelect // 自定义参数
  refundStatus?: REFUND_STATUS[]
  refundType?: string
  refundTime?: string[] // 自定义参数
  timeBegin?: string
  timeEnd?: string
  pageNum?: number
  pageSize?: number
  [x: string]: any
}
// 退款订单列表
export type OrderRefundListItem = {
  amount: number
  applyTime: string
  id: number
  initiator: string
  initiatorSource: number
  merchant: OrderUserInfo
  operateTime: string
  orderNum: string
  productChinese: string
  productEnglish: string
  refundAmount: number
  refundCause: string
  refundNum: string
  refundStatus: number
  refundType: number
  rejectCause: string
  shootModel: ModelInfo
  status: number
  videoCode: string
  videoId: number
}
export type OrderRefundList = OrderRefundListItem[]

export type Logistics = {
  city: string
  country: string
  curTime: string
  description: string
  id: number
  latitude: string
  location: string
  longitude: string
  mainStatus: string
  mainStatusSketch: string
  number: string
  state: string
  street: string
  subStatus: string
  subStatusSketch: string
}
