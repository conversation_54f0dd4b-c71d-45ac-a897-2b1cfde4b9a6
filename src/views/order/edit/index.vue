<template>
  <div class="pages edit-order-page">
    <!-- <HeaderBox title="修改订单" /> -->

    <div class="order-list-box" v-loading="formLoading">
      <div
        class="order-list-item"
        v-for="(item, index) in orderFormList"
        :key="item.key"
        ref="OrderListItemRef"
      >
        <OrderFormComp
          ref="OrderListItemFormRef"
          :key="item.form.key"
          :index="index"
          :orderNum="item.orderNum"
          :videoPromotionAmount="isVideoPromotionAmount"
          @showSelModelList="doShowSelModelList"
          @openSelect="openSelect"
          @onUpload="handleUploadProof"
          @action="handleAction"
        />
      </div>

      <div class="flex-center" key="btn">
        <el-button class="save-order-btn" :loading="formDisabled" type="primary" @click="onSave">
          保存修改
        </el-button>
      </div>
    </div>

    <PublicDialog
      ref="ModelsDialogRef"
      width="500px"
      :title="ModelsDialogTitle"
      :showFooterButton="false"
      :titleCenter="false"
      custom-close
      align-center
      destroy-on-close
      :close-on-click-modal="true"
    >
      <div class="model-info" style="padding-bottom: 20px; gap: 16px">
        <div
          class="model-info__item"
          v-for="(item, i) in orderFormList[actionIndex].form.selModel"
          :key="item.id"
        >
          <div>
            <div
              class="modal-lose flex-around"
              v-if="
                orderFormList[actionIndex].cannotModelList &&
                orderFormList[actionIndex].cannotModelList.includes(item.id)
              "
              @click="openSelect(-1)"
            >
              失效
            </div>
            <div class="play-modal flex-around">
              <img
                style="height: 24px; width: 24px"
                @click="doDeleteModel(actionIndex, i)"
                src="@/assets/icon/icon_delete_fff.png"
                alt=""
              />
            </div>
            <el-image class="head-img" :src="$picUrl + item.modelPic + '!3x4'" fit="fill">
              <template #error>
                <el-icon size="50"><Avatar /></el-icon>
              </template>
            </el-image>
            <div class="head-name more-ell" style="margin-top: -5px">{{ item.name }}</div>
          </div>
        </div>
      </div>
    </PublicDialog>
    <SelectSingleModel
      v-if="orderFormList.length > 0"
      :limit="orderFormList[actionIndex].form.shootCount"
      :nation="orderFormList[actionIndex].form.shootingCountry"
      :modelType="orderFormList[actionIndex].form.modelType"
      :platform="orderFormList[actionIndex].form.platform"
      ref="SelectSingleModelRef"
      @close="handleCloseSelectModel"
      @success="handleSelectSuccess"
    />
    <UploadProof
      ref="UploadProofRef"
      :title="uploadProofTitle"
      :fileType="['jpg', 'jpeg', 'png', 'bmp']"
      @success="upSuccess"
      :limit="upLimit"
    >
      <template #tip>
        <div class="el-upload__tip">
          请上传大小不超过
          <span style="color: #f29c2d">5M</span>
          ，格式为
          <span style="color: #f29c2d">png/jpg/jpeg/bmp</span>
          的图片
        </div>
      </template>
    </UploadProof>
  </div>
</template>

<script lang="ts" setup>
import HeaderBox from '@/views/order/create/components/HeaderBox.vue'
import OrderFormComp from '@/views/order/create/components/OrderForm.vue'
import SelectSingleModel from '@/views/model/components/SelectSingleModel.vue'
import UploadProof from '@/components/public/dialog/DragUploadDialog.vue'
import { computed, getCurrentInstance, ref } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { OrderForm } from '@/views/order/type'
import { updateOrder, updateOrderDetails } from '@/api/order'
import {
  orderFormList,
  formLoading,
  formDisabled,
  doDeleteModel,
  handleAddOrderForm,
  handleSubmitOrder,
} from '@/views/order/create/hooks/index'
import { ElMessageBox } from 'element-plus'

const { proxy } = getCurrentInstance() as any
const picUrlPath = proxy.$picUrl

const store = useUserStore()
const route = useRoute()
const router = useRouter()

const SelectSingleModelRef = ref()
const ModelsDialogRef = ref()
const actionIndex = ref(0)
const ModelsDialogTitle = computed(() => {
  if (orderFormList.value.length) {
    return `已选的意向模特(${orderFormList.value[actionIndex.value].form?.selModel?.length || 0})`
  }
  return ''
})
const isVideoPromotionAmount = ref(false)

const uploadProofTitle = ref('')
const upLimit = ref(1)
const UploadProofRef = ref()

const OrderListItemRef = ref()
const OrderListItemFormRef = ref()

// 当前操作的表单索引
function handleAction(i: number) {
  actionIndex.value = i
}

//查看更多模特
function doShowSelModelList(i: number) {
  actionIndex.value = i
  ModelsDialogRef.value?.open()
}
// 选择意向模特
function openSelect(index: number) {
  if (index >= 0) {
    actionIndex.value = index
  }
  if (!orderFormList.value[actionIndex.value].form.modelType) {
    ElMessage.warning('请选择模特类型')
    return
  }
  if (
    orderFormList.value[actionIndex.value].cannotModelList &&
    orderFormList.value[actionIndex.value].cannotModelList.length > 0
  ) {
    orderFormList.value[actionIndex.value].form.selModel = orderFormList.value[
      actionIndex.value
    ].form.selModel?.filter((item: any) => {
      return orderFormList.value[actionIndex.value].cannotModelList.indexOf(item.id) == -1
    })
    orderFormList.value[actionIndex.value].cannotModelList = []
    orderFormList.value[actionIndex.value].cannotModelNumber = 0
  }
  SelectSingleModelRef.value.open(orderFormList.value[actionIndex.value].form.selModel)
}

// 关闭选择意向模特
function handleCloseSelectModel() {
  if (!orderFormList.value[actionIndex.value]?.form?.selModel?.length) {
    orderFormList.value[actionIndex.value].form.selModelType = 0
  }
}
// 选择意向模特成功
function handleSelectSuccess(arr: any[]) {
  orderFormList.value[actionIndex.value].form.selModel = arr
  orderFormList.value[actionIndex.value].form.selModelType = 1
  SelectSingleModelRef.value?.close()
}
// 上传
function handleUploadProof(type: string, index: number) {
  actionIndex.value = index
  if (type === 'picCount') {
    uploadProofTitle.value = '上传参考图片'
    if (orderFormList.value[actionIndex.value].form.picCount) {
      upLimit.value =
        orderFormList.value[actionIndex.value].addPicNumber -
        (orderFormList.value[actionIndex.value].form.referencePic?.length || 0)
      UploadProofRef.value?.open()
    }
  } else {
    uploadProofTitle.value = '上传产品图片'
    upLimit.value = 1
    UploadProofRef.value?.open()
  }
}
function upSuccess(data: any) {
  if (uploadProofTitle.value === '上传参考图片') {
    orderFormList.value[actionIndex.value].form.referencePic?.push(...data)
  } else {
    orderFormList.value[actionIndex.value].form.filePicList.push(...data)
  }
}

// 保存
function onSave() {
  if (!OrderListItemFormRef.value) {
    throw new Error('OrderListItemFormRef is error')
  }
  if (OrderListItemFormRef.value.length) {
    formDisabled.value = true
    OrderListItemFormRef.value[0]
      .validate()
      .then(() => handleSaveOrder())
      .catch(() => {
        formDisabled.value = false
      })
  }
}
function handleSaveOrder() {
  ElMessageBox.confirm('确认修改！', '提示', {})
    .then(() => {
      let params = handleSubmitOrder()
      const intentionModelId = params[0].intentionModelIds?.length ? params[0].intentionModelIds[0] : ''
      const intentionModelIds: any[] = []
      updateOrder({
        ...params[0],
        intentionModelId,
        intentionModelIds,
        id: route.params.orderId,
      })
        .then(res => {
          ElMessage.success('修改成功')
          router.replace('/order/list')
        })
        .catch((err: any) => {
          if (err.data?.msg?.indexOf('已取消') > -1 || err.data?.msg?.indexOf('已支付') > -1) {
            router.replace('/order/list')
          }
        })
        .finally(() => {
          formDisabled.value = false
        })
    })
    .catch(() => {
      formDisabled.value = false
    })
}

// 获取订单数据
function getOrderDetails(orderId: any) {
  formLoading.value = true
  updateOrderDetails(orderId)
    .then((res: any) => {

      isVideoPromotionAmount.value = res.data.videoPromotionAmount ? true : false

      let demands = ''
      let conditions = []
      let referencePic = []

      if (res.data.shootRequired && res.data.shootRequired.length) {
        let countList: any[] = []
        res.data.shootRequired.forEach((item: any, index: any) => {
          // countList.push(`${index + 1}.` + item.content)
          countList.push(item.content)
        })
        demands = countList.join('\n')
        // console.log(countList.join('\n'),777);
      }
      if (res.data.limitingCondition && res.data.limitingCondition.length) {
        conditions = res.data.limitingCondition.map((item: { [x: string]: any }) => ({
          value: item.content,
          content: item.content,
          id: item.id,
        }))
      } else {
        conditions.push({
          value: '',
        })
      }
      if (res.data.referencePic && res.data.referencePic.length) {
        referencePic = res.data.referencePic.map((item: any) => ({
          url: item,
          picUrl: item,
        }))
      }

      let selModel: any[] = []
      if (res.data.intentionModel) {
        selModel.push(res.data.intentionModel)
        selModel[0].picUrl = selModel[0].pic
        // selModel[0].platform = res.data.platform
      }

      const tempData = {
        url: res.data.productPic ? picUrlPath + res.data.productPic : '',
        name: res.data.productPic ? res.data.productPic : '',
        id: undefined,
        picUrl: res.data.productPic ? res.data.productPic : undefined,
      }
      let filePicList: any[] = []
      if (tempData.url && tempData.url != null) {
        filePicList = [tempData]
      }
      const data: {
        form: OrderForm
        addPicNumber: number
        orderNum: string
      } = {
        form: {
          platform: '' + res.data.platform,
          productChinese: res.data.productChinese,
          productEnglish: res.data.productEnglish,
          isLink: res.data.productLink ? 1 : 0,
          productLink: res.data.productLink,
          productPic: res.data.productLink ? '' : res.data.productPic,
          productPicInfo: res.data.productPic,
          isObject: res.data.isObject,
          referenceVideoLink: res.data.referenceVideoLink,
          videoFormat: res.data.videoFormat,
          shootingCountry: res.data.shootingCountry + '',
          modelType: '' + res.data.modelType,
          picCount: !res.data.picCount ? -1 : res.data.picCount,
          referencePic,
          filePicList: filePicList || [],
          selModelType: res.data.intentionModel ? 1 : 0,
          selModel,
          demands,
          conditions,
          shootCount: 1,
          shootSuggestType: setSuggestType(),
          sellingPointProduct: res.data.sellingPointProduct,
        },
        addPicNumber: res.data.picCount == 2 ? 5 : 2,
        orderNum: res.data.orderNum,
      }
      // console.log(data)
      function setSuggestType() {
        if (res.data.referenceVideoLink) {
          return 'link'
        }
        if (demands) {
          return 'demands'
        }
        return 'null'
      }
      // 添加到表单列表
      handleAddOrderForm(data)
    })
    .finally(() => (formLoading.value = false))
}

function init() {
  if (!route.params.orderId) {
    window.location.href = '/order/list'
    throw new Error('orderId is undefined')
  }
  getOrderDetails(route.params.orderId)
}
init()
</script>

<style scoped lang="scss">
.edit-order-page {
  .order-list-box {
    height: calc(100% - 50px);
    margin-top: 5px;
    padding: 0 0 0 5px;
    overflow-y: overlay;

    .order-list-item {
      width: 100%;
      border-radius: 6px;
    }
  }

  .save-order-btn {
    width: 200px;
    margin: 15px 0 20px;
  }
}
</style>
