<template>
  <div style="padding-right: 8px">
    <div class="card-head">
      <!-- <div class="card-head__title">
        <div class="head-title">
          <el-icon color="#5695c0"><CaretRight /></el-icon>
          我的购物车
        </div>
      </div> -->
      <el-form :model="queryParams" label-width="68px" :inline="true" style="margin: 0">
        <el-form-item>
          <el-input
            v-model="queryParams.keyword"
            style="max-width: 300px"
            placeholder="请输入搜索内容"
            width="200"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-select
            style="width: 180px"
            placeholder="请选择平台"
            multiple
            collapse-tags
            collapse-tags-tooltip
            v-model="queryParams.platforms"
            clearable
          >
            <el-option
              v-for="item in modelPlatformOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select
            style="width: 180px"
            placeholder="请选择订单运营"
            v-model="queryParams.createOrderUserIds"
            multiple
            clearable
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option
              v-for="item in orderOperationList"
              :key="item.id"
              :label="item.name || item.nickName || `${item.id}(员工已解绑)`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery(true)">搜索</el-button>
          <el-button plain icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="content-box">
      <div class="flex-start gap-10" style="margin: 0 20px 15px 0">
        <!-- <el-button type="primary" icon="Plus" @click="handleAddOrder('add')">创建订单</el-button> -->
        <!-- <div style="color: var(--text-gray-color)">购物车内已有 {{ orderList.length }} 个订单</div> -->
      </div>
      <div style="height: calc(100vh - 100px - 220px); overflow: auto">
        <el-table
          v-loading="loading"
          :data="orderList"
          @row-click="handleRowClick"
          :header-cell-style="{
            background: '#f6f6f6',
            color: '#09172F',
            paddingLeft: '30px',
            fontWeight: '400',
          }"
          max-height="calc(100vh - 100px - 220px)"
        >
          <template #empty>
            <Empty image-type="order-content" description="暂无数据" :image-size="150" />
          </template>
          <el-table-column prop="productChinese" minWidth="300" label="拍摄产品">
            <template v-slot="{ row }">
              <div style="display: flex">
                <div @click.stop>
                  <el-checkbox-group
                    v-model="checkedOrders"
                    :disabled="submitLoading"
                    @change="handleCheckedOrdersChange($event, row)"
                  >
                    <el-checkbox :value="row.id"></el-checkbox>
                  </el-checkbox-group>
                </div>
                <div class="info-box">
                  <!-- <div class="time">
                    {{ row.createOrderUserName || row.createOrderUserNickName }}（{{ row.createTime }}
                    加入购物车）
                  </div> -->
                  <div class="flex-start gap">
                    <el-image class="img" :src="pathHead + row.productPic + '!thumbnail200'" fit="fill">
                      <template #error>
                        <svg
                          v-if="!row.productPic"
                          class="icon"
                          aria-hidden="true"
                          style="width: 100%; height: 100%"
                        >
                          <use xlink:href="#icon-zanwutupian"></use>
                        </svg>
                        <div v-else class="img img-error">加载失败</div>
                      </template>
                    </el-image>
                    <div class="flex-column info">
                      <div class="one-ell" style="max-width: 300px">产品名称：{{ row.productChinese }}</div>
                      <!-- <div class="one-ell" style="max-width: 300px">英文名称：{{ row.productEnglish }}</div> -->
                      <span
                        v-if="row.productLink && http_reg.test(row.productLink)"
                        @click.stop="doGoProductLink(row.productLink)"
                        class="one-ell link"
                      >
                        <span style="color: #333">产品链接：</span>
                        {{ row.productLink }}
                      </span>
                      <span v-else class="one-ell">
                        <span style="color: #333">产品链接：</span>
                        {{ row.productLink }}
                      </span>
                      <div class="flex-start gap tag--box">
                        <template v-for="dict in biz_model_platform" :key="dict.value">
                          <el-tag
                            class="tag"
                            v-if="dict.value == row.platform"
                            type="info"
                            size="small"
                            round
                          >
                            {{ dict.label }}
                          </el-tag>
                        </template>
                        <template v-for="dict in biz_nation" :key="dict.value">
                          <el-tag
                            class="tag"
                            v-if="dict.value == row.shootingCountry"
                            type="info"
                            size="small"
                            round
                          >
                            {{ dict.label }}
                          </el-tag>
                        </template>
                        <template v-for="dict in biz_model_type" :key="dict.value">
                          <el-tag
                            class="tag"
                            v-if="dict.value == row.modelType"
                            type="info"
                            size="small"
                            round
                          >
                            {{ dict.label }}
                          </el-tag>
                        </template>
                        <template v-if="row.modelType == '3'">
                          <el-tag class="tag" type="info" size="small" round>亚马逊影响者/素人创作者</el-tag>
                        </template>
                        <!-- <template v-for="op in videoFormatOptions" :key="op.value">
                          <el-tag
                            class="tag"
                            v-if="op.value == row.videoFormat"
                            type="info"
                            size="small"
                            round
                          >
                            {{ op.label }}
                          </el-tag>
                        </template> -->
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="intentionModel" minWidth="200" label="意向模特">
            <template v-slot="{ row, $index }">
              <div class="pic-count-box" v-if="row.intentionModel && row.intentionModel.id">
                <div
                  style="height: 49px; height: 49px"
                  :class="{
                    'pic-err':
                      errorShoppingCartIds.includes(row.id) && errorModelIds.includes(row.intentionModel.id),
                  }"
                >
                  <el-image
                    class="img"
                    :src="row.intentionModel?.modelPic ? $picUrl + row.intentionModel?.modelPic + '!3x4' : ''"
                    fit="fill"
                  >
                    <template #error>
                      <el-icon size="40"><Avatar /></el-icon>
                    </template>
                  </el-image>
                </div>
                <div class="model-info">
                  <div class="model-info__name one-ell">{{ row.intentionModel?.name }}</div>
                  <div class="model-info__id">(ID:{{ row.intentionModel?.account }})</div>
                  <div class="flex-start model-info__btn">
                    <el-button
                      link
                      type="danger"
                      v-if="
                        errorShoppingCartIds.includes(row.id) && errorModelIds.includes(row.intentionModel.id)
                      "
                      @click.stop="againSelectModel(row.intentionModel, $index, row.id)"
                    >
                      该模特已无法接单，请重新选择
                    </el-button>
                    <template v-else>
                      <el-button
                        link
                        type="primary"
                        @click.stop="againSelectModel(row.intentionModel, $index, row.id)"
                      >
                        重新选择
                      </el-button>
                      <el-button link type="primary" @click.stop="delSelectModel(row, $index)">
                        删除
                      </el-button>
                    </template>
                  </div>
                </div>
                <!-- <div style="display: flex">
                  <el-button
                    link
                    type="danger"
                    v-if="row.intentionModel?.id && errorModelIds.includes(row.intentionModel.id)"
                    @click.stop="againSelectModel(row.intentionModel, $index, row.id)"
                  >
                    请重新选择
                  </el-button>
                  <div style="width: 80px" v-else>
                    <img
                      class="icon"
                      @click.stop="againSelectModel(row.intentionModel, $index, row.id)"
                      src="@/assets/icon/icon_change_model.png"
                      alt=""
                    />
                    <img
                      class="icon"
                      @click.stop="delSelectModel(row, $index)"
                      src="@/assets/icon/icon_delete.png"
                      alt=""
                    />
                  </div>
                </div> -->
              </div>
              <div class="pic-count-box" v-else>
                <el-button
                  style="font-size: 16px"
                  type="primary"
                  link
                  @click.stop="againSelectModel('', $index, row.id)"
                >
                  选择意向模特
                </el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="amount" minWidth="150" label="金额">
            <template v-slot="{ row, $index }">
              <div class="action-box">
                <div class="price-info">
                  <div class="money">
                    <span>{{ row.amount ? row.amount.toFixed(2) : '-' }}</span>
                    USD
                  </div>
                  <div @click.stop>
                    <el-popover
                      popper-style="border-radius: 10px"
                      :width="320"
                      placement="left-end"
                      trigger="click"
                      @show="handleChangeIcon($index)"
                      @hide="handleChangeIcon($index)"
                    >
                      <template #reference>
                        <el-button link style="color: #7f7f7f">
                          明细
                          <el-icon>
                            <ArrowUp v-if="row.isShowDetailBox" />
                            <ArrowDown v-else />
                          </el-icon>
                        </el-button>
                      </template>
                      <template #default>
                        <div class="detail-box">
                          <div class="detail-title">含以下费用</div>
                          <div class="detail-money">
                            <div class="text">视频佣金:</div>
                            <div class="price">${{ completeDecimals(row.videoPrice) }}</div>
                          </div>
                          <div class="detail-money">
                            <div class="text">照片佣金:</div>
                            <div class="price">
                              <span>${{ completeDecimals(row.picPrice) }}</span>
                            </div>
                          </div>
                          <div class="detail-money">
                            <div class="text">佣金代缴税费:</div>
                            <div class="price">${{ completeDecimals(row.commissionPaysTaxes) }}</div>
                          </div>
                          <div class="detail-money">
                            <div class="text">PayPal代付手续费:</div>
                            <div class="price">${{ completeDecimals(row.exchangePrice) }}</div>
                          </div>
                          <div class="detail-money">
                            <div class="text">服务费:</div>
                            <div class="price">${{ row.servicePrice }}</div>
                          </div>
                          <div class="detail-hint">
                            <div>个别费用说明：</div>
                            <div>&emsp;&emsp;· 美国PayPal官方手续费=本金*4.4%+0.3(USD)</div>
                            <div>&emsp;&emsp;· 佣金代缴税费=(视频佣金+照片佣金)*6%</div>
                          </div>
                        </div>
                      </template>
                    </el-popover>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="time" width="230" label="订单运营">
            <template v-slot="{ row }">
              <div class="order-user-info">
                <div>{{ row.createOrderUserName || row.createOrderUserNickName }}</div>
                <div style="font-size: 12px">{{ row.createTime }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column width="150" label="操作">
            <template v-slot="{ row }">
              <div class="options">
                <el-button
                  class="options-btn"
                  type="primary"
                  :disabled="submitLoading"
                  link
                  @click.stop="copyOrder(row.id)"
                >
                  复制一单
                </el-button>
                <el-button
                  class="options-btn"
                  style="margin-left: 0"
                  type="primary"
                  :disabled="submitLoading"
                  link
                  @click.stop="handleAddOrder('editCartOrder', row.id)"
                >
                  修改
                </el-button>
                <el-button
                  class="options-btn"
                  type="primary"
                  style="margin-left: 0"
                  :disabled="submitLoading"
                  link
                  @click.stop="del([row.id])"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="flex-between bottom-box">
        <div class="flex-start" style="margin: 0 20px 0 2px">
          <el-checkbox
            v-if="orderList.length"
            v-model="checkAll"
            :indeterminate="isIndeterminate"
            :disabled="submitLoading"
            @change="handleCheckAllChange"
          >
            全选
          </el-checkbox>
          &emsp;
          <el-button
            style="color: #d67c5a"
            v-if="orderList.length && checkedOrders.length"
            :disabled="submitLoading"
            link
            type="danger"
            @click="del(checkedOrders.filter(Boolean))"
          >
            删除选中订单
          </el-button>
        </div>
        <div class="flex-end gap bottom-right">
          <div style="margin-right: 10px">
            <div class="flex-end text">
              <span>
                已选数量：
                <span style="color: #d67c5a">{{ checkedOrders.length }}</span>
                ，
              </span>
              <span>当前预估总计&nbsp;</span>
              <div class="money">
                <span>{{ totalAmount?.toFixed(2) }}</span>
                USD
              </div>
            </div>
            <div class="tip">*此处价格展示为美金价格，最终结算价格请以结算页为准。</div>
          </div>
          <el-button
            class="submit-button"
            type="primary"
            round
            :disabled="!checkedOrders.length"
            :loading="submitLoading"
            @click="onSubmit()"
          >
            立即支付
          </el-button>
        </div>
      </div>
    </div>
    <SelectSingleModel
      :limit="1"
      :nation="selModel.shootingCountry"
      :modelType="selModel.modelType"
      @success="selModelSuccess"
      ref="SelectSingleModelRef"
      :platform="selModel.platform"
    />
    <MessageBoxDialog
      ref="MessageBoxRef"
      title=""
      width="500px"
      cancelText="重新选择模特"
      confirmText="忽略，直接下单"
      :dialogStyle="{
        '--el-dialog-padding-primary': '0px',
      }"
      @confirm="onSubmit()"
    >
      <div style="width: 100%">
        <h2 style="text-align: center; margin-top: 0">很抱歉，您选择的意向模特已无法接单</h2>
        <div style="color: var(--el-color-primary); text-align: center; font-size: 16px">
          您可以选择忽略，届时蜗牛运营会为您匹配相应合适的模特
        </div>
        <div
          style="
            background: var(--el-color-warning-light-9);
            padding: 10px;
            border-radius: 5px;
            margin-top: 24px;
            font-size: 13px;
          "
        >
          <span style="color: var(--el-color-warning)">无法接单模特：</span>
          {{ errorModelNames.join('，') }}，共{{ errorModelNames.length }}位。
        </div>
      </div>
    </MessageBoxDialog>
    <PublicDialog
      ref="orderFormDialogRef"
      width="1100px"
      :title="orderFormDialogTitle"
      :showFooterButton="false"
      custom-close
      align-center
      append-to-body
      :titleCenter="false"
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="beforeClose"
    >
      <OrderForm
        ref="orderFormRef"
        :type="orderFormType"
        :orderId="orderFormId"
        @successCart="createOrderSuccess"
        @success="createOrderSuccess"
        @error="orderFormDialogRef?.close()"
      />
    </PublicDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import {
  orderCartList,
  deleteOrderCart,
  copyOrderCart,
  cartSettleAccounts,
  orderEditCart,
  cartCreateUserSelect,
  updateCartIntentionModel,
} from '@/api/order'
import type { OrderUserInfo } from '../type'
import { videoFormatOptions, modelPlatformOptions } from '@/utils/data'
import { http_reg } from '@/utils/RegExp'
import SelectSingleModel from '@/views/model/components/SelectSingleModel.vue'
import OrderForm from '@/views/order/components/OrderForm.vue'
import { useUserStore } from '@/stores/modules/user'
import type { FormCompType, QueryCarParams } from '../type'
import { crossTagSendMsg, crossTagListenMsg, CrossTagMsgType } from '@/hooks/crossTagMsg'
import { useRouter } from 'vue-router'

const router = useRouter()
const { proxy } = getCurrentInstance() as any
const pathHead = proxy.$picUrl

const { biz_nation, biz_model_type, biz_model_platform } = proxy.useDict(
  'biz_nation',
  'biz_model_type',
  'biz_model_platform'
)

const checkAll = ref(false)
const isIndeterminate = ref(false)
const checkedOrders = ref<Array<string | number>>([])
const checkedOrderList = ref<any[]>([])
const loading = ref(false)
const orderList = ref<any[]>([])
const errorModelNames = ref<Array<string>>([])
const errorModelIds = ref<Array<string | number>>([])
const errorShoppingCartIds = ref<Array<string | number>>([])
const SelectSingleModelRef = ref<InstanceType<typeof SelectSingleModel>>()
const selModel = ref({
  shootingCountry: '',
  modelType: '',
  platform: '',
})
const selModelInfo = ref<
  {
    id?: number
    picUrl?: string
    name?: string
    type?: string
    [x: string]: any
  }[]
>([])
const cartListIndex = ref<number>(0)
const submitLoading = ref(false)
const MessageBoxRef = ref()

const orderOperationList = ref<OrderUserInfo[]>([])
const queryParams = ref<QueryCarParams>({
  keyword: '',
  createOrderUserIds: [],
  platforms: [],
})

const totalAmount = computed(() => {
  if (checkedOrders.value.length) {
    let t = 0
    checkedOrderList.value.forEach(item => {
      if (checkedOrders.value.includes(item.id) && item.amount) {
        t += item.amount * 100
      }
    })
    return t ? t / 100 : 0
  }
  return 0
})

//计算paypal 手续费
function completeDecimals(amount: number) {
  return amount ? amount.toFixed(2) : 0
}

const handleCheckAllChange = (val: boolean) => {
  if (val) {
    checkedOrderList.value = []
    checkedOrders.value = orderList.value.map(item => {
      checkedOrderList.value.push(item)
      return item.id
    })
  } else {
    checkedOrders.value = []
    checkedOrderList.value = []
  }
  isIndeterminate.value = false
}
const handleShowDetailBox = () => {
  orderList.value.forEach(item => {
    item.isShowDetailBox = false
  })
}
function handleChangeIcon(i: number) {
  orderList.value[i].isShowDetailBox = !orderList.value[i].isShowDetailBox
}
const handleCheckedOrdersChange = (value: string[], row: any) => {
  if (checkedOrders.value.length > checkedOrderList.value.length) {
    checkedOrderList.value.push(row)
  } else if (checkedOrders.value.length < checkedOrderList.value.length) {
    checkedOrderList.value = checkedOrderList.value.filter(item => value.includes(item.id))
  }
  const checkedCount = value.length
  checkAll.value = checkedCount === orderList.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < orderList.value.length
}
//下单运营查询
async function getOrderOperation() {
  return cartCreateUserSelect().then(res => {
    orderOperationList.value = res.data
    if (!orderOperationList.value.length) {
      queryParams.value.createOrderUserIds = []
    }
  })
}

//外链跳转
function doGoProductLink(link: string) {
  window.open(link, '_blank')
}

// function handlePlatform(val: any, platform: string) {
//   let arr = platform.split(',') || ''
//   return arr.includes(val)
// }

// 删除订单
function del(ids: any[]) {
  ElMessageBox.confirm('确认删除', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在删除中，请稍后',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      deleteOrderCart(ids)
        .then(res => {
          ElMessage.success('删除成功')
          handleDeleteList(ids)
          crossTagSendMsg(CrossTagMsgType.ORDER_CART_DELETE, { ids })
        })
        .catch(() => init())
        .finally(() => el_loading.close())
    })
    .catch(() => {})
}
// 处理删除成功后的订单列表和选中
function handleDeleteList(ids: any[]) {
  checkedOrders.value = checkedOrders.value.filter(item => !ids.includes(item))
  checkedOrderList.value = checkedOrderList.value.filter(item => !ids.includes(item.id))
  if (!checkedOrders.value || checkedOrders.value.length == 0) {
    checkAll.value = false
    isIndeterminate.value = false
  }
  // handleQuery()
  orderList.value = orderList.value.filter(item => !ids.includes(item.id))
}

function getCartList(isCheckAll = false, params: QueryCarParams = queryParams.value) {
  if (params.keyword) {
    params.keyword = params.keyword.trim()
  }
  loading.value = true
  orderCartList({
    pageNum: 1,
    pageSize: 200,
    ...params,
    createOrderUserIds: undefined,
    userName: params.createOrderUserIds,
  })
    .then(res => {
      if (res.data) {
        orderList.value = res.data.rows || []

        //让明细icon单独切换
        handleShowDetailBox()
        if (isCheckAll) {
          handleCheckAllChange(true)
          checkAll.value = true
        }
        // 清除意向模特错误提示
        errorModelNames.value.length = 0
        errorModelIds.value.length = 0
        errorShoppingCartIds.value.length = 0
      }
    })
    .catch(() => {
      handleCheckAllChange(false)
      checkAll.value = false
    })
    .finally(() => {
      loading.value = false
    })
}
function resetQuery() {
  queryParams.value = {
    keyword: '',
    createOrderUserIds: [],
    platforms: [],
  }
  init()
}

function resetSelModelInfo() {
  selModelInfo.value = []
  selModel.value.shootingCountry = ''
  selModel.value.modelType = ''
  selModel.value.platform = ''
}

const curCardId = ref('')

function againSelectModel(data: any, i: number, cardId: any) {
  resetSelModelInfo()
  if (data && data !== '') {
    selModelInfo.value.push({
      ...data,
      picUrl: data.pic,
    })
  }
  cartListIndex.value = i
  curCardId.value = cardId
  selModel.value = {
    shootingCountry: orderList.value[cartListIndex.value].shootingCountry + '',
    modelType: orderList.value[cartListIndex.value].modelType + '',
    platform: orderList.value[cartListIndex.value].platform + '',
  }

  nextTick(() => {
    SelectSingleModelRef.value?.open(selModelInfo.value)
  })
}
function selModelSuccess(rows: any[]) {
  if (cartListIndex.value || cartListIndex.value === 0) {
    let { intentionModel, createUser, ...params } = orderList.value[cartListIndex.value]
    let data = rows[0]
    updateCartIntentionModel({ modelId: data.id, id: curCardId.value }).then(res => {
      if (res.data.cannotModel?.length) {
        SelectSingleModelRef.value?.clearSelectModels()
        ElMessage.warning('模特暂时无法接单，请重新选择')
        return
      }

      SelectSingleModelRef.value?.close()
      // handleQuery()
      handleUpdateOrderData(curCardId.value)
    })
    // orderEditCart({
    //   ...params,
    //   intentionModelId: data.id,
    // }).then(res => {
    //   data['pic'] = data.picUrl
    //   orderList.value[cartListIndex.value].intentionModel = data
    // })
  }
}
//删除当前模特
function delSelectModel(data: any, index: number) {
  ElMessageBox.confirm('确认删除该模特吗', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(() => {
    let { intentionModel, ...params } = data
    updateCartIntentionModel({ modelId: null, id: data.id }).then(res => {
      ElMessage.success('删除成功')
      orderList.value[index].intentionModel = null
    })
    // orderEditCart({
    //   ...params,
    //   intentionModelId: null,
    // })
    //   .then(res => {
    //     ElMessage.success('删除成功')
    //     orderList.value[index].intentionModel = null
    //   })
    //   .finally(() => {})
  })
  // .catch(() => {})
}

function onSubmit() {
  store.realTimeCheckVip().then(() => {
    if (store.isVip()) {
      submitLoading.value = true
      let params = checkedOrders.value.map(id => {
        let repetition = 1
        if (errorShoppingCartIds.value.includes(id)) {
          let data = orderList.value.find(item => item.id === id)
          if (data && errorModelIds.value.includes(data.intentionModel?.id)) {
            repetition = 0
          }
        }
        return {
          id,
          repetition,
        }
      })
      cartSettleAccounts(params)
        .then(res => {
          if (res.data.cannotModel?.length) {
            errorModelNames.value = res.data.cannotModelNames || []
            errorModelIds.value.push(...res.data.cannotModel)
            errorModelIds.value = [...new Set(errorModelIds.value)]
            if (res.data.cannotShoppingCartId.length) {
              errorShoppingCartIds.value.push(...res.data.cannotShoppingCartId)
            }
            MessageBoxRef.value.open()

            // === 订单排序（把异常模特的订单排在前面） ===
            // 选中的订单
            let checkList: any[] = []
            // 未选中的订单
            let otherList: any[] = []
            orderList.value.forEach(item => {
              if (checkedOrders.value.includes(item.id)) {
                checkList.push(item)
              } else {
                otherList.push(item)
              }
            })
            // 选中订单中有异常模特的订单
            let errModelList: any[] = []
            // 选中订单中没有异常模特的订单
            let notErrModelList: any[] = []
            checkList.forEach(item => {
              if (errorShoppingCartIds.value.includes(item.id)) {
                errModelList.push(item)
              } else {
                notErrModelList.push(item)
              }
            })
            orderList.value = [...errModelList, ...notErrModelList, ...otherList]
          } else if (res.data.orderNum) {
            router.push({ name: 'order-pay', state: { orderNum: res.data.orderNum } })
          } else {
            handleQuery(true)
          }
        })
        .finally(() => (submitLoading.value = false))
    } else {
      ElMessage.error('请开通会员后再进行下单')
    }
  })
}

//复制一单
function copyOrder(id: any) {
  // copyOrderLoading.value =
  store.realTimeCheckVip().then(() => {
    if (store.isVip()) {
      ElMessageBox.confirm('确认复制该订单', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(() => {
          const el_loading = ElLoading.service({
            lock: true,
            text: '正在复制中，请稍后',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          copyOrderCart({
            cartId: id,
          })
            .then(res => {
              if (res.code === 200) {
                ElMessage.success('复制成功')
                if (res.data) {
                  if (checkedOrders.value.includes(id)) {
                    checkedOrders.value.push(res.data.id)
                    checkedOrderList.value.push(res.data)
                  }
                  orderList.value.unshift(res.data)
                } else {
                  handleCheckQuery()
                }
              }
            })
            .catch(() => init())
            .finally(() => el_loading.close())
        })
        .catch(() => {})
    } else {
      ElMessage.error('请开通会员后再进行下单')
    }
  })
}

//创建订单
const orderFormDialogRef = ref()
const orderFormDialogTitle = ref('创建订单')
const orderFormType = ref<FormCompType>('add')
const orderFormId = ref('')

const orderFormRef = ref<InstanceType<typeof OrderForm>>()
const store = useUserStore()

//点击购物车订单行
function handleRowClick(row: any, column: any, event: any) {
  if (checkedOrders.value.includes(row.id)) {
    checkedOrders.value = checkedOrders.value.filter(item => item != row.id)
    checkedOrderList.value = checkedOrderList.value.filter(item => item.id != row.id)
  } else {
    checkedOrders.value.push(row.id)
    checkedOrderList.value.push(row)
  }
  const checkedCount = checkedOrders.value.length
  checkAll.value = checkedCount === orderList.value.length
  isIndeterminate.value = checkedCount > 0 && checkedCount < orderList.value.length
}

// 创建订单
function handleAddOrder(type: string, id?: any) {
  store.realTimeCheckVip().then(() => {
    if (store.isVip()) {
      switch (type) {
        case 'add':
          orderFormDialogTitle.value = '创建订单'
          orderFormType.value = 'add'
          orderFormId.value = ''
          orderFormDialogRef.value.open()
          break
        case 'editCartOrder':
          orderFormDialogTitle.value = '修改订单'
          orderFormType.value = 'editCartOrder'
          orderFormId.value = id
          orderFormDialogRef.value.open()
          break
        default:
          break
      }
    } else {
      ElMessage.error('请开通会员后再进行下单')
    }
  })
}

//创建订单关闭前的回调
function beforeClose(close: () => void) {
  orderFormRef.value?.saveOrderForm()
  close()
}

function createOrderSuccess(data: any, type: string) {
  orderFormDialogRef.value.close()
  if (type == 'edit') {
    handleUpdateOrderData(data)
    crossTagSendMsg(CrossTagMsgType.ORDER_CART_EDIT, data)
  } else {
    if (checkedOrders.value.length) {
      handleCheckQuery()
    } else {
      init()
    }
    crossTagSendMsg(CrossTagMsgType.ORDER_CART_ADD, {})
  }
}
// 更新其中一条订单数据
function handleUpdateOrderData(id: any) {
  loading.value = true
  orderCartList({
    pageNum: 1,
    pageSize: 200,
  })
    .then(res => {
      if (res.code === 200 && res.data) {
        let result = res.data.rows.find((item: any) => item.id === id)
        let i = orderList.value.findIndex((item: any) => item.id === id)
        if (result && i > -1) {
          orderList.value[i] = result
        }
        let index = checkedOrderList.value.findIndex((item: any) => item.id === id)
        if (index > -1) {
          checkedOrderList.value[index] = result
        }
      }
      loading.value = false
    })
    .catch(() => {
      init()
    })
}
// 搜索
async function handleQuery(isCheckAll?: boolean) {
  loading.value = true
  await getOrderOperation()
  if (queryParams.value.createOrderUserIds?.length) {
    let result = orderOperationList.value.map(item => item.id)
    queryParams.value.createOrderUserIds = queryParams.value.createOrderUserIds.filter(item =>
      result.includes(item)
    )
  }
  getCartList(isCheckAll, queryParams.value)
}
// 查询但不清除已选 并把已选的订单排在前面
async function handleCheckQuery() {
  loading.value = true
  await getOrderOperation()
  queryParams.value = {
    keyword: '',
    createOrderUserIds: [],
    platforms: [],
  }
  orderCartList({
    pageNum: 1,
    pageSize: 200,
  }).then(res => {
    if (res.code === 200 && res.data) {
      let checkList: any[] = []
      let noCheckList: any[] = []
      res.data.rows.forEach((item: any) => {
        if (checkedOrders.value.includes(item.id)) {
          checkList.push(item)
        } else {
          noCheckList.push(item)
        }
      })
      orderList.value = [...checkList, ...noCheckList]
      if (checkedOrders.value.length != orderList.value.length) {
        isIndeterminate.value = checkedOrders.value.length ? true : false
      }
      loading.value = false
    }
  })
}
async function init() {
  loading.value = true
  await getOrderOperation()
  let result = orderOperationList.value.find(item => item.id === store.userInfo.id)
  if (result) {
    queryParams.value.createOrderUserIds = [store.userInfo.id]
  } else {
    queryParams.value.createOrderUserIds = []
  }
  getCartList(true, queryParams.value)
}

// 跨标签监听消息
crossTagListenMsg((data) => {
  if (data.type === CrossTagMsgType.ORDER_CART_DELETE) {
    handleDeleteList(data.msg.ids)
  } else if (data.type === CrossTagMsgType.ORDER_CART_EDIT) {
    handleUpdateOrderData(data.msg)
  } else if (data.type === CrossTagMsgType.ORDER_CART_ADD) {
    init()
  }
})

init()
</script>

<style scoped lang="scss">
:deep(.el-table__row) {
  cursor: pointer;
}

.icon {
  height: 24px;
  width: 24px;
  margin-right: 16px;
  cursor: pointer;
}
.tag {
  border: 1px solid #b2b2b2;
  background: #f6f6f6;
  color: #b2b2b2;
}
.card-head {
  border-radius: 14px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.24) 0%, #ffffff 100%);
  margin: 10px 0;
  padding: 15px 20px 0 20px;
  border: 1px solid #fff;
  &__title {
    font-size: 18px;
    font-weight: 800;
    .head-title {
      display: flex;
      align-items: center;
    }
  }
  &__title::after {
    content: '';
    display: block;
    height: 1px;
    background-color: #e4e8eb;
    margin-top: 5px;
  }
}
:deep(.el-table_1_column_1.is-leaf.el-table__cell) {
  padding-left: 30px;
}
.content-box {
  background: #fff;
  position: relative;
  border-radius: 10px;
  padding: 15px 10px 10px 10px;
  // max-height: calc(100vh - 140px);
  .gap {
    gap: 10px;
  }

  .link {
    max-width: 400px;
    color: #02a7f2;
    cursor: pointer;
  }

  .el-checkbox {
    margin: 35px 10px 0 0;
  }

  .info-box {
    flex-shrink: 0;
    width: 460px;

    .time {
      color: var(--text-gray-color);
      font-size: 13px;
    }

    .img {
      width: 100px;
      height: 100px;
      flex-shrink: 0;
      align-self: flex-start;
    }
    .img-error {
      font-size: 12px;
      background-color: #f5f7fa;
      margin-top: 0;
      text-align: center;
      line-height: 80px;
      color: var(--text-gray-color);
    }

    .info {
      align-items: flex-start;
      gap: 5px;
      font-size: 14px;
      color: var(--text-color);
    }
    .tag--box {
      flex-wrap: wrap;
      margin-top: 4px;
    }
  }
  .pic-count-box {
    :deep(.el-image__inner) {
      height: auto;
    }
    margin-left: 18px;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    width: 250px;
    padding: 0 10px;

    .pic-err {
      position: relative;

      &::after {
        content: '失效';
        position: absolute;
        top: 0;
        left: 0;
        z-index: 9;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 50%;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .img {
      width: 49px;
      height: 49px;
      border-radius: 50%;
    }
    .model-info {
      font-weight: 400px;
      margin: 0 10px;
      // text-align: center;

      &__name {
        font-weight: 500;
        color: #09172f;
        text-align: left;
        max-width: 150px;
        font-size: 16px;
      }
      &__id {
        color: #6a7484;
        font-size: 14px;
      }
      &__btn {
        .el-button {
          font-size: 12px;
        }
      }
    }
  }
  .action-box {
    margin-left: 30px;
    flex-shrink: 0;
    .price-info {
      display: flex;
      flex-direction: column;
    }
    .price-detail {
      display: flex;
      align-items: center;
    }
  }
  .options {
    margin-left: 30px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
    &-btn {
      font-size: 14px;
      color: #5695c0;
    }
  }
  .order-user-info {
    margin-left: 30px;
    color: var(--text-color);
    font-size: 16px;
  }
  // .bottom-box {
  //   position: fixed;
  //   margin-top: 12px;
  //   bottom: 38px;
  //   z-index: 9;
  //   width: 83%;
  //   /* min-width: 955px; */
  //   background: #fff;
  //   // position: fixed;
  //   // margin-top: 12px;
  //   .el-checkbox {
  //     margin: 0 10px 0 0;
  //   }

  //   .bottom-right {
  //     .text {
  //       color: var(--text-color);
  //       font-size: 15px;
  //       align-items: baseline;
  //     }

  //     .submit-button {
  //       padding: 25px 30px;
  //       // width: 100px;
  //       // height: 40px;
  //       font-size: 25px;
  //       font-weight: 600;
  //       border-radius: 35px;
  //     }
  //   }

  //   .tip {
  //     font-size: 13px;
  //     color: var(--text-gray-color);
  //   }
  // }

  .money {
    font-size: 16px;
    color: #d67c5a;

    span {
      font-size: 25px;
      font-weight: 500;
    }
  }
  .pic-price {
    font-size: 12px;
  }
  :deep(.el-image__error) {
    text-align: center;
  }
}
.bottom-box {
  height: 70px;
  /* min-width: 955px; */
  background: #fff;
  padding: 10px 10px 0px;
  box-sizing: border-box;
  position: relative;
  left: 0px;
  bottom: 1px;
  z-index: 9;
  width: 100%;
  border-top: 1px solid #ebeef5;

  .el-checkbox {
    margin: 0 10px 0 0;
  }

  .bottom-right {
    .text {
      color: var(--text-color);
      font-size: 15px;
      align-items: baseline;
    }

    .submit-button {
      padding: 25px 30px;
      // width: 100px;
      // height: 40px;
      font-size: 25px;
      font-weight: 600;
      border-radius: 35px;
    }
  }

  .tip {
    font-size: 13px;
    color: var(--text-gray-color);
  }
}
.detail-box {
  font-size: 16px;
  .detail-title {
    font-weight: 400;
    color: #3a3939;
    margin-bottom: 10px;
  }
  .detail-money {
    display: flex;
    color: #3a3939;
    font-weight: 100;
    .text {
      flex: 4;
      text-align: right;
    }
    .price {
      flex: 3;
      margin-left: 20px;
    }
  }
  .detail-hint {
    font-weight: 300;
    margin: 10px 0 15px 0;
    color: #b8741a;
    font-size: 10px;
    text-align: left;
    // transform: scale(0.83333);
    // transform-origin: 0 0;
  }
}
</style>
