<template>
  <el-table
    ref="tableRef"
    :data="tableData"
    style="width: 100%"
    border
    :header-cell-style="{
      'background-color': '#F6F6F6',
      color: 'var(--text-color)',
      'font-size': '13px',
    }"
    v-loading="loading"
  >
    <el-table-column prop="orderNum" label="订单号" minWidth="260" align="center"></el-table-column>
    <el-table-column prop="createTime" label="订单创建时间" minWidth="180" align="center"></el-table-column>
    <el-table-column prop="videoCount" label="视频数量" minWidth="120" align="center"></el-table-column>
    <el-table-column prop="orderPromotionAmount" label="优惠（CNY ）" minWidth="140" align="center">
      <template v-slot="{ row }">
        {{ row.orderPromotionAmount || '-' }}
      </template>
    </el-table-column>
    <el-table-column prop="amountDollar" label="小计（USD）" minWidth="140" align="center">
      <template v-slot="{ row }">
        {{ row.amountDollar ? `$${row.amountDollar.toFixed(2)}` : '-' }}
      </template>
    </el-table-column>
    <el-table-column prop="amount" label="小计（CNY）" minWidth="140" align="center">
      <template v-slot="{ row }">
        {{ !errorDisabled && row.amount ? `￥${row.amount.toFixed(2)}` : '-' }}
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
const props = defineProps({
  tableData: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  errorDisabled: {
    type: Boolean,
    default: false,
  },
})
</script>

<style scoped lang="scss"></style>
