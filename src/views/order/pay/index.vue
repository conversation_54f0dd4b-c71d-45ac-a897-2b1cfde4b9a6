<template>
  <div class="pages">
    <div class="head-box">
      <!-- <div class="head-box__title">
        <div class="head-title">
          <el-icon color="#5695c0"><CaretRight /></el-icon>
          <span style="margin-left: 5px">我的订单</span>
          <span style="color: #e4e8eb; margin: 0 10px">/</span>
          <span>创建订单</span>
          <span style="color: #e4e8eb; margin: 0 10px">/</span>
          <span style="font-weight: 800">支付订单</span>
        </div>
      </div> -->
      <div class="head-content">
        <div class="head-icon">
          <img style="width: 80px; height: 100%" src="@/assets/icon/icon_order_succ.png" alt="" />
        </div>
        <div v-if="mergeOrderNum" style="width: calc(100% - 100px); margin-left: 24px">
          <div class="flex-between">
            <span class="head-text__title">订单合并提交成功，请尽快付款～</span>
            <!-- <el-button link type="primary" @click="handleScollToTop">查看明细</el-button> -->
          </div>
          <div class="flex-start" style="margin-top: 12px; color: #777777">
            <div>共合并提交{{ tableData.length }}笔订单，{{ handleVideoCount(tableData) }}个视频</div>
            <el-button
              link
              type="primary"
              style="font-size: 16px; margin-left: 14px"
              @click="handleScollToTop"
            >
              查看详情
              <el-icon size="14"><ArrowDown /></el-icon>
            </el-button>
          </div>
          <!-- <PayOrderList :table-data="tableData" :loading="loading" :error-disabled="errorDisabled" /> -->
        </div>
        <div class="head-text" v-else>
          <div class="head-text__title">订单提交成功，请尽快付款~</div>
          <div class="head-text__order">
            订单号：{{ orderNum }}
            <CopyButton v-if="orderNum" :copy-content="orderNum" link>
              <el-icon size="14"><CopyDocument /></el-icon>
            </CopyButton>
          </div>
        </div>
      </div>
      <div style="padding: 0 35px">
        <el-divider style="margin: 16px 0" />
      </div>
      <div
        style="margin: 0 0 35px 140px; padding-bottom: 30px; padding-right: 35px; position: relative"
        v-loading="loading"
      >
        <div
          class="total-box"
          v-if="
            orderInfo.orderPromotionAmountSum &&
            !mergeOrderNum &&
            orderInfo.orderDiscountDetailVOS &&
            orderInfo.orderDiscountDetailVOS.length > 0
          "
        >
          <div class="box-title">
            <div class="total-box-title">订单合计</div>
            <span v-if="errorDisabled" style="font-size: 18px">-</span>
            <span v-else>
              {{
                payWay === PAY_TYPE['全币种支付']
                  ? toFixed2(orderInfo.oldNeedPayAmountDollar)
                  : toFixed2(orderInfo.oldNeedPayAmount)
              }}
            </span>
            <div class="total-box-icon">{{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}</div>
          </div>
        </div>
        <div
          class="total-box"
          v-if="orderInfo.orderDiscountDetailVOS && orderInfo.orderDiscountDetailVOS.length > 0"
        >
          <!-- <div>
            优惠金额：
            <span style="color: var(--member-color)">{{ orderInfo.orderPromotionAmountSum }}</span>
            CNY
          </div> -->
          <div class="box-title" v-if="handleShowDiscount(orderInfo.orderDiscountDetailVOS, '1')">
            <div class="total-box-title">限时满减活动</div>
            <span style="color: var(--member-color)">
              -{{ hanldeDiscount(orderInfo.orderDiscountDetailVOS, '1') }}
            </span>
            <div class="total-box-icon">CNY</div>
          </div>
          <div
            class="box-title"
            style="margin-top: 15px"
            v-if="handleShowDiscount(orderInfo.orderDiscountDetailVOS, '4')"
          >
            <div class="total-box-title">每月首单返还会员费</div>
            <span style="color: var(--member-color)">
              -{{ hanldeDiscount(orderInfo.orderDiscountDetailVOS, '4') }}
            </span>
            <div class="total-box-icon">USD</div>
          </div>
        </div>
        <div class="total-box">
          <div class="box-title" v-if="payWay === PAY_TYPE['全币种支付']">
            <div class="total-box-title">应付合计</div>
            <span style="font-size: 20px; font-weight: bold">
              {{ toFixed2(orderInfo.afterPromotionAmountDollar) }}
            </span>
            <div class="total-box-icon">USD</div>
          </div>
          <div class="box-title" v-else>
            <div class="total-box-title">应付合计</div>
            <span style="font-size: 20px; font-weight: bold">
              {{ toFixed2(orderInfo.afterPromotionAmount) }}
            </span>
            <div class="total-box-icon">CNY</div>
          </div>
        </div>
        <el-divider
          v-if="orderInfo.orderDiscountDetailVOS && orderInfo.orderDiscountDetailVOS.length > 0"
          style="margin: 20px 0 0 0"
        />
        <AnotherPayInfo
          v-if="isAnotherPay"
          :orderNum="orderNum"
          :mergeId="mergeOrderNum"
          :disabled="disabled || errorDisabled"
        />
        <template v-else>
          <div class="balance-checkbox">
            <div class="balance-checkbox-title">使用余额</div>
            <div class="money-checkbox" style="display: flex; align-items: center">
              <template v-if="orderInfo.orderBalance || orderInfo.validBalance">
                <el-checkbox
                  v-model="checked"
                  :disabled="disabled || occupying || errorDisabled"
                  label="使用钱包支付"
                  size="large"
                  @change="checkedChange"
                />
                <div style="margin-left: 14px; font-size: 18px">
                  <span v-if="errorDisabled">-</span>
                  <span v-else-if="orderInfo.orderBalance">
                    {{ toFixed2(orderInfo.orderBalance, 0) }}
                    <span style="font-size: 14px; font-weight: 400">CNY</span>
                  </span>
                  <span v-else-if="orderInfo.validBalance > orderInfo.afterPromotionAmount">
                    {{ toFixed2(orderInfo.afterPromotionAmount) }}
                    <span style="font-size: 14px; font-weight: 400">CNY</span>
                  </span>
                  <span v-else>
                    {{ toFixed2(orderInfo.validBalance, 0) }}
                    <span style="font-size: 14px; font-weight: 400">CNY</span>
                  </span>
                </div>
              </template>
              <div v-else>暂无可用余额</div>
            </div>
          </div>
          <div class="pay-type flex-start">
            <div class="pay-type-title" v-if="!isAllBalancePay">请选择支付方式</div>
            <div class="select-pay-box" v-if="!isAllBalancePay">
              <el-radio-group size="large" v-model="payWay" :disabled="disabled" @change="payWayChange">
                <el-radio-button
                  style="border-radius: 6px"
                  :value="PAY_TYPE['微信支付']"
                  :label="PAY_TYPE['微信支付']"
                >
                  <!-- <i
                    class="iconfont icon-weixinzhifu"
                    :class="{ 'wx-color': payWay !== PAY_TYPE['微信支付'] }"
                  ></i> -->
                  <img
                    v-show="payWay !== PAY_TYPE['微信支付']"
                    src="@/assets/image/pay_wechat_icon.png"
                    alt=""
                    class="pay-icon"
                  />
                  <img
                    v-show="payWay === PAY_TYPE['微信支付']"
                    src="@/assets/image/pay_wechat_icon2.png"
                    alt=""
                    class="pay-icon"
                  />
                  微信支付
                </el-radio-button>
                <el-tooltip :content="payDollarTips" raw-content placement="top" effect="dark">
                  <el-radio-button
                    :value="PAY_TYPE['全币种支付']"
                    :label="PAY_TYPE['全币种支付']"
                    :disabled="disabled || minAmount"
                    style="position: relative; overflow: hidden"
                  >
                    <div class="btn-corner-mark"></div>
                    <!-- <i class="iconfont icon-Dollar"></i> -->
                    <img
                      v-show="payWay !== PAY_TYPE['全币种支付']"
                      src="@/assets/image/pay_all_icon.png"
                      alt=""
                      class="pay-icon"
                    />
                    <img
                      v-show="payWay === PAY_TYPE['全币种支付']"
                      src="@/assets/image/pay_all_icon2.png"
                      alt=""
                      class="pay-icon"
                    />
                    全币种支付
                  </el-radio-button>
                </el-tooltip>
                <el-radio-button :value="PAY_TYPE['支付宝支付']" :label="PAY_TYPE['支付宝支付']">
                  <!-- <i
                    class="iconfont icon-zhifubaozhifu"
                    :class="{ 'zfb-color': payWay !== PAY_TYPE['支付宝支付'] }"
                  ></i> -->
                  <img
                    v-show="payWay !== PAY_TYPE['支付宝支付']"
                    src="@/assets/image/pay_ali_icon.png"
                    alt=""
                    class="pay-icon"
                  />
                  <img
                    v-show="payWay === PAY_TYPE['支付宝支付']"
                    src="@/assets/image/pay_ali_icon2.png"
                    alt=""
                    class="pay-icon"
                  />
                  支付宝支付
                </el-radio-button>
                <!-- <el-radio-button :value="PAY_TYPE['银行卡转账']" :label="PAY_TYPE['银行卡转账']">
                <i v-if="payWay === PAY_TYPE['银行卡转账']" class="iconfont icon-iconfontjikediancanicon20"></i>
                <svg v-else class="icon" aria-hidden="true" style="width: 20px; height: 20px">
                  <use xlink:href="#icon-yinhangqiazhuanzhang"></use>
                </svg>
                银行转账
              </el-radio-button> -->
                <!-- <el-tooltip content="对公转账，需要额外支付5%的开票服务费" placement="top" effect="light"> -->
                <el-radio-button :value="PAY_TYPE['对公转账']" :label="PAY_TYPE['对公转账']">
                  <!-- <i class="iconfont icon-duigongzhuanzhang"></i> -->
                  <img
                    v-show="payWay !== PAY_TYPE['对公转账']"
                    src="@/assets/image/pay_public_icon.png"
                    alt=""
                    class="pay-icon"
                  />
                  <img
                    v-show="payWay === PAY_TYPE['对公转账']"
                    src="@/assets/image/pay_public_icon2.png"
                    alt=""
                    class="pay-icon"
                  />
                  对公转账
                </el-radio-button>
                <!-- </el-tooltip> -->
              </el-radio-group>
            </div>
          </div>

          <div class="pay-info flex-between">
            <div class="flex-start">
              <div class="pay-info-title">还需支付金额</div>
              <span v-if="errorDisabled" style="font-size: 24px">-</span>
              <div v-else>
                <span>
                  {{
                    payWay === PAY_TYPE['全币种支付']
                      ? toFixed2(orderInfo.payAmountDollar)
                      : toFixed2(orderInfo.payAmount)
                  }}
                </span>
                <span style="font-size: 14px; margin-left: 6px; font-weight: 400">
                  {{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}
                </span>
              </div>

              <div v-if="payWay === PAY_TYPE['全币种支付']">
                <span style="font-weight: normal; margin: 0 8px">/</span>
                <span>{{ toFixed2(orderInfo.payAmount) }}</span>
                <span style="font-weight: normal; font-size: 16px">
                  <span style="font-size: 14px; margin-left: 6px; font-weight: 400">CNY</span>
                </span>
              </div>
              <!-- <span v-else>{{ orderInfo.payAmount }}</span> -->
            </div>
            <!-- <div v-if="payWay === PAY_TYPE['对公转账'] && !isAllBalancePay && !errorDisabled" class="tips">
              已包含服务费：{{ orderInfo.taxPointCost }}元（{{ orderInfo.taxPoint }}%）
            </div> -->
            <div class="btn-box">
              <el-button
                v-if="isAnotherPayButton"
                class="another-pay-btn"
                round
                :disabled="disabled || errorDisabled"
                @click="handleAnotherPay"
              >
                找人代付
                <span class="tip">*可以分享给财务或其他人帮你付款</span>
              </el-button>
              <el-button
                v-if="isAllBalancePay"
                type="primary"
                round
                style="width: 180px; height: 40px; font-size: 15px; font-weight: bold"
                :disabled="disabled || errorDisabled"
                @click="balancePay"
              >
                余额支付
              </el-button>
              <el-button
                v-else
                type="primary"
                round
                style="width: 180px; height: 40px; font-size: 18px; font-weight: bold"
                :disabled="disabled || errorDisabled"
                @click="tryLock"
              >
                立即支付
              </el-button>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- <div class="title primary">订单提交成功，请尽快付款！</div> -->

    <!-- <div class="head-box card flex-start">
      订单号：{{ orderNum }}
      <CopyButton v-if="orderNum" :copy-content="orderNum" link>
        <el-icon><CopyDocument /></el-icon>
      </CopyButton>
    </div> -->

    <!-- <div class="title">订单结算</div> -->

    <div class="card-two">
      <div class="flex-start gap-10" style="margin-top: 20px">
        <!-- <p class="card-two__title">订单费用明细</p> -->
        <div class="card-two__title">订单费用明细</div>
        <DownloadButton
          type="primary"
          link
          icon="Download"
          text="下载费用清单"
          message="确认下载费用清单"
          loadingText="下载中"
          url="/order/pay/download-pay-info"
          :disabled="disabled || errorDisabled"
          :params="() => ({ orderNum: orderNum, mergeId: mergeOrderNum })"
        />
      </div>
      <div style="width: 100%" v-for="(item, i) in tableData" :key="i">
        <!-- v-if="mergeOrderNum" -->
        <div class="flex-between card-two__order-head">
          <div style="display: flex; align-items: center">
            <span>订单号：{{ item.orderNum }}</span>
            <CopyButton v-if="item.orderNum" :copy-content="item.orderNum" link>
              <el-icon size="14"><CopyDocument /></el-icon>
            </CopyButton>
          </div>
          <div>
            <span>合计(CNY)：{{ item.amount }}</span>
            <span v-if="item.orderPromotionAmount" style="margin-left: 10px">
              总优惠(CNY)：{{ item.orderPromotionAmount }}
            </span>
          </div>
        </div>
        <el-table
          ref="tableRef"
          :data="item.orderVideoPayInfos"
          style="width: 100%"
          border
          header-cell-class-name="table-head-primary"
          v-loading="loading"
        >
          <template #empty>
            <Empty image-type="order" description="暂无数据" :image-size="100" />
          </template>
          <el-table-column prop="productChinese" label="产品名称" minWidth="260" align="center">
            <template v-slot="{ row }">
              <div v-if="row.productChinese">
                <span v-if="row.productChinese.length <= 16">
                  {{ row.productChinese }}
                </span>
                <el-tooltip
                  v-else
                  effect="dark"
                  :content="`<div style='max-width: 600px;'>${row.productChinese}</div>`"
                  raw-content
                  placement="top"
                >
                  <span>{{ row.productChinese.substring(0, 16) }}...</span>
                </el-tooltip>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column prop="videoPrice" label="视频佣金" width="140" align="center">
            <!-- <template #header>
              <div class="flex-center gap-5">
                <span>视频佣金</span>
                <el-icon
                  color="rgb(155, 166, 186)"
                  style="margin: 1px 0 0 -2px"
                  @mouseenter="showTooltips($event, 'order-pay-video-commission')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template> -->
            <template v-slot="{ row }">
              {{ row.videoPrice ? `$${row.videoPrice}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="picPrice" label="照片佣金" width="140" align="center">
            <!-- <template #header>
              <div class="flex-center gap-5">
                <span>照片佣金</span>
                <el-icon
                  color="rgb(155, 166, 186)"
                  style="margin: 1px 0 0 -2px"
                  @mouseenter="showTooltips($event, 'order-pay-picCount')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template> -->
            <template v-slot="{ row }">
              {{ row.picPrice ? `$${row.picPrice}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="commissionPaysTaxes" label="佣金代缴税费" width="140" align="center">
            <template #header>
              <div class="flex-center gap-5">
                <span>佣金代缴税费</span>
                <el-icon
                  color="rgb(155, 166, 186)"
                  style="margin: 1px 0 0 -2px"
                  @mouseenter="showTooltips($event, 'order-pay-taxes')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <template v-slot="{ row }">
              {{ row.commissionPaysTaxes ? `$${row.commissionPaysTaxes.toFixed(2)}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="exchangePrice" label="PayPal代付手续费" width="160" align="center">
            <template #header>
              <div class="flex-center gap-5">
                <span>PayPal代付手续费</span>
                <el-icon
                  color="rgb(155, 166, 186)"
                  style="margin: 1px 0 0 -2px"
                  @mouseenter="showTooltips($event, 'order-pay-PayPal')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <template v-slot="{ row }">
              {{ row.exchangePrice ? `$${row.exchangePrice.toFixed(2)}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="servicePrice" label="服务费" width="140" align="center">
            <template #header>
              <div class="flex-center gap-5">
                <span>服务费</span>
                <el-icon
                  color="rgb(155, 166, 186)"
                  style="margin: 1px 0 0 -2px"
                  @mouseenter="showTooltips($event, 'order-pay-service')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <template v-slot="{ row }">
              {{ row.servicePrice ? `$${row.servicePrice}` : '0' }}
            </template>
          </el-table-column>
          <el-table-column prop="currentExchangeRate" label="实时百度汇率" width="140" align="center">
            <!-- <template #header>
              <div class="flex-center gap-5">
                <span>实时百度汇率</span>
                <el-icon
                  color="rgb(155, 166, 186)"
                  style="margin: 1px 0 0 -2px"
                  @mouseenter="showTooltips($event, 'order-pay-rate')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template> -->
            <template v-slot="{ row }">
              {{
                !errorDisabled && row.currentExchangeRate >= 6.5 && row.currentExchangeRate <= 8
                  ? row.currentExchangeRate
                  : '-'
              }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="小计（USD）" width="140" align="center">
            <template v-slot="{ row }">
              {{ row.amountDollar ? `$${row.amountDollar.toFixed(2)}` : '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="小计（CNY）" width="140" align="center">
            <template v-slot="{ row }">
              {{ !errorDisabled && row.amount ? `￥${row.amount.toFixed(2)}` : '-' }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <div class="card-two__total">
        <div class="flex-start" v-if="payWay === PAY_TYPE['对公转账'] && !isAnotherPay">
          <p>开票服务费：</p>
          <span v-if="orderInfo.taxPointCost && !errorDisabled">
            ￥{{ orderInfo.taxPointCost }}（{{ orderInfo.taxPoint }}%）
          </span>
          <span v-else>-</span>
        </div>
        <div class="flex-start">
          <p>订单合计：</p>
          <span v-if="orderInfo.oldNeedPayAmount && !errorDisabled">￥{{ orderInfo.oldNeedPayAmount }}</span>
          <span v-else>-</span>
        </div>
      </div> -->
    </div>

    <template v-if="payWay === PAY_TYPE['全币种支付']">
      <TransferMoneyPaySelect
        :payeeId="orderInfo.payeeId"
        :pay-amount="orderInfo.payAmountDollar"
        :cny-amount="orderInfo.payAmount"
        :orderNum="orderNum"
        :mergeId="mergeOrderNum"
        ref="TransferMoneyPaySelectRef"
        @submit="onSubmit"
      />
    </template>

    <TransferMoneyPay
      v-if="underWay"
      ref="TransferMoneyPayRef"
      :title="dialogTitle"
      :pay-way="payWay"
      :pay-amount="payWay === PAY_TYPE['全币种支付'] ? orderInfo.payAmountDollar : orderInfo.payAmount"
      :cny-amount="orderInfo.payAmount"
      :tax-point-cost="orderInfo.taxPointCost"
      :orderNum="orderNum"
      :mergeId="mergeOrderNum"
      @submit="onSubmit"
      @close="handleClose"
    />
    <QrCodeDialog
      v-if="underWay"
      ref="QrCodeDialogRef"
      width="450px"
      :title="dialogTitle"
      :http-code="getPayCode"
      :check-code="getPayCheck"
      :auto-check-code="false"
      :is-zfb-pay="payWay === PAY_TYPE['支付宝支付']"
      :loading="QrCodeDialogLoading"
      @close="handleClose"
    >
      <!-- @success="payCodeSuccess" @change="handleQrCodePayStatus" -->
      <!-- <DownloadButton
        class="link-a down-btn"
        link
        text="下载凭证单据"
        loadingText="下载中"
        message="确认下载凭证单"
        fileName="订单凭据"
        url="/order/order/get-document-pdf"
        :params="{
          orderNum,
        }"
      /> -->
      <div class="qrcode-pay-box">
        <span class="flex-start gap-5 qrcode-tips">
          <img v-if="payWay === PAY_TYPE['微信支付']" src="@/assets/icon/icon_weixinzhifu.png" alt="" />
          <!-- <img v-if="payWay === PAY_TYPE['支付宝支付']" src="@/assets/icon/icon_zhifubaozhifu.png" alt="" /> -->
          <svg v-if="payWay === PAY_TYPE['支付宝支付']" class="icon" aria-hidden="true">
            <use xlink:href="#icon-zhifubaozhifu"></use>
          </svg>
          手机
          {{ payWay === PAY_TYPE['微信支付'] ? '微信' : '支付宝' }}
          扫码支付
        </span>
        <div class="pay-total-box">
          <!-- <div>支付金额</div> -->
          <div class="pay-total">
            <span v-if="payWay === PAY_TYPE['全币种支付']">{{ orderInfo.payAmountDollar }}</span>
            <span v-else>{{ orderInfo.payAmount }}</span>
            {{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}
          </div>
        </div>
      </div>
    </QrCodeDialog>

    <AnotherPayLink ref="AnotherPayLinkRef" @submit="showAnotherPay" />
    <PayTipDialog :table-data="tableData" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { PAY_TYPE, payType } from '@/utils/order'
import PayOrderList from '@/views/order/pay/components/PayOrderList.vue'
import CopyButton from '@/components/public/button/CopyButton.vue'
import DownloadButton from '@/components/public/button/DownloadButton.vue'
import TransferMoneyPay from '@/views/order/components/dialog/TransferMoneyPay.vue'
import TransferMoneyPaySelect from '@/views/order/components/dialog/TransferMoneyPaySelect.vue'
import QrCodeDialog from '@/components/public/dialog/QrCodeDialog.vue'
import AnotherPayLink from '@/views/anotherPay/dialog/Link.vue'
import AnotherPayInfo from '@/views/anotherPay/info.vue'
import PayTipDialog from '@/views/order/components/dialog/PayTipDialog.vue'
import {
  orderPayInfo,
  orderPayCode,
  orderPayCheck,
  orderUploadCredentials,
  orderBalancePay,
  orderPayLock,
} from '@/api/order'
import { useUserStore } from '@/stores/modules/user'
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus'
// import useQrCodeVerify from '@/hooks/useQrCodeVerify'
import { usePay } from '@/hooks/usePay'
import { useTooltips } from '@/hooks/useTooltips'

// const { handleQrCodePayStatus } = useQrCodeVerify()
const {
  orderNum,
  mergeOrderNum,
  underWay,
  isOpenPayDialog,
  routerPathUrl,
  checkPlatform,
  isAnotherPay,
  payTipDialogVisible,
  payTipType,
  checkPayStatus,
} = usePay(history.state.orderNum, history.state.mergeId)

const { showTooltips } = useTooltips()

const store = useUserStore()
store.getUserInfo()

function toFixed2(val: any, df?: any) {
  if (val) {
    return val.toFixed(2)
  }
  if (df !== undefined) {
    return df
  }
  return '0'
}

interface discountData {
  type?: number
}

const orderInfo = ref<{
  isBalanceLock: number
  validBalance: number
  // videoPrice: number
  // picPrice: number
  // exchangePrice: number
  // servicePrice: number
  discountsAmount: number
  orderOriginAmount: number
  orderAmountResult: number
  // currentExchangeRate: number
  orderAmount: number
  oldNeedPayAmount: number
  oldNeedPayAmountDollar: number
  orderPromotionAmountSum: number
  afterPromotionAmount: number
  afterPromotionAmountDollar: number
  balance: number
  taxPoint: number
  taxPointCost: number
  payAmount: number
  payAmountDollar: number
  payType: PAY_TYPE | null
  orderBalance: number
  payeeId: number
  orderDiscountDetailVOS?: any[]
}>({
  // videoPrice: 0,
  // picPrice: 0,
  // exchangePrice: 0,
  // servicePrice: 0,
  // currentExchangeRate: 0,
  orderAmount: 0,
  oldNeedPayAmount: 0,
  balance: 0,
  taxPoint: 0,
  taxPointCost: 0,
  payType: null,
  orderBalance: 0,
  payAmount: 0,
  isBalanceLock: 0,
  validBalance: 0,
  discountsAmount: 0,
  orderAmountResult: 0,
  orderOriginAmount: 0,
  oldNeedPayAmountDollar: 0,
  payAmountDollar: 0,
  payeeId: 0,
  orderPromotionAmountSum: 0,
  afterPromotionAmount: 0,
  afterPromotionAmountDollar: 0,
  orderDiscountDetailVOS: [],
})
const tableData = ref<any[]>([
  [
    {
      productChinese: '-',
      videoPrice: 0,
      picPrice: 0,
      exchangePrice: 0,
      servicePrice: 0,
      currentExchangeRate: 0,
      amount: 0,
    },
  ],
])

const checked = ref(false)
const occupying = ref(false)
const payWay = ref<PAY_TYPE>(PAY_TYPE['微信支付'])

const TransferMoneyPayRef = ref<InstanceType<typeof TransferMoneyPay>>()
const QrCodeDialogRef = ref<InstanceType<typeof QrCodeDialog>>()
const TransferMoneyPaySelectRef = ref<InstanceType<typeof TransferMoneyPaySelect>>()
const AnotherPayLinkRef = ref<InstanceType<typeof AnotherPayLink>>()

const QrCodeDialogLoading = ref(false)

const dialogTitle = computed(() => {
  return payType(payWay.value) as string
})

const disabled = ref(true)
const loading = ref(true)
const errorDisabled = ref(false)

const payDollarTips = ref(`<div style="text-align: center">
    跨境卖家的最优选择<br/>
    收款支持币种:  离岸人民币,美元,澳元,加币,欧元,英镑,港币,日元,新西兰元,新加坡元
  </div>`)
const minAmount = computed(() => {
  if (orderInfo.value.payAmountDollar < 50) {
    payDollarTips.value = '<div>当前交易金额低于 50 USD，不支持使用全币种支付。</div>'
    return true
  }
  payDollarTips.value = `<div style="text-align: center">
    跨境卖家的最优选择<br/>
    收款支持币种:  离岸人民币,美元,澳元,加币,欧元,英镑,港币,日元,新西兰元,新加坡元
  </div>`
  return false
})

// 是否余额支付
const isAllBalancePay = computed(() => {
  return checked.value && orderInfo.value.payAmount === 0
})
const use_balance = computed(() => {
  if (!checked.value) {
    return 0
  }
  return orderInfo.value.validBalance > orderInfo.value.oldNeedPayAmount
    ? orderInfo.value.oldNeedPayAmount
    : orderInfo.value.validBalance
})

// 找人代付按钮
const isAnotherPayButton = computed(() => {
  return (
    (!checked.value || !(orderInfo.value.orderBalance || orderInfo.value.validBalance)) &&
    (payWay.value === PAY_TYPE['全币种支付'] ? orderInfo.value.payAmountDollar : orderInfo.value.payAmount) >
      0
  )
})

const hanldeDiscount = (list: any, type: any) => {
  return list.find((item: any) => item.type == type).amount
}
const handleShowDiscount = (list: any, type: any) => {
  return list && list.length > 0 ? list.some((item: any) => item.type == type) : false
}

async function init() {
  // console.log(history.state);
  if (!history.state.orderNum && !history.state.mergeId) {
    window.location.href = '/order/list'
    return
  }
  routerPathUrl.value = '/order/list'
  let p = +history.state.payType
  if (p) {
    if (p < PAY_TYPE['余额支付']) {
      payWay.value = p
    } else {
      payWay.value = p - PAY_TYPE['余额支付']
    }
  }
  await orderPayInfo({
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
    isPublic: payWay.value === PAY_TYPE['对公转账'] ? 0 : 1,
    useBalance: use_balance.value || undefined,
  })
    .then(res => {
      // 记录支付方式
      if (res.data.payType != null) {
        history.state.payType = res.data.payType
        if (res.data.payType < PAY_TYPE['余额支付']) {
          payWay.value = res.data.payType
        } else if (res.data.payType === PAY_TYPE['余额支付']) {
          payWay.value = PAY_TYPE['余额支付']
        } else {
          payWay.value = res.data.payType - PAY_TYPE['余额支付']
        }
      }
      getPayInfo(true)
    })
    .catch(() => {
      errorDisabled.value = true
    })
}

const platform = ref(0)
// 支付二维码
function getPayCode() {
  let params = {
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
    payType: payWay.value,
  }
  return orderPayCode(params)
}
// 二维码状态
function getPayCheck() {
  let params = {
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
    platform: platform.value,
  }
  return orderPayCheck(params)
}

// 扫码成功
function payCodeSuccess() {
  underWay.value = false
  // router.replace('/order/list')
  ElMessage.success('支付成功')
}

// 支付方式选择
function payWayChange() {
  getPayInfo()
}
// 使用余额选择
function checkedChange() {
  getPayInfo()
}
// 找人代付
function handleAnotherPay() {
  AnotherPayLinkRef.value?.open(orderNum.value, mergeOrderNum.value)
}
function showAnotherPay() {
  if (orderInfo.value.payType == null) {
    payWay.value = PAY_TYPE['微信支付']
  }
  getPayInfo()
  isAnotherPay.value = true
}

// 打开支付窗口
function openPayBox() {
  checkPlatform.value = payWay.value
  if (payWay.value === PAY_TYPE['全币种支付']) {
    TransferMoneyPaySelectRef.value?.open(orderInfo.value)
  }
  if (payWay.value === PAY_TYPE['银行卡转账'] || payWay.value === PAY_TYPE['对公转账']) {
    TransferMoneyPayRef.value?.open({ payeeId: orderInfo.value.payeeId })
  } else if (payWay.value === PAY_TYPE['微信支付']) {
    platform.value = 1
    handleOpenQrcodeDelay()
  } else if (payWay.value === PAY_TYPE['支付宝支付']) {
    platform.value = 2
    handleOpenQrcodeDelay()
  }
}
let openTimer: any
// 打开 微信支付/支付宝支付 延迟check接口
function handleOpenQrcodeDelay() {
  // QrCodeDialogLoading.value = true
  QrCodeDialogRef.value?.open()
  openTimer = setTimeout(() => {
    isOpenPayDialog.value = true
    // QrCodeDialogLoading.value = false
  }, 5000)
}

function tryLock() {
  if (!payWay.value || payWay.value >= PAY_TYPE['余额支付']) {
    ElMessage.warning('请选择支付方式')
    return
  }
  loading.value = true
  orderPayLock({
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
    payType: payWay.value,
    payAmount: orderInfo.value.payAmount,
    taxPointCost: orderInfo.value.taxPointCost,
    useBalance: use_balance.value,
  })
    .then(() => {
      let t = payWay.value
      if (checked.value) {
        t += PAY_TYPE['余额支付']
      }
      orderInfo.value.payType = t
      // openPayBox()
      getPayInfo(true)
    })
    .catch(() => {
      loading.value = false
    })
}
function handleVideoCount(data: Array<any>) {
  return data.reduce((pre: any, cur: any) => {
    return pre + (cur.orderVideoPayInfos?.length || 0)
  }, 0)
}
// 获取订单信息
function getPayInfo(first: boolean = false) {
  loading.value = true
  disabled.value = true
  orderPayInfo({
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
    isPublic: payWay.value === PAY_TYPE['对公转账'] ? 0 : 1,
    useBalance: use_balance.value || undefined,
  })
    .then(res => {
      orderInfo.value = res.data
      if (res.data.orderPayInfoDetailVOS?.length) {
        tableData.value = res.data.orderPayInfoDetailVOS
      }
      if (!res.data.orderPayInfoDetailVOS?.length || res.data.defaultExchangeRate) {
        errorDisabled.value = true
        if (res.data.defaultExchangeRate) {
          ElMessageBox.alert(
            `<div>
              <p style="text-align: center;margin: 10px 0;font-size: 20px">百度汇率自动获取失败</p>
              <p style="text-align: center;margin: 10px 0;font-size: 15px">请联系您的对接客服为您处理~</p>
            </div>`,
            '',
            {
              customStyle: {
                '--el-messagebox-width': '360px',
                '--el-messagebox-border-radius': '10px',
              },
              showClose: false,
              center: true,
              roundButton: true,
              showCancelButton: true,
              showConfirmButton: false,
              cancelButtonText: '知道了',
              cancelButtonClass: 'message-box-cancel-btn-primary',
              dangerouslyUseHTMLString: true,
              callback: () => {
                window.location.href = routerPathUrl.value
              },
            }
          )
        }
        return
      }
      errorDisabled.value = false
      // 是否锁定余额
      if (orderInfo.value.orderBalance > 0) {
        checked.value = true
        occupying.value = true
      }
      // 记录支付方式
      if (orderInfo.value.payType != null) {
        history.state.payType = orderInfo.value.payType
      }
      // 防止在锁定全币种支付时金额变更低于50 USD
      if (payWay.value === PAY_TYPE['全币种支付'] && orderInfo.value.payAmountDollar < 50) {
        ElMessage.warning('当前交易金额低于50 USD，不支持使用全币种支付。')
        payWay.value = PAY_TYPE['微信支付']
        getPayInfo()
        return
      }
      // 支付方式
      if (orderInfo.value.payType != null && first) {
        if (orderInfo.value.payType < PAY_TYPE['余额支付']) {
          payWay.value = orderInfo.value.payType
        } else if (orderInfo.value.payType === PAY_TYPE['余额支付']) {
          payWay.value = PAY_TYPE['余额支付']
        } else {
          payWay.value = orderInfo.value.payType - PAY_TYPE['余额支付']
        }
        // 防止多页面支付切换对公转账时，税点未更新
        if (payWay.value === PAY_TYPE['对公转账'] && !orderInfo.value.taxPointCost) {
          getPayInfo()
        }

        openPayBox()
      } else if (first && (orderInfo.value.orderBalance || orderInfo.value.validBalance)) {
        checked.value = true
        getPayInfo()
      }
    })
    .catch(() => {
      errorDisabled.value = true
    })
    .finally(() => {
      loading.value = false
      disabled.value = false
    })
}
// 银行/对公 转账
function onSubmit(params: any, close: () => void) {
  params.useBalance = use_balance.value
  params.payAmount = orderInfo.value.payAmount
  params.payAmountDollar = orderInfo.value.payAmountDollar
  orderUploadCredentials(params)
    .then(() => {
      ElMessage.success('提交成功！')
      underWay.value = false
      payTipDialogVisible.value = true
      payTipType.value = 1
      // router.replace('/order/list')
    })
    .finally(() => close())
}
// 余额支付
function balancePay() {
  ElMessageBox.confirm('确认支付？', '温馨提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(() => {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在支付中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      disabled.value = true
      orderBalancePay({
        orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
        mergeId: mergeOrderNum.value || undefined,
      })
        .then(res => {
          payCodeSuccess()
          payTipDialogVisible.value = true
          payTipType.value = 0
        })
        .finally(() => {
          el_loading.close()
          disabled.value = false
        })
    })
    .catch(() => {})
}

function handleClose() {
  // if (orderInfo.value.payType != null) {
  //   router.replace('/order/list')
  // }
  if (openTimer) clearTimeout(openTimer)
  isOpenPayDialog.value = false
}

// 滚动到订单费用清单
function handleScollToTop() {
  const scrollBox = document.querySelector('.pages')
  if (scrollBox?.children?.length) {
    for (const el of scrollBox.children) {
      if (el.classList.contains('card-two')) {
        el.scrollIntoView({ behavior: 'smooth' })
        break
      }
    }
  }
}

init()
</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';
.pages {
  padding-top: 0;
}
.head-box {
  min-width: 1000px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.24) 0%, #ffffff 100%);
  padding: 13px;
  border-radius: 19px;
  gap: 10px;
  font-size: 16px;
  box-shadow: 3px 3px 4px 0px var(--shadow-gray);
  &__title {
    font-size: 14px;
    .head-title {
      display: flex;
      align-items: center;
    }
  }
  &__title::after {
    content: '';
    display: block;
    height: 1px;
    background-color: #e4e8eb;
    margin-top: 8px;
  }

  .mt-20 {
    margin-top: 20px;
  }
  .btn-box {
    // position: absolute;
    // bottom: 0px;
    // right: 45px;
  }
}
.head-content {
  margin: 35px 35px 0px;
  display: flex;
  .head-icon {
    height: 80px;
    width: 80px;
  }
  .head-text {
    // margin: 10px 0 0 22px;
    margin-left: 24px;

    flex: 1;
    &__title {
      font-size: 24px;
      font-weight: 600;
      color: #333333;

      &.span {
        font-size: 18px;
      }
    }
    &__order {
      display: flex;
      font-size: 16px;
      align-items: center;
      color: #777777;
      margin-top: 12px;
    }
  }
}
.title {
  font-size: 16px;
  font-weight: 700;
  margin: 4px 0 10px;

  &.primary {
    font-size: 25px;
    font-weight: 600;
    color: var(--el-color-primary);
  }
}
.card-one {
  width: 66vw;
  min-width: 653px;
  // display: flex;
  // flex-wrap: wrap;
  background: #f1f4fb;

  border-radius: 14px;
  // background-color: var(--bg);
  padding: 14px;
}
.card-two {
  min-width: 700px;
  padding: 5px 55px 25px;
  margin-top: 27px;
  background: #fff;
  border-radius: 19px;
  font-weight: 500;
  color: var(--text-color);
  &__title {
    font-size: 16px;
    color: #333333;
    // margin: 20px 0;
  }
  &__total {
    width: fit-content;
    margin: 20px 0 0 auto;

    p {
      width: 100px;
      text-align: right;
      margin: 0;
    }
  }
  &__order-head {
    font-size: 14px;
    margin: 10px 0;
    // color: var(--text-gray-color);
    color: #777777;
  }
}
.another-pay-btn {
  border-color: var(--el-color-primary);
  color: var(--el-color-primary);
  width: 180px;
  height: 40px;
  font-size: 15px;
  font-weight: bold;
  position: relative;

  .tip {
    width: 180px;
    position: absolute;
    bottom: -20px;
    left: -10px;
    font-size: 12px;
    color: #aaa;
    font-weight: 500;
    transform: scale(0.8);
    letter-spacing: 1px;
  }
}
.down-btn {
  position: absolute;
  top: 10px;
  left: 10px;
}
.info-box {
  font-size: 16px;
  .el-col {
    align-items: baseline;
  }

  .el-col-8 {
    display: flex;

    .label {
      flex-shrink: 0;
      width: 80px;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 95px);
      word-break: break-all;
    }
  }
  .el-col-12 {
    display: flex;
    .label {
      flex-shrink: 0;
      width: 80px;
    }
    .content {
      flex-shrink: 0;
      width: calc(100% - 95px);
      word-break: break-all;
    }
  }

  .flex-start {
    align-items: flex-start;
  }

  .label {
    width: 150px;
    text-align: right;
    margin: 0 5px 3px 0;
    color: #7f7f7f;
  }
  .money {
    font-size: 16px;
    font-weight: 500;

    span {
      font-size: 12px;
      font-weight: 200;
      color: var(--el-text-color-placeholder);
    }

    del {
      color: var(--el-text-color-placeholder);
    }
  }
  .money-checkbox {
    :deep(.el-checkbox) {
      .el-checkbox__label {
        font-size: 16px;
      }
    }
    span {
      font-size: 20px;
      color: var(--el-text-color-regular);
    }
  }
}
.pay-info {
  margin-top: 40px;
  &-title {
    font-size: 16px;
    color: #333333;
    text-align: left;
    width: 140px;
    margin-right: 14px;
  }
  span {
    font-size: 24px;
    font-weight: 600;
    color: #09172f;
  }
}
.total-box {
  margin-top: 15px;
  text-align: left;
  font-size: 14px;
  width: fit-content;
  position: relative;
  .box-title {
    display: flex;
    align-items: baseline;
  }
  &-title {
    font-size: 14px;
    color: #777777;
    text-align: left;
    width: 140px;
    margin-right: 14px;
  }
  &-icon {
    margin-left: 6px;
  }
  span {
    font-size: 18px;
    font-weight: 400;
    color: #09172f;
  }
  .tips {
    position: absolute;
    bottom: 6px;
    left: calc(100% + 10px);
    width: max-content;
    font-size: 13px;
    color: #9ba6ba;
    // color: var(--text-gray-color);
  }
}
.select-pay-label {
  :deep(.el-divider) {
    max-width: 350px;
    margin: 40px auto;

    .el-divider__text {
      font-size: 18px;
      color: var(--text-gray-color);
    }
  }
}
.pay-icon {
  width: 24px;
  height: 24px;
}
.pay-type {
  margin-top: 19px;
  &-title {
    font-size: 14px;
    color: #777777;
    text-align: left;
    width: 140px;
    margin-right: 14px;
  }
}
.select-pay-box {
  // margin: 5px 0 0;

  :deep(.el-radio-group) {
    // margin: 5px 20px 0 0px;
    font-size: 16px;

    .el-radio-button__inner {
      border-radius: 6px;
      display: flex;
      align-items: center;
      gap: 5px;
      padding: 9px 19px;
    }
  }
  .wx-color {
    color: rgb(25, 213, 108);
  }
  .zfb-color {
    color: rgb(0, 159, 232);
  }

  .iconfont {
    // background: linear-gradient(to right, #fff, #fff) no-repeat right;
    // background-size: 18px 11px;
    font-size: 20px;
  }

  .btn-corner-mark {
    position: absolute;
    top: -1px;
    left: -1px;
    width: 32px;
    height: 32px;
    background-image: url('@/assets/image/recommend_corner_mark.png');
    background-size: 100%;

    // position: absolute;
    // top: -1px;
    // left: -1px;
    // width: 35px;
    // height: 35px;
    // overflow: hidden;

    // &::after {
    //   content: '推荐';
    //   display: block;
    //   position: absolute;
    //   top: -3px;
    //   left: -41px;
    //   width: 100px;
    //   height: 24px;
    //   line-height: 33px;
    //   font-size: 12px;
    //   text-align: center;
    //   background-color: rgb(255, 85, 82);
    //   color: #fff;
    //   transform: rotate(-45deg);
    // }
  }
}
.balance-checkbox {
  margin-top: 15px;
  display: flex;
  align-items: center;
  &-title {
    font-size: 14px;
    color: #777777;
    width: 140px;
    text-align: left;
    margin-right: 14px;
  }
}
.qrcode-pay-box {
  margin-left: 20px;
  line-height: 40px;

  .pay-total-box {
    color: var(--text-gray-color);
    font-size: 14px;
    margin-top: 10px;

    .pay-total {
      font-family: PingFangSC;
      color: var(--el-color-warning);

      span {
        font-size: 26px;
        font-weight: 600;
      }
    }
  }

  .qrcode-tips {
    font-size: 16px;
    font-weight: bold;
    color: #1f2122;

    img {
      width: 16px;
      height: 16px;
    }

    .icon {
      width: 16px;
      height: 16px;
    }
  }
}
:deep(.el-table) {
  width: 100%;

  .table-head-primary.el-table__cell {
    padding: 13px 0;
    text-align: center;
    color: var(--text-color);
    background-color: var(--table-head-bg);
  }
}
</style>
