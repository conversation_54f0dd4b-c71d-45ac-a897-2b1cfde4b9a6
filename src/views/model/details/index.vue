<template>
  <div class="model-details-page">
    <div class="flex-between header">
      <el-icon class="back-icon" @click="handleBack" :size="20" color="#9BA6BA"><ArrowLeftBold /></el-icon>
      <div class="flex-start" v-if="isShow && store.userInfo.account">
        <img v-if="!isBlackModel" @click="handleBlackModel(true)" src="@/assets/icon/icon_ban_1.png" alt="" />
        <img v-else @click="handleBlackModel(false)" src="@/assets/icon/icon_ban_2.png" alt="" />
        <template v-if="!isBlackModel">
          <img
            v-if="modelInfo.collect"
            @click="handleCollect"
            src="@/assets/icon/icon_collect_on_2.png"
            alt=""
          />
          <img v-else @click="handleCollect" src="@/assets/icon/icon_collect_2.png" alt="" />
        </template>
      </div>
    </div>

    <div class="model-info">
      <div class="model-info-img">
        <el-avatar :size="100" :src="modelInfo.modelPic ? $picUrl + modelInfo.modelPic + '!3x4' : ''" />
      </div>
      <div class="model-info-name">
        <div>{{ modelInfo.name }}</div>
        <div>ID:{{ modelInfo.account }}</div>
      </div>
      <div class="flex-center gap-10 model-info-desc">
        <span style="color: #ff6538c8">$29.9</span>
        <NationTag :type="modelInfo.nation" isIcon :color="'#9ba6ba'" />
        <ModelTypeTag :type="modelInfo.type" isIcon color="#9ba6ba" />
      </div>

      <div class="flex-start model-info-tags" v-if="modelInfo.specialtyCategory?.length">
        <div class="model-info-tags-label">擅长品类：</div>
        <div class="model-info-tags-content">
          <template v-for="(item, i) in modelInfo.specialtyCategory" :key="item.id">
            <el-tag
              v-if="isShowSpecialtyCategoryAll || i < 5"
              class="one-ell max-w"
              :effect="item.highlight ? 'dark' : 'plain'"
              round
              :type="item.highlight ? 'primary' : 'info'"
            >
              {{ item.name }}
            </el-tag>
          </template>
          <div
            v-if="modelInfo.specialtyCategory.length > 5"
            style="width: 50px; text-align: center; color: var(--el-color-primary)"
            @click="isShowSpecialtyCategoryAll = !isShowSpecialtyCategoryAll"
          >
            {{ isShowSpecialtyCategoryAll ? '收起' : '展开' }}
          </div>
        </div>
      </div>
      <div class="flex-start model-info-tags" v-if="modelInfo.tags?.length">
        <div class="model-info-tags-label">模特标签：</div>
        <div class="model-info-tags-content">
          <template v-for="(item, i) in modelInfo.tags" :key="item.id">
            <el-tag
              v-if="isShowTagsAll || i < 5"
              class="one-ell max-w"
              :effect="item.highlight ? 'dark' : 'plain'"
              round
              :type="item.highlight ? 'primary' : 'info'"
            >
              {{ item.name }}
            </el-tag>
          </template>
          <div
            v-if="modelInfo.tags.length > 5"
            style="width: 50px; text-align: center; color: var(--el-color-primary)"
            @click="isShowTagsAll = !isShowTagsAll"
          >
            {{ isShowTagsAll ? '收起' : '展开' }}
          </div>
        </div>
      </div>
    </div>

    <div class="model-video-case">
      <div class="model-video-title">视频案例</div>
      <Empty
        v-if="!videoList || videoList.length === 0"
        image-type="video"
        description="暂无视频案例"
        style="grid-column: 1/-1; justify-self: center; align-self: center"
      />
      <div class="model-video-list">
        <div class="video-item" v-for="(item, i) in videoList" :key="i">
          <img class="play-icon" src="@/assets/icon/icon_play.png" alt="" />
          <el-image
            :src="item.picUri ? $picUrl + item.picUri + '!1x1' : ''"
            fit="fill"
            loading="lazy"
            preview-teleported
            @click="openVideo(item.videoUrl)"
          >
            <template #error>
              <div class="image-error">
                <el-icon :size="25"><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div class="flex-center one-ell video-name">
            <div :class="{ 'is-highlight': item.highlight }">{{ handleVideoName(item.name) }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import NationTag from '@/components/public/tag/NationTag.vue'
import ModelTypeTag from '@/components/public/tag/ModelTypeTag.vue'
import {
  modelMerchantDetails,
  referenceModelInfo,
  modelCollect,
  blackModel,
  cancelBlackModel,
} from '@/api/model'
import type { modelItem, amazonVideoList } from '../type/model'
import { nextTick, onMounted, ref } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import { useRoute, useRouter } from 'vue-router'
import { http_reg } from '@/utils/RegExp'
import { stringLength } from '@/utils/public'
import { ElMessage } from 'element-plus'
import { MessageBox } from '@/utils/message'
import { loginSuccessAction, openLogin } from '@/hooks/useLogin'
import aegis from '@/utils/aegis'
import Aegis from 'aegis-web-sdk'

const store = useUserStore()
const route = useRoute()
const router = useRouter()

const modelInfo = ref<modelItem>({
  amazonVideo: [],
  id: undefined,
  name: '',
  platform: '',
  specialtyCategory: [],
  tags: [],
  tiktokVideo: [],
  type: 0,
  account: '',
  status: '',
  modelPic: '',
})
const loading = ref(false)
const curSpecialtyCategory = ref<any[]>([])
const curKeyword = ref('')

const isShowSpecialtyCategoryAll = ref(false)
const isShowTagsAll = ref(false)

const isShow = ref(false)
const showText = ref('')

const isBlackModel = ref(false)

const allLifePhoto = ref<string[]>([])
const lifePhoto = ref<string[]>([])
const videoList = ref<amazonVideoList>([])

function handleBack() {
  router.replace('/model/list')
}

function handleBlackModel(isBlack: boolean) {
  if (isBlack) {
    MessageBox(
      `<p>拉黑模特后，系统会为您隐藏该模特，其中包含：</p>
      <ol style="font-size: 14px;padding-left: 16px;color: var(--text-gray-color);">
        <li>模特库无法查看该模特信息</li>
        <li>选择意向模特时搜索不到该模特</li>
        <li>蜗牛客服匹配模特时，不会选中该模特为意向模特</li>
<!--        <li>如果购物车中有订单选择该模特为意向模特，系统会为您取消意向（订单中其他信息不会发生变更）</li>-->
      </ol>
      <p>您在【我的模特-我拉黑的】中可管理已拉黑的模特，取消拉黑后可继续选择该模特为您的意向模特。</p>`,
      {
        showClose: true,
        title: '拉黑模特确认',
        customStyle: {
          '--el-messagebox-width': '520px',
        },
        cancelButtonText: '取消',
        confirmButtonText: '确定',
      }
    )
      .then(() => {
        blackModel(modelInfo.value.id).then(res => {
          if (res.code == 200) {
            isBlackModel.value = true
            // getModelDetails(data.value.id)
            ElMessage.success('拉黑成功')
            let form: any = localStorage.getItem('saveOrderForm_' + store.userInfo.account)
            if (form) {
              form = JSON.parse(form)
              if (form.selModel?.length) {
                form.selModel = form.selModel.filter((item: any) => item.id != modelInfo.value.id)
              }
              localStorage.setItem('saveOrderForm_' + store.userInfo.account, JSON.stringify(form))
            }
          }
        })
      })
      .catch(() => {})
  } else {
    MessageBox(`<p>确认取消拉黑？</p>`, {
      showClose: true,
      title: '提示',
      customStyle: {
        '--el-messagebox-width': '420px',
      },
      cancelButtonText: '取消',
      confirmButtonText: '确定',
    })
      .then(() => {
        cancelBlackModel(modelInfo.value.id).then(res => {
          if (res.code == 200) {
            isBlackModel.value = false
            getModelDetails(modelInfo.value.id as number)
            ElMessage.success('操作成功')
          }
        })
      })
      .catch(() => {})
  }
}

function handleVideoName(name: string) {
  if (name && stringLength(name) > 20) {
    return name.slice(0, 9) + '...'
  }
  return name
}

let collecting = false
// 收藏
function handleCollect() {
  if (collecting) return
  // let m = data.value.collect ? '取消' : ''
  collecting = true
  modelCollect({ id: modelInfo.value.id })
    .then(res => {
      if (res.code == 200) {
        // ElMessage.success('收藏成功')
        modelInfo.value.collect = !modelInfo.value.collect
      }
    })
    .finally(() => (collecting = false))
}

function getModelDetails(id: string | number) {
  loading.value = true
  modelMerchantDetails(id, {
    keyword: curKeyword.value,
    specialtyCategory: curSpecialtyCategory.value?.join(','),
  })
    .then(res => {
      modelInfo.value = res.data

      showText.value = modelInfo.value.collect ? '哎呀，你收藏的TA下线了' : '哎呀，模特下线啦'
      isShow.value = modelInfo.value.isShow === 1

      if (modelInfo.value.lifePhoto && modelInfo.value.lifePhoto.length >= 10) {
        lifePhoto.value = modelInfo.value.lifePhoto.slice(0, 10)
      } else {
        lifePhoto.value = modelInfo.value.lifePhoto || []
      }
      allLifePhoto.value = modelInfo.value.lifePhoto || []
      if (res.data.videoCase && res.data.videoCase.length > 0) {
        videoList.value = res.data.videoCase
      } else {
        videoList.value = []
      }
    })
    .finally(() => {
      loading.value = false
    })
}

function getModelDetails2(id: string) {
  loading.value = true
  referenceModelInfo(id, {
    keyword: curKeyword.value,
    specialtyCategory: curSpecialtyCategory.value?.join(','),
  })
    .then(res => {
      modelInfo.value = res.data

      showText.value = '哎呀，模特下线啦'
      isShow.value = modelInfo.value.isShow === 1

      if (modelInfo.value.lifePhoto && modelInfo.value.lifePhoto.length >= 10) {
        lifePhoto.value = modelInfo.value.lifePhoto.slice(0, 10)
      } else {
        lifePhoto.value = modelInfo.value.lifePhoto || []
      }
      allLifePhoto.value = modelInfo.value.lifePhoto || []
      videoList.value = [...res.data.amazonVideo, ...res.data.tiktokVideo]
    })
    .finally(() => {
      loading.value = false
    })
}

function openVideo(url: any) {
  if (!url) {
    ElMessage.warning('无视频链接！')
    return
  }
  if (!http_reg.test(url)) {
    ElMessage.warning('视频链接格式有误！')
    return
  }
  if (!store.userInfo.account) {
    console.log(url)

    loginSuccessAction.value.fun = () => {
      if (curSpecialtyCategory.value.length) {
        sessionStorage.setItem('curSpecialtyCategory', JSON.stringify(curSpecialtyCategory.value))
      }
      if (curKeyword.value) {
        sessionStorage.setItem('curKeyword', curKeyword.value)
      }
      window.location.reload()
    }
    loginSuccessAction.value.intercept = true
    openLogin()
    try {
      aegis?.report({
        msg: '未登录用户播放案例视频',
        level: Aegis.logType.REPORT,
        trace: 'trace',
      })
    } catch (error) {
      console.log('日志上报失败')
    }
    return
  }
  window.open(url)
  try {
    aegis?.report({
      msg: '登录用户播放案例视频',
      level: Aegis.logType.REPORT,
      trace: 'trace',
    })
  } catch (error) {
    console.log('日志上报失败')
  }
}

function init() {
  if (!route.params.modelId) {
    window.location.href = '/model/list'
    return
  }
  let k = sessionStorage.getItem('curKeyword') || undefined
  if (k) {
    curKeyword.value = k
  }
  sessionStorage.removeItem('curKeyword')
  let s = sessionStorage.getItem('curSpecialtyCategory')
  if (s) {
    curSpecialtyCategory.value = JSON.parse(s)
  }
  sessionStorage.removeItem('curSpecialtyCategory')
  if (!store.userInfo.account) {
    getModelDetails2(route.params.modelId as string)
  } else {
    getModelDetails(route.params.modelId as string)
  }
}
init()

onMounted(() => {
  window.addEventListener('pageshow', function (event) {
    if (event.persisted && /iPhone|iPad|iPod/i.test(navigator.userAgent)) {
      document.body.style.display = 'none'
      document.body.offsetHeight // 触发重排
      document.body.style.display = ''
    }
  })
})
</script>

<style scoped lang="scss">
.model-details-page {
  width: 100vw;
  min-height: 100vh;
  background-color: rgb(243, 243, 247);
  background-image: url('@/assets/image/mobile_model_detail_bg.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 100px 16px 20px;
  box-sizing: border-box;
  position: relative;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);

  .header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100vw;
    padding: 19px 16px;
    box-sizing: border-box;

    & > div {
      gap: 8px;
    }

    img {
      width: 36px;
      height: 36px;
    }

    .back-icon {
      background-color: rgba(255, 255, 255, 0.56);
      padding: 8px;
      border-radius: 50%;
    }
  }

  .model-info {
    width: 100%;
    min-height: 160px;
    background-color: #fff;
    border-radius: 24px;
    position: relative;

    &-img {
      position: absolute;
      top: -50px;
      left: 50%;
      transform: translateX(-50%);
      width: 100px;
      height: 100px;
      border: 2px solid #fff;
      border-radius: 50%;

      .el-avatar {
        align-items: flex-start;
        position: absolute;
        top: 0;
        left: 0;
      }
    }
    &-name {
      padding-top: 60px;
      text-align: center;

      :nth-child(1) {
        font-weight: 500;
        font-size: 18px;
        color: var(--text-color);
        line-height: 28px;
      }
      :nth-child(2) {
        font-weight: 400;
        font-size: 12px;
        color: #9ba6ba;
        line-height: 22px;
      }
    }
    &-tags {
      padding: 10px 20px;
      align-items: flex-start;
      gap: 6px;

      &-label {
        flex-shrink: 0;
        font-weight: 500;
        font-size: 14px;
        color: #777777;
        line-height: 24px;
      }
      &-content {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;

        .el-tag {
          display: inline-block;
          line-height: 22px;
          text-align: center;
          width: 70px;
          min-width: 70px;
        }
        .max-w {
          width: auto;
          max-width: 220px;
        }
      }
    }
  }

  .model-video-case {
    width: 100%;
    background-color: #fff;
    border-radius: 24px;
    margin-top: 20px;
    padding: 16px;
    box-sizing: border-box;

    .model-video-title {
      text-align: center;
      font-weight: 500;
      font-size: 16px;
      color: var(--text-color);
      line-height: 24px;
      padding: 0 0 13px;
    }
    .model-video-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
      gap: 13px;

      .video-item {
        position: relative;
        width: calc((100% - 13px) / 2);
        height: auto;

        .el-image {
          width: 100%;
          border-radius: 12px;
        }

        .video-name {
          text-align: center;

          .is-highlight {
            border-radius: 20px;
            background-color: var(--el-color-primary);
            color: #fff;
            width: fit-content;
            padding: 0 6px;
            max-width: 100%;
            box-sizing: border-box;
          }
        }

        .play-icon {
          position: absolute;
          top: 5px;
          right: 8px;
          z-index: 9;
          width: 15px;
          height: 15px;
        }
      }
    }
  }
}
</style>
