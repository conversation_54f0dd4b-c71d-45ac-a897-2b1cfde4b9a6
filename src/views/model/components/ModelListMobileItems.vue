<template>
  <div class="model-list-mobile-items" @click.stop="openDetail">
    <el-image
      :src="data.modelPic ? $picUrl + data.modelPic + '!3x4' : ''"
      fit="fill"
      loading="lazy"
      class="cover-img"
      preview-teleported
    >
      <template #error>
        <div class="image-error">
          <el-icon :size="25"><Picture /></el-icon>
        </div>
      </template>
    </el-image>

    <div class="info-item">
      <div class="info-item__name one-ell">{{ data.name }}</div>
      <div class="info-item__desc flex-start">
        <template v-for="item in nationOptions" :key="item.value">
          <span v-if="data.nation == item.value">
            {{ item.label }}
          </span>
        </template>
        <span>丨</span>
        <template v-for="item in modelTypeOptions" :key="item.value">
          <span v-if="data.type == item.value">
            {{ item.label }}
          </span>
        </template>
        <span>丨</span>
        <span>$29.9</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type PropType } from 'vue'
import type { biz, modelItem } from '../type/model'
import { modelTypeOptions, nationOptions } from '@/utils/data'
import { useRouter } from 'vue-router'

const router = useRouter()

const emits = defineEmits(['openDetail'])

const props = defineProps({
  data: {
    type: Object as PropType<modelItem>,
    required: true,
  },
  bizModelType: {
    type: Array<biz>,
    default: () => [],
  },
  bizModelPlatform: {
    type: Array<biz>,
    default: () => [],
  },
  bizNation: {
    type: Array<biz>,
    default: () => [],
  },
})

function openDetail() {
  emits('openDetail')
  router.push('/model/details/' + props.data.id)
}

</script>

<style scoped lang="scss">
.model-list-mobile-items {
  position: relative;
  width: calc((100% - 13px) / 2);
  height: auto;
  aspect-ratio: 3 / 4;
  border-radius: 12px;
  box-shadow: var(--el-box-shadow-light);

  .cover-img {
    display: block;
    border-radius: 12px;
  }
  .image-error {
    width: 100%;
    height: 100%;
    min-height: 165px;
    color: var(--el-text-color-secondary);
    display: flex;
    justify-content: center;
    align-items: center;

    @include mediaTo('phone') {
      min-height: 50vw;
    }
  }

  .info-item {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 0 8px 4px;
    border-radius: 0 0 12px 12px;
    box-sizing: border-box;
    color: #fff;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.1) 30%,rgba(0, 0, 0, 0.6) 100%);

    &__name {
      font-size: 16px;
      width: 80%;
    }
    &__desc {
      // gap: 6px;
      flex-shrink: 0;
      font-size: 12px;
      color: #fffc;
    }
  }
}
</style>