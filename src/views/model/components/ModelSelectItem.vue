<template>
  <div class="list-item-box">
    <div
      class="model-select-list-tooltip"
      :class="{ bottom: itemIndex < 3 }"
      v-if="tipsModels.length && tipsModels.includes(data.account)"
    >
      上次您选择了这个模特，但模特没有档期啦
      <br />
      建议您再挑选其他模特~
    </div>
    <div
      class="model-list-box flex-start"
      :class="{ 'select-all-box': curModelId.includes(data.id), 'not-select-box': data.isNoSlot }"
      ref="modelListBoxRef"
      @click.stop="handleSelect(curModelId.includes(data.id))"
    >
      <div class="flex-start left-box">
        <div v-if="data.isNoSlot" class="tag-status">暂无档期</div>
        <PercentageImg
          class="img model-img"
          width="110px"
          :isPreview="true"
          :src="data?.modelPic"
          radius="8px"
          @more="viewLifePhoto"
        />
        <!-- 模特类型 -->
        <template v-for="item in modelTypeOptions" :key="item.value">
          <div :class="[data.type == 1 ? 'sr' : 'yxz']" class="model-type-tag" v-if="data.type == item.value">
            {{ item.label }}
          </div>
        </template>
      </div>
      <div class="right-box">
        <div class="info-box">
          <div class="flex-column gap info-head">
            <div class="name">
              <div class="one-ell">{{ data.name }}</div>
              <span class="acc">{{ data.account ? `ID：${data.account}` : '' }}</span>
            </div>
            <div class="flex-start gap tag-box">
              <!-- 性别 -->
              <!-- <SexTag :type="data.sex" /> -->
              <!-- <span>{{ data.sex == 1 ? '男性' : '女性' }}</span> -->
              <svg
                v-if="data.sex == 1"
                t="*************"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="2364"
                width="16"
                height="16"
              >
                <path
                  d="M872.335 421.516V151.71H602.528L702.949 252.13 601.4 353.68c-46.47-32.81-103.174-52.091-164.385-52.091-157.595 0-285.351 127.756-285.351 285.35S279.421 872.29 437.014 872.29s285.352-127.755 285.352-285.35c0-57.78-17.19-111.537-46.711-156.47l102.818-102.814 93.862 93.861zM437.015 782.18c-107.827 0-195.24-87.413-195.24-195.24s87.413-195.24 195.24-195.24 195.24 87.413 195.24 195.24-87.413 195.24-195.24 195.24z"
                  fill="#5695c0"
                  p-id="2365"
                  data-spm-anchor-id="a313x.search_index.0.i2.44e73a81p8VfaI"
                  class="selected"
                ></path>
              </svg>
              <svg
                v-else
                t="1741593332041"
                class="icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
                p-id="9176"
                data-spm-anchor-id="a313x.search_index.0.i5.30923a81xUfu5y"
                width="16"
                height="16"
              >
                <path
                  d="M625.2 97.7c-166.5 0-301.4 134.9-301.4 301.4 0 66.7 21.7 128.3 58.4 178.2L288.8 670 176.2 556.5c-17.9-18.1-47.1-18.2-65.1-0.3-18.1 17.9-18.2 47.1-0.3 65.1l112.6 113.5L111 846.4c-18.1 17.9-18.2 47.1-0.3 65.1 9 9.1 20.8 13.6 32.7 13.6 11.7 0 23.5-4.5 32.4-13.4l112.4-111.5 111.5 112.4c9 9.1 20.8 13.6 32.7 13.6 11.7 0 23.5-4.5 32.4-13.4 18.1-17.9 18.2-47.1 0.3-65.1L353.7 735.4l93.7-93c49.9 36.5 111.3 58.1 177.8 58.1 166.5 0 301.4-134.9 301.4-301.4S791.7 97.7 625.2 97.7z m148 449.4c-39.5 39.5-92.1 61.3-148 61.3s-108.5-21.8-148-61.3-61.3-92.1-61.3-148 21.8-108.5 61.3-148 92.1-61.3 148-61.3 108.5 21.8 148 61.3 61.3 92.1 61.3 148-21.8 108.5-61.3 148z"
                  fill="#F283B9"
                  p-id="9177"
                  data-spm-anchor-id="a313x.search_index.0.i6.30923a81xUfu5y"
                  class="selected"
                ></path>
              </svg>

              <!-- 国家 -->
              <!-- <NationTag color="#777" :type="data.nation" /> -->
              <template v-for="item in nationOptions" :key="item.value">
                <span v-if="data.nation == item.value">
                  {{ item.label }}
                </span>
              </template>

              <!-- 年龄层 -->
              <!-- <AgeLayerTag :type="data.ageGroup" /> -->
              <template v-for="item in ageLayerOptions" :key="item.value">
                <span v-if="data.ageGroup == item.value">
                  {{ item.label }}
                </span>
              </template>

              <!-- 平台 -->
              <!-- <PlatformTag :type="data.platform" /> -->
            </div>
          </div>
          <div class="flex-start gap" style="align-items: baseline">
            <div class="label-text" style="color: #9ba6ba">模特标签</div>
            <div class="flex-start gap tag-list">
              <template v-for="(item, index) in data.tags" :key="item.id">
                <template v-if="index < 2">
                  <el-tag
                    class="one-ell tag blue"
                    :class="{
                      'max-w': data.tags.length < 3,
                    }"
                    round
                    v-ellipsis-tooltips="item.name"
                  >
                    {{ item.name }}
                  </el-tag>
                </template>
              </template>
              <template v-if="data.tags.length > 2">
                <el-tooltip placement="top" effect="light" trigger="hover">
                  <el-tag class="tag blue" effect="plain" type="primary" round>. . .</el-tag>
                  <template #content>
                    <div class="tooltip-tag">
                      <template v-for="item in data.tags" :key="item.id">
                        <el-tag class="tag blue" round>{{ item.name }}</el-tag>
                      </template>
                    </div>
                  </template>
                </el-tooltip>
              </template>
            </div>
          </div>
          <div class="flex-start gap" style="align-items: baseline">
            <div class="label-text" style="color: #9ba6ba">擅长品类</div>
            <div class="flex-start gap tag-list">
              <template v-for="(item, index) in data.specialtyCategory" :key="item.id">
                <template v-if="index < 2">
                  <el-tag
                    class="one-ell tag"
                    :class="{
                      'max-w': data.specialtyCategory.length < 3,
                    }"
                    type="primary"
                    effect="plain"
                    round
                    v-ellipsis-tooltips="item.name"
                  >
                    {{ item.name }}
                  </el-tag>
                </template>
              </template>
              <template v-if="data.specialtyCategory.length > 2">
                <el-tooltip placement="top" effect="light" trigger="hover">
                  <el-tag class="tag" type="primary" effect="plain" round>. . .</el-tag>
                  <template #content>
                    <div class="tooltip-tag">
                      <template v-for="item in data.specialtyCategory" :key="item.dictId">
                        <el-tag class="tag" type="primary" effect="plain" round>{{ item.name }}</el-tag>
                      </template>
                    </div>
                  </template>
                </el-tooltip>
              </template>
            </div>
          </div>
        </div>

        <div class="video-box flex-start">
          <VideoCover
            v-for="item in allVideo"
            :key="item.picUri"
            class="img"
            width="40px"
            height="40px"
            suffix="!1x1"
            :src="item.picUri"
            fit="fill"
            playType
            @click.stop="openVideo(item)"
          />
        </div>
      </div>

      <div class="flex-column select-box" @click.stop="handleSelect(curModelId.includes(data.id))">
        <slot name="selectbox" :row="data">
          <div>
            <div v-if="curModelId.includes(data.id)">
              <img class="icon" src="@/assets/icon/icon_radio_sel.png" alt="" />
            </div>
            <div v-else>
              <img class="icon" src="@/assets/icon/icon_radio.png" alt="" />
            </div>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRef, watch, type PropType } from 'vue'
import PercentageImg from '@/components/public/image/PercentageImg.vue'
import VideoCover from '@/components/public/image/VideoCover.vue'
// import SexTag from '@/components/public/tag/SexTag.vue'
// import AgeLayerTag from '@/components/public/tag/AgeLayerTag.vue'
import NationTag from '@/components/public/tag/NationTag.vue'
// import ModelTypeTag from '@/components/public/tag/ModelTypeTag.vue'
import { modelTypeOptions, nationOptions, ageLayerOptions } from '@/utils/data'
// import PlatformTag from '@/components/public/tag/PlatformTag.vue'
import { ElMessage } from 'element-plus'
import { modelCollect } from '@/api/model'
import type { biz, modelItem } from '../type/model'
import { useUserStore } from '@/stores/modules/user'
import { useRouter } from 'vue-router'
// import { ageLayerOptions } from '@/utils/data'
import { http_reg } from '@/utils/RegExp'
import { stringLength } from '@/utils/public'
import { useTooltips } from '@/hooks/useTooltips'

const emits = defineEmits(['openDetails', 'openVideo', 'openPhoto', 'changeModel'])

const { showTooltips } = useTooltips()

const router = useRouter()
const store = useUserStore()

const props = defineProps({
  data: {
    type: Object as PropType<modelItem>,
    required: true,
  },
  itemIndex: {
    type: Number,
    default: 1,
  },
  tipsModels: {
    type: Array,
    default: () => [],
  },
  bizModelType: {
    type: Array<biz>,
    default: () => [],
  },
  bizModelPlatform: {
    type: Array<biz>,
    default: () => [],
  },
  bizNation: {
    type: Array<biz>,
    default: () => [],
  },
  showCollect: {
    type: Boolean,
    default: true,
  },
  selectModels: {
    type: Array<modelItem>,
    default: () => [],
  },
  limit: {
    type: Number,
    default: 99,
  },
})

const data = toRef(() => props.data)

const curModelId = computed(() => {
  return props.selectModels.map((item: any) => item.id)
})

const allVideo = computed(() => {
  return data.value.amazonVideo.concat(data.value.tiktokVideo).splice(0, 3)
})

const modelListBoxRef = ref()
const isOverflow = ref(false)

function handlePlatform(val: any) {
  let arr = data.value.platform.split(',') || ''
  return arr.includes(val)
}

//选择
function selectModel() {}

let collecting = false
// 收藏
function handleCollect() {
  if (collecting) return
  // let m = data.value.collect ? '取消' : ''
  collecting = true
  modelCollect({ id: data.value.id })
    .then(res => {
      if (res.code == 200) {
        // ElMessage.success(m + '收藏成功')
        data.value.collect = !data.value.collect
      }
    })
    .finally(() => (collecting = false))
}

function openDetails() {
  emits('openDetails', data.value)
}

function openVideo(item: { videoUrl: any }) {
  if (!item?.videoUrl) {
    ElMessage.warning('无视频链接！')
    return
  }
  if (!http_reg.test(item.videoUrl)) {
    ElMessage.warning('视频链接格式有误！')
    return
  }
  emits('openVideo', item.videoUrl)
}
function selectTa() {
  store.realTimeCheckVip().then(() => {
    if (store.isVip()) {
      sessionStorage.setItem('createOrderModelInfo', JSON.stringify(data.value))
      router.push('/order/create')
      return
    }
    ElMessage.warning('请先开通会员！')
    if (store.isOwnerAcc()) {
      router.push('/vip')
    }
  })
}
function viewLifePhoto() {
  emits('openPhoto', data.value.lifePhoto)
}

const resize = () => {
  if (
    modelListBoxRef.value.offsetWidth <
    modelListBoxRef.value.children[0].offsetWidth + modelListBoxRef.value.children[1].offsetWidth + 50
  ) {
    isOverflow.value = true
  } else {
    isOverflow.value = false
  }
}

function handleSelect(type: any) {
  if (props.data.isNoSlot) {
    if (props.limit == 1) {
      emits('changeModel', data.value.id, true, true)
    } else if (props.limit > curModelId.value.length) {
      emits('changeModel', data.value.id, true)
    } else if (props.limit <= curModelId.value.length) {
      emits('changeModel', data.value.id, true, false)
    }
    return
  }
  if (props.limit == 1) {
    emits('changeModel', data.value.id, type, true)
  } else if (props.limit > curModelId.value.length) {
    emits('changeModel', data.value.id, type)
  } else if (props.limit <= curModelId.value.length) {
    emits('changeModel', data.value.id, true, false)
  }
}

defineExpose({
  resize,
})
</script>

<style scoped lang="scss">
.list-item-box {
  position: relative;
  .model-select-list-tooltip {
    position: absolute;
    padding: 6px 12px;
    background: #c8e3f5cc;
    color: #015478;
    border-radius: 10px;
    text-align: center;
    font-size: 12px;
    line-height: 1.3;
    z-index: 9;
    top: -35px;
    left: 70px;

    &::before {
      content: '';
      position: absolute;
      background: transparent;
      bottom: -10px;
      left: 45%;
      width: 0px;
      height: 0px;
      border: 5px solid transparent !important;
      border-top: 5px solid #c8e3f5cc !important;
      transform: rotate(0deg);
    }

    &.bottom {
      top: 35px;

      &::before {
        bottom: 42px;
        transform: rotate(180deg);
      }
    }
  }
}

.model-list-box {
  cursor: pointer;
  gap: 10px;
  align-items: flex-start;
  padding: 10px 6px 10px 8px;
  // margin-top: 18px;
  overflow: hidden;
  position: relative;
  border-radius: 16px;
  border: 1px transparent solid;
  background-color: var(--bg);
  // box-shadow: var(--el-box-shadow-light);
  box-shadow: 0px 0px 4px var(--el-color-primary-light-7);
  width: 320px;
  height: 150px;

  .img {
    flex-shrink: 0;
    margin: 0;
  }

  .left-box {
    cursor: pointer;
    // margin-left: 35px;
    align-items: flex-start;
    // flex-shrink: 0;
    // flex-grow: 2;
    gap: 12px;
    // width: 550px;
    position: relative;

    .img {
      width: 110px;
      height: 146px;
      border-radius: 8px;
      border: 1px solid #f2faff;

      .image-error {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .model-img {
      :deep(.el-image-viewer__wrapper) {
        img {
          border-radius: 16px;
        }
      }
    }

    .icon {
      height: 18px;
      width: 18px;
      cursor: pointer;
    }

    .tag-status {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      color: #fff;
      padding: 0 44%;
      box-sizing: border-box;
      border-radius: 8px;
      display: flex;
      align-items: center;
    }

    .model-type-tag {
      position: absolute;
      left: 0px;
      bottom: 0px;
      z-index: 2;
      font-size: 12px;
      font-weight: 500;
      border-radius: 0 8px 0 6px;
      padding: 0 6px;

      &.sr {
        background: #c9e8fc;
        color: #5696c0;
      }
      &.yxz {
        background: #faf6ed;
        color: #deb372;
      }
    }
  }

  .info-box {
    display: flex;
    flex-direction: column;
    align-content: flex-start;
    gap: 6px;
    flex-shrink: 0;

    .info-head {
      align-items: baseline;
      width: 200px;
    }

    .name {
      display: flex;
      align-items: center;
      gap: 0 5px;
      font-size: 16px;
      line-height: 20px;
      font-weight: 600;
      color: var(--text-color);
      max-width: 175px;

      .acc {
        display: inline-block;
        font-size: 12px;
        font-weight: 300;
        color: var(--text-gray-color);
        flex-shrink: 0;
      }
    }
    .text {
      font-size: 14px;
      color: var(--text-color);
    }
    .label-text {
      font-size: 12px;
      font-weight: 500;
      color: var(--text-color);
      flex-shrink: 0;
    }
    .tag-box {
      color: #777;
      flex-wrap: wrap;
      font-size: 12px;
    }
    .tag-list {
      flex-wrap: wrap;
      width: 450px;
    }
    .tag {
      width: 44px;
      display: block;
      line-height: 22px;
      text-align: center;

      &.max-w {
        width: 68px;
      }

      &.cur {
        cursor: pointer;
      }

      &.blue {
        background: linear-gradient(90deg, #b8e3ff 0%, #f2faff 100%);
        --el-tag-border-color: #f2faff;
        --el-tag-text-color: #215476;
      }
    }

    .gap {
      gap: 8px;
    }
  }
  .video-box {
    margin-top: 4px;
    gap: 10px;
  }

  .more-box {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 9;
    width: 10px;
    height: 100%;
    padding-left: 18px;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(90deg, transparent, #fff 30%);

    .action-box {
      position: absolute;
      top: 0;
      right: 18px;
    }

    .collect-box {
      cursor: pointer;
      color: #ec808d;
      height: 40px;
      margin-right: -8px;

      .iconfont {
        font-size: 28px;
        line-height: 38px;

        &.icon-shoucang1 {
          font-size: 25px;
        }
      }
    }

    .gengduo {
      cursor: pointer;
      margin-bottom: -30px;
      width: 24px;
      height: 25px;
      line-height: 24px;
      text-align: center;
      margin-left: -32px;
      color: #d7d7d7;
      font-size: 25px;
      background: #fff;
      border-radius: 50%;
      box-shadow: 0px 0px 4px rgb(135 135 135);

      &:hover {
        color: #ec808d;
        box-shadow: 0px 0px 4px #ec808d;
      }
    }

    img {
      cursor: pointer;
      width: 25px;
      height: 25px;
    }

    .btn {
      cursor: pointer;
      color: #ec808d;
      border: 3px solid #ec808d;
      border-radius: 50%;
      font-size: 15px;
      width: 40px;
      height: 39px;
      line-height: 40px;
      text-align: center;
      transform: scale(0.6);
    }
  }
  .select-box {
    position: absolute;
    top: 9px;
    right: 10px;
    z-index: 9;

    .icon {
      cursor: pointer;
      width: 16px;
      height: 16px;
    }
  }
}
.tooltip-tag {
  max-width: 380px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 5px 0;

  .tag {
    min-width: 80px;

    &.blue {
      background: linear-gradient(90deg, #b8e3ff 0%, #f2faff 100%);
      --el-tag-border-color: #f2faff;
      --el-tag-text-color: #215476;
    }
  }
}
.select-all-box {
  border-radius: 15px;
  border: 1px solid #5695c0;
  background: linear-gradient( 180deg, #EEF9FF 0%, #FCFBFF 100%);
}
.not-select-box {
  border: 1px solid #d0d0d063;
  background: #eee;
  cursor: not-allowed;

  .left-box,
  .collect-box,
  .gengduo,
  .btn,
  .select-box,
  .icon,
  .cur {
    cursor: not-allowed !important;
  }
}
</style>
