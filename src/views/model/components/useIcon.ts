import iconCollect from '@/assets/icon/icon_collect.png'
import iconCollectOn from '@/assets/icon/icon_collect_on.png'
import iconCollect1 from '@/assets/icon/icon_collect_1.png'
import iconCollectOn1 from '@/assets/icon/icon_collect_on_1.png'
import iconSelectTa from '@/assets/icon/icon_select_ta.png'
import iconJinZhi1 from '@/assets/icon/icon_jinzhi_1.png'
import iconJinZhi2 from '@/assets/icon/icon_jinzhi_2.png'

const iconUrl = (icon: string) => {
  switch (icon) {
    case 'icon_collect':
      return iconCollect
    case 'icon_collect_on':
      return iconCollectOn
    case 'icon_collect_1':
      return iconCollect1
    case 'icon_collect_on_1':
      return iconCollectOn1
    case 'icon_select_ta':
      return iconSelectTa
    case 'icon_jinzhi_1':
      return iconJinZhi1
    case 'icon_jinzhi_2':
      return iconJinZhi2
    default:
      return ''
  }
}

export default function useIcon() {
  return {
    iconUrl,
  }
}
