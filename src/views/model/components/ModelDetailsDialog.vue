<template>
  <div class="model-details" @click.stop>
    <el-dialog
      v-model="dialogVisible"
      width="1000px"
      align-center
      :show-close="false"
      style="min-height: 600px; border-radius: 27px; overflow: hidden"
      destroy-on-close
      @close="close"
    >
      <div class="model-head">
        <div class="model-head__close flex-end" @click="close">
          <el-icon size="36"><Close /></el-icon>
        </div>
        <div class="model-head__btn flex-end" v-if="isShow && !isNotLogin">
          <el-button
            v-if="!isBlackModel"
            @click="handleBlackModel(true)"
            type="primary"
            plain
            round
            class="black-btn"
          >
            <!-- <span class="iconfont icon-jinzhi"></span> -->
            <img class="icon" :src="iconUrl('icon_jinzhi_1')" alt="" />
            <span class="btn-text">加入黑名单</span>
          </el-button>
          <el-button v-else @click="handleBlackModel(false)" type="primary" round>
            <!-- <span class="iconfont icon-jinzhi"></span> -->
            <img class="icon" :src="iconUrl('icon_jinzhi_2')" alt="" />
            <span class="btn-text">取消拉黑</span>
          </el-button>
          <el-button v-if="!isBlackModel" @click="handleCollect" type="primary" plain round class="black-btn">
            <img
              class="icon"
              :src="data.collect ? iconUrl('icon_collect_on_1') : iconUrl('icon_collect_1')"
              alt=""
            />
            <span class="btn-text">{{ data.collect ? '取消收藏' : '加入收藏' }}</span>
          </el-button>
          <el-button
            v-if="!isBlackModel"
            @click="selectTa(data.status)"
            style="padding: 10px 8px"
            type="primary"
            round
          >
            <img class="icon" src="@/assets/icon/icon_line_select_ta.png" alt="" />
            <span class="btn-text">选ta拍摄</span>
          </el-button>
        </div>
      </div>
      <div v-loading="loading" v-if="isShow">
        <div class="model-info">
          <div class="model-info__head">
            <div class="head-img">
              <el-avatar
                style="align-items: flex-start"
                :size="100"
                :src="data.modelPic ? $picUrl + data.modelPic + '!3x4' : ''"
              />
            </div>
            <div class="head-name">
              <div class="head-name__top flex-center" style="justify-content: start">
                <span class="name">{{ data.name }}</span>
                <span class="id">ID: {{ data.account }}</span>
              </div>
              <div class="head-name__bottom gap-10">
                <span style="color: #ff6538c8">$29.9</span>
                <NationTag :type="data.nation" isIcon :color="'#6A7484'" />
                <ModelTypeTag :type="data.type" isIcon color="#6A7484" />
              </div>
            </div>
          </div>
        </div>
        <div ref="scrollEl" style="max-height: 600px; overflow: auto; scrollbar-width: none">
          <div class="model-tags" v-if="data.specialtyCategory && data.specialtyCategory.length > 0">
            <div class="model-tags__title">擅长品类</div>
            <div
              class="model-tags__content"
              id="isSpecialtyCategory"
              :style="{
                maxHeight: isShowSpecialtyCategoryAll ? 'none' : '92px',
              }"
            >
              <template v-for="item in data.specialtyCategory" :key="item.id">
                <el-tag
                  class="template-pre"
                  :disable-transitions="true"
                  round
                  :class="{ active: item.highlight }"
                  type="primary"
                >
                  {{ item.name }}
                </el-tag>
              </template>
            </div>
            <div class="model-tags__more">
              <el-button
                link
                v-if="isMoreBtnV1"
                type="primary"
                @click="isShowSpecialtyCategoryAll = !isShowSpecialtyCategoryAll"
              >
                {{ isShowSpecialtyCategoryAll ? '收起' : '展开' }}
                <el-icon>
                  <ArrowDown v-if="!isShowSpecialtyCategoryAll" />
                  <ArrowUp v-else />
                </el-icon>
              </el-button>
            </div>
          </div>
          <div class="model-tags" v-if="data.tags && data.tags.length > 0">
            <div class="model-tags__title">模特标签</div>
            <div
              class="model-tags__content"
              id="isTags"
              :style="{
                maxHeight: isShowTagsAll ? 'none' : '92px',
              }"
            >
              <template v-for="item in data.tags" :key="item.id">
                <el-tag
                  class="template-pre"
                  :disable-transitions="true"
                  :class="{ active: item.highlight }"
                  round
                  type="primary"
                >
                  {{ item.name }}
                </el-tag>
              </template>
            </div>
            <div class="model-tags__more">
              <el-button link v-if="isMoreBtnV2" type="primary" @click="isShowTagsAll = !isShowTagsAll">
                {{ isShowTagsAll ? '收起' : '展开' }}
                <el-icon>
                  <ArrowDown v-if="!isShowTagsAll" />
                  <ArrowUp v-else />
                </el-icon>
              </el-button>
            </div>
          </div>
          <div class="model-box">
            <!-- <div v-if="data.lifePhoto?.length" class="model-box__title">生活照</div>
          <div v-if="data.lifePhoto?.length" class="model-life">
            <ImageViews :url-list="lifePhotoMoreBtn ? lifePhoto : allLifePhoto" />
          </div>
          <div
            class="model-box__more"
            @click="lifePhotoMoreBtn = !lifePhotoMoreBtn"
            v-if="data.lifePhoto && data.lifePhoto.length > 10"
          >
            <el-button round plain>
              <template v-if="lifePhotoMoreBtn">
                更多
                <el-icon size="16"><ArrowDown /></el-icon>
              </template>
              <template v-else>
                收起
                <el-icon size="16"><ArrowUp /></el-icon>
              </template>
            </el-button>
          </div> -->
            <div class="model-box__title" style="margin-top: 10px">视频案例</div>
            <div class="model-video">
              <Empty
                v-if="!videoList || videoList.length === 0"
                image-type="video"
                description="暂无视频案例"
                style="grid-column: 1/-1; justify-self: center; align-self: center"
              />
              <ImageViews showVideo :url-list="videoList" @openVideo="openVideo" />
            </div>
          </div>
        </div>
      </div>
      <div v-else class="flex-column show-tip-box">
        <img src="@/assets/image/home1.png" alt="" />
        <div class="text">{{ showText }}</div>
        <el-button type="default" round @click="toModelPage">去看看其他模特~</el-button>
      </div>
    </el-dialog>
    <DialogVideo v-if="!isNotLogin" ref="dialogVideoRef" :videoSrc="videoSrc" @close="videoSrc = ''" />
  </div>
</template>

<script setup lang="ts">
import NationTag from '@/components/public/tag/NationTag.vue'
import ModelTypeTag from '@/components/public/tag/ModelTypeTag.vue'
import DialogVideo from '@/components/public/video/DialogVideo.vue'
import ImageViews from './ImageViews.vue'
import { ref, nextTick, onUnmounted } from 'vue'
import {
  modelMerchantDetails,
  referenceModelInfo,
  modelCollect,
  blackModel,
  cancelBlackModel,
} from '@/api/model'
import { ElMessage } from 'element-plus'
import type { modelItem, amazonVideoList } from '../type/model'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { MessageBox } from '@/utils/message'
// import { useViewer } from '@/hooks/useViewer'
import useIcon from './useIcon'
const { iconUrl } = useIcon()
// const { showViewer } = useViewer()
import { openLogin } from '@/hooks/useLogin'
import aegis from '@/utils/aegis'
import Aegis from 'aegis-web-sdk'

const router = useRouter()
const route = useRoute()
const store = useUserStore()
const dialogVisible = ref(false)
const loading = ref(false)
const data = ref<modelItem>({
  amazonVideo: [],
  id: undefined,
  name: '',
  platform: '',
  specialtyCategory: [],
  tags: [],
  tiktokVideo: [],
  type: 0,
  account: '',
  status: '',
})
const isShow = ref(false)
const showText = ref('')

const allLifePhoto = ref<string[]>([])
const lifePhoto = ref<string[]>([])

const videoList = ref<amazonVideoList[]>([])
const lifePhotoMoreBtn = ref(true)
const scrollEl = ref<HTMLElement>()

const emits = defineEmits(['collectSuccess', 'blackChange'])

defineExpose({
  open,
  close,
})

const isMoreBtnV1 = ref(false)
const isMoreBtnV2 = ref(false)
const isShowTagsAll = ref(false)
const isShowSpecialtyCategoryAll = ref(false)
const containerObserver = ref<ResizeObserver>()
function checkHeight() {
  nextTick(() => {
    containerObserver.value?.disconnect()
    containerObserver.value = new ResizeObserver(entries => {
      entries.forEach(entry => {
        const id = entry.target.id
        // const height = entry.contentRect.height
        if (id === 'isSpecialtyCategory') {
          isMoreBtnV1.value = entry.target.scrollHeight > 92
        } else if (id === 'isTags') {
          isMoreBtnV2.value = entry.target.scrollHeight > 92
        }
      })
    })

    // 开始观察目标元素
    const containerV1 = document.getElementById('isSpecialtyCategory')
    const containerV2 = document.getElementById('isTags')
    containerV1 && containerObserver.value.observe(containerV1)
    containerV2 && containerObserver.value.observe(containerV2)
  })
  // nextTick(() => {
  //   setTimeout(() => {
  //     const containerV1 = document.getElementById('isSpecialtyCategory')
  //     const containerV2 = document.getElementById('isTags')

  //     if (containerV1) {
  //       isMoreBtnV1.value = containerV1.scrollHeight > 92
  //     }
  //     if (containerV2) {
  //       isMoreBtnV2.value = containerV2.scrollHeight > 92
  //     }
  //   }, 500)
  // })
}

const isBlackModel = ref(false)
function handleBlackModel(isBlack: boolean) {
  if (isBlack) {
    MessageBox(
      `<p>拉黑模特后，系统会为您隐藏该模特，其中包含：</p>
      <ol style="font-size: 14px;padding-left: 16px;color: var(--text-gray-color);">
        <li>模特库无法查看该模特信息</li>
        <li>选择意向模特时搜索不到该模特</li>
        <li>蜗牛客服匹配模特时，不会选中该模特为意向模特</li>
<!--        <li>如果购物车中有订单选择该模特为意向模特，系统会为您取消意向（订单中其他信息不会发生变更）</li>-->
      </ol>
      <p>您在【我的模特-我拉黑的】中可管理已拉黑的模特，取消拉黑后可继续选择该模特为您的意向模特。</p>`,
      {
        showClose: true,
        title: '拉黑模特确认',
        customStyle: {
          '--el-messagebox-width': '520px',
        },
        cancelButtonText: '取消',
        confirmButtonText: '确定',
      }
    )
      .then(() => {
        blackModel(data.value.id).then(res => {
          if (res.code == 200) {
            isBlackModel.value = true
            // getModelDetails(data.value.id)
            ElMessage.success('拉黑成功')
            emits('blackChange')
            let form: any = localStorage.getItem('saveOrderForm_' + store.userInfo.account)
            if (form) {
              form = JSON.parse(form)
              if (form.selModel?.length) {
                form.selModel = form.selModel.filter((item: any) => item.id != data.value.id)
              }
              localStorage.setItem('saveOrderForm_' + store.userInfo.account, JSON.stringify(form))
            }
          }
        })
      })
      .catch(() => {})
  } else {
    MessageBox(`<p>确认取消拉黑？</p>`, {
      showClose: true,
      title: '提示',
      customStyle: {
        '--el-messagebox-width': '420px',
      },
      cancelButtonText: '取消',
      confirmButtonText: '确定',
    })
      .then(() => {
        cancelBlackModel(data.value.id).then(res => {
          if (res.code == 200) {
            isBlackModel.value = false
            getModelDetails(data.value.id)
            ElMessage.success('操作成功')
            emits('blackChange')
          }
        })
      })
      .catch(() => {})
  }
}

let collecting = false
// 收藏
function handleCollect() {
  if (collecting) return
  // let m = data.value.collect ? '取消' : ''
  collecting = true
  modelCollect({ id: data.value.id })
    .then(res => {
      if (res.code == 200) {
        // ElMessage.success('收藏成功')
        data.value.collect = !data.value.collect
        emits('collectSuccess')
      }
    })
    .finally(() => (collecting = false))
}

//xuanta下单
function selectTa(status: any) {
  store.realTimeCheckVip().then(() => {
    if (store.isVip()) {
      if (status != '0') {
        MessageBox(`模特暂无档期，无法接单<br/><span>您可以选择其他模特下单哦~</span>`, {
          confirmButtonText: '好的',
          showCancelButton: false,
          closeOnClickModal: false,
          closeOnPressEscape: false,
        }).then(() => {})
        return
      }
      sessionStorage.setItem('createOrderModelInfo', JSON.stringify(data.value))
      router.push('/order/create')
      return
    }
    ElMessage.warning('请先开通会员！')
    if (store.isOwnerAcc()) {
      router.push('/vip')
    }
  })
}

function getModelDetails(
  id: number | string | undefined,
  keyword?: string,
  specialtyCategory?: string[] | number[]
) {
  loading.value = true
  modelMerchantDetails(id, { keyword, specialtyCategory: specialtyCategory?.join(',') })
    .then(res => {
      data.value = res.data
      if (keyword && keyword != '' && data.value.tags && data.value.tags.length > 0) {
        data.value.tags = data.value.tags.map(tag => ({
          ...tag,
          isTrue: String(tag.name).includes(keyword as string),
        }))
      }
      if (
        specialtyCategory &&
        specialtyCategory.length > 0 &&
        data.value.specialtyCategory &&
        data.value.specialtyCategory.length > 0
      ) {
        data.value.specialtyCategory = data.value.specialtyCategory.map(item => ({
          ...item,
          isTrue: (specialtyCategory as number[]).includes(item.id as number),
        }))
      }
      if (
        keyword &&
        keyword != '' &&
        data.value.specialtyCategory &&
        data.value.specialtyCategory.length > 0
      ) {
        data.value.specialtyCategory = data.value.specialtyCategory.map(item => ({
          ...item,
          isTrue: item.isTrue ? true : String(item.name).includes(keyword as string),
        }))
      }
      showText.value = data.value.collect ? '哎呀，你收藏的TA下线了' : '哎呀，模特下线啦'
      isShow.value = data.value.isShow === 1

      if (data.value.lifePhoto && data.value.lifePhoto.length >= 10) {
        lifePhoto.value = data.value.lifePhoto.slice(0, 10)
      } else {
        lifePhoto.value = data.value.lifePhoto || []
      }
      allLifePhoto.value = data.value.lifePhoto || []
      // videoList.value = [...res.data.amazonVideo, ...res.data.tiktokVideo]
      if (res.data.videoCase && res.data.videoCase.length > 0) {
        videoList.value = res.data.videoCase.map((item: any) => ({
          ...item,
          isTrue: keyword && keyword != '' ? String(item.name).includes(keyword) : false,
        }))
      } else {
        videoList.value = []
      }
      //   amazonVideo.value = res.data.amazonVideo
      //   tiktokVideo.value = res.data.tiktokVideo
    })
    .finally(() => {
      loading.value = false
      checkHeight()
    })
}

function getModelDetails2(
  id: number | string | undefined,
  keyword?: string,
  specialtyCategory?: string[] | number[]
) {
  loading.value = true
  referenceModelInfo(id, { keyword, specialtyCategory: specialtyCategory?.join(',') })
    .then(res => {
      data.value = res.data

      showText.value = '哎呀，模特下线啦'
      isShow.value = data.value.isShow === 1

      if (data.value.lifePhoto && data.value.lifePhoto.length >= 10) {
        lifePhoto.value = data.value.lifePhoto.slice(0, 10)
      } else {
        lifePhoto.value = data.value.lifePhoto || []
      }
      allLifePhoto.value = data.value.lifePhoto || []
      videoList.value = [...res.data.amazonVideo, ...res.data.tiktokVideo]
    })
    .finally(() => {
      loading.value = false
      checkHeight()
    })
}

const isNotLogin = ref(false)
const videoSrc = ref('')
const dialogVideoRef = ref<InstanceType<typeof DialogVideo>>()

function toModelPage() {
  if (router.currentRoute.value.path == '/model/list') {
    router.go(0)
  } else {
    router.push('/model/list')
  }
}

const curKeyword = ref('')
const curSpecialtyCategory = ref<string[] | number[]>([])

// 打开模特案例视频
function openVideo(src: string) {
  if (isNotLogin.value) {
    openLogin()
    try {
      aegis?.report({
        msg: '未登录用户播放案例视频',
        level: Aegis.logType.REPORT,
        trace: 'trace',
      })
    } catch (error) {
      console.log('日志上报失败')
    }
    if (data.value.id) {
      sessionStorage.setItem('openModelDetailDialog', data.value.id + '')
    }
    if (curSpecialtyCategory.value && curSpecialtyCategory.value.length > 0) {
      sessionStorage.setItem('openModelDetailSpecialtyCategory', JSON.stringify(curSpecialtyCategory.value))
    }
    return
  }
  videoSrc.value = src
  dialogVideoRef.value?.open()
  try {
    aegis?.report({
      msg: '登录用户播放案例视频',
      level: Aegis.logType.REPORT,
      trace: 'trace',
    })
  } catch (error) {
    console.log('日志上报失败')
  }
}

function open(
  id: number | string | undefined,
  isBlack?: boolean,
  keyword?: string,
  specialtyCategory?: string[] | number[]
) {
  curKeyword.value = keyword || ''
  curSpecialtyCategory.value = specialtyCategory || []
  if (isNotLogin.value) {
    getModelDetails2(id, keyword, specialtyCategory)
    dialogVisible.value = true
    return
  }
  getModelDetails(id, keyword, specialtyCategory)
  dialogVisible.value = true
  nextTick(() => {
    if (scrollEl.value) {
      scrollEl.value.scrollTo({ top: 0, behavior: 'auto' })
    }
  })
  isBlackModel.value = isBlack || false
}
function close() {
  curKeyword.value = ''
  curSpecialtyCategory.value = []
  isMoreBtnV1.value = false
  isMoreBtnV2.value = false
  isShowTagsAll.value = false
  isShowSpecialtyCategoryAll.value = false
  lifePhotoMoreBtn.value = true
  dialogVisible.value = false
  data.value = {
    amazonVideo: [],
    id: undefined,
    name: '',
    platform: '',
    specialtyCategory: [],
    tags: [],
    tiktokVideo: [],
    type: 0,
    account: '',
    status: '',
  }
  sessionStorage.removeItem('openModelDetailDialog')
  sessionStorage.removeItem('openModelDetailSpecialtyCategory')
}

// init
if (!store.userInfo.account) {
  isNotLogin.value = true
}

// 组件卸载时清理
onUnmounted(() => {
  containerObserver.value?.disconnect()
})
</script>

<style lang="scss" scoped>
.model-details {
  .icon {
    width: 16px;
    height: 16px;
    margin-right: 5px;
  }
  .model-head {
    width: 1000px;
    height: 175px;
    position: absolute;
    left: 0;
    top: 0;
    background: url('@/assets/image/model_detail_bg.png') round;
    &__close {
      margin: 25px 20px 0;
      cursor: pointer;
      color: #fff;
    }
    &__btn {
      position: relative;
      z-index: 999999;
      margin: 40px 25px 0;

      .el-button {
        width: 105px;
        line-height: 16px;
      }
      .btn-text {
        line-height: 16px;
      }
      .black-btn {
        font-size: 14px;
        padding: 10px 8px;
        background: transparent;
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);

        &:hover {
          background-color: #fff3;
        }
      }
      .icon-jinzhi {
        width: 16px;
        height: 16px;
        margin-right: 3px;
      }
    }
  }
  .model-info {
    margin-top: 75px;
    position: relative;
    z-index: 99999;
    &__head {
      display: flex;
      // padding-left: 79px;
      padding-left: 20px;
    }
    .head-img {
      // cursor: pointer;
      border-radius: 50%;
      width: 100px;
      height: 100px;

      .el-avatar {
        border: 4px solid #fff;
      }
    }
    .head-name {
      margin-top: 50px;
      margin-left: 20px;

      &__top {
        .name {
          font-weight: 600;
          font-size: 20px;
          color: #09172f;
          margin-right: 14px;
        }
        .id {
          font-size: 14px;
          color: #9ba6ba;
        }
      }
      &__bottom {
        display: flex;
        align-items: center;
        .type-one {
          margin-left: 18px;
          font-size: 12px;
          color: #fff;
          padding: 0 4px;
          border-radius: 5px;
        }
        // .type-two {
        //     color: #fff;
        //   padding: 0 4px;
        //   background: #d34840;
        //   border-radius: 5px;
        // }
      }
    }
  }
  .model-tags {
    display: flex;
    // margin: 10px 0 0 15px;
    // padding: 0 72px 0 81px;
    margin-top: 10px;
    padding: 0 72px 0 20px;
    &__title {
      margin-right: 20px;
      flex-shrink: 0;
    }
    &__content {
      max-height: 92px;
      overflow: hidden;
      transition: max-height 0.3s ease;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      width: 90%;
    }
    &__more {
      display: flex;
      align-items: end;
    }
    .active {
      background: #81d3f8;
      color: #fff;
    }
  }
  .model-box {
    min-height: 500px;
    // width: 1000px;
    // overflow: auto;
    padding: 0 20px;
    margin-top: 20px;
    &__title {
      font-weight: 600;
      margin-bottom: 18px;
      text-align: center;
      font-size: 16px;
      color: #09172f;
    }
    &__more {
      margin-top: 20px;
      text-align: center;
    }
  }
  .model-box::-webkit-scrollbar {
    display: none; /* 隐藏滚动条（Chrome、Safari） */
  }
  .model-box {
    -ms-overflow-style: none; /* 隐藏滚动条（Internet Explorer 和 Edge） */
    scrollbar-width: none; /* 隐藏滚动条（Firefox） */
  }
  .model-life {
    gap: 22px;
    display: flex;
    flex-wrap: wrap;
  }
  .model-video {
    height: auto;
    gap: 25px;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    // display: flex;
    // flex-wrap: wrap;
  }

  .show-tip-box {
    margin-top: 170px;

    .text {
      font-size: 32px;
      font-weight: bold;
      color: #333;
      margin: 10px 0 25px;
    }

    .el-button {
      --el-button-text-color: var(--el-color-primary);
      --el-button-border-color: var(--el-color-primary);
      width: 200px;
      font-size: 20px;
      height: 50px;
      border-radius: 25px;
    }
  }
}

@media screen and (max-width: 768px) {
  .model-details {
    .model-box {
      padding: 0 10px;
    }

    .model-head {
      .model-head__close {
        width: 90vw;
      }
    }

    .model-info {
      .model-info__head {
        padding-left: 5vw;
      }
    }

    .model-video {
      gap: 20px 18px;
    }

    .model-tags {
      padding: 0 5vw;
    }
  }
}
</style>
