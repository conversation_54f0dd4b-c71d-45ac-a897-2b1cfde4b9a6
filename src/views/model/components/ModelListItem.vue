<template>
  <div class="model-list-box flex-start" ref="modelListBoxRef">
    <div class="flex-start left-box">
      <PercentageImg class="img" width="100px" :src="data?.modelPic" :moreLength="data?.lifePhoto?.length || 0" radius="8px" @more="viewLifePhoto" />
      <div class="info-box">
        <div class="flex-start gap info-head">
          <div class="name one-ell">
            <div>{{ data.name }}</div>
            <div class="acc">{{ data.account ? `(ID:${data.account})` : '' }}</div>
          </div>
          <div class="flex-start gap tag-box">
            <!-- 性别 -->
            <SexTag :type="data.sex" />
            <!-- 年龄层 -->
            <AgeLayerTag :type="data.ageGroup" />
            <!-- 国家 -->
            <NationTag :type="data.nation" />
            <!-- 平台 -->
            <!-- <PlatformTag :type="data.platform" /> -->
            <!-- 模特类型 -->
            <ModelTypeTag :type="data.type" />
          </div>
        </div>
        <div class="flex-start gap" style="align-items: baseline">
          <div class="label-text" style="color: #015478">模特标签</div>
          <div class="flex-start gap tag-list">
            <template v-for="(item, index) in data.tags" :key="item.id">
              <template v-if="index < 9">
                <el-tag v-if="stringLength(item.name) <= 9" class="one-ell tag" type="primary" round>{{ item.name }}</el-tag>
                <el-tooltip v-else placement="top" effect="light" trigger="hover">
                  <el-tag class="one-ell tag cur" type="primary" round>{{ item.name }}</el-tag>
                  <template #content>
                    <el-tag type="primary" round>{{ item.name }}</el-tag>
                  </template>
                </el-tooltip>
              </template>
            </template>
            <template v-if="data.tags.length > 9">
              <el-tooltip placement="top" effect="light" trigger="click">
                <el-tag class="tag cur" type="primary" round>. . .</el-tag>
                <template #content>
                  <div class="tooltip-tag">
                    <template v-for="item in data.tags" :key="item.id">
                      <el-tag type="primary" round>{{ item.name }}</el-tag>
                    </template>
                  </div>
                </template>
              </el-tooltip>
            </template>
          </div>
        </div>
        <div class="flex-start gap" style="align-items: baseline">
          <div class="label-text" style="color: #7b4d12">擅长品类</div>
          <div class="flex-start gap tag-list">
            <template v-for="(item, index) in data.specialtyCategory" :key="item.id">
              <template v-if="index < 4">
                <el-tag v-if="stringLength(item.name) <= 9" class="one-ell tag" type="warning" round>{{ item.name }}</el-tag>
                <el-tooltip v-else placement="top" effect="light" trigger="hover">
                  <el-tag class="one-ell tag cur" type="warning" round>{{ item.name }}</el-tag>
                  <template #content>
                    <el-tag type="warning" round>{{ item.name }}</el-tag>
                  </template>
                </el-tooltip>
              </template>
            </template>
            <template v-if="data.specialtyCategory.length > 4">
              <el-tooltip placement="top" effect="light" trigger="click">
                <el-tag class="tag cur" type="warning" round>. . .</el-tag>
                <template #content>
                  <div class="tooltip-tag">
                    <template v-for="item in data.specialtyCategory" :key="item.dictId">
                      <el-tag type="warning" round>{{ item.name }}</el-tag>
                    </template>
                  </div>
                </template>
              </el-tooltip>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="video-box flex-start">
      <VideoCover
        v-for="item in data.amazonVideo"
        :key="item.picUri"
        platform="0"
        class="img"
        width="100px"
        height="100px"
        suffix="!1x1"
        :src="item.picUri"
        fit="fill"
        @click="openVideo(item)"
      />
      <VideoCover
        v-for="item in data.tiktokVideo"
        :key="item.picUri"
        platform="1"
        class="img"
        width="100px"
        height="100px"
        suffix="!1x1"
        :src="item.picUri"
        fit="fill"
        @click="openVideo(item)"
      />
    </div>
    <div class="flex-column more-box">
      <slot name="morebox" :row="data">
        <div class="flex-start gap-10 action-box">
          <div class="collect-box" v-if="showCollect" @click="handleCollect">
            <i v-if="data.collect" class="iconfont icon-shoucang1"></i>
            <i v-else class="iconfont icon-shoucang"></i>
          </div>
          <div class="btn" @click="selectTa"><span>选Ta</span></div>
        </div>
        <div v-if="isOverflow" class="gengduo" @click="openDetails()">
          ···
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRef, type PropType } from 'vue'
import PercentageImg from '@/components/public/image/PercentageImg.vue'
import VideoCover from '@/components/public/image/VideoCover.vue'
import SexTag from '@/components/public/tag/SexTag.vue'
import AgeLayerTag from '@/components/public/tag/AgeLayerTag.vue'
import NationTag from '@/components/public/tag/NationTag.vue'
import ModelTypeTag from '@/components/public/tag/ModelTypeTag.vue'
// import PlatformTag from '@/components/public/tag/PlatformTag.vue'
import { ElMessage } from 'element-plus'
import { modelCollect } from '@/api/model'
import type { biz, modelItem } from '../type/model'
import { useUserStore } from '@/stores/modules/user'
import { useRouter } from 'vue-router'
// import { ageLayerOptions } from '@/utils/data'
import { http_reg } from '@/utils/RegExp'
import { stringLength } from '@/utils/public'

const emits = defineEmits(['openDetails', 'openVideo', 'openPhoto'])

const router = useRouter()
const store = useUserStore()

const props = defineProps({
  data: {
    type: Object as PropType<modelItem>,
    required: true,
  },
  bizModelType: {
    type: Array<biz>,
    default: () => [],
  },
  bizModelPlatform: {
    type: Array<biz>,
    default: () => [],
  },
  bizNation: {
    type: Array<biz>,
    default: () => [],
  },
  showCollect: {
    type: Boolean,
    default: true,
  },
})

const data = toRef(() => props.data)

const modelListBoxRef = ref()
const isOverflow = ref(false)

function handlePlatform(val: any) {
  let arr = data.value.platform.split(',') || ''
  return arr.includes(val)
}

let collecting = false
// 收藏
function handleCollect() {
  if (collecting) return
  // let m = data.value.collect ? '取消' : ''
  collecting = true
  modelCollect({ id: data.value.id })
    .then(res => {
      if (res.code == 200) {
        // ElMessage.success(m + '收藏成功')
        data.value.collect = !data.value.collect
      }
    })
    .finally(() => (collecting = false))
}

function openDetails() {
  emits('openDetails', data.value)
}
function openVideo(item: { videoUrl: any }) {
  if (!item?.videoUrl) {
    ElMessage.warning('无视频链接！')
    return
  }
  if (!http_reg.test(item.videoUrl)) {
    ElMessage.warning('视频链接格式有误！')
    return
  }
  emits('openVideo', item.videoUrl)
}
function selectTa() {
  store.realTimeCheckVip().then(() => {
    if(store.isVip()) {
      sessionStorage.setItem('createOrderModelInfo', JSON.stringify(data.value))
      router.push('/order/create')
      return
    }
    ElMessage.warning('请先开通会员！')
    if(store.isOwnerAcc()) {
      router.push('/vip')
    }
  })
}
function viewLifePhoto() {
  emits('openPhoto', data.value.lifePhoto)
}

const resize = () => {
  if (
    modelListBoxRef.value.offsetWidth <
    modelListBoxRef.value.children[0].offsetWidth + modelListBoxRef.value.children[1].offsetWidth + 50
  ) {
    isOverflow.value = true
  } else {
    isOverflow.value = false
  }
}

defineExpose({
  resize
})
</script>

<style scoped lang="scss">
.model-list-box {
  gap: 10px;
  padding: 10px;
  margin-top: 18px;
  overflow-x: hidden;
  position: relative;
  border-radius: 10px;
  background-color: var(--bg);
  // box-shadow: var(--el-box-shadow-light);
  box-shadow: 0px 0px 4px var(--el-color-primary-light-7);

  .img {
    flex-shrink: 0;
    margin: 0;
  }

  .left-box {
    align-items: flex-start;
    flex-shrink: 0;
    // flex-grow: 2;
    gap: 12px;
    width: 650px;
  }

  .info-box {
    display: flex;
    flex-direction: column;
    align-content: flex-start;
    gap: 6px;
    flex-shrink: 0;

    .info-head {
      align-items: baseline;
      width: 630px;
    }

    .name {
      display: flex;
      align-items: center;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
      max-width: 40%;

      .acc {
        font-size: 12px;
        font-weight: 300;
        color: var(--text-gray-color);
      }
    }
    .text {
      font-size: 14px;
      color: var(--text-color);
    }
    .label-text {
      font-size: 14px;
      color: var(--text-color);
    }
    .tag-box {
      flex-wrap: wrap;
      min-width: 350px;
      // max-width: 70%;
    }
    .tag-list {
      flex-wrap: wrap;
      width: 450px;
    }
    .tag {
      width: 80px;
      display: block;
      line-height: 24px;
      text-align: center;

      &.cur {
        cursor: pointer;
      }
    }

    .gap {
      gap: 8px;
    }
  }
  .video-box {
    margin: 0 8px -30px;
    gap: 10px;
  }

  .more-box {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 9;
    width: 10px;
    height: 100%;
    padding-left: 18px;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(90deg, transparent, #fff 30%);

    .action-box {
      position: absolute;
      top: 0;
      right: 18px;
    }

    .collect-box {
      cursor: pointer;
      color: #ec808d;
      height: 40px;
      margin-right: -8px;

      .iconfont {
        font-size: 28px;
        line-height: 38px;

        &.icon-shoucang1 {
          font-size: 25px;
        }
      }
    }

    .gengduo {
      cursor: pointer;
      margin-bottom: -30px;
      width: 24px;
      height: 25px;
      line-height: 24px;
      text-align: center;
      margin-left: -32px;
      color: #d7d7d7;
      font-size: 25px;
      background: #fff;
      border-radius: 50%;
      box-shadow: 0px 0px 4px rgb(135 135 135);

      &:hover {
        color: #ec808d;
        box-shadow: 0px 0px 4px #ec808d;
      }
    }

    img {
      cursor: pointer;
      width: 25px;
      height: 25px;
    }

    .btn {
      cursor: pointer;
      color: #ec808d;
      border: 3px solid #ec808d;
      border-radius: 50%;
      font-size: 15px;
      width: 40px;
      height: 39px;
      line-height: 40px;
      text-align: center;
      transform: scale(0.6);
    }
  }
}
.tooltip-tag {
  width: 380px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .tag {
    min-width: 80px;
    // margin: 0 10px 10px 0;

    // &:nth-child(4n) {
    //   margin-right: 0;
    // }
  }
}
</style>
