<template>
  <div class="model-details-box" v-loading="loading">
    <div class="model-bg">
      <div class="model-btn">
        <el-button type="primary" round>收藏模特</el-button>
        <el-button type="primary" round>选ta拍摄</el-button>
      </div>
    </div>
    <!-- <Title>基本资料</Title> -->

    <!-- <div class="info-box flex-between">
      <div class="flex-start gap">
        <el-image class="head-img" :src="data.picUrl" fit="cover" />
        <div>
          <div class="flex-start gap">
            <div class="name">{{ data.name }}</div>
            <template v-for="item in biz_model_platform" :key="item.value">
              <el-tag v-if="item.value && data.platform.includes(item.value)" type="warning" size="small" round>
                {{ item.label }}
              </el-tag>
            </template>
            <template v-for="item in biz_model_type" :key="item.value">
              <el-tag v-if="item.value == data.type" type="warning" size="small" round>
                {{ item.label }}
              </el-tag>
            </template>
          </div>
          <div class="flex-start gap">
            <template v-for="item in ageLayerOptions" :key="item.value">
              <span class="text" v-if="item.value == data.ageGroup">{{ item.label }}</span>
            </template>
            <span class="text">{{ data.sex ? '男性' : '女性' }}</span>
            <span class="text">{{ nation }}</span>
          </div>
        </div>
      </div>
      <div class="flex-center collect-box" @click="handleCollect">
        <el-icon v-if="data.collect" :size="26" color="#ec808d"><StarFilled /></el-icon>
        <el-icon v-else :size="26" color="#ec808d"><Star /></el-icon>
        <span>{{ collectText }}</span>
      </div>
    </div> -->

    <!-- <Title>模特标签</Title> -->

    <!-- <div class="flex-start gap tag-box">
      <template v-for="item in data.tags" :key="item.dictId">
        <el-tag type="danger" round>{{ item.name }}</el-tag>
      </template>
    </div> -->

    <!-- <Title>擅长品类</Title> -->

    <!-- <div class="flex-start gap tag-box">
      <template v-for="item in data.specialtyCategory" :key="item.dictId">
        <el-tag type="danger" round>{{ item.name }}</el-tag>
      </template>
    </div> -->

    <!-- <Title style="margin-bottom: 10px">案例视频</Title> -->

    <div class="flex-start videoList">
      <VideoCover
        v-for="item in amazonVideo"
        platform="0"
        :key="item.id"
        :src="item.picUri"
        :title="item.name"
        suffix="!1x1"
        @click="playVideo(item.videoUrl)"
      />
      <VideoCover
        v-for="item in tiktokVideo"
        platform="1"
        :key="item.id"
        :src="item.picUri"
        :title="item.name"
        suffix="!1x1"
        @click="playVideo(item.videoUrl)"
      />
    </div>
    <DialogVideo ref="dialogCompRef" :videoSrc="videoSrc" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, getCurrentInstance } from 'vue'
import { modelMerchantDetails, modelCollect } from '@/api/model'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { modelItem, amazonVideoList } from '../type/model'
// import { ageLayerOptions } from '@/utils/data'
// import Title from '@/components/public/Title.vue'
import VideoCover from '@/components/public/image/VideoCover.vue'
import DialogVideo from '@/components/public/video/DialogVideo.vue'

const { proxy } = getCurrentInstance() as any

const props = defineProps({
  modelId: {
    type: [String, Number],
  },
})

const emits = defineEmits(['error', 'collectChange'])

const data = ref<modelItem>({
  amazonVideo: [],
  id: undefined,
  name: '',
  platform: '',
  specialtyCategory: [],
  tags: [],
  tiktokVideo: [],
  type: 0,
  account: '',
  status: ''
})
const amazonVideo = ref<amazonVideoList>([])
const tiktokVideo = ref<amazonVideoList>([])
const loading = ref(false)
const dialogCompRef = ref()
const videoSrc = ref('')

// const collectText = computed(() => {
//   return data.value.collect ? '取消收藏' : '收藏模特'
// })

function playVideo(url: string | null) {
  if (!url) return
  dialogCompRef.value.open()
  videoSrc.value = url
  // videoSrc.value = 'https://uqcf789uvlq.feishu.cn/file/QlnVbKaUrooTn3xUvNjcdQGynrg'
}

let collecting = false
// 收藏
function handleCollect() {
  if (collecting) return
  if (!data.value.id) {
    if (props.modelId) {
      ElMessage.warning('正在重新获取模特数据···')
      getModelDetails()
      return
    }
    ElMessage.error('系统错误！请刷新重试')
    emits('error', 'not modelId')
    return
  }
  let m = data.value.collect ? '取消' : ''
  collecting = true
  modelCollect({ id: data.value.id })
    .then(res => {
      if (res.code == 200) {
        ElMessage.success(m + '收藏成功')
        data.value.collect = !data.value.collect
        emits('collectChange', data.value.id, data.value.collect)
      }
    })
    .finally(() => (collecting = false))
}

function getModelDetails() {
  loading.value = true
  modelMerchantDetails(props.modelId)
    .then(res => {
      data.value = res.data
      amazonVideo.value = res.data.amazonVideo
      tiktokVideo.value = res.data.tiktokVideo
    })
    .finally(() => (loading.value = false))
}
if (props.modelId) {
  getModelDetails()
}
</script>

<style scoped lang="scss">
.model-details-box {
  min-height: 200px;
  .model-bg {
    width: 1445px;
    height: 261px;
    position: absolute;
    left: 0;
    top: 0;
    background: url('@/assets/image/model_detail_bg.png') round;
  }
  .model-btn {
    position: absolute;
    right: 55px;
    top: 143px;
  }
  .gap {
    gap: 10px;
  }
  .info-box {
    margin: 10px 0;
    gap: 5px;

    .head-img {
      width: 80px;
      height: 80px;
      flex-shrink: 0;
      border-radius: 50%;
      border: 1px solid var(--border-gray-color);
    }
    .name {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
    }
    .text {
      font-size: 14px;
      color: var(--text-color);
    }

    .collect-box {
      flex-direction: column;
      flex-shrink: 0;
      cursor: pointer;
      background: #cee6a9;
      border-radius: 10px;
      padding: 10px;
      color: #ec808d;
    }
  }
  .tag-box {
    margin: 10px 0;
  }

  .videoList {
    gap: 20px 40px;
    flex-wrap: wrap;
  }
}
</style>
