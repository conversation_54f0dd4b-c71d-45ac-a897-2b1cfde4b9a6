<template>
  <!-- <PublicDialog
    ref="DialogRef"
    width="1100px"
    :title="title"
    :showFooterButton="false"
    custom-close
    align-center
    :title-center="false"
    destroy-on-close
    :close-on-click-modal="false"
  > -->
  <el-drawer v-model="showDrawer" :title="title" size="1110px" class="select-model-drawer" @close="close">
    <div class="select-model-header-tip">
      若您暂无意向模特,此处可不选择模特。(蜗牛海拍将自动匹配合适模特,匹配完成后若您不喜欢该模特,可拒绝并且要求重新匹配)
    </div>
    <div class="model-select-list-box">
      <el-form
        class="model-select-form"
        :model="queryForm"
        ref="queryRef"
        :inline="true"
        label-width="88px"
        @submit.prevent
      >
        <!-- <el-form-item label="">
          <el-select
            placeholder="请选择平台"
            v-model="props.platform"
            :disabled="true"
            suffix-icon=""
            style="width: 180px"
          >
            <el-option
              v-for="item in biz_model_platform"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
            <el-option label="APP/解说" value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-select
            placeholder="请选择国家"
            v-model="props.nation"
            :disabled="true"
            suffix-icon=""
            style="width: 180px"
          >
            <el-option v-for="item in biz_nation" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item> -->

        <!-- <el-form-item label="" v-if="props.modelType == '3'">
          <el-select
            class="custom-radius"
            placeholder="请选择模特类型"
            v-model="queryForm.type"
            style="width: 180px"
            clearable
          >
            <el-option
              v-for="item in biz_model_type"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item> -->
        <el-form-item label="" prop="keyword">
          <el-input
            class="custom-radius"
            v-model="queryForm.keyword"
            clearable
            style="width: 300px"
            prefix-icon="Search"
            placeholder="请输入模特名称/ID/标签"
          ></el-input>
        </el-form-item>
        <!-- <el-form-item label="" prop="modelAccount">
          <el-input
            class="custom-radius"
            v-model.trim="queryForm.modelAccount"
            clearable
            style="width: 180px"
            placeholder="请输入模特ID"
          ></el-input>
        </el-form-item> -->
        <el-form-item label="" prop="specialtyCategory">
          <el-select
            class="custom-radius"
            v-model="queryForm.specialtyCategory"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            placeholder="模特擅长品类"
            style="width: 180px"
          >
            <el-option v-for="item in modelCategoryList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="sex">
          <el-select
            class="custom-radius"
            v-model="queryForm.sex"
            multiple
            clearable
            placeholder="性别"
            style="width: 180px"
          >
            <el-option label="男性" :value="1" />
            <el-option label="女性" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="ageGroup">
          <el-select
            class="custom-radius"
            v-model="queryForm.ageGroup"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            placeholder="年龄层"
            style="width: 180px"
          >
            <el-option
              v-for="item in ageLayerOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="" prop="modelTag">
          <el-select
            class="custom-radius"
            v-model="queryForm.modelTag"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            placeholder="请选择模特标签"
            style="width: 180px"
          >
            <el-option v-for="item in modelTagsList" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item> -->

        <el-form-item style="margin: 0 0 12px 10px">
          <el-button type="primary" icon="Search" native-type="submit" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <div style="position: relative; left: -4px; width: calc(100% + 4px)">
        <div class="tab-head-right-box">
          <span>(以下仅展示</span>
          <template v-for="item in biz_model_platform" :key="item.value">
            <span v-if="item.value == props.platform">"{{ item.label }}"</span>
          </template>
          <template v-for="item in biz_nation" :key="item.value">
            <span v-if="item.value == props.nation">"{{ item.label }}"</span>
          </template>
          <template v-for="item in biz_model_type" :key="item.value">
            <span v-if="item.value == props.modelType">"{{ item.label }}"</span>
          </template>
          <span>数据)</span>
        </div>
        <el-tabs :model-value="curTab" class="tabs" @tab-change="handleTabChange">
          <el-tab-pane label="模特库" :name="1">
            <div class="dialog-content">
              <div
                class="list-box model-select-tooltip-1"
                v-infinite-scroll="handleInfiniteScroll"
                :infinite-scroll-distance="150"
                :infinite-scroll-disabled="scrollDisabled"
              >
                <ModelSelectItem
                  v-for="(item, i) in modelList"
                  :key="item.id"
                  :data="item"
                  :limit="props.limit"
                  :selectModels="selectModels"
                  :item-index="i"
                  :tipsModels="tipsAcc"
                  @openVideo="openVideo"
                  @openPhoto="openPhoto"
                  @changeModel="changeModel"
                  :bizModelType="biz_model_type"
                  :bizModelPlatform="biz_model_platform"
                  :bizNation="biz_nation"
                ></ModelSelectItem>
                <Empty
                  style="margin-top: 5%; width: 100%"
                  v-if="!modelList.length"
                  v-loading="loading"
                  description="暂无数据"
                  :image-size="120"
                />
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="我的收藏" :name="0">
            <div class="dialog-content">
              <div
                class="list-box model-select-tooltip-0"
                v-infinite-scroll="handleInfiniteScroll"
                :infinite-scroll-distance="150"
                :infinite-scroll-disabled="scrollDisabled"
              >
                <ModelSelectItem
                  v-for="(item, i) in modelList"
                  :key="item.id"
                  :data="item"
                  :limit="props.limit"
                  :selectModels="selectModels"
                  :item-index="i"
                  :tipsModels="tipsAcc"
                  @openVideo="openVideo"
                  @openPhoto="openPhoto"
                  @changeModel="changeModel"
                  :bizModelType="biz_model_type"
                  :bizModelPlatform="biz_model_platform"
                  :bizNation="biz_nation"
                ></ModelSelectItem>
                <Empty
                  style="margin-top: 5%; width: 100%"
                  v-if="!modelList.length"
                  v-loading="loading"
                  description="暂无数据"
                  :image-size="120"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      <!-- <div class="pagination-box">
        <el-pagination
          class="custom-pagination-radius"
          background
          size="small"
          @size-change="pageChange({ pageNum: 1, pageSize: $event })"
          @current-change="pageChange({ pageNum: $event, pageSize })"
          :current-page="pageNum"
          :page-size="pageSize"
          :page-sizes="pageSizes"
          layout="total, prev, pager, next, jumper"
          :total="total"
        />
      </div> -->
    </div>

    <div class="cur-select-list">
      <div class="flex-start" style="gap: 5px 16px; align-items: normal" v-if="limit > 1">
        <div style="height: 60px">
          <div>
            已选模特
            <span style="color: #d67c5a">{{ selectModels.length }}</span>
            个：
          </div>
          <div style="color: #b4b4b4; font-size: 12px">
            一个模特对应
            <br />
            一个视频
          </div>
        </div>

        <TransitionGroup name="select-model-list">
          <template v-for="(item, i) in selectModels" :key="item.id">
            <div class="model-info__item" v-if="i < 10">
              <div class="modal-lose flex-around" v-if="item.isNoSlot">失效</div>
              <div>
                <div class="play-modal flex-around">
                  <img
                    style="height: 24px; width: 24px"
                    @click="handleCancelSelect(item)"
                    src="@/assets/icon/icon_delete_fff.png"
                    alt=""
                  />
                </div>
                <el-image class="head-img" :src="$picUrl + item.modelPic + '!3x4'" fit="fill">
                  <template #error>
                    <el-icon size="50"><Avatar /></el-icon>
                  </template>
                </el-image>
                <div class="head-name more-ell" style="margin-top: -5px">{{ item.name }}</div>
              </div>
            </div>
          </template>
          <div class="flex-column" v-if="selectModels.length > 10" key="more">
            <div class="avatar" style="cursor: pointer" @click="viewMore">
              <!-- <el-avatar :size="50" style="background: #5695c0" icon="MoreFilled" /> -->
              <div class="more flex-center"><span style="padding-bottom: 2px">· · ·</span></div>
            </div>
            <span class="name">更多</span>
          </div>
        </TransitionGroup>
      </div>
      <el-button
        v-if="noIntentionModelBtn"
        class="big-btn"
        style="right: 160px"
        plain
        round
        @click="handleNotSelect"
      >
        无意向模特
      </el-button>
      <el-button
        class="big-btn"
        type="primary"
        round
        @click="handleConfirmSelect"
        :loading="confirmSelectLoading"
        :disabled="selectModels.length === 0"
      >
        确认选择
      </el-button>
      <PublicDialog
        ref="ModelsDialogRef"
        width="500px"
        :title="`已选的意向模特(${selectModels.length})`"
        :showFooterButton="false"
        :titleCenter="false"
        custom-close
        align-center
        destroy-on-close
        :close-on-click-modal="true"
      >
        <div class="model-info" style="padding-bottom: 20px">
          <div class="model-info__item" v-for="(item, i) in selectModels" :key="item.id">
            <div class="modal-lose flex-around" v-if="item.isNoSlot">失效</div>
            <div>
              <div class="play-modal flex-around">
                <img
                  style="height: 24px; width: 24px"
                  @click="handleCancelSelect(item)"
                  src="@/assets/icon/icon_delete_fff.png"
                  alt=""
                />
              </div>
              <el-image class="head-img" :src="$picUrl + item.modelPic + '!3x4'" fit="fill">
                <template #error>
                  <el-icon size="50"><Avatar /></el-icon>
                </template>
              </el-image>
              <div class="head-name more-ell" style="margin-top: -5px">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </PublicDialog>
    </div>
  </el-drawer>
  <!-- </PublicDialog> -->
  <ModelLifePhoto ref="dialogPhotoRef" />
  <DialogVideo ref="dialogVideoRef" :videoSrc="videoSrc" @close="videoSrc = ''" />
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, ref, watch } from 'vue'
// import ModelListItem from '@/views/model/components/ModelListItem.vue'
import ModelSelectItem from '@/views/model/components/ModelSelectItem.vue'
import DialogVideo from '@/components/public/video/DialogVideo.vue'
import ModelLifePhoto from '@/views/model/components/ModelLifePhoto.vue'
import {
  modelMerchantList,
  modelMerchantCollectList,
  modelCategorySelect,
  modelCategorySelectRank,
  modelCategoryTagSelect,
  modelIsSlotOrNo,
} from '@/api/model'
import { ageLayerOptions } from '@/utils/data'
import type { modelItem } from '../type/model'
import { ElMessage } from 'element-plus'

const { proxy } = getCurrentInstance() as any

const { biz_nation, biz_model_type, biz_model_platform } = proxy.useDict(
  'biz_nation',
  'biz_model_type',
  'biz_model_platform'
)

const DialogRef = ref()
const showDrawer = ref(false)
const ModelsDialogRef = ref()
const props = defineProps({
  limit: {
    type: Number,
    default: 99,
  },
  title: {
    type: String,
    default: '选择意向模特',
  },
  nation: {
    type: String,
    required: true,
  },
  platform: {
    type: String,
    required: true,
  },
  modelType: {
    type: String,
    required: true,
  },
  noIntentionModelBtn: {
    type: Boolean,
    default: false,
  },
})
const emits = defineEmits(['change', 'success', 'close'])

const selectModels = ref<any[]>([])
const tipsAcc = ref<any[]>([])

type Params = {
  keyword?: string
  modelAccount?: string
  name?: string
  nation?: string
  platform?: string
  type?: string
  sex?: Array<string | number>
  ageGroup?: Array<string | number>
  specialtyCategory?: Array<string | number>
  modelTag?: Array<string | number>
  pageNum?: number
  pageSize?: number
}
const queryForm = ref<Params>({
  keyword: '',
  modelAccount: undefined,
  name: undefined,
  sex: [],
  ageGroup: [],
  specialtyCategory: [],
  modelTag: [],
  type: '',
})
const queryParams = ref<Params>({
  keyword: '',
  modelAccount: undefined,
  name: undefined,
  sex: [],
  ageGroup: [],
  specialtyCategory: [],
  modelTag: [],
  type: '',
})
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(20)
const pageSizes = [10, 20, 50, 100]
const total = ref(0)
const modelList = ref<modelItem[]>([])
const curTab = ref(1)
const curModelId = computed(() => {
  return selectModels.value.map((item: any) => item.id)
})

const modelCategoryList = ref<any[]>([])
const modelTagsList = ref<any[]>([])

const dialogPhotoRef = ref<InstanceType<typeof ModelLifePhoto>>()
const dialogVideoRef = ref<InstanceType<typeof DialogVideo>>()
const videoSrc = ref('')
const confirmSelectLoading = ref<boolean>(false)

const scrollDisabled = computed(() => {
  if (showDrawer.value && pageNum.value * pageSize.value >= total.value) {
    return true
  }
  return false
})

defineExpose({
  open,
  close,
  clearSelectModels,
})

function open(arr: any[], tipAcc?: string, type?: string) {
  if (type) {
    selectModels.value = [] // 取消默认选中原拍摄模特
  } else {
    selectModels.value = JSON.parse(JSON.stringify(arr)) || []
  }
  tipsAcc.value = tipAcc ? [tipAcc] : []
  // DialogRef.value.open()
  showDrawer.value = true
  resetQueryParams()
  pageNum.value = 1
  modelList.value = []
  getModelMerchantList()
}
function close() {
  // DialogRef.value.close()
  modelList.value = []
  resetQueryParams()
  pageNum.value = 1
  total.value = 0
  showDrawer.value = false
  emits('close')
}
//清空已选模特
function clearSelectModels() {
  selectModels.value = []
}

function handleQuery() {
  pageNum.value = 1
  modelList.value = []
  queryParams.value = JSON.parse(JSON.stringify(queryForm.value))
  getModelMerchantList()
}
function resetQuery() {
  resetQueryParams()
  handleQuery()
}
function resetQueryParams() {
  queryForm.value = {
    keyword: '',
    modelAccount: undefined,
    name: undefined,
    sex: [],
    ageGroup: [],
    specialtyCategory: [],
    modelTag: [],
    type: '',
  }
  queryParams.value = {
    keyword: '',
    modelAccount: undefined,
    name: undefined,
    sex: [],
    ageGroup: [],
    specialtyCategory: [],
    modelTag: [],
    type: '',
  }
}
// tab切换
function handleTabChange(tab: number) {
  curTab.value = tab
  modelList.value = []
  pageNum.value = 1
  queryParams.value = JSON.parse(JSON.stringify(queryForm.value))
  const el = document.querySelector('.list-box')
  if (el) {
    el.scrollTo(0, 0)
  }
  getModelMerchantList()
}
// 选择模特
function handleSelect(row: any) {
  row.isSelect = true
  if (props.limit === 1) {
    selectModels.value = [row]
    emits('change', row)
    return
  }
  if (selectModels.value.length >= props.limit) return
  selectModels.value.unshift(row)
  emits('change', row)
}
function handleCancelSelect(row: any) {
  let i = selectModels.value.findIndex((item: any) => item.id == row.id)
  if (i != -1) {
    selectModels.value.splice(i, 1)
  }
  emits('change', row)
}
function viewMore() {
  ModelsDialogRef.value.open()
}

function handleNotSelect() {
  if (confirmSelectLoading.value) return
  confirmSelectLoading.value = true
  emits('success', [])
  // resetQuery()
  setTimeout(() => {
    confirmSelectLoading.value = false
  }, 100)
}
function handleConfirmSelect() {
  if (confirmSelectLoading.value) return
  if (selectModels.value.some(item => item.isNoSlot)) {
    ElMessage.warning('已选择的模特中有暂无档期的模特，请重新选择！')
    return
  }
  confirmSelectLoading.value = true
  modelIsSlotOrNo({
    id: selectModels.value.map(item => item.id),
  })
    .then(res => {
      if (res.code == 200) {
        if (res.data?.length > 0) {
          selectModels.value.forEach(item => {
            if (res.data.includes(item.id)) {
              item.isNoSlot = true
            }
          })
          ElMessage.warning('已选择的模特中有暂无档期的模特，请重新选择！')
          return
        }
        emits('success', selectModels.value)
        // resetQuery()
      }
    })
    .finally(() => {
      setTimeout(() => {
        confirmSelectLoading.value = false
      }, 100)
    })

  // DialogRef.value.close()
}
function handleParams() {
  let params: any = {}
  params.pageNum = pageNum.value
  params.pageSize = pageSize.value
  if (queryParams.value.keyword) {
    params.keyword = queryParams.value.keyword.trim()
  }
  if (queryParams.value.name && typeof queryParams.value.name === 'string') {
    params.name = queryParams.value.name.trim()
  }
  if (queryParams.value.modelAccount) {
    params.modelAccount = queryParams.value.modelAccount
  }
  if (props.nation) {
    params.nation = props.nation
  }
  if (props.platform) {
    params.platform = props.platform
  }
  if (props.modelType) {
    params.type = props.modelType == '3' ? '' : props.modelType
  }
  if (queryParams.value.type) {
    params.type = queryParams.value.type
  }
  if (queryParams.value.sex) {
    params.sex = queryParams.value.sex
  }
  if (queryParams.value.ageGroup) {
    params.ageGroup = queryParams.value.ageGroup
  }
  if (queryParams.value.modelTag) {
    params.modelTag = queryParams.value.modelTag
  }
  if (queryParams.value.specialtyCategory) {
    params.specialtyCategory = queryParams.value.specialtyCategory
  }
  return params
}
// 获取模特列表
function getModelMerchantList() {
  let params = handleParams()
  // console.log(params);
  loading.value = true
  if (curTab.value) {
    modelMerchantList({
      ...params,
      isIndex: 2,
    })
      .then(res => {
        // const tempList = res.data.rows
        // tempList.forEach((item: any) => {
        //   item.amazonVideo && item.amazonVideo.length > 2?
        // })
        let selModelIds = selectModels.value.map(item => item.id)
        modelList.value.push(
          ...res.data.rows.map((item: any) => {
            let i = selModelIds.indexOf(item.id)
            if (i > -1) {
              selectModels.value.splice(i, 1, item)
            }
            return item
          })
        )
        total.value = res.data.total
      })
      .finally(() => (loading.value = false))
  } else {
    modelMerchantCollectList(params)
      .then(res => {
        let selModelIds = selectModels.value.map(item => item.id)
        modelList.value.push(
          ...res.data.rows.map((item: any) => {
            let i = selModelIds.indexOf(item.id)
            if (i > -1) {
              selectModels.value.splice(i, 1, item)
            }
            return item
          })
        )
        total.value = res.data.total
      })
      .finally(() => (loading.value = false))
  }
}
// 分页跳转
function pageChange(page: { pageNum: number; pageSize: number }) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  const el = document.querySelector('.list-box')
  if (el) {
    el.scrollTo(0, 0)
  }
  getModelMerchantList()
}
// 滚动加载
function handleInfiniteScroll() {
  if (loading.value) return
  if (pageNum.value * pageSize.value >= total.value) return
  pageNum.value++
  getModelMerchantList()
}

// 打开模特案例视频
function openVideo(src: string) {
  videoSrc.value = src
  dialogVideoRef.value?.open()
}
// 打开模特生活照
function openPhoto(photos: any[]) {
  dialogPhotoRef.value?.open(photos)
}

//模特选择切换了
function changeModel(id: any, type?: boolean, isOne?: boolean) {
  if (isOne) {
    selectModels.value = selectModels.value.filter(item => item.id == id)
  } else {
    selectModels.value = selectModels.value.filter(item => item.id != id)
  }
  if (type) {
    selectModels.value = selectModels.value.filter(item => item.id != id)
  } else {
    selectModels.value.unshift(modelList.value.find(item => item.id == id))
  }
}

// 模特擅长品类下拉
function getModelCategorySelect() {
  modelCategorySelectRank(1, 1008).then(res => {
    if (res.code == 200) {
      modelCategoryList.value = res.data
    }
  })
}
// 模特标签下拉
function getModelTagsSelect() {
  modelCategoryTagSelect({ categoryId: '1009' }).then(res => {
    if (res.code == 200) {
      modelTagsList.value = res.data
    }
  })
  // modelCategorySelect({ status: 0, categoryId: 1009 }).then(res => {
  //   if (res.code == 200) {
  //     modelTagsList.value = res.data
  //   }
  // })
}

getModelTagsSelect()
getModelCategorySelect()
</script>

<style scoped lang="scss">
.select-model-list-enter-active,
.select-model-list-leave-active {
  transition: all 0.5s ease;
}
.select-model-list-enter-from,
.select-model-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
.select-model-header-tip {
  position: absolute;
  top: 32px;
  left: 150px;
  color: rgba(0, 0, 0, 0.4);
  font-size: 12px;
}
.model-select-list-box {
  position: relative;
  padding-bottom: 100px;

  .model-select-form {
    .el-form-item {
      margin-right: 12px;
      margin-bottom: 12px;
    }

    .el-button {
      border-radius: 8px;
    }

    :deep(.el-select) {
      .el-select__suffix {
        .el-icon {
          color: #777;
        }
      }
    }
    :deep(.el-input) {
      .el-input__prefix {
        color: #777;
      }
    }
  }

  :deep(.el-tabs) {
    .el-tabs__header {
      margin-bottom: 0px;
      padding-left: 6px;

      .el-tabs__nav-wrap {
        &::after {
          height: 0;
        }
      }
      .el-tabs__active-bar {
        bottom: 6px;
      }
    }
  }
  .tab-head-right-box {
    position: absolute;
    right: 0;
    top: 0;
    line-height: 40px;
    font-size: 14px;
    color: #777;
  }

  .pagination-box {
    margin-top: 25px;
  }
}
.cur-select-list {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 11;
  height: 90px;
  width: 100%;
  padding: 5px 20px;
  background: #fff;
  box-sizing: border-box;
  box-shadow: 0px -6px 6px 0px rgba(0, 0, 0, 0.06);

  :deep(.el-image__inner) {
    height: auto;
  }
  .more {
    width: 50px;
    height: 50px;
    background: #5695c0;
    border-radius: 50%;
    color: #fff;
    font-size: 20px;
    font-weight: 800;
  }

  .model-item {
    .name {
      width: 60px;
      text-align: center;
    }

    .avatar {
      position: relative;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      overflow: hidden;

      &:hover {
        .del {
          display: flex;
        }
      }

      .del {
        position: absolute;
        top: 0;
        right: 0;
        display: none;
        background: #00000085;
        width: 100%;
        height: 100%;
        align-items: center;
        justify-content: center;

        .el-icon {
          cursor: pointer;
        }
      }
    }
  }

  .big-btn {
    position: absolute;
    right: 20px;
    top: calc(50% - 20px);
    width: 130px;
    height: 35px;
  }

  .models-dialog {
    min-height: 300px;
    max-height: 600px;
    overflow-y: auto;

    .models-dialog-list {
      flex-wrap: wrap;
      gap: 14px;
    }
  }
}

.dialog-content {
  position: relative;
}
.list-box {
  height: calc(100vh - 260px);
  min-height: 300px;
  overflow-y: auto;
  // min-height: 210px;
  padding: 10px 10px 30px 6px;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  gap: 14px;

  .select-btn-box {
    // position: absolute;
    // right: 0;
    // top: 0;
    // height: 100%;
    // width: 70px;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    // background: linear-gradient(90deg, transparent, #fff 20%);
    .icon {
      width: 18px;
      height: 18px;
      cursor: pointer;
    }

    .select-btn {
      color: #ec808d;
      border: 2px solid #ec808d;
      border-radius: 50%;
      font-size: 13px;
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      margin-right: -10px;

      &.cur {
        cursor: pointer;
      }

      &.sel {
        line-height: 16px;
        padding: 2px;
        box-sizing: border-box;
      }

      &.disabled {
        color: #ec808c8c;
        border: 2px solid #ec808d8c;
        cursor: not-allowed;
      }
    }
  }
}
.list {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--border-gray-color);

  .list-info-item {
    gap: 10px;
    flex-grow: 1;
    flex-shrink: 0;
  }
  .list-item {
    width: 120px;
    flex-shrink: 0;
    text-align: center;
  }
  .el-button {
    width: 62px;
  }
}
.head-img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid var(--border-gray-color);
  background: var(--border-gray-color);
}
.model-info {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  // align-items: center;
  margin-top: 10px;
  justify-content: start;
  max-height: 400px;
  overflow: auto;
  &__item {
    //
    //margin-right: 8px;
    position: relative;
    cursor: pointer;

    .head-img {
      cursor: pointer;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      border: 1px solid var(--border-gray-color);
      background: var(--border-gray-color);
      line-height: 1;
    }
    .modal-lose {
      color: #fff;
      width: 50px;
      height: 50px;
      position: absolute;
      inset: 0;
      border-radius: 50%;
      background-color: #2c2b2b66;
      z-index: 8;
      opacity: 1;
    }
    .play-modal {
      width: 50px;
      height: 50px;
      position: absolute;
      top: 0;
      left: 0;
      border-radius: 50%;
      // width: 100%;
      // height: 100%;
      background-color: #2c2b2b66;
      z-index: 9;
      opacity: 0;
    }
    &:hover {
      .modal-lose {
        opacity: 0;
      }
      .play-modal {
        opacity: 1;
      }
    }
  }
  .model-more {
    cursor: pointer;
    &__icon {
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 800;
      color: #fff;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--el-color-primary);
      font-size: 20px;
    }
    &__text {
      margin-top: -7px;
      // line-height: 1;
      font-size: 12px;
      text-align: center;
      color: var(--el-color-primary);
    }
  }
}
.head-name {
  max-width: 50px;
  margin-top: -10px;
  line-height: 1;
  font-size: 12px;
  color: #000;
  text-align: center;
}
</style>

<style lang="scss">
.select-model-drawer {
  border-radius: 24px 0 0 24px;

  .el-drawer__header {
    margin-bottom: 16px;

    .el-drawer__title {
      font-size: 22px;
      color: #333;
    }
  }
  .el-drawer__body {
    padding: 0 20px;
    overflow: hidden;
  }
}
</style>
