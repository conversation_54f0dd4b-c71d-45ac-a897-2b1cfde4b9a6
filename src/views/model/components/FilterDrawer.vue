<template>
  <el-drawer
    v-model="drawer"
    :with-header="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    direction="ttb"
    size="60%"
    destroy-on-close
    @close="reset"
  >
    <div class="filter-form-box">
      <el-form :model="form" label-width="85px" label-position="top" @click.stop>
        <el-form-item label="平台">
          <el-checkbox-group v-model="form.platform" size="small">
            <el-checkbox-button
              v-for="item in biz_model_platform"
              :key="item.value"
              :value="item.value"
              name="type"
              class="custom-btn-radius"
            >
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="国家">
          <el-checkbox-group v-model="form.nation" size="small">
            <el-checkbox-button
              v-for="item in biz_nation"
              :key="item.value"
              :value="item.value"
              name="type"
              class="custom-btn-radius"
            >
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="类型">
          <el-checkbox-group v-model="form.type" size="small">
            <el-checkbox-button
              v-for="item in biz_model_type"
              :key="item.value"
              :value="item.value"
              name="type"
              class="custom-btn-radius"
            >
              <span class="model-type-tag">
                <span>{{ item.label }}</span>
              </span>
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="性别">
          <el-checkbox-group v-model="form.sex" size="small">
            <el-checkbox-button class="custom-btn-radius" :value="1" :label="1">男性</el-checkbox-button>
            <el-checkbox-button class="custom-btn-radius" :value="0" :label="0">女性</el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="年龄">
          <el-checkbox-group v-model="form.ageGroup" size="small">
            <el-checkbox-button
              v-for="item in ageLayerOptions"
              :key="item.value"
              :value="item.value"
              class="custom-btn-radius"
            >
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="擅长品类">
          <el-checkbox-group v-model="form.specialtyCategory" size="small">
            <el-checkbox-button
              v-for="item in modelCategoryList"
              :key="item.id"
              :value="item.id"
              name="type"
              class="custom-btn-radius max-w"
            >
              {{ item.name }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex-center bottom-box">
      <el-button round plain size="large" @click="reset">重置</el-button>
      <el-button round size="large" type="primary" @click="submit">确定</el-button>
    </div>
    <img class="close-icon" src="@/assets/icon/while-close.png" alt="" @click.stop="close" />
    <div class="filter-form-drawer-modal" @click.stop="close"></div>
  </el-drawer>
</template>

<script setup lang="ts">
import { modelCategorySelectRank } from '@/api/model'
import { getCurrentInstance, ref } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import type { queryParams } from '../type/model'
import { ageLayerOptions, modelPlatformOptions, modelTypeOptions, nationOptions } from '@/utils/data'

defineExpose({
  open,
})
const emits = defineEmits(['submit'])
const store = useUserStore()

const { proxy } = getCurrentInstance() as any

let biz_model_platform = ref(modelPlatformOptions)
let biz_model_type = ref(modelTypeOptions)
let biz_nation = ref(nationOptions)

function getBiz() {
  const biz = proxy.useDict('biz_nation', 'biz_model_type', 'biz_model_platform')
  biz_model_platform = biz.biz_model_platform
  biz_model_type = biz.biz_model_type
  biz_nation = biz.biz_nation
}

const drawer = ref(false)
const form = ref<queryParams>({
  platform: [],
  nation: [],
  type: [],
  sex: [],
  ageGroup: [],
  specialtyCategory: [],
})

const modelCategoryList = ref<any[]>([])

function open(params: queryParams) {
  form.value = params
  drawer.value = true
}
function close() {
  drawer.value = false
}
function submit() {
  emits('submit', form.value)
  close()
}
function reset() {
  form.value = {
    platform: [],
    nation: [],
    type: [],
    sex: [],
    ageGroup: [],
    specialtyCategory: [],
  }
}

// 模特擅长品类
function getModelCategorySelect() {
  modelCategorySelectRank(1, 1008).then(res => {
    if (res.code == 200) {
      modelCategoryList.value = res.data
    }
  })
}

// init
getModelCategorySelect()
if (store.userInfo.account) {
  getBiz()
}
</script>

<style scoped lang="scss">
.filter-form-box {
  padding-bottom: 70px;

  .custom-btn-radius {
    --el-checkbox-button-checked-bg-color: var(--el-color-white);
    --el-checkbox-button-checked-text-color: var(--el-color-primary);

    :deep(.el-checkbox-button__inner) {
      border-radius: 20px;
      font-size: 14px;
    }
    &.is-checked {
      :deep(.el-checkbox-button__inner) {
        background-color: var(--el-color-primary-light-9);
        border-color: var(--el-color-primary);
      }
    }
  }
  .max-w {
    :deep(.el-checkbox-button__inner) {
      max-width: calc(100vw - 32px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.bottom-box {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 3;
  background-color: #fff;
  gap: 20px;
  width: 100%;
  height: 70px;
  box-shadow: 0px -2px 13px 0px rgb(0 0 0 / 10%);

  .el-button {
    width: 150px;

    & + .el-button {
      margin: 0;
    }
  }
}
.close-icon {
  position: fixed;
  left: 50%;
  top: 65%;
  z-index: 99;
  transform: translateX(-50%);
  width: 40px;
  height: 40px;
}
.filter-form-drawer-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 98;
  width: 100vw;
  height: 40%;
}
</style>
