<template>
  <div class="image-list" v-for="(item, i) in props.urlList" :key="i">
    <template v-if="props.showVideo">
      <VideoCover
        class="img-vc-box"
        width="162px"
        height="162px"
        suffix="!1x1"
        :playType="false"
        :src="item.picUri"
        fit="fill"
        @click="openVideo(item)"
      />
    </template>
    <template v-else>
      <el-image
        ref="imgViewRef"
        preview-teleported
        :src="$picUrl + item + '!1x1'"
        fit="fill"
        class="image-item"
        @click="showViewer(props.urlList, { index: i })"
      >
        <template #error>
          <div class="image-error">暂无图片</div>
        </template>
      </el-image>
    </template>
    <div class="one-ell video-name">
      <!-- <el-tag v-if="item.isTrue" class="active-tag" round>{{ item.name }}</el-tag>
      <span v-else>{{ item.name }}</span> -->
      <template v-if="item.highlight">
        <el-tag v-if="stringLength(item.name) <= 18" class="active-tag" round>{{ item.name }}</el-tag>
        <el-tooltip v-else placement="top" effect="light" trigger="hover">
          <el-tag class="active-tag" type="warning" round>{{ item.name }}</el-tag>
          <template #content>
            {{ item.name }}
          </template>
        </el-tooltip>
      </template>
      <template v-else>
        <span v-if="stringLength(item.name) <= 18">{{ item.name }}</span>
        <el-tooltip v-else placement="top" effect="light" trigger="hover">
          <div class="one-ell">{{ item.name }}</div>
          <template #content>
            {{ item.name }}
          </template>
        </el-tooltip>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import VideoCover from '@/components/public/image/VideoCover.vue'
import { http_reg } from '@/utils/RegExp'
import { ElMessage } from 'element-plus'
import { type PropType } from 'vue'
import { useViewer } from '@/hooks/useViewer'
import { stringLength } from '@/utils/public'

const { showViewer } = useViewer()

const props = defineProps({
  urlList: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  type: {
    type: String,
    default: 'amazon',
  },
  isShowIcon: {
    type: Boolean,
    default: false,
  },
  showVideo: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['change', 'openVideo'])

function openVideo(item: any) {
  if (!item?.videoUrl) {
    ElMessage.warning('无视频链接！')
    return
  }
  if (!http_reg.test(item.videoUrl)) {
    ElMessage.warning('视频链接格式有误！')
    return
  }
  emits('openVideo', item.videoUrl)
}
</script>

<style scoped lang="scss">
.image-list {
  height: 168px;
  position: relative;
  //   margin-right: 10px;
  //   margin-bottom: 25px;
  .image-modal {
    position: absolute;
    bottom: 0;
    right: 1px;
    z-index: 9;
  }
}
.image-item {
  border-radius: 11px;
  box-sizing: border-box;
  width: 168px;
  height: 168px;
  cursor: pointer;
}

.image-error {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #333;
}
.video-name {
  text-align: center;
  margin-top: 1px;
  color: #09172f;
  // text-align: center;
  max-width: 162px;
  font-size: 14px;
}
.active-tag {
  background: #81d3f8;
  color: #fff;
  padding: 0 20px;
  max-width: 162px;
  :deep(.el-tag__content) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
  }
}

@media screen and (max-width: 768px) {
  .image-list {
    .img-vc-box {
      :deep(.image-box) {
        width: 40vw !important;
        height: 40vw !important;
      }
      :deep(.vc-title) {
        width: 40vw !important;
      }
    }
  }
  .image-item {
    width: 40vw;
    height: 40vw;
  }
}
</style>
