<template>
  <div class="vc-box" @mouseleave="handleMouseLeave($event)">
    <div class="image-box" @click.stop="openDetails">
      <div
        class="play-modal flex-column"
        @click.stop="openDetails"
        @mouseenter="handleMouseEnter($event)"
        @mouseleave="handleMouseLeave($event)"
      >
        <div class="play-modal__name">{{ data.name }}</div>
        <slot name="play-modal-content">
          <div @click.stop="openDetails" class="play-modal__cancel flex-center">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-gengduo-copy"></use>
            </svg>
            查看案例
          </div>
          <div class="play-modal__select flex-center" v-if="showCollect" @click.stop="handleCollect">
            <img
              class="icon"
              :src="data.collect ? iconUrl('icon_collect_on') : iconUrl('icon_collect')"
              alt=""
            />
            {{ data.collect ? '取消收藏' : '加入收藏' }}
          </div>
          <div class="play-modal__select flex-center" @click.stop="selectTa(data.status)">
            <img class="icon" :src="iconUrl('icon_select_ta')" alt="" />
            选ta拍摄
          </div>
        </slot>
      </div>
      <div
        class="model-info"
        @click.stop="openDetails"
        :style="{ opacity: isShowModelInfo ? 1 : 0 }"
        @mouseenter="handleMouseEnter($event)"
      >
        <div class="icon-item" v-if="data.collect">
          <svg class="svg-icon icon-item-icon" style="fill: #ffffff99" aria-hidden="true">
            <use xlink:href="#icon-tianchongxing-"></use>
          </svg>
        </div>
        <div class="icon-item2" v-if="data.collect">
          <svg class="svg-icon" aria-hidden="true">
            <use xlink:href="#icon-shoucang4"></use>
          </svg>
        </div>
        <div class="info-item">
          <div class="info-item__name one-ell">{{ data.name }}</div>
          <div class="info-item__desc flex-start">
            <!-- <NationTag :type="data.nation" isIcon colorWrite color="#fff" size="0.9vw" />
            <ModelTypeTag :type="data.type" isIcon color="#fff" size="0.9vw" /> -->
            <template v-for="item in nationOptions" :key="item.value">
              <span v-if="data.nation == item.value">
                {{ item.label }}
              </span>
            </template>
            <div class="line-border"></div>
            <template v-for="item in modelTypeOptions" :key="item.value">
              <span v-if="data.type == item.value">
                {{ item.label }}
              </span>
            </template>
            <div class="line-border"></div>
            <span>$29.9</span>
          </div>
        </div>
      </div>
      <el-image
        :src="data.modelPic ? $picUrl + data.modelPic + '!3x4' : ''"
        fit="fill"
        loading="lazy"
        class="cover-img"
        preview-teleported
      >
        <template #error>
          <div class="image-error">
            <el-icon :size="25"><Picture /></el-icon>
          </div>
        </template>
      </el-image>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, toRef, type PropType } from 'vue'
import type { biz, modelItem } from '../type/model'
import { modelCollect } from '@/api/model'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { ElMessage } from 'element-plus'
// import NationTag from '@/components/public/tag/NationTag.vue'
// import ModelTypeTag from '@/components/public/tag/ModelTypeTag.vue'
import { modelTypeOptions, nationOptions } from '@/utils/data'
import useIcon from './useIcon'
import { MessageBox } from '@/utils/message'
import { openLogin } from '@/hooks/useLogin'

const { iconUrl } = useIcon()

const router = useRouter()
const store = useUserStore()

const emits = defineEmits(['openDetails', 'success', 'openWarningDialog'])

const props = defineProps({
  data: {
    type: Object as PropType<modelItem>,
    required: true,
  },
  bizModelType: {
    type: Array<biz>,
    default: () => [],
  },
  bizModelPlatform: {
    type: Array<biz>,
    default: () => [],
  },
  bizNation: {
    type: Array<biz>,
    default: () => [],
  },
  showCollect: {
    type: Boolean,
    default: true,
  },
})

function openDetails() {
  // if (!store.userInfo.account) {
  //   openLogin()
  //   return
  // }
  emits('openDetails', data.value)
}
const data = toRef(() => props.data)

let collecting = false

const isShowModelInfo = ref(true)

function handleMouseEnter(e: Event) {
  // if (e.target instanceof HTMLElement) {
  //   e.target.style.display = 'none';
  // }
  isShowModelInfo.value = false
}
function handleMouseLeave(e: Event) {
  isShowModelInfo.value = true
}

// 收藏
function handleCollect() {
  if (!store.userInfo.account) {
    openLogin()
    return
  }
  if (collecting) return
  // let m = data.value.collect ? '取消' : ''
  collecting = true
  modelCollect({ id: data.value.id })
    .then(res => {
      if (res.code == 200) {
        // ElMessage.success(m + '收藏成功')
        emits('success')
        data.value.collect = !data.value.collect
      }
    })
    .finally(() => (collecting = false))
}

//xuanta下单
function selectTa(status: any) {
  if (!store.userInfo.account) {
    openLogin()
    return
  }
  store.realTimeCheckVip().then(() => {
    if (store.isVip()) {
      //模特暂不支持下单
      if (status != '0') {
        MessageBox(
          `模特暂无档期，无法接单<br/>
  <span>您可以选择其他模特下单哦~</span>`,
          {
            confirmButtonText: '好的',
            showCancelButton: false,
          }
        )
        return
      }
      sessionStorage.setItem('createOrderModelInfo', JSON.stringify(data.value))
      router.push('/order/create')
      return
    }
    ElMessage.warning('请先开通会员！')
    if (store.isOwnerAcc()) {
      router.push('/vip')
    }
  })
}
</script>

<style scoped lang="scss">
.icon {
  width: 18px;
  height: 18px;
  margin-right: 5px;
}
.vc-box {
  position: relative;
  width: calc((100% - 8%) / 5);
  height: auto;
  aspect-ratio: 3 / 4;
  object-fit: cover;
  margin-right: 2%;
  &:nth-child(5n) {
    margin-right: 0;
  }
  // .platform-title {
  //   position: absolute;
  //   top: 2px;
  //   right: 0;
  //   font-size: 12px;
  //   font-weight: 400;
  //   color: rgb(123 123 123 / 70%);
  //   background-color: rgb(255 255 255 / 50%);
  //   padding: 1px 3px;
  //   border-radius: 3px;
  //   height: 16px;
  //   line-height: 16px;
  //   transform: scale(0.8);
  //   // text-shadow: 1px 1px 1px #000c;
  //   z-index: 8;
  // }
  .model-info {
    height: 100%;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 8;
    font-size: 22px;
    color: #fff;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0) 65%,
      rgba(0, 0, 0, 0.1) 80%,
      rgba(0, 0, 0, 0.2) 85%,
      rgba(0, 0, 0, 0.4) 90%,
      rgba(0, 0, 0, 0.6) 95%,
      rgba(0, 0, 0, 0.8) 100%
    );
    .icon-item {
      opacity: 0.6;
      position: absolute;
      top: -1.9px;
      right: 5px;
      &-icon {
        height: 40px;
        width: 40px;
      }
    }
    .icon-item2 {
      opacity: 0.8;
      position: absolute;
      top: -6px;
      right: 14.6px;
      &-icon {
        height: 20px;
        width: 20px;
      }
    }
    .info-item {
      position: absolute;
      bottom: 13px;
      left: 0;
      width: 100%;
      padding: 0 15px;
      box-sizing: border-box;

      &__name {
        font-size: 1.1vw;
        width: 70%;
        // font-size: 21px;
      }
      &__desc {
        gap: 6px;
        flex-shrink: 0;
        font-size: 0.9vw;
        color: #fff;

        .line-border {
          width: 1px;
          height: 1em;
          background-color: #fff;
        }
      }
    }
  }

  .image-box {
    width: 100%;
    height: 100%;
    flex-shrink: 0;
    position: relative;
    cursor: pointer;
    border-radius: 12px;
    overflow: hidden;

    &:hover {
      .play-modal {
        transform: translateY(0);
        // opacity: 1;
      }
    }

    .el-image {
      width: 100%;
      height: 100%;
    }

    .play-modal {
      justify-content: center;
      color: #fff;
      transition: transform 0.5s ease-out;
      transform: translateY(100%);
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0.27) 0%, rgba(0, 0, 0, 0.82) 100%);
      z-index: 9;
      // opacity: 0;
      font-size: 0.9vw;
      // font-size: 18px;
      border-radius: 12px;
      &__name {
        font-size: 1.5vw;
        text-align: center;
        margin-top: 2vh;
      }
      &__cancel {
        margin: 5.3vh 0 1vw 0;
      }
      &__select {
        margin-bottom: 1vw;
      }
    }

    .cover-img {
      width: 100%;
      height: 100%;
    }
    .image-error {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .vc-title {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
  }
}
</style>
