<template>
  <PublicDialog
    ref="dialogPhotoRef"
    width="660px"
    :showFooterButton="false"
    :dialogPadding="false"
    :dialogStyle="{
      padding: '20px 10px 20px 20px',
    }"
    title="模特生活照"
    custom-close
    align-center
    append-to-body
    destroy-on-close
  >
    <div class="flex-between model-life-photo-list">
      <div class="list-item">
        <template v-for="item in lifePhotoListLeft" :key="item.id">
          <img :src="$picUrl + item.picUrl" alt="" />
        </template>
      </div>
      <div class="list-item">
        <template v-for="item in lifePhotoListRight" :key="item.id">
          <img :src="$picUrl + item.picUrl" alt="" />
        </template>
      </div>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

defineExpose({
  open,
})

const dialogPhotoRef = ref()
const lifePhotoListLeft = ref<any[]>([])
const lifePhotoListRight = ref<any[]>([])

function open(photos: any[]) {
  if (!photos.length) return
  lifePhotoListLeft.value = []
  lifePhotoListRight.value = []

  photos.forEach((item, i) => {
    if (i % 2 === 0) {
      let obj = {
        // ...item,
        picUrl: item + (i === 0 ? '!1x1' : '!3x4'),
      }
      lifePhotoListLeft.value.push(obj)
    } else {
      let obj = {
        // ...item,
        picUrl: item + '!3x4',
      }
      lifePhotoListRight.value.push(obj)
    }
  })
  dialogPhotoRef.value.open()
}
</script>

<style scoped lang="scss">
.model-life-photo-list {
  align-items: flex-start;
  min-height: 400px;
  max-height: 700px;
  padding-right: 10px;
  overflow-y: overlay;

  .list-item {
    width: 302px;

    img {
      width: 100%;
    }
  }
}
</style>
