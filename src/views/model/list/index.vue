<template>
  <div class="pages" :class="{ 'is-mobile-page': isMobileDevice() }" ref="ModelPageRef">
    <template v-if="isMobileDevice()">
      <div class="flex-between mobile-filter-search-box">
        <div class="input-box" @click.stop>
          <el-icon color="#aaa" size="16" @click="onQuery"><Search /></el-icon>
          <el-input v-model="queryInput" @keyup.enter="onQuery" placeholder="搜索" clearable />
        </div>
        <el-button icon="Filter" link @click.stop="openFilterDrawer">筛选</el-button>
      </div>

      <div class="mobile-model-list">
        <ModelListMobileItems
          v-for="item in modelList"
          :key="item.id"
          :data="item"
          :bizModelType="biz_model_type"
          :bizModelPlatform="biz_model_platform"
          :bizNation="biz_nation"
          @openDetail="handleOpenDetail"
        />
        <Empty v-if="!total" description="无数据" :image-size="180" style="flex: 1" />
      </div>

      <div class="flex-center mobile-model-list-load-more" ref="LoadMoreRef" v-if="!isNotLogin">
        <div v-if="loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          加载中
        </div>
        <div v-else-if="total && modelList.length >= total">没有更多了</div>
        <div v-else-if="total">加载更多</div>
      </div>

      <FilterDrawer ref="FilterDrawerRef" @submit="onFilterSubmit" />
    </template>

    <div class="filter-search-box" v-if="!isMobileDevice()">
      <div class="input-box" @click.stop>
        <el-icon color="#aaa" size="16" @click="onQuery"><Search /></el-icon>
        <el-input
          v-model="queryInput"
          @keyup.enter="onQuery"
          placeholder="可输入名称或者其他关键字搜索..."
          clearable
        />
      </div>
      <el-form :model="form" label-width="85px" style="max-width: 76%" @click.stop>
        <el-form-item label="平台">
          <template #label>
            <div class="flex-start">
              <span>平台</span>
              <!-- <el-icon @mouseenter="showTooltips($event, 'model-title-platform')"><QuestionFilled /></el-icon> -->
            </div>
          </template>
          <el-checkbox-group v-model="form.platform" @change="onQuery">
            <el-checkbox-button
              v-for="item in biz_model_platform"
              :key="item.value"
              :value="item.value"
              name="type"
              class="custom-radius"
            >
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="国家">
          <el-checkbox-group v-model="form.nation" @change="onQuery">
            <el-checkbox-button
              v-for="item in biz_nation"
              :key="item.value"
              :value="item.value"
              name="type"
              class="custom-radius"
            >
              {{ item.label }}
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="类型">
          <template #label>
            <div class="flex-start">
              <span>类型</span>
              <!-- <el-icon @mouseenter="showTooltips($event, 'model-title-type')"><QuestionFilled /></el-icon> -->
            </div>
          </template>
          <el-checkbox-group v-model="form.type" @change="onQuery">
            <el-checkbox-button
              v-for="item in biz_model_type"
              :key="item.value"
              :value="item.value"
              name="type"
              class="custom-radius"
            >
              <span class="model-type-tag">
                <span>{{ item.label }}</span>
                <el-icon
                  :color="form.type?.includes(item.value) ? '#ffff' : 'rgb(155, 166, 186)'"
                  @mouseenter="handleMouseEnterModelType($event, item.value)"
                >
                  <QuestionFilled />
                </el-icon>
              </span>
            </el-checkbox-button>
          </el-checkbox-group>
        </el-form-item>
        <div class="filter-box" :class="{ maxHeight: isFilterSearchBox }">
          <el-form-item label="性别">
            <el-checkbox-group v-model="form.sex" @change="onQuery">
              <el-checkbox-button class="custom-radius" :value="1" :label="1">男性</el-checkbox-button>
              <el-checkbox-button class="custom-radius" :value="0" :label="0">女性</el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="年龄">
            <el-checkbox-group v-model="form.ageGroup" @change="onQuery">
              <el-checkbox-button
                v-for="item in ageLayerOptions"
                :key="item.value"
                :value="item.value"
                class="custom-radius"
              >
                {{ item.label }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="擅长品类">
            <template #label>
              <div class="flex-start">
                <span>擅长品类</span>
                <el-icon @mouseenter="showTooltips($event, 'model-title-category')">
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <el-checkbox-group v-model="form.specialtyCategory" @change="onQuery">
              <el-checkbox-button
                v-for="item in modelCategoryList"
                :key="item.id"
                :value="item.id"
                name="type"
                class="custom-radius"
              >
                {{ item.name }}
              </el-checkbox-button>
            </el-checkbox-group>
          </el-form-item>
        </div>
      </el-form>
      <div class="flex-start search-btn">
        <el-button class="custom-radius" plain icon="Refresh" @click.stop="reset()">重置</el-button>
        <div class="flex-start filterBtn" @click.stop="isFilterSearchBox = !isFilterSearchBox">
          <el-button link size="large">{{ isFilterSearchBox ? '收起筛选' : '展开筛选' }}</el-button>
          <el-icon class="filterIcon" :class="{ up: isFilterSearchBox }"><ArrowDown /></el-icon>
        </div>
      </div>
    </div>

    <!-- <div class="flex-end" v-loginModal>
      <el-button class="my-collect" type="primary" size="large" text @click="toMyCollect">
        <el-icon size="22"><StarFilled /></el-icon> 我的收藏
      </el-button>
    </div> -->

    <div v-if="!isMobileDevice()" v-loading="loading" class="model-list">
      <ModelListItems
        v-for="item in modelList"
        :key="item.id"
        :data="item"
        @openDetails="openDetails"
        :bizModelType="biz_model_type"
        :bizModelPlatform="biz_model_platform"
        :bizNation="biz_nation"
      />
      <!-- <ModelListItem
        ref="ModelListRef"
        v-for="item in modelList"
        :key="item.id"
        :data="item"
        @openVideo="openVideo"
        @openPhoto="openPhoto"
        @openDetails="openDetails"
        :bizModelType="biz_model_type"
        :bizModelPlatform="biz_model_platform"
        :bizNation="biz_nation"
      /> -->
      <Empty v-if="!total" description="无数据" :image-size="180" style="flex: 1" />
    </div>

    <div class="pagination-box" v-if="!isMobileDevice()">
      <el-pagination
        class="custom-pagination-radius"
        v-if="!isNotLogin"
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      />
    </div>
    <el-backtop style="z-index: 99" :bottom="100" target=".pages">
      <img style="height: 40px; width: 40px" src="@/assets/icon/backtop.png" alt="" />
    </el-backtop>

    <ModelLifePhoto v-if="!isNotLogin" ref="dialogPhotoRef" />
    <DialogVideo v-if="!isNotLogin" ref="dialogVideoRef" :videoSrc="videoSrc" @close="videoSrc = ''" />
    <ModelDetailDialog
      ref="dialogDetailRef"
      @collect-success="handleCollectSuccess"
      @black-change="handleQuery"
    />
  </div>
</template>

<script setup lang="ts">
import { ageLayerOptions, modelPlatformOptions, modelTypeOptions, nationOptions } from '@/utils/data'
import { ref, getCurrentInstance, onMounted, nextTick, watchEffect, onBeforeUnmount, onActivated } from 'vue'
import { modelCategorySelectRank, modelMerchantList, staticModelList } from '@/api/model'
// import ModelListItem from '@/views/model/components/ModelListItem.vue'
import ModelListItems from '@/views/model/components/ModelListItems.vue'
import ModelListMobileItems from '@/views/model/components/ModelListMobileItems.vue'
// import ModelDetails from '@/views/model/components/ModelDetails.vue'
import ModelDetailDialog from '@/views/model/components/ModelDetailsDialog.vue'
import DialogVideo from '@/components/public/video/DialogVideo.vue'
import ModelLifePhoto from '@/views/model/components/ModelLifePhoto.vue'
import FilterDrawer from '@/views/model/components/FilterDrawer.vue'
import type { modelItem, queryParams } from '../type/model'
import { useRoute, useRouter } from 'vue-router'
import { isMobileDevice } from '@/utils/public'
import { useUserStore } from '@/stores/modules/user'
import { useTooltips } from '@/hooks/useTooltips'
import { loginDialogVisible, openLogin, loginSuccessAction } from '@/hooks/useLogin'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'

defineOptions({
  name: 'ModelListPage',
})
const ModelPageRef = ref()

const { handelRenewDialog } = useShowRenewDialog()

const { showTooltips } = useTooltips()

const store = useUserStore()

const { proxy } = getCurrentInstance() as any

let biz_model_platform = ref(modelPlatformOptions)
let biz_model_type = ref(modelTypeOptions)
let biz_nation = ref(nationOptions)

function getBiz() {
  const biz = proxy.useDict('biz_nation', 'biz_model_type', 'biz_model_platform')
  biz_model_platform = biz.biz_model_platform
  biz_model_type = biz.biz_model_type
  biz_nation = biz.biz_nation
}
function handleMouseEnterModelType(e: any, val: any) {
  if (val == '0') {
    return showTooltips(e, 'model-yxz')
  }
  if (val == '1') {
    return showTooltips(e, 'model-sr')
  }
  return ''
}

const router = useRouter()
const route = useRoute()
// console.log(route);

const isNotLogin = ref(false)

const isFilterSearchBox = ref(false)
const queryInput = ref('')
const form = ref<queryParams>({
  platform: [],
  nation: [],
  type: [],
  sex: [],
  ageGroup: [],
  specialtyCategory: [],
})
const cacheParams = ref('')
const CACHE_PARAMS_KEY = 'modelCacheParams'
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(50)
const pageSizes = [10, 20, 50, 100]
const total = ref(0)
const modelList = ref<modelItem[]>([])

watchEffect(() => {
  if (loginDialogVisible.value) {
    if (cacheParams.value) {
      localStorage.setItem(CACHE_PARAMS_KEY, cacheParams.value)
    } else {
      localStorage.removeItem(CACHE_PARAMS_KEY)
    }
  }
})

const modelCategoryList = ref<
  {
    id: number
    name: string
  }[]
>([])

const dialogPhotoRef = ref<InstanceType<typeof ModelLifePhoto>>()
const dialogVideoRef = ref<InstanceType<typeof DialogVideo>>()
const videoSrc = ref('')
const modelId = ref<string | number | undefined>('')
const dialogDetailRef = ref<InstanceType<typeof ModelDetailDialog>>()
const FilterDrawerRef = ref<InstanceType<typeof FilterDrawer>>()
const LoadMoreRef = ref()

function openFilterDrawer() {
  FilterDrawerRef.value?.open(form.value)
}
function onFilterSubmit(params: queryParams) {
  form.value = params
  onQuery()
}

// 模特擅长品类下拉
function getModelCategorySelect() {
  modelCategorySelectRank(1, 1008).then(res => {
    if (res.code == 200) {
      modelCategoryList.value = res.data
    }
  })
}

function reset() {
  form.value = {
    platform: [],
    nation: [],
    type: [],
    sex: [],
    ageGroup: [],
    specialtyCategory: [],
  }
  queryInput.value = ''
  if (isNotLogin.value) {
    cacheParams.value = ''
    localStorage.removeItem(CACHE_PARAMS_KEY)
    getStaticModelList()
    return
  }
  pageNum.value = 1
  handleQuery()
}

function collectChange(id: number, collect: boolean) {
  if (isNotLogin.value) return
  let m = modelList.value.find(item => item.id === id)
  if (m) {
    m.collect = collect
  }
}

// 搜索
function onQuery() {
  if (isNotLogin.value) {
    // 未登录时搜索条件有内容
    if (queryInput.value) {
      if (cacheParams.value) {
        form.value = JSON.parse(cacheParams.value)
      } else {
        form.value = {
          platform: [],
          nation: [],
          type: [],
          sex: [],
          ageGroup: [],
          specialtyCategory: [],
        }
      }
      openLogin()
      return
    }

    let i = 0
    for (const key of Object.keys(form.value) as (keyof queryParams)[]) {
      if (Array.isArray(form.value[key])) {
        i += form.value[key].length
      }
    }
    // 未登录时筛选项只能筛选1个
    if (i > 1) {
      if (cacheParams.value) {
        form.value = JSON.parse(cacheParams.value)
      }
      openLogin()
    } else {
      cacheParams.value = i == 0 ? '' : JSON.stringify(form.value)
      getStaticModelList()
    }
    return
  }
  pageNum.value = 1
  handleQuery()
}
function handleQuery(isPush = false) {
  let params: queryParams = {
    ...form.value,
    keyword: queryInput.value.trim(),
  }
  getModelMerchantList(params, isPush)
}

// 分页跳转
function pageChange(page: { pageNum: number; pageSize: number }) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  document.querySelector('.filter-search-box')?.scrollIntoView({
    behavior: 'smooth',
  })
  handleQuery()
}

// 打开模特详情
function openDetails(data: { id: string | number | undefined }) {
  // if (isNotLogin.value) return
  // console.log(data);
  modelId.value = data.id
  dialogDetailRef.value?.open(data.id, false, curKeyword.value, curSpecialtyCategory.value)
  // dialogCompRef.value.open()
}
function handleCollectSuccess() {
  let i = modelList.value.findIndex(item => item.id == modelId.value)
  if (i > -1) {
    modelList.value[i].collect = !modelList.value[i].collect
  }
}
// 打开模特案例视频
function openVideo(src: string) {
  if (isNotLogin.value) return
  videoSrc.value = src
  dialogVideoRef.value?.open()
}
// 打开模特生活照
function openPhoto(photos: any[]) {
  if (isNotLogin.value) return
  dialogPhotoRef.value?.open(photos)
}
const curKeyword = ref<string | undefined>('')
const curSpecialtyCategory = ref()
// 获取模特列表
function getModelMerchantList(params: queryParams = {}, isPush = false) {
  if (isNotLogin.value) return
  params.pageNum = pageNum.value
  params.pageSize = pageSize.value
  loading.value = true
  modelMerchantList({ ...params, needAllModel: 1 })
    .then(res => {
      if (isPush) {
        modelList.value.push(...res.data.rows)
      } else {
        modelList.value = res.data.rows
      }
      total.value = res.data.total
      handelRenewDialog()
      if (params.keyword) {
        curKeyword.value = params.keyword
      } else {
        curKeyword.value = ''
      }
      if (params.specialtyCategory) {
        curSpecialtyCategory.value = params.specialtyCategory
      } else {
        curSpecialtyCategory.value = []
      }

      setTimeout(() => resizeChange())
    })
    .finally(() => (loading.value = false))
}

function toMyCollect() {
  if (isNotLogin.value) return
  router.push('/model/collect')
}

let isScroll = false
function getStaticModelList() {
  let params: any = {}
  for (const key of Object.keys(form.value) as (keyof queryParams)[]) {
    if (Array.isArray(form.value[key]) && form.value[key].length) {
      params[key] = form.value[key]
    }
  }
  loading.value = true
  staticModelList(params)
    .then(res => {
      curSpecialtyCategory.value = params.specialtyCategory
      modelList.value = res.data
      total.value = res.data?.length
      if (!isScroll) {
        isScroll = true
        nextTick(() => {
          let sTop = 0
          document.querySelector('.pages')?.addEventListener('scroll', (e: any) => {
            // console.log('scroll',e, e.target.scrollTop, e.target.scrollHeight);
            if (
              e.target.scrollTop > sTop &&
              e.target.scrollTop > e.target.scrollHeight - e.target.offsetHeight - 30
            ) {
              openLogin()
            }
            sTop = e.target.scrollTop
          })
        })
      }
    })
    .finally(() => (loading.value = false))
}

function handleOpenDetail() {
  let sc = curSpecialtyCategory.value?.length
    ? JSON.stringify(curSpecialtyCategory.value)
    : JSON.stringify([])
  sessionStorage.setItem('curSpecialtyCategory', sc)
  if (curKeyword.value) sessionStorage.setItem('curKeyword', curKeyword.value || '')
  let scrollTop = ModelPageRef.value?.scrollTop
  if (scrollTop && scrollTop > 0) sessionStorage.setItem('scrollTop-model-list-page', `${scrollTop}`)
}

// init
if (store.userInfo.account) {
  isNotLogin.value = false
  total.value = 0
  getBiz()
  getModelCategorySelect()
  if (store.userInfo.account && route.query && route.query.name) {
    queryInput.value = JSON.parse(route.query.name as string) || ''
  }
  let modelCacheParams = localStorage.getItem(CACHE_PARAMS_KEY)
  if (modelCacheParams) {
    form.value = JSON.parse(modelCacheParams)
    localStorage.removeItem(CACHE_PARAMS_KEY)
  }
  handleQuery()

  let mId = sessionStorage.getItem('openModelDetailDialog')
  curSpecialtyCategory.value = JSON.parse(sessionStorage.getItem('openModelDetailSpecialtyCategory') || '[]')
  if (mId) {
    nextTick(() => {
      openDetails({ id: +mId })
    })
    sessionStorage.removeItem('openModelDetailDialog')
    sessionStorage.removeItem('openModelDetailSpecialtyCategory')
  }
} else {
  isNotLogin.value = true
  getModelCategorySelect()
  getStaticModelList()
  loginSuccessAction.value.eval = `localStorage.removeItem("${CACHE_PARAMS_KEY}")`
}

function resizeChange() {}

let ob: IntersectionObserver
function setIntersectionObserver() {
  if (store.userInfo.account && isMobileDevice()) {
    ob = new IntersectionObserver(
      entries => {
        const entry = entries[0]
        if (entry.isIntersecting && !loading.value && total.value && modelList.value.length < total.value) {
          pageNum.value += 1
          handleQuery(true)
        }
      },
      {
        rootMargin: '0px',
        threshold: 0.75,
      }
    )
    ob.observe(LoadMoreRef.value)
  }
}
onMounted(() => {
  nextTick(() => {
    setIntersectionObserver()
  })
})
onBeforeUnmount(() => {
  if (ob) ob.unobserve(LoadMoreRef.value)
})
onActivated(() => {
  // 调用时机为首次挂载
  // 以及每次从缓存中被重新插入时
  let old_scrollTop = sessionStorage.getItem('scrollTop-model-list-page')
  if (old_scrollTop) {
    setTimeout(() => {
      ModelPageRef.value?.scrollTo({
        top: +old_scrollTop,
        behavior: 'smooth',
      })
    }, 500)
    sessionStorage.removeItem('scrollTop-model-list-page')
  }
})
</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';
.pages {
  padding-left: 28px;
  min-width: 1200px;
  max-height: 100vh;

  &.is-mobile-page {
    @include mediaTo('phone') {
      padding: 0px;
      min-width: 375px;
    }
  }
}

.mobile-filter-search-box {
  padding: 12px 16px;

  :deep(.el-button) {
    & [class*='el-icon'] + span {
      margin-left: 3px;
    }
  }

  .input-box {
    display: flex;
    align-items: center;
    width: 74%;
    background-color: #fff;
    padding: 0px 10px 0px 15px;
    border-radius: 28px;
    border: 0px solid #ccdfff;

    :deep(.el-icon) {
      cursor: pointer;
    }

    :deep(.el-input .el-input__wrapper) {
      box-shadow: none;
    }
  }
}
.mobile-model-list {
  padding: 0 16px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 13px;
}
.mobile-model-list-load-more {
  margin-top: 16px;
}

.my-collect {
  font-size: 17px;
  margin: 0 0 -5px 0;
  padding: 0 4px;
}
.filter-search-box {
  position: relative;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.24) 0%, #ffffff 100%);
  border-radius: 14px;
  margin: 0 0 20px;
  padding: 10px 0 14px;
  border: 1px solid #fff;

  .model-type-tag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .input-box {
    margin: 14px 0 12px 30px;
    display: flex;
    align-items: center;
    width: 400px;
    background-color: #fff;
    padding: 4px 10px 4px 15px;
    border-radius: 28px;
    border: 1px solid #ccdfff;

    :deep(.el-icon) {
      cursor: pointer;
    }

    :deep(.el-input .el-input__wrapper) {
      box-shadow: none;
      // background-color: var(--input-bg);
    }
  }

  .filter-box {
    max-height: 0;
    overflow: hidden;
    transition: 0.5s;

    &.maxHeight {
      max-height: 400px;
    }
  }

  :deep(.el-form) {
    padding: 8px 0 0;
    margin-left: 23px;

    .el-form-item {
      margin-bottom: 10px;
      .el-form-item__label {
        color: #9ba6ba;
        // color: rgba(75, 121, 2, 0.996);
      }
    }
  }
}
.model-list {
  gap: 2vw 0;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 41px;
  background: #fff;
  border-radius: 14px;
}
.search-btn {
  position: absolute;
  bottom: 24px;
  right: 10px;

  .filterBtn {
    cursor: pointer;
    margin-left: 8px;
  }
  .filterIcon {
    transition: 0.5s;
  }
  .up {
    transform: rotateZ(180deg);
  }
}
</style>
