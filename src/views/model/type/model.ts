export type biz = {
  value: string | number | undefined
  label: string
}
export type videoList = {
  id: number
  name: string
  picUrl: string
  videoUrl: string | null
}
export type newVideoList = {
  id: number
  name: string
  picUri: string
  videoUrl: string | null
  isTrue?: boolean
  highlight?: boolean
}
export type amazonVideoList = newVideoList[]
export type tiktokVideo = newVideoList[]
export type specialtyCategory = {
  name: string
  id: string | number
  modelId?: string | number
  categoryId?: string | number
  isTrue?: boolean
  highlight?: boolean
}[]
export type tags = {
  name: string
  id: string | number
  modelId?: string | number
  categoryId?: string | number
  isTrue?: boolean
  highlight?: boolean
}[]
export type modelItem = {
  age?: number
  ageGroup?: number
  amazonVideo: amazonVideoList
  collect?: boolean
  id?: number
  lifePhoto?: string[]
  account: string
  name: string
  nation?: number
  modelPic?: string
  platform: string
  sex?: number
  specialtyCategory: specialtyCategory
  tags: tags
  tiktokVideo: tiktokVideo
  type: number
  isSelect?: boolean
  isNoSlot?: boolean
  status: number | string
  isShow?: number
}
export type queryParams = {
  platform?: Array<string | number>
  nation?: Array<string | number>
  type?: Array<string | number>
  sex?: Array<string | number>
  ageGroup?: Array<string | number>
  specialtyCategory?: Array<string | number>
  keyword?: string
  pageNum?: number
  pageSize?: number
}
