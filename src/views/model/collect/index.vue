<template>
  <div class="pages">
    <div class="flex-start tabs">
      <div class="tab" :class="{ active: curTab == 'collect' }" @click="changeTab('collect')">我收藏的</div>
      <div class="tab" :class="{ active: curTab == 'black' }" @click="changeTab('black')">我拉黑的</div>
    </div>
    <div v-if="curTab == 'collect'">
      <div class="filter-search-box">
        <div class="input-box flex-start">
          <el-icon color="#aaa" size="16" @click="onQuery"><Search /></el-icon>
          <el-input
            v-model="queryInput"
            @keyup.enter="onQuery"
            style="width: 400px"
            placeholder="可输入名称或者其他关键字搜索..."
            clearable
          />
        </div>
      </div>

      <div v-loading="loading" class="model-list radius-1">
        <ModelListItems
          v-for="item in modelList"
          :key="item.id"
          :data="item"
          @openDetails="openDetails"
          :bizModelType="biz_model_type"
          :bizModelPlatform="biz_model_platform"
          :bizNation="biz_nation"
          @success="getCollectModelList"
        />
        <!-- <ModelListItem
          v-for="item in modelList"
          :key="item.id"
          :data="item"
          @openVideo="openVideo"
          @openPhoto="openPhoto"
          @openDetails="openDetails"
          :bizModelType="biz_model_type"
          :bizModelPlatform="biz_model_platform"
          :bizNation="biz_nation"
        > -->
        <!-- <template #morebox="{ row }">
            <div class="flex-column more-box">
              <div class="more-btn" @click="openDetails(row)">了解模特更多信息＞</div>
              <div class="flex-center collect-box" @click="handleCollect(row)">
                <el-icon v-if="row.collect" :size="26" color="#ec808d"><StarFilled /></el-icon>
                <el-icon v-else :size="26" color="#ec808d"><Star /></el-icon>
                <span>取消收藏</span>
              </div>
            </div>
          </template> -->
        <!-- </ModelListItem> -->
        <Empty v-if="!total" style="flex: 1" image-type="collect" description="暂无收藏" :image-size="180" />
      </div>
    </div>

    <template v-if="curTab == 'black'">
      <div v-loading="loading" class="model-list radius-2">
        <ModelListItems
          v-for="item in modelList"
          :key="item.id"
          :data="item"
          :bizModelType="biz_model_type"
          :bizModelPlatform="biz_model_platform"
          :bizNation="biz_nation"
          @openDetails="openDetails2"
        >
          <template #play-modal-content>
            <div
              class="play-modal__black flex-center gap-5"
              style="margin-top: 5.3vh"
              @click.stop="openDetails2(item)"
            >
              <span class="iconfont icon-gengduo-copy"></span>
              查看案例
            </div>
            <div class="play-modal__black flex-center gap-5" @click.stop="handleBlackModel(item.id)">
              <span class="iconfont icon-jinzhi"></span>
              取消拉黑
            </div>
          </template>
        </ModelListItems>
        <Empty v-if="!total" style="flex: 1" image-type="collect" description="暂无拉黑" :image-size="180" />
      </div>
    </template>

    <div class="pagination-box">
      <el-pagination
        class="custom-pagination-radius"
        background
        @size-change="pageChange({ pageNum: 1, pageSize: $event })"
        @current-change="pageChange({ pageNum: $event, pageSize })"
        :current-page="pageNum"
        :page-size="pageSize"
        :page-sizes="pageSizes"
        layout="total, prev, pager, next, jumper"
        :total="total"
      />
    </div>

    <!-- <PublicDialog
      ref="dialogCompRef"
      width="660px"
      :showFooterButton="false"
      custom-close
      align-center
      append-to-body
      destroy-on-close
    >
      <ModelDetails :modelId="modelId" :key="modelId" @error="dialogCompRef.close()" />
    </PublicDialog> -->
    <ModelDetailDialog
      ref="dialogDetailRef"
      @collectSuccess="pageChange({ pageNum, pageSize })"
      @blackChange="pageChange({ pageNum, pageSize })"
    />
    <ModelLifePhoto ref="dialogPhotoRef" />
    <DialogVideo ref="dialogVideoRef" :videoSrc="videoSrc" @close="videoSrc = ''" />
  </div>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance } from 'vue'
import ModelListItem from '@/views/model/components/ModelListItem.vue'
import ModelListItems from '@/views/model/components/ModelListItems.vue'
import ModelDetails from '@/views/model/components/ModelDetails.vue'
import DialogVideo from '@/components/public/video/DialogVideo.vue'
import ModelDetailDialog from '@/views/model/components/ModelDetailsDialog.vue'
import ModelLifePhoto from '@/views/model/components/ModelLifePhoto.vue'
import { modelMerchantCollectList, modelCollect, blackModelList, cancelBlackModel } from '@/api/model'
import { ElMessage } from 'element-plus'
import type { modelItem, queryParams } from '../type/model'
import { MessageBox } from '@/utils/message'

const { proxy } = getCurrentInstance() as any

const { biz_nation, biz_model_type, biz_model_platform } = proxy.useDict(
  'biz_nation',
  'biz_model_type',
  'biz_model_platform'
)

const curTab = ref('collect')

const queryInput = ref('')
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(50)
const pageSizes = [10, 20, 50, 100]
const total = ref(0)
const modelList = ref<any[]>([])

const dialogDetailRef = ref<InstanceType<typeof ModelDetailDialog>>()
const dialogPhotoRef = ref<InstanceType<typeof ModelLifePhoto>>()
const dialogVideoRef = ref<InstanceType<typeof DialogVideo>>()
const videoSrc = ref('')
const dialogCompRef = ref()
const modelId = ref<string | number | undefined>('')

function changeTab(tab: string) {
  curTab.value = tab
  pageNum.value = 1
  queryInput.value = ''
  modelList.value.length = 0
  onQuery()
}

// 搜索
function onQuery() {
  pageNum.value = 1
  if (curTab.value == 'collect') {
    let params: queryParams = {
      keyword: queryInput.value.trim(),
    }
    getCollectModelList(params)
  } else if (curTab.value == 'black') {
    getBlackModelList()
  }
}

// 分页跳转
function pageChange(page: { pageNum: number; pageSize: number }) {
  pageNum.value = page.pageNum
  pageSize.value = page.pageSize
  if (curTab.value == 'collect') {
    let params: queryParams = {
      keyword: queryInput.value,
    }
    getCollectModelList(params)
  } else if (curTab.value == 'black') {
    getBlackModelList()
  }
}
// 获取收藏模特列表
function getCollectModelList(params: queryParams = {}) {
  params.pageNum = pageNum.value
  params.pageSize = pageSize.value
  loading.value = true
  modelMerchantCollectList({ ...params, needAllModel: 1 })
    .then(res => {
      modelList.value = res.data?.rows || []
      total.value = res.data?.total || 0
    })
    .catch(() => {
      total.value = 0
    })
    .finally(() => (loading.value = false))
}
// 打开模特详情
// function openDetails(data: modelItem) {
//   modelId.value = data.id
//   dialogCompRef.value.open()
// }
// 打开模特详情
function openDetails(data: { id: string | number | undefined }) {
  modelId.value = data.id
  dialogDetailRef.value?.open(data.id)
  // dialogCompRef.value.open()
}
function openDetails2(data: { id: string | number | undefined }) {
  modelId.value = data.id
  dialogDetailRef.value?.open(data.id, true)
  // dialogCompRef.value.open()
}
// 打开模特案例视频
function openVideo(src: string) {
  videoSrc.value = src
  dialogVideoRef.value?.open()
}
// 打开模特生活照
function openPhoto(photos: any[]) {
  dialogPhotoRef.value?.open(photos)
}
// 取消收藏模特
function handleCollect(row: modelItem) {
  modelCollect({ id: row.id }).then(res => {
    if (res.code == 200) {
      ElMessage.success('取消收藏成功')
      getCollectModelList()
    }
  })
}

// 获取黑名单模特列表
function getBlackModelList() {
  loading.value = true
  blackModelList({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  })
    .then(res => {
      modelList.value =
        res.data?.rows.map((item: any) => {
          return {
            ...item,
            id: item.modelId,
          }
        }) || []
      total.value = res.data?.total || 0
    })
    .catch(() => {
      total.value = 0
    })
    .finally(() => {
      loading.value = false
    })
}

function handleBlackModel(id: any) {
  MessageBox(`<p>确认取消拉黑？</p>`, {
    showClose: true,
    title: '提示',
    customStyle: {
      '--el-messagebox-width': '420px',
    },
    cancelButtonText: '取消',
    confirmButtonText: '确定',
  })
    .then(() => {
      cancelBlackModel(id).then(res => {
        if (res.code == 200) {
          ElMessage.success('操作成功')
          getBlackModelList()
        }
      })
    })
    .catch(() => {})
}

getCollectModelList()
</script>

<style scoped lang="scss">
@use '@/views/center/tabs.scss';

.tabs {
  padding: 0;
}
.model-list {
  gap: 2vw 0;
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 41px;
  background: #fff;

  &.radius-1 {
    border-radius: 0 0 14px 14px;
    padding-top: 0;
  }
  &.radius-2 {
    border-radius: 0 14px 14px 14px;
  }

  .play-modal__black {
    color: #fff;
    font-size: 0.9vw;
    margin-bottom: 1vw;
  }
}
.filter-search-box {
  position: relative;
  // background: linear-gradient(180deg, rgba(255, 255, 255, 0.24) 0%, #ffffff 100%);
  background: #fff;
  border-radius: 0 14px 0 0;
  border: 1px solid #fff;

  .input-box {
    margin: 19px 0 19px 30px;
    display: flex;
    align-items: center;
    width: 400px;
    background-color: #fff;
    padding: 4px 10px 4px 15px;
    border-radius: 28px;
    border: 1px solid #ccdfff;

    :deep(.el-icon) {
      cursor: pointer;
    }

    :deep(.el-input .el-input__wrapper) {
      box-shadow: none;
      // background-color: var(--input-bg);
    }
  }
}

.more-box {
  .more-btn {
    color: var(--el-color-primary);
    cursor: pointer;
    margin-bottom: 30px;
    font-size: 14px;
  }

  .collect-box {
    flex-direction: column;
    cursor: pointer;
    background: #cee6a9;
    border-radius: 10px;
    padding: 10px;
    color: #ec808d;
    position: relative;
    top: -15px;
  }
}
</style>
