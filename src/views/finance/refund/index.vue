<template>
  <div class="pages">
    <div class="search-box">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
        <el-form-item label="" prop="id">
          <el-input class="custom-radius" v-model="queryParams.val" clearable style="width: 300px" placeholder="请输入对应的值">
            <template #prepend>
              <el-select v-model="queryParams.select" placeholder="请选择" clearable style="width: 120px">
                <el-option
                  v-for="item in searchSelectList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="" prop="refundStatus">
          <el-select
            class="custom-radius"
            v-model="queryParams.refundStatus"
            placeholder="请选择退款状态"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 220px"
          >
            <el-option
              v-for="item in orderStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="refundType">
          <el-select
            class="custom-radius"
            v-model="queryParams.refundType"
            placeholder="请选择退款类型"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 220px"
          >
            <el-option
              v-for="item in refundTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="" style="width: 400px">
          <el-date-picker
            class="custom-radius"
            v-model="queryParams.refundTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="退款开始日期"
            end-placeholder="退款结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button class="custom-radius" type="primary" icon="Search" native-type="submit" @click="onQuery">搜索</el-button>
          <el-button class="custom-radius" icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="w-bg">
      <div class="table-box">
        <el-table
          ref="tableRef"
          class="custom-table-border radius-box"
          :data="tableData"
          style="width: 100%"
          border
          header-cell-class-name="table-head-primary"
          v-loading="tableLoading"
        >
          <template #empty>
            <Empty image-type="order" description="暂无数据" :image-size="120" />
          </template>
          <el-table-column prop="applyTime" label="申请时间" width="170" align="center"></el-table-column>
          <el-table-column prop="orderNum" label="单号" minWidth="270">
            <template v-slot="{ row }">
              <div>
                <div>订单号：</div>
                <div>{{ row.orderNum }}</div>
                <div>退款审批号：</div>
                <div>{{ row.refundNum }}</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="msg" label="订单信息" minWidth="250">
            <template v-slot="{ row }">
              <div>
                <div>编码：{{ row.videoCode }}</div>
                <div>中文名：{{ row.productChinese }}</div>
                <div>英文名：{{ row.productEnglish }}</div>
                <div>
                  拍摄模特：{{
                    row.shootModel?.account ? row.shootModel.name + `（ID${row.shootModel.account}）` : '-'
                  }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="订单金额" width="120" align="center" />
          <el-table-column prop="refundAmount" label="退款金额" width="120" align="center" />
          <el-table-column prop="refundType" label="退款类型" width="150" align="center">
            <template v-slot="{ row }">
              {{ refundTypeList.find(item => item.value == row.refundType)?.label || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="refundInfo" label="退款信息" minWidth="250">
            <template v-slot="{ row }">
              <div>
                <div>发起方：{{ row.initiatorSource == 1 ? '商家' : '平台' }}</div>
                <div>发起人：{{ row.initiatorName }}</div>
                <!-- <div>退款原因：{{ row.refundCause }}</div> -->
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="refundStatus" label="退款状态" minWidth="170" align="center">
            <template v-slot="{ row }">
              <div>{{ REFUND_STATUS[row.refundStatus] || '' }}</div>
              <div>{{ row.operateTime }}</div>
              <!-- <div v-if="row.refundStatus === REFUND_STATUS['已拒绝']">拒绝理由：{{ row.rejectCause }}</div> -->
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" :align="'center'" width="120">
            <template v-slot="{ row }">
              <template v-if="row.refundStatus === REFUND_STATUS['退款待审核'] && row.initiatorSource == 1">
                <el-button
                  v-if="store.isOwnerAcc() || store.userInfo.account == row.createOrderUserAccount"
                  link
                  type="primary"
                  @click="handleCancel(row)"
                >
                  取消退款
                </el-button>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="table-page-box">
        <el-pagination
          class="custom-pagination-radius"
          background
          @size-change="pageSizeChange"
          @current-change="curPageChange"
          :current-page="pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total,prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { orderRefundList } from '@/api/order'
import useOrderApi from '@/hooks/useOrderApi'
import { REFUND_STATUS } from '@/utils/order'
import type { RefundQueryParams, OrderRefundList, SearchSelect } from '@/views/order/type'
import { useUserStore } from '@/stores/modules/user'

const store = useUserStore()

const { handleCancelRefund } = useOrderApi()

const searchSelectList: {
  label: string
  value: SearchSelect
}[] = [
  { label: '订单号', value: 'orderNum' },
  { label: '退款审批号', value: 'refundNum' },
  { label: '视频编码', value: 'videoCode' },
  { label: '产品名称', value: 'productName' },
  { label: '拍摄模特', value: 'shootModelName' },
  { label: '模特ID', value: 'modelAccount' },
]
const orderStatusList = ref<
  {
    label: string
    value: number
  }[]
>([])
const refundTypeList = ref<
  {
    label: string
    value: string
  }[]
>([
  { label: '补偿订单', value: '1' },
  { label: '取消订单', value: '2' },
  { label: '取消选配', value: '3' },
])

function init() {
  Object.entries(REFUND_STATUS).forEach(([key, value]) => {
    if (typeof value === 'number' && value !== REFUND_STATUS['退款中']) {
      orderStatusList.value.push({
        label: key,
        value,
      })
    }
  })
  handleQuery()
}

const tableData = ref<OrderRefundList>([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)

const queryParams = ref<RefundQueryParams>({
  val: '',
  select: 'refundNum',
  refundStatus: [],
  refundType: '',
  refundTime: [],
  timeBegin: '',
  timeEnd: '',
})

function resetQuery() {
  queryParams.value = {
    val: '',
    select: 'refundNum',
    refundStatus: [],
    refundType: '',
    refundTime: [],
    timeBegin: '',
    timeEnd: '',
  }
  handleQuery()
}
function handleParams() {
  let { val, select, refundTime, ...params } = queryParams.value
  if (select) {
    params[select] = val ? val.trim() : ''
  }
  if (refundTime && refundTime.length === 2) {
    params.timeBegin = refundTime[0]
    params.timeEnd = refundTime[1]
  }
  return params
}
function onQuery() {
  pageNum.value = 1
  handleQuery(handleParams())
}
function handleQuery(params: any = {}) {
  tableLoading.value = true
  params.pageNum = pageNum.value
  params.pageSize = pageSize.value
  orderRefundList(params)
    .then(res => {
      if (res.data) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => (tableLoading.value = false))
}
// 分页
function pageSizeChange(val: number) {
  pageNum.value = 1
  pageSize.value = val
  handleQuery(handleParams())
}
function curPageChange(val: number) {
  pageNum.value = val
  handleQuery(handleParams())
}
// 取消退款
function handleCancel(row: any) {
  // console.log(row)
  handleCancelRefund([row.id], () => {
    handleQuery(handleParams())
  })
}

init()
</script>

<style scoped lang="scss">
:deep(.el-table) {
  .table-head-primary.el-table__cell {
    padding: 13px 0;
    text-align: center;
    color: var(--text-color);
    background-color: var(--table-head-bg);
  }
}

.search-box {
  padding-bottom: 3px !important;
  margin-bottom: 20px;
}
</style>
