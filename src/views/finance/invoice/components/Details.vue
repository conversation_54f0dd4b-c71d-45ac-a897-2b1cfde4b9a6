<template>
  <el-dialog
    v-model="dialogVisible"
    width="900px"
    style="border-radius: var(--dialog-radius);"
    align-center
    :show-close="false"
    destroy-on-close
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <template #header="{ titleId, titleClass, close }">
      <div class="flex-between dialog-header">
        <h4 v-if="dialogType === 'invoice'" :id="titleId" :class="titleClass">开票详情</h4>
        <h4 v-if="dialogType === 'order'" :id="titleId" :class="titleClass">订单详情</h4>
        <div class="close-round-btn" @click="close"></div>
      </div>
      <div style="height: 40px"></div>
    </template>
    <div class="invoice-details" v-if="dialogType === 'order'">
      <div class="title">订单号：{{ detailsData.orderNum }}</div>
      <el-row>
        <el-col :span="12">
          <div class="flex-start">
            <div class="label">支付时间</div>
            <div class="content">{{ detailsData.payTime }}</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="flex-start">
            <div class="label">订单类型</div>
            <template v-for="item in typeList" :key="item.value">
              <div class="content" v-if="item.value === detailsData.orderType">{{ item.label }}</div>
            </template>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="flex-start">
            <div class="label">支付方式</div>
            <div class="content">{{ handlePayType(detailsData.payType) }}</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="flex-start">
            <div class="label">开票金额</div>
            <div class="content">{{ detailsData.invoiceAmount }} CNY</div>
          </div>
        </el-col>
      </el-row>
      <el-row v-if="detailsData.orderType === 1">
        <el-col :span="24">
          <div class="flex-start">
            <div class="label">会员套餐</div>
            <template v-for="item in vipSetMealList" :key="item.type">
              <div class="content" v-if="item.type == detailsData.packageType">{{ item.label }}</div>
            </template>
          </div>
        </el-col>
      </el-row>
      <div v-if="detailsData.orderVideoSimpleVOS?.length" class="label-title">关联视频订单</div>
      <div v-if="detailsData.orderVideoSimpleVOS?.length">
        <div class="order-info-item" v-for="(item, i) in detailsData.orderVideoSimpleVOS" :key="i">
          <div class="index">{{ i + 1 }}</div>
          <div>视频编码：{{ item.videoCode }}</div>
          <div>产品名称：{{ item.productChinese }}</div>
          <div>英文名称：{{ item.productEnglish }}</div>
          <div class="one-ell productLink">
            产品链接：
            <el-link
              v-if="item?.productLink && http_reg.test(item.productLink)"
              :underline="false"
              target="_blank"
              type="primary"
              :href="item.productLink"
            >
              {{ item.productLink }}
            </el-link>
            <span v-else>{{ item.productLink }}</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 开票详情 -->
    <div class="invoice-details" v-if="dialogType === 'invoice'">
      <el-row>
        <el-col :span="8">
          <div class="flex-start">
            <div class="label">开票金额</div>
            <div class="content">{{ detailsData.invoiceAmount }} CNY</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex-start">
            <div class="label">订单类型</div>
            <template v-for="item in typeList" :key="item.value">
              <div class="content" v-if="item.value === detailsData.orderType">{{ item.label }}</div>
            </template>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex-start">
            <div class="label">发票类型</div>
            <template v-for="item in invoiceTypeList" :key="item.value">
              <div class="content" v-if="item.value === detailsData.invoiceType">{{ item.label }}</div>
            </template>
          </div>
        </el-col>
      </el-row>
      <el-row v-if="detailsData.invoiceType === 1">
        <el-col :span="8">
          <div class="flex-start">
            <div class="label">发票抬头</div>
            <div class="content">{{ detailsData.title }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex-start">
            <div class="label">公司税号</div>
            <div class="content">{{ detailsData.dutyParagraph }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex-start">
            <div class="label">提交信息</div>
            <div class="content">
              {{ detailsData.submitter?.name || detailsData.submitter?.nickName }}
              {{ detailsData.submitTime ? `(${detailsData.submitTime})` : '' }}
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row v-if="detailsData.invoiceType === 2">
        <el-col :span="8">
          <div class="flex-start">
            <div class="label">公司名称</div>
            <div class="content">{{ detailsData.companyName }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex-start">
            <div class="label">联系电话</div>
            <div class="content">{{ detailsData.companyPhone || '无' }}</div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="flex-start">
            <div class="label">提交信息</div>
            <div class="content">
              {{ detailsData.submitter?.name || detailsData.submitter?.nickName }}
              {{ detailsData.submitTime ? `(${detailsData.submitTime})` : '' }}
            </div>
          </div>
        </el-col>
      </el-row>
      <template v-if="detailsData.invoiceType === 2">
        <div class="flex-start">
          <div class="label">联系人</div>
          <div class="content">{{ detailsData.companyContact }}</div>
        </div>
        <div class="flex-start">
          <div class="label">公司地址</div>
          <div class="content">{{ detailsData.companyAddress }}</div>
        </div>
        <div class="flex-start">
          <div class="label">附件上传</div>
          <div class="content">
            <div class="flex-start gap-10 file-upload-box" v-if="detailsData.attachmentObjectKey">
              <div class="one-ell">{{ attachmentsName }}</div>
              <el-button link type="success" @click="handleDownload">下载附件</el-button>
            </div>
          </div>
        </div>
        <div class="flex-start">
          <div class="label">发票内容</div>
          <div class="content">现代服务推广费</div>
        </div>
      </template>
      <el-row>
        <el-col :span="24">
          <div class="flex-start">
            <div class="label">发票备注</div>
            <div class="content">{{ detailsData.invoiceRemark }}</div>
          </div>
        </el-col>
      </el-row>

      <div class="label-title">开票相关订单信息</div>
      <el-collapse v-model="activeCollapse" v-if="detailsData.orderInvoiceVideoCompanyVOS?.length">
        <el-collapse-item
          v-for="(item, i) in detailsData.orderInvoiceVideoCompanyVOS"
          :key="i"
          :name="i"
          :disabled="detailsData.orderType != 0"
        >
          <template #icon="{ isActive }">
            <div class="flex-between" style="width: 100%; padding: 0 10px">
              <div class="collapse-title" v-if="detailsData.orderType == 0">
                <div @click.stop style="cursor: text; user-select: text">订单号：{{ item.orderNum }}</div>
                <div class="flex-start gap-5">
                  {{ isActive ? '收起包含视频订单' : '展开包含视频订单' }}
                  <el-icon :class="isActive ? 'icon-arrow-up' : 'icon-arrow-right'"><ArrowUpBold /></el-icon>
                </div>
              </div>
              <div v-else style="color: var(--text-color)">订单号：{{ item.orderNum }}</div>
              <div style="color: var(--text-red-color); font-size: 15px">
                {{ item.invoiceAmount }}
                <span style="font-size: 12px">CNY</span>
              </div>
            </div>
          </template>
          <div class="collapse-content">{{ item.videoCodes?.join('、') }}</div>
        </el-collapse-item>
      </el-collapse>
      <div class="flex-center" v-else>
        <Empty image-type="order" description="暂无开票相关订单信息" :image-size="120" />
      </div>
    </div>
    <template #footer>
      <div class="flex-end">
        <el-button @click="close">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { payType } from '@/utils/order'
import { vipSetMealList } from '@/views/center/data'
import { invoiceTypeList, typeList } from '@/views/finance/invoice/data'
import { getInvoiceDetail, getInvoiceOrderDetail } from '@/api/invoice'
import { downUrlFile } from '@/utils/download'
import { http_reg } from '@/utils/RegExp'
import { computed, ref } from 'vue'

defineExpose({
  open,
  close,
})

const dialogVisible = ref(false)
const dialogType = ref('order')
const orderType = ref(0)
const detailsData = ref<any>({})
const activeCollapse = ref<any[]>([])
const detailId = ref('')
const loading = ref(false)

const attachmentsName = computed(() => {
  if (detailsData.value.attachmentObjectKey) {
    return detailsData.value.attachmentObjectKey.substring(
      detailsData.value.attachmentObjectKey.lastIndexOf('/') + 1
    )
  }
  return ''
})

function open(id: any, type: string, order_type?: number) {
  detailId.value = id
  dialogType.value = type
  if (order_type || order_type == 0) {
    orderType.value = order_type
  }
  getDetails()
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}

function handleClose() {
  detailsData.value = {}
  activeCollapse.value = []
  detailId.value = ''
}

async function getDetails() {
  loading.value = true
  let res: any
  if (dialogType.value === 'invoice') {
    res = await getInvoiceDetail({
      invoiceId: detailId.value,
    })
  } else if (dialogType.value === 'order') {
    res = await getInvoiceOrderDetail({
      orderId: detailId.value,
      orderType: orderType.value,
    })
  }
  if (res?.code === 200) {
    detailsData.value = res.data
  }
  loading.value = false
}

function handlePayType(val: number) {
  let str = payType(val) as string
  return str?.replace(/支付$/, '') || ''
}

function handleDownload() {
  if (!detailsData.value.attachmentObjectKey) return
  downUrlFile(detailsData.value.attachmentObjectKey, attachmentsName.value)
}
</script>

<style scoped lang="scss">
.dialog-header {
  border-bottom: 1px solid #e4e8eb;
  width: 100%;
  height: 50px;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 20px;
  box-sizing: border-box;
  background: #f6f6f6;
  border-radius: var(--dialog-radius) var(--dialog-radius) 0 0;
  --el-dialog-title-font-size: 16px;

  h4 {
    margin: 0;
  }
  .close-round-btn {
    top: 16px;
  }
}
.invoice-details {
  min-height: 500px;
  max-height: 600px;
  overflow-y: auto;
  padding-left: 20px;

  .title {
    font-size: 18px;
    color: var(--text-color);
    margin-bottom: 15px;
  }

  .el-row {
    margin-bottom: 15px;
  }
  .label {
    flex-shrink: 0;
    width: 66px;
    font-size: 14px;
    font-weight: 400;
    color: var(--text-gray-color);
  }
  .label-title {
    font-size: 14px;
    font-weight: 400;
    color: var(--text-gray-color);
    margin: 0 0 10px;
  }
  .content {
    font-size: 14px;
    // flex-shrink: 0;
    width: calc(100% - 95px);
    word-break: break-all;
    color: var(--text-color);
  }
  .file-upload-box {
    width: 468px;
  }

  .order-info-item {
    position: relative;
    border: 1px solid #efefef;
    background-color: #fbfbfb;
    padding: 10px;
    margin-bottom: 10px;

    div {
      padding-left: 40px;
    }

    .index {
      width: 20px;
      height: 20px;
      line-height: 20px;
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      border-radius: 50%;
      border: 1px solid #e4e8eb;
      padding: 0;
      position: absolute;
      top: 5px;
      left: 5px;
    }

    .productLink {
      position: relative;
      padding-right: 50px;
      width: fit-content;
      max-width: 90%;

      :deep(.el-link) {
        display: contents;

        .el-link__inner {
          display: inline;
        }
      }
    }
  }

  .collapse-title {
    line-height: 22px;

    .el-icon {
      transition: all 0.3s;
    }

    .icon-arrow-up {
      transform: translateY(-1px) rotateZ(0deg);
    }
    .icon-arrow-right {
      transform: translateY(-1px) rotateZ(90deg);
    }
  }
  .collapse-content {
    border-left: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
    padding: 15px;
  }
  :deep(.el-collapse) {
    border-top: none;

    .el-collapse-item {
      --el-collapse-header-height: 55px;

      &.is-disabled {
        .el-collapse-item__header {
          cursor: auto;
        }
      }

      .el-collapse-item__header {
        background-color: #f8f8f9;
        border-left: 1px solid #ebeef5;
        border-right: 1px solid #ebeef5;
      }
      &__content {
        padding: 0;
      }
    }
  }
}
</style>
