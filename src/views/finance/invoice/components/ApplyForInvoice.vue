<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    style="border-radius: var(--dialog-radius)"
    align-center
    :show-close="false"
    destroy-on-close
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <template #header="{ titleId, titleClass, close }">
      <div class="flex-between dialog-header">
        <h4 v-if="title" :id="titleId" :class="titleClass">{{ title }}</h4>
        <div class="close-round-btn" @click="close"></div>
      </div>
      <div style="height: 40px"></div>
    </template>
    <div class="invoice-container">
      <el-form ref="formRef" :model="form" :rules="rules" :disabled="submitLoading" label-width="100px">
        <Title>发票类型</Title>
        <el-form-item label="发票类型：" prop="invoiceType">
          <el-radio-group v-model="form.invoiceType" v-if="dialogType == 0">
            <el-radio-button :value="1">增值税普通发票</el-radio-button>
            <el-radio-button :value="2">形式发票</el-radio-button>
          </el-radio-group>
          <el-radio-button
            v-if="dialogType == 1 && form.invoiceType == 1"
            v-model="form.invoiceType"
            :value="1"
          >
            增值税普通发票
          </el-radio-button>
          <el-radio-button
            v-if="dialogType == 1 && form.invoiceType == 2"
            v-model="form.invoiceType"
            :value="2"
          >
            形式发票
          </el-radio-button>
        </el-form-item>
        <Title>开票信息</Title>
        <template v-if="form.invoiceType == 1">
          <el-form-item label="抬头类型：" prop="titleType">
            <!-- <el-radio-button v-model="form.titleType":value="1">企业单位</el-radio-button> -->
            <strong>企业单位</strong>
          </el-form-item>
          <el-form-item label="发票抬头：" prop="title">
            <el-input v-model="form.title" placeholder="填写发票抬头" maxlength="100" />
          </el-form-item>
          <el-form-item label="企业税号：" prop="dutyParagraph">
            <el-input v-model="form.dutyParagraph" placeholder="填写税号" maxlength="100" />
          </el-form-item>
        </template>
        <template v-if="form.invoiceType == 2">
          <el-form-item label="公司名称：" prop="companyName">
            <el-input v-model="form.companyName" placeholder="填写公司名称" maxlength="100" />
          </el-form-item>
          <el-form-item label="公司地址：" prop="companyAddress">
            <el-input v-model="form.companyAddress" placeholder="填写公司地址" maxlength="150" />
          </el-form-item>
          <el-form-item label="联系电话：" prop="companyPhone">
            <el-input v-model="form.companyPhone" placeholder="填写联系电话" maxlength="150" />
          </el-form-item>
          <el-form-item label="联系人：" prop="companyContact">
            <el-input v-model="form.companyContact" placeholder="填写联系人" maxlength="150" />
          </el-form-item>
          <el-form-item label="附件上传：" prop="file">
            <div class="flex-start gap-10 file-upload-box" v-if="form.file.length">
              <div class="one-ell">{{ form.file[0].name }}</div>
              <el-icon @click="form.file.length = 0"><Delete /></el-icon>
            </div>
            <div class="flex-start gap-10" v-else>
              <PictureCard
                v-model="form.file"
                :limit="1"
                :preview="false"
                :file-type="['doc', 'docx', 'xlsx', 'xls']"
              />
              <div>
                可上传1份您需要形式发票的模板；
                <br />
                文件支持上传doc/docx/xlsx/xls
              </div>
            </div>
          </el-form-item>
        </template>
        <Title>发票内容</Title>
        <el-form-item label="发票内容：" prop="content">
          <!-- <el-radio-button v-model="form.content" value="现代服务推广费">现代服务推广费</el-radio-button> -->
          <strong>现代服务推广费</strong>
        </el-form-item>
        <el-form-item label="发票备注：" prop="invoiceRemark">
          <el-input
            v-model="form.invoiceRemark"
            type="textarea"
            :rows="3"
            maxlength="300"
            show-word-limit
            placeholder="请输入发票备注"
          />
        </el-form-item>
      </el-form>
      <div class="tips" v-if="dialogType == 1">
        温馨提示
        <br />
        1.请您仔细核对您填写的发票信息，发票只能重开一次，重开发票后不允许再次修改
        <br />
        2.按照新的信息重新开发票以后，原来的旧发票将作废，不能进行报销
      </div>
    </div>
    <template #footer>
      <div class="flex-end">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSubmit">提交</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import Title from '@/components/public/Title.vue'
import PictureCard from '@/components/public/upload/PictureCard.vue'
import { ref } from 'vue'
import { applyForBilling, applyForReopening } from '@/api/invoice'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { phone_reg, telephone_reg } from '@/utils/RegExp'

defineExpose({
  open,
  close,
})

const emits = defineEmits(['sucess'])
const store = useUserStore()

const dialogVisible = ref(false)
const formRef = ref()
const form = ref<any>({
  invoiceType: 1,
  titleType: 1,
  title: '',
  dutyParagraph: '',
  companyName: '',
  companyAddress: '',
  companyPhone: '',
  companyContact: '',
  file: [],
  content: '现代服务推广费',
  invoiceRemark: '',
})
const rules = {
  titleType: [{ required: true, message: '请选择抬头类型', trigger: 'change' }],
  title: [{ required: true, message: '请填写发票抬头', trigger: 'blur' }],
  dutyParagraph: [{ required: true, message: '请填写税号', trigger: 'blur' }],
  companyName: [{ required: true, message: '请填写公司名称', trigger: 'blur' }],
  companyAddress: [{ required: true, message: '请填写公司地址', trigger: 'blur' }],
  companyPhone: [
    { required: false, message: '请填写联系电话', trigger: 'blur' },
    { validator: validPhone, trigger: 'change' },
  ],
  // file: [{ required: true, message: '请上传附件', trigger: 'change' }],
}
function validPhone(rule: any, value: string, callback: any) {
  if (!value) {
    return callback()
  }
  if (!/^[0-9]+$/.test(value) && !telephone_reg.test(value)) {
    return callback(new Error('请输入正确的联系电话'))
  }
  return callback()
}
const submitLoading = ref(false)

const orderId = ref<number[]>([])
const orderType = ref<string | number>('')
const dialogType = ref<number>(0)
const title = ref<string>('')

function open(ids: number[], order_type: string | number, invoice_type: string | number, type: 0 | 1) {
  if (ids?.length) {
    orderId.value = ids
  }
  dialogType.value = type
  form.value.invoiceType = invoice_type || 1
  if (type == 0) {
    title.value = '申请开票'
  } else {
    title.value = '申请重开'
  }
  if (form.value.invoiceType == 1) {
    form.value.title = store.userInfo.businessVO?.invoiceTitle || ''
    form.value.dutyParagraph = store.userInfo.businessVO?.invoiceDutyParagraph || ''
  }
  orderType.value = order_type
  dialogVisible.value = true
}
function close() {
  dialogVisible.value = false
}

function handleClose() {
  form.value = {
    invoiceType: 1,
    titleType: 1,
    title: '',
    dutyParagraph: '',
    companyName: '',
    companyAddress: '',
    companyPhone: '',
    companyContact: '',
    file: [],
    content: '现代服务推广费',
    invoiceRemark: '',
  }
  formRef.value?.clearValidate()
  orderId.value.length = 0
  orderType.value = 0
}

function handleSubmit() {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      let params: any = {
        orderType: orderType.value,
        invoiceType: form.value.invoiceType,
        content: form.value.content,
        invoiceRemark: form.value.invoiceRemark,
      }

      if (form.value.invoiceType === 1) {
        params.titleType = form.value.titleType
        params.title = form.value.title
        params.dutyParagraph = form.value.dutyParagraph
      }
      if (form.value.invoiceType === 2) {
        params.companyName = form.value.companyName
        params.companyAddress = form.value.companyAddress
        params.companyPhone = form.value.companyPhone.trim() || null
        params.companyContact = form.value.companyContact
        if (form.value.file.length) {
          params.attachmentObjectKey = form.value.file[0].picUrl
        }
      }
      submitLoading.value = true
      if (dialogType.value == 0) {
        params.orderId = orderId.value
        applyForBilling(params)
          .then((res: any) => {
            if (res.code === 200) {
              ElMessage.success('申请成功')
              close()
              emits('sucess')
            }
          })
          .finally(() => {
            submitLoading.value = false
          })
      } else if (dialogType.value == 1) {
        params.id = orderId.value[0]
        applyForReopening(params)
          .then((res: any) => {
            if (res.code === 200) {
              ElMessage.success('申请成功')
              close()
              emits('sucess')
            }
          })
          .finally(() => {
            submitLoading.value = false
          })
      }
    }
  })
}
</script>

<style scoped lang="scss">
.dialog-header {
  border-bottom: 1px solid #e4e8eb;
  width: 100%;
  height: 50px;
  position: absolute;
  top: 0;
  left: 0;
  padding: 0 20px;
  box-sizing: border-box;
  background: #f6f6f6;
  border-radius: var(--dialog-radius) var(--dialog-radius) 0 0;
  --el-dialog-title-font-size: 16px;

  h4 {
    margin: 0;
  }
  .close-round-btn {
    top: 16px;
  }
}
.invoice-container {
  .title {
    margin-bottom: 14px;
  }

  .file-upload-box {
    width: 468px;

    div {
      max-width: 80%;
    }

    .el-icon {
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }

  .tips {
    font-size: 13px;
    color: var(--el-text-color-regular);
    padding-left: 22px;
    transform: translateY(-15px);
  }
}
</style>
