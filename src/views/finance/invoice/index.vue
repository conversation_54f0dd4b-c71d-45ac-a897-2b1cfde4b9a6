<template>
  <div class="pages">
    <div class="search-box">
      <el-tabs :model-value="curTab" class="tabs" @tab-change="handleTabChange">
        <el-tab-pane v-for="(tab, i) in tabList" :key="tab.value" :name="tab.value">
          <template #label>
            <div class="flex-center gap-5">
              {{ tab.label }}
              (
              <span style="color: red">{{ tab.number }}</span>
              )
            </div>
          </template>
        </el-tab-pane>
      </el-tabs>
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
        <el-form-item label="" prop="keyword">
          <el-input
            class="custom-radius"
            v-model="queryParams.keyword"
            clearable
            style="width: 250px"
            :placeholder="curTab === INVOICE_STATUS['未开票'] ? '输入订单号进行搜索' : '请输入关键字搜索'"
          />
        </el-form-item>
        <el-form-item label="" prop="orderType">
          <el-select
            class="custom-radius"
            v-model="queryParams.orderType"
            placeholder="请选择订单类型"
            clearable
            style="width: 180px"
          >
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="payType" v-if="curTab === INVOICE_STATUS['未开票']">
          <el-select
            class="custom-radius"
            v-model="queryParams.payType"
            placeholder="请选择支付方式"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in payTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="" style="width: 400px" v-if="curTab === INVOICE_STATUS['未开票']">
          <el-date-picker
            class="custom-radius"
            v-model="queryParams.time"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="支付开始日期"
            end-placeholder="支付结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="" prop="invoiceType" v-if="curTab != INVOICE_STATUS['未开票']">
          <el-select
            class="custom-radius"
            v-model="queryParams.invoiceType"
            placeholder="请选择发票类型"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in invoiceTypeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="" style="width: 400px" v-if="curTab != INVOICE_STATUS['未开票']">
          <el-date-picker
            class="custom-radius"
            v-model="queryParams.time"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="提交开始日期"
            end-placeholder="提交结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button class="custom-radius" type="primary" icon="Search" native-type="submit" @click="onQuery">
            搜索
          </el-button>
          <el-button class="custom-radius" icon="Refresh" plain @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="w-bg">
      <div class="table-box" :key="curTab">
        <div style="margin-bottom: 10px">
          <el-button
            v-if="curTab === INVOICE_STATUS['未开票']"
            :disabled="multipleSelection.length === 0"
            @click="handleBatchApplyForInvoice"
            type="primary"
          >
            批量开票
          </el-button>
        </div>
        <el-table
          ref="tableRef"
          class="custom-table-border radius-box"
          :data="tableData"
          style="width: 100%"
          border
          header-cell-class-name="table-head-primary"
          v-loading="tableLoading"
          row-key="orderNum"
          @select="handleSelectionChange"
          @select-all="handleSelectAll"
        >
          <template #empty>
            <Empty image-type="order" description="暂无数据" :image-size="120" />
          </template>
          <el-table-column
            v-if="curTab === INVOICE_STATUS['未开票']"
            type="selection"
            width="55"
            align="center"
            reserve-selection
            :selectable="selectable"
            class-name="invoice-select"
          />
          <el-table-column
            type="index"
            :index="(index: number) => (pageNum - 1) * pageSize + (index + 1)"
            label="序号"
            width="70"
            align="center"
          />
          <el-table-column
            v-if="curTab === INVOICE_STATUS['已开票']"
            prop="number"
            label="发票号"
            minWidth="250"
            align="center"
          >
            <template v-slot="{ row }">
              <el-tag class="corner-tag" v-if="row.status == 4" type="danger" size="small">发票作废</el-tag>
              <div>{{ row.number }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="orderType" label="订单类型" width="130" align="center">
            <template v-slot="{ row }">
              {{ handleOrderType(row.orderType) }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="curTab === INVOICE_STATUS['未开票']"
            prop="orderNum"
            label="订单号"
            minWidth="250"
            align="center"
          />
          <el-table-column
            v-if="curTab === INVOICE_STATUS['未开票']"
            prop="payType"
            label="支付方式"
            minWidth="150"
            align="center"
          >
            <template v-slot="{ row }">
              <div>
                {{ handlePayType(row.payType) }}
                {{ row?.payType == 7 ? '- ' + payMoneyTypeStatus(row?.payTypeDetail) : '' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="curTab === INVOICE_STATUS['未开票']"
            prop="payTime"
            label="支付时间"
            width="200"
            align="center"
          />
          <el-table-column prop="invoiceAmount" label="开票金额(CNY)" minWidth="170" align="center" />
          <el-table-column
            v-if="curTab != INVOICE_STATUS['未开票']"
            prop="invoiceInfo"
            label="发票信息"
            minWidth="250"
            align="center"
          >
            <template v-slot="{ row }">
              <template v-if="row.invoiceType == 1">
                <div>发票抬头：{{ row.title }}</div>
                <div>企业税号：{{ row.dutyParagraph }}</div>
              </template>
              <template v-else-if="row.invoiceType == 2">
                <div>公司名称：{{ row.companyName }}</div>
                <div>公司地址：{{ row.companyAddress }}</div>
                <div>联系电话：{{ row.companyPhone || '无' }}</div>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="curTab != INVOICE_STATUS['未开票']"
            prop="invoiceType"
            label="发票类型"
            minWidth="160"
            align="center"
          >
            <template v-slot="{ row }">
              <template v-for="item in invoiceTypeList" :key="item.value">
                <div v-if="item.value == row.invoiceType">
                  {{ item.label }}
                </div>
              </template>
            </template>
          </el-table-column>
          <el-table-column
            v-if="curTab != INVOICE_STATUS['未开票']"
            prop="info"
            label="提交信息"
            width="150"
            align="center"
          >
            <template v-slot="{ row }">
              <div>{{ row.submitter?.name || row.submitter?.nickName }}</div>
              <div>{{ row.submitTime }}</div>
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" :align="'center'" width="170">
            <template v-slot="{ row }">
              <el-button
                v-if="curTab === INVOICE_STATUS['未开票']"
                link
                type="primary"
                @click="openApplyForInvoice(row, 0)"
              >
                申请开票
              </el-button>
              <el-tooltip
                v-if="curTab === INVOICE_STATUS['已开票'] && row.isNew == 1"
                :visible="true"
                placement="top"
                effect="custom-danger"
                append-to=".pages"
                :ref="handleRefs"
              >
                <template #content>
                  <div style="color: red">有一张新的发票~</div>
                </template>
                <el-button link type="primary" @click="handleDownloadInvoice(row)">下载发票</el-button>
              </el-tooltip>
              <el-button
                v-else-if="curTab === INVOICE_STATUS['已开票']"
                link
                type="primary"
                @click="handleDownloadInvoice(row)"
              >
                下载发票
              </el-button>
              <el-button
                v-if="curTab === INVOICE_STATUS['已开票'] && row.status != 4 && row.canApplyReopening != null"
                link
                type="primary"
                @click="openApplyForInvoice(row, 1)"
              >
                申请重开
              </el-button>
              <span
                v-if="curTab === INVOICE_STATUS['已开票'] && row.status != 4 && row.canApplyReopening != null"
              ></span>
              <el-button link type="primary" @click="handleViewDetail(row.id, row.orderType)">
                查看详情
              </el-button>
              <el-button
                v-if="curTab === INVOICE_STATUS['待开票'] && row.status == 5"
                link
                type="primary"
                @click="handleCancelInvoice(row.id)"
              >
                取消开票
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="table-page-box">
        <el-pagination
          class="custom-pagination-radius"
          background
          @current-change="curPageChange"
          :current-page="pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total,prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>

    <ApplyForInvoice ref="ApplyForInvoiceRef" @sucess="onQuery" />
    <InvoiceDetail ref="InvoiceDetailRef" />
    <MessageBoxDialog ref="MessageBoxDialogRef" title="" @confirm="onConfirm">
      <h1 style="margin-bottom: 0">是否确定取消开票？</h1>
      <!-- <h2 style="margin-top: 0">取消后您仍然可以再重新开票</h2> -->
    </MessageBoxDialog>
  </div>
</template>

<script setup lang="ts">
import ApplyForInvoice from '@/views/finance/invoice/components/ApplyForInvoice.vue'
import InvoiceDetail from '@/views/finance/invoice/components/Details.vue'
import MessageBoxDialog from '@/components/public/dialog/MessageBoxDialog.vue'
import { payType, payMoneyTypeStatus } from '@/utils/order'
import { nextTick, onMounted, onUnmounted, ref } from 'vue'
import {
  invoiceStatistics,
  cancelInvoice,
  companyNotInvoicedList,
  toBeInvoicedList,
  invoicedFinishList,
  removeInvoiceNewFlag,
} from '@/api/invoice'
import { downUrlFile } from '@/utils/download'
import { throttle } from '@/utils/public'
import { ElLoading, ElMessage } from 'element-plus'
import { invoiceTypeList, typeList, payTypeList } from '@/views/finance/invoice/data'

enum INVOICE_STATUS {
  '未开票' = 1,
  '待开票' = 2,
  '已开票' = 3,
}
const curTab = ref<INVOICE_STATUS>(INVOICE_STATUS['未开票'])
const tabList = ref([
  { label: '未开票', value: INVOICE_STATUS['未开票'], number: 0 },
  { label: '待开票', value: INVOICE_STATUS['待开票'], number: 0 },
  { label: '已开票', value: INVOICE_STATUS['已开票'], number: 0 },
])

const queryParams = ref<any>({
  keyword: '',
  orderType: '',
  payType: [],
  invoiceType: '',
  time: [],
})

const tableRef = ref()
const tableData = ref<any[]>([])
const tableLoading = ref(false)
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const multipleSelection = ref([])
const selectType = ref()

const tooltipRefs = ref<any[]>([])

const ApplyForInvoiceRef = ref<InstanceType<typeof ApplyForInvoice>>()
const InvoiceDetailRef = ref<InstanceType<typeof InvoiceDetail>>()
const MessageBoxDialogRef = ref<InstanceType<typeof MessageBoxDialog>>()

function handleSelectionChange(val: any) {
  if (!multipleSelection.value.length || !val.length) {
    selectType.value = val[0]?.orderType >= 0 ? val[0].orderType : undefined
  }
  multipleSelection.value = val
}
function handleSelectAll(val: any) {
  multipleSelection.value = val
  if (selectType.value == undefined) {
    nextTick(() => {
      tableRef.value!.clearSelection()
      if (tableData.value.findIndex((item: any) => item.orderType == 0) > -1) {
        selectType.value = 0
      } else if (tableData.value.findIndex((item: any) => item.orderType == 1) > -1) {
        selectType.value = 1
      } else if (tableData.value.findIndex((item: any) => item.orderType == 5) > -1) {
        selectType.value = 5
      } else {
        selectType.value = 0
      }
      nextTick(() => {
        tableRef.value!.toggleAllSelection()
      })
    })
  } else if (!val.length) {
    nextTick(() => {
      selectType.value = undefined
    })
  }
}

function selectable(row: any) {
  if (selectType.value >= 0) {
    return row.orderType == selectType.value
  }
  return true
}

function resetQuery() {
  queryParams.value = {
    keyword: '',
    orderType: '',
    payType: [],
    invoiceType: '',
    time: [],
  }
  onQuery()
}

function onQuery() {
  pageNum.value = 1
  tableRef.value!.clearSelection()
  multipleSelection.value.length = 0
  selectType.value = undefined
  handleQuery()
}

function handleParams() {
  const { time, payType, invoiceType, ...params } = queryParams.value
  if (curTab.value === INVOICE_STATUS['未开票']) {
    if (time?.length === 2) {
      params.payTimeBegin = time[0]
      params.payTimeEnd = time[1]
    }
    params.payType = payType
  } else {
    if (time?.length === 2) {
      params.submitTimeBegin = time[0]
      params.submitTimeEnd = time[1]
    }
    params.invoiceType = invoiceType
  }
  if (params.keyword) {
    params.keyword = params.keyword.trim()
  }
  return params
}

async function handleQuery() {
  getStatistics()
  tableLoading.value = true
  let res: any
  tableData.value = []
  tooltipRefs.value = []
  if (curTab.value === INVOICE_STATUS['未开票']) {
    res = await companyNotInvoicedList({
      ...handleParams(),
      pageNum: pageNum.value,
      pageSize: pageSize.value,
    })
  } else if (curTab.value === INVOICE_STATUS['待开票']) {
    res = await toBeInvoicedList({
      ...handleParams(),
      pageNum: pageNum.value,
      pageSize: pageSize.value,
    })
  } else if (curTab.value === INVOICE_STATUS['已开票']) {
    res = await invoicedFinishList({
      ...handleParams(),
      pageNum: pageNum.value,
      pageSize: pageSize.value,
    })
  }
  if (res) {
    tableData.value = res.data.rows
    total.value = res.data.total
  }
  tableLoading.value = false
}
// 获取统计数据
function getStatistics() {
  invoiceStatistics().then((res: any) => {
    if (res.data) {
      tabList.value[0].number = res.data.unbilledQuantity || 0
      tabList.value[1].number = res.data.quantityToBeInvoiced || 0
      tabList.value[2].number = res.data.invoicedQuantity || 0
    }
  })
}

function handleTabChange(name: INVOICE_STATUS) {
  curTab.value = name
  resetQuery()
}

function curPageChange(val: number) {
  pageNum.value = val
  handleQuery()
}

function handleOrderType(val: number) {
  let res = typeList.find(item => item.value === val)
  return res?.label || ''
}
function handlePayType(val: number) {
  let str = payType(val) as string
  return str?.replace(/支付$/, '') || ''
}

// 申请开票
function openApplyForInvoice(row: any, isReopen: 0 | 1) {
  ApplyForInvoiceRef.value?.open([row.id], row.orderType, row.invoiceType, isReopen)
}
function handleBatchApplyForInvoice() {
  if (multipleSelection.value.length) {
    ApplyForInvoiceRef.value?.open(
      multipleSelection.value.map((item: any) => item.id),
      selectType.value,
      1,
      0
    )
  }
}

// 下载发票
function handleDownloadInvoice(row: any) {
  if (row.isNew == 1) {
    removeInvoiceNewFlag({ invoiceId: row.id }).then(() => {
      row.isNew = 0
    })
  }
  if (!row.objectKey) return
  const fileSuffix = row.objectKey.substring(row.objectKey.lastIndexOf('/') + 1)
  if (row.objectKey.includes(',')) {
    // 分割字符串并去除空白字符
    const items = row.objectKey.split(',')

    // 遍历数组调用download方法
    items.forEach((datas: any) => {
      if (datas) {
        // 确保不为空字符串
        downUrlFile(datas, datas.substring(datas.lastIndexOf('/') + 1))
      }
    })
  } else {
    // 无逗号则直接调用
    downUrlFile(row.objectKey, fileSuffix)
  }

  // 'prod/a70a4129bef845cf9e6be36a39441665.pdf'
}

// 查看详情
function handleViewDetail(id: number, type: number) {
  if (curTab.value === INVOICE_STATUS['未开票']) {
    InvoiceDetailRef.value?.open(id, 'order', type)
  } else {
    InvoiceDetailRef.value?.open(id, 'invoice')
  }
}

// 取消开票
let invoiceId = 0
function handleCancelInvoice(id: number) {
  MessageBoxDialogRef.value?.open()
  invoiceId = id
}

function onConfirm(close: () => void) {
  const el_loading = ElLoading.service({
    lock: true,
    text: '正在取消开票...',
    background: 'rgba(0, 0, 0, 0.7)',
  })
  cancelInvoice({ invoiceId })
    .then(() => {
      ElMessage.success('取消成功')
      handleQuery()
    })
    .finally(() => {
      close()
      el_loading.close()
    })
}

handleQuery()

function handleRefs(el: any) {
  tooltipRefs.value.push(el)
}
function updateTootipRef() {
  if (tooltipRefs.value.length) {
    console.log('updateTootip')
    try {
      tooltipRefs.value.forEach((item: any) => {
        item.updatePopper()
      })
    } catch (error) {
      console.error(error)
    }
  }
}
const handleResize: () => void = throttle(updateTootipRef, 500) as () => void
onMounted(() => {
  window.addEventListener('resize', handleResize)
})
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';
.search-box {
  padding: 12px 21px 3px !important;
  margin-bottom: 20px;
}
:deep(.el-table) {
  .table-head-primary.el-table__cell {
    padding: 13px 0;
    text-align: center;
    color: var(--text-color);
    background-color: var(--table-head-bg);
  }
  .el-table__cell {
    &.invoice-select {
      padding: 0;
      position: relative;

      .el-checkbox {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
      }
    }
  }
}
.table-box {
  .corner-tag {
    position: absolute;
    top: 5px;
    left: 5px;
  }
  .el-button + .el-button {
    margin-left: 8px;
  }
}
</style>
