import { PAY_TYPE } from '@/utils/order'

const invoiceTypeList = [
  { label: '增值税普通发票', value: 1 },
  { label: '形式发票', value: 2 },
]

const typeList = [
  { label: '视频订单', value: 0 },
  { label: '会员订单', value: 1 },
  { label: '线上钱包充值', value: 5 },
]

const payTypeList = [
  { label: '全币种', value: PAY_TYPE['全币种支付'] },
  { label: '微信', value: PAY_TYPE['微信支付'] },
  { label: '支付宝', value: PAY_TYPE['支付宝支付'] },
  { label: '对公转账', value: PAY_TYPE['对公转账'] },
  // { label: '银行卡转账', value: PAY_TYPE['银行卡转账'] },
]

export { invoiceTypeList, typeList, payTypeList }