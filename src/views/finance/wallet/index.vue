<template>
  <div class="pages">
    <div class="flex-between search-box">
      <WalletCard />

      <div style="padding: 20px">
        <div class="flex-end">
          <el-button type="primary" icon="Document" link @click="toRecordOrder">订单记录</el-button>
        </div>
        <div class="flex-end" style="margin: 10px 0px 6px 0px">
          <el-button class="custom-radius" type="primary" @click="toTopUpPage">
            <template #icon>
              <img src="@/assets/icon/wallet.png" alt="" style="width: 16px; height: 16px" />
            </template>
            去充值
          </el-button>
        </div>
        <div class="flex-start un-pay-number">
          <div v-if="unPayNum">您有{{ unPayNum }}笔未支付的钱包充值订单，</div>
          <el-button v-if="unPayNum" type="primary" link @click="toPayRecordOrder">
            立即查看
          </el-button>
        </div>
      </div>
    </div>

    <div class="w-bg">
      <el-form :model="queryParams" ref="queryRef" :inline="true" label-width="68px" @submit.prevent>
        <el-form-item label="" prop="id">
          <el-input
            class="custom-radius"
            v-model="queryParams.val"
            clearable
            style="max-width: 300px"
            placeholder="请输入对应的值"
          >
            <template #prepend>
              <el-select v-model="queryParams.select" placeholder="请选择" clearable style="width: 120px">
                <el-option
                  v-for="item in searchSelectList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="" prop="type">
          <el-select
            class="custom-radius"
            v-model="queryParams.type"
            placeholder="请选择收支类型"
            clearable
            style="width: 220px"
          >
            <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="origins">
          <el-select
            class="custom-radius"
            v-model="queryParams.origins"
            placeholder="请选择收支来源"
            multiple
            collapse-tags
            collapse-tags-tooltip
            clearable
            style="width: 220px"
          >
            <el-option v-for="item in sourceList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- 交易时间 -->
        <el-form-item label="" style="width: 400px">
          <el-date-picker
            class="custom-radius"
            v-model="queryParams.tradingTime"
            format="YYYY/M/D HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            range-separator="-"
            unlink-panels
            start-placeholder="下单开始日期"
            end-placeholder="下单结束日期"
            :default-time="[new Date(2000, 1, 1, 0, 0, 0), new Date(2000, 2, 1, 23, 59, 59)]"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button class="custom-radius" type="primary" icon="Search" native-type="submit" @click="onQuery">
            搜索
          </el-button>
          <el-button class="custom-radius" icon="Refresh" plain @click="resetQuery">重置</el-button>
          <DownloadButton
            class="custom-radius"
            text="导出数据"
            plain
            icon="Download"
            fileName="交易记录.xlsx"
            url="/biz/business/export/businessBalanceFlowList"
            :params="handleParams()"
          />
        </el-form-item>
      </el-form>

      <div class="table-box">
        <el-table
          ref="tableRef"
          class="custom-table-border radius-box"
          :data="tableData"
          style="width: 100%"
          border
          header-cell-class-name="table-head-primary"
          v-loading="tableLoading"
        >
          <template #empty>
            <Empty image-type="wallet" description="暂无数据" :image-size="120" />
          </template>
          <el-table-column prop="createTime" label="交易时间" minWidth="170" align="center"></el-table-column>
          <el-table-column prop="type" label="收支类型" minWidth="150" align="center">
            <template v-slot="{ row }">
              {{ typeList.find(item => item.value === row.type)?.label || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额（元）" minWidth="120" align="center">
            <template v-slot="{ row }">
              {{ row.amount > 0 ? '+' + row.amount : row.amount }}
            </template>
          </el-table-column>
          <el-table-column prop="origin" label="收支来源" minWidth="150" align="center">
            <template #header>
              <div class="flex-center gap-5">
                <span>收支来源</span>
                <el-icon
                  color="var(--text-gray-color)"
                  style="margin-top: 1px"
                  @mouseenter="showTooltips($event, 'finance-wallet-origins')"
                >
                  <QuestionFilled />
                </el-icon>
              </div>
            </template>
            <template v-slot="{ row }">
              {{ sourceList.find(item => item.value === row.origin)?.label || '-' }}
            </template>
          </el-table-column>
          <el-table-column prop="balance" label="余额（元）" minWidth="120" align="center" />
          <el-table-column prop="msg" label="订单信息" minWidth="250" align="center">
            <template v-slot="{ row }">
              <div v-if="row.prepayNum">订单号：{{ row.prepayNum }}</div>
              <div v-if="row.videoCode">视频编码：{{ row.videoCode }}</div>
              <div v-if="row.orderNum">订单号：{{ row.orderNum }}</div>
              <div v-if="row.refundNum">退款审批号：{{ row.refundNum }}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="table-page-box">
        <el-pagination
          class="custom-pagination-radius"
          background
          @size-change="pageSizeChange"
          @current-change="curPageChange"
          :current-page="pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import WalletCard from '@/views/finance/wallet/components/WalletCard.vue'
import DownloadButton from '@/components/public/button/DownloadButton.vue'
import { ref } from 'vue'
import { businessBalanceFlowList, getOnlineUnPay } from '@/api/wallet'
import type { WalletQueryParams, SearchSelect } from '../type'
import { useTooltips } from '@/hooks/useTooltips'
import { useUserStore } from '@/stores/modules/user'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const { showTooltips } = useTooltips()

const router = useRouter()
const store = useUserStore()

const searchSelectList: {
  label: string
  value: SearchSelect
}[] = [
  { label: '订单号', value: 'orderNum' },
  { label: '退款审批号', value: 'refundNum' },
  { label: '视频编码', value: 'videoCode' },
]
const typeList: {
  label: string
  value: number
}[] = [
  { label: '收入', value: 0 },
  { label: '支出', value: 1 },
]
const sourceList: {
  label: string
  value: number
}[] = [
  { label: '补偿订单收入', value: 1 },
  { label: '取消订单收入', value: 2 },
  { label: '取消选配收入', value: 3 },
  { label: '视频订单支出', value: 4 },
  { label: '会员订单支出', value: 5 },
  { label: '线下余额提现', value: 6 },
  { label: '线下钱包充值', value: 7 },
  { label: '线上钱包充值', value: 8 },
]

const unPayNum = ref(0)
const unPayOnlineList = ref<any[]>([])

const tableData = ref([])
const pageNum = ref(1)
const pageSize = ref(20)
const total = ref(0)
const tableLoading = ref(false)

const queryParams = ref<WalletQueryParams>({
  val: '',
  select: 'orderNum',
  type: undefined,
  origins: [],
  tradingTime: [],
  orderTimeBegin: '',
  orderTimeEnd: '',
})

function resetQuery() {
  queryParams.value = {
    val: '',
    select: 'orderNum',
    type: undefined,
    origins: [],
    tradingTime: [],
    orderTimeBegin: '',
    orderTimeEnd: '',
  }
  onQuery()
}
function handleParams() {
  let { val, select, tradingTime, origins, ...obj } = queryParams.value
  let params: any = {
    ...obj,
  }
  if (select) {
    params[select] = val ? val.trim() : ''
  }
  if (tradingTime && tradingTime.length === 2) {
    params.orderTimeBegin = tradingTime[0]
    params.orderTimeEnd = tradingTime[1]
  }
  if (origins?.length) {
    params.origins = origins.join(',')
  }
  return params
}
function onQuery() {
  pageNum.value = 1
  handleQuery(handleParams())
}
function handleQuery(params: any = {}) {
  tableLoading.value = true
  params.pageNum = pageNum.value
  params.pageSize = pageSize.value
  businessBalanceFlowList(params)
    .then(res => {
      if (res.data) {
        tableData.value = res.data.rows
        total.value = res.data.total
      }
    })
    .finally(() => (tableLoading.value = false))
}
// 分页
function pageSizeChange(val: number) {
  pageNum.value = 1
  pageSize.value = val
  handleQuery(handleParams())
}
function curPageChange(val: number) {
  pageNum.value = val
  handleQuery(handleParams())
}

function toTopUpPage() {
  if (store.isVip()) {
    router.push('/finance/wallet/topUp')
  } else {
    ElMessage.error('请先开通会员！')
  }
}

function toRecordOrder() {
  router.push('/finance/wallet/topUp/record')
}

function toPayRecordOrder() {
  if (unPayNum.value) {
    if (unPayNum.value === 1) {
      store.realTimeCheckVip().then(() => {
        router.push({
          name: 'finance-wallet-pay',
          state: {
            prepayId: unPayOnlineList.value[0].id,
            orderNum: unPayOnlineList.value[0].prepayNum,
            payType: unPayOnlineList.value[0].payType,
          },
        })
      })
      return
    }
    toRecordOrder()
  }
}

function init() {
  getOnlineUnPay().then(res => {
    if (res.data) {
      unPayNum.value = res.data.unPayNum
      unPayOnlineList.value = res.data.unPayOnlineList
    }
  })
  handleQuery()
}
init()
</script>

<style scoped lang="scss">
.search-box {
  margin-bottom: 20px;
  .header {
    padding: 0 0 10px 0;
    border-bottom: 1px solid #e4e8eb;
    margin-bottom: 20px;
  }

  .un-pay-number {
    font-size: 14px;
    min-height: 22px;
  }
}
:deep(.el-table) {
  .table-head-primary.el-table__cell {
    padding: 13px 0;
    text-align: center;
    color: var(--text-color);
    background-color: var(--table-head-bg);
  }
}
</style>
