<template>
  <div class="pages">
    <div class="content-box">
      <Title>钱包充值</Title>

      <div class="flex-start head-box">
        <div class="head-icon">
          <img src="@/assets/icon/icon_order_succ.png" alt="" />
        </div>
        <div class="head-text">
          <div class="head-text__title">订单支付成功，钱包余额已充值！</div>
          <div class="head-text__text">
            <span class="label">订单号：</span>
            <span>{{ orderNum }}</span>
            <CopyButton v-if="orderNum" :copy-content="orderNum" link>
              <el-icon size="14"><CopyDocument /></el-icon>
            </CopyButton>
          </div>
          <div class="head-text__text">
            <span class="label">支付方式：</span>
            <span v-if="orderInfo.payType">{{ payType(orderInfo.payType) }}</span>
          </div>
          <div class="head-text__text">
            <span class="label">支付时间：</span>
            <span>{{ orderInfo.payTime }}</span>
          </div>
        </div>
      </div>

      <div class="form-box">
        <el-divider />
      </div>

      <div class="flex-start gap-10 form-box">
        <div class="form-label">充值金额：</div>
        <div class="form-content">
          <span class="total-value">{{ toFixed2(orderInfo.amount) }}</span>
          <span class="total-text">CNY</span>
          <span class="total-tip" v-if="orderInfo.containPresentedAmount">
            (含赠送金额{{ toFixed2(orderInfo.containPresentedAmount) }}CNY)
          </span>
        </div>
      </div>

      <div class="flex-start gap-10 form-box">
        <div class="form-label">订单支付金额：</div>
        <div class="form-content">
          <span class="total-value">{{ toFixed2(orderInfo.realPayAmount) }}</span>
          <span class="total-text">CNY</span>
        </div>
      </div>

      <div class="flex-end gap-10 form-box" style="margin-top: 24px">
        <div class="form-content" style="margin: 0">
          <span class="total-text">当前钱包余额：</span>
          <span class="total-value2">{{ toFixed2(WalletAmount) }}</span>
          <span class="total-text">CNY</span>
        </div>

        <el-button type="primary" round size="large" class="big-btn" @click="toPage">钱包管理</el-button>
      </div>
    </div>

    <!-- <TopUpTip /> -->
  </div>
</template>

<script setup lang="ts">
import Title from '@/components/public/Title.vue'
import CopyButton from '@/components/public/button/CopyButton.vue'
// import TopUpTip from '@/views/finance/wallet/components/TopUpTip.vue'
import { ref } from 'vue'
import { getBusinessBalanceDetailVo } from '@/api/user'
import { PAY_TYPE, payType } from '@/utils/order'
import { walletTopUpOrderDetail } from '@/api/wallet'
import { useRouter } from 'vue-router'

const router = useRouter()

function toFixed2(val: any) {
  if (val) {
    return val.toFixed(2)
  }
  return '0'
}

const orderInfo = ref<{
  currentExchangeRate: number
  amount: number
  realPayAmount: number
  payAmount: number
  payAmountDollar: number
  payType: PAY_TYPE | null
  containPresentedAmount: number
  payeeId: number
  payTime: string
}>({
  currentExchangeRate: 0,
  amount: 0,
  realPayAmount: 0,
  payAmount: 0,
  payAmountDollar: 0,
  payType: null,
  containPresentedAmount: 0,
  payeeId: 0,
  payTime: '',
})
const WalletAmount = ref(0)
const prepayId = ref('')
const orderNum = ref('')
const disabled = ref(true)
const loading = ref(true)

function toPage() {
  router.replace('/finance/wallet')
}

async function init() {
  if (!history.state.prepayId || !history.state.orderNum) {
    window.location.href = '/finance/wallet'
    return
  }
  prepayId.value = history.state.prepayId
  orderNum.value = history.state.orderNum

  await walletTopUpOrderDetail(prepayId.value)
    .then((res: any) => {
      if (res.code === 200 && res.data) {
        orderInfo.value = res.data
      }
    })
    .finally(() => {
      disabled.value = false
      loading.value = false
    })
  getBusinessBalanceDetailVo().then(res => {
    if (res.data) {
      WalletAmount.value = res.data.balance || 0
    }
  })
}
init()
</script>

<style scoped lang="scss">
.content-box {
  min-width: 700px;
  padding: 32px;
  background-color: #fff;
  border-radius: 14px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 16px;

  .head-box {
    margin-top: 24px;
    gap: 20px;

    .head-icon {
      flex-shrink: 0;
      align-self: flex-start;

      img {
        width: 80px;
        height: 100%;
      }
    }

    .head-text {
      padding-bottom: 8px;

      &__title {
        color: var(--text-color);
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
      }
      &__text {
        font-size: 14px;
        color: var(--text-color);
        line-height: 24px;

        .label {
          color: #777;
        }
      }
    }
  }

  .form-box {
    align-items: flex-start;
    padding-left: 100px;

    .form-label {
      width: 100px;
      font-size: 14px;
      line-height: 40px;
    }

    .form-content {
      .total-value {
        font-size: 22px;
        color: var(--el-color-primary);
      }
      .total-value2 {
        font-size: 22px;
        color: var(--el-color-warning);
        margin: 0 4px;
      }
      .total-text {
        font-size: 14px;
        margin: 0 4px;
      }
      .total-tip {
        font-size: 12px;
        color: #777;
      }
    }

    .big-btn {
      width: 170px;
      font-size: 16px;
      margin-left: 10px;
    }
  }
}
</style>
