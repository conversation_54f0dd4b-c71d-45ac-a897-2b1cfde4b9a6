<template>
  <div class="flex-start wallet-card">
    <div class="flex-center img">
      <img src="@/assets/icon/icon_wallet.png" alt="" />
    </div>
    <div class="money">
      <el-statistic title="钱包余额（元）" :value="amount" :formatter="handleFormatter" :precision="2">
        <template #title>
          <div class="flex-start title">
            &nbsp;钱包余额（元）
            <el-icon style="margin-top: 1px" @mouseenter="showTooltips($event, 'finance-wallet-amount')">
              <QuestionFilled />
            </el-icon>
          </div>
        </template>
      </el-statistic>
      <div class="flex-start lock" v-if="lockBalance">
        <el-icon :size="14"><WarnTriangleFilled /></el-icon>
        {{ lockBalance }}
        <el-button type="primary" link @click="viewLockRecord">查看锁定记录</el-button>
      </div>
    </div>
    
    <RecordDialog ref="RecordDialogRef" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { getBusinessBalanceDetailVo } from '@/api/user'
import { useRouter } from 'vue-router'
import { useTooltips } from '@/hooks/useTooltips'
import RecordDialog from '@/views/finance/wallet/components/RecordDialog.vue'

const { showTooltips } = useTooltips()

const router = useRouter()

const amount = ref(0)
const orderLockBalance = ref(0)
const payOutLockBalance = ref(0)
const useBalance = ref(0)
const RecordDialogRef = ref()

const lockBalance = computed(() => {
  if (payOutLockBalance.value > 0 && orderLockBalance.value > 0) {
    return `其中已被提现及订单锁定${useBalance.value}元`
  }
  if (payOutLockBalance.value > 0) {
    if (amount.value === payOutLockBalance.value) {
      return `余额已被提现锁定`
    }
    return `其中已被提现锁定${payOutLockBalance.value}元`
  }
  if (orderLockBalance.value > 0) {
    if (amount.value === orderLockBalance.value) {
      return `余额已被订单锁定`
    }
    return `其中已被订单锁定${orderLockBalance.value}元`
  }
  return ''
})

function handleFormatter(value: number) {
  if (!value) {
    return '0'
  }
  return value
}

function viewLockRecord() {
  RecordDialogRef.value?.open()
}

function init() {
  getBusinessBalanceDetailVo().then(res => {
    if (res.data) {
      amount.value = res.data.balance
      orderLockBalance.value = res.data.orderLockBalance
      payOutLockBalance.value = res.data.payOutLockBalance
      useBalance.value = res.data.useBalance
    }
  })
}

init()
</script>

<style scoped lang="scss">
.wallet-card {
  position: relative;
  gap: 20px;
  padding: 20px;

  .img {
    width: 96px;
    height: 96px;
    background: #FFFFFF;
    box-shadow: 0px 0px 8px 0px rgba(141,169,189,0.34);
    border-radius: 6px;

    img {
      width: 54px;
      height: 54px;
    }
  }

  .money {
    :deep(.el-statistic) {
      .el-statistic__content {
        text-align: left;

        .el-statistic__number {
          text-align: center;
          font-size: 28px;
          font-weight: bold;
          margin: 0 auto;
          color: var(--text-color);
        }
      }
    }

    .title {
      text-align: center;
      font-size: 16px;
      color: var(--text-gray-color);
    }

    .lock {
      gap: 3px;
      font-size: 14px;
      color: var(--el-color-warning);
    }
  }
}
.lock-record-title {
  color: var(--text-color);
  margin: 0 0 10px 1px;
}
</style>
