<template>
  <PublicDialog ref="RecordDialogRef" title="余额锁定记录" width="700" custom-close :title-center="false">
    <div class="lock-record-title">锁定金额总计（元）：{{ detailAmount }}</div>
    <el-table
      :data="tableData"
      border
      header-cell-class-name="table-head-primary"
      max-height="550"
      v-loading="loading"
    >
      <template #empty>
        <Empty style="--empty-desc-color: #5696c0" image-type="wallet" description="暂无记录" :image-size="180" />
      </template>
      <el-table-column prop="type" label="类型" width="120" align="center">
        <template v-slot="{ row }">
          <span v-if="row.type == 2">提现</span>
          <span v-else-if="row.type == 1">会员订单</span>
          <span v-else>视频订单</span>
        </template>
      </el-table-column>
      <el-table-column prop="orderNum" label="订单号" min-width="250" align="center" />
      <el-table-column prop="amount" label="锁定金额（元）" min-width="180" align="center" />
      <el-table-column label="操作" width="100" align="center">
        <template v-slot="{ row }">
          <el-button type="primary" link @click="openDetail(row)" v-if="row.type != 2">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footerButton>
      <el-button style="margin-right: 20px" type="primary" @click="close()">确定</el-button>
    </template>
  </PublicDialog>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { getBalanceLockRecord } from '@/api/user'

defineExpose({
  open,
  close,
})

const router = useRouter()
const RecordDialogRef = ref()
const tableData = ref([])
const loading = ref(false)
const detailAmount = ref(0)

function open() {
  viewLockRecord()
}

function close() {
  RecordDialogRef.value?.close()
}

function viewLockRecord() {
  detailAmount.value = 0
  loading.value = true
  getBalanceLockRecord()
    .then(res => {
      tableData.value = res.data
      if (tableData.value.length > 0) {
        tableData.value.forEach((item: any) => {
          detailAmount.value = (detailAmount.value * 100 + item.amount * 100) / 100
        })
      }
    })
    .finally(() => {
      loading.value = false
    })
  RecordDialogRef.value?.open()
}

function openDetail(row: any) {
  if (row.type == 1) {
    const name = 'center-tradingRecord'
    const { href } = router.resolve({ name })
    window.open(href, '_blank')
  } else if (row.type == '0') {
    router.push({ name: 'order-list', query: { keyword: row.orderNum } })
  }
}
</script>
