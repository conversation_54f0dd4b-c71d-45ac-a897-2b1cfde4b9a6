<template>
  <div class="pages">
    <div class="content-box" v-loading="loading">
      <Title>钱包充值</Title>

      <div class="flex-start head-box">
        <div class="head-icon">
          <img src="@/assets/icon/icon_order_pay.png" alt="" />
        </div>
        <div class="head-text">
          <div class="head-text__title">订单提交成功，请尽快付款！</div>
          <div class="head-text__order" v-if="isAnotherPay && orderNum">
            订单号：{{ orderNum }}
            <CopyButton :copy-content="orderNum" link>
              <el-icon size="14"><CopyDocument /></el-icon>
            </CopyButton>
          </div>
          <div class="head-text__time" v-else-if="orderInfo.creatTime">
            订单将在
            <CountDown :end-time="handleEndTime(orderInfo.creatTime)" show-days @end="handleCountDownEnd" />
            后自动关闭
          </div>
        </div>
      </div>

      <div class="flex-start gap-10 form-box" v-if="!isAnotherPay">
        <div class="form-label">充值金额：</div>
        <div class="form-content">
          <span class="total-value">{{ toFixed2(orderInfo.amount) }}</span>
          <span class="total-text">CNY</span>
          <span class="total-tip" v-if="orderInfo.containPresentedAmount">
            (含赠送金额{{ toFixed2(orderInfo.containPresentedAmount) }}CNY)
          </span>
        </div>
      </div>

      <div class="form-box">
        <el-divider />
      </div>

      <template v-if="isAnotherPay">
        <div class="flex-start gap-10 form-box">
          <div class="form-label">充值金额：</div>
          <div class="form-content">
            <span class="total-value">{{ toFixed2(orderInfo.amount) }}</span>
            <span class="total-text">CNY</span>
            <span class="total-tip" v-if="orderInfo.containPresentedAmount">
              (含赠送金额{{ toFixed2(orderInfo.containPresentedAmount) }}CNY)
            </span>
          </div>
        </div>
        <div class="flex-start gap-10 form-box">
          <div class="form-label">实付金额：</div>
          <div class="form-content">
            <span class="total-value2">{{ toFixed2(orderInfo.payAmount) }}</span>
            <span class="total-text">CNY</span>
          </div>
        </div>
        <div class="form-box">
          <AnotherPayInfo :orderNum="orderNum" :disabled="disabled || errorDisabled" />
        </div>
      </template>

      <template v-else>
        <div class="flex-start gap-10 form-box">
          <div class="form-label" style="line-height: 46px">支付方式：</div>
          <div class="form-content select-pay-box">
            <el-radio-group size="large" v-model="payWay" :disabled="disabled" @change="payWayChange">
              <el-radio-button :value="PAY_TYPE['微信支付']" :label="PAY_TYPE['微信支付']">
                <i
                  class="iconfont icon-weixinzhifu"
                  :class="{ 'wx-color': payWay !== PAY_TYPE['微信支付'] }"
                ></i>
                微信支付
              </el-radio-button>
              <el-tooltip :content="payDollarTips" raw-content placement="top" effect="dark">
                <el-radio-button
                  :value="PAY_TYPE['全币种支付']"
                  :label="PAY_TYPE['全币种支付']"
                  :disabled="disabled || minAmount || currentExchangeRateError"
                  style="position: relative; overflow: hidden"
                >
                  <div class="btn-corner-mark"></div>
                  <i class="iconfont icon-Dollar"></i>
                  全币种支付
                </el-radio-button>
              </el-tooltip>
              <el-radio-button :value="PAY_TYPE['支付宝支付']" :label="PAY_TYPE['支付宝支付']">
                <i
                  class="iconfont icon-zhifubaozhifu"
                  :class="{ 'zfb-color': payWay !== PAY_TYPE['支付宝支付'] }"
                ></i>
                支付宝支付
              </el-radio-button>
              <!-- <el-radio-button :value="PAY_TYPE['银行卡转账']" :label="PAY_TYPE['银行卡转账']">
                <i v-if="payWay === PAY_TYPE['银行卡转账']" class="iconfont icon-iconfontjikediancanicon20"></i>
                <svg v-else class="icon" aria-hidden="true" style="width: 20px; height: 20px">
                  <use xlink:href="#icon-yinhangqiazhuanzhang"></use>
                </svg>
                银行转账
              </el-radio-button> -->
              <!-- <el-tooltip content="对公转账，需要额外支付5%的开票服务费" placement="top" effect="light"> -->
              <el-radio-button :value="PAY_TYPE['对公转账']" :label="PAY_TYPE['对公转账']">
                <i class="iconfont icon-duigongzhuanzhang"></i>
                对公转账
              </el-radio-button>
              <!-- </el-tooltip>-->
            </el-radio-group>
          </div>
        </div>

        <div class="flex-end gap-10 form-box" style="margin-top: 24px">
          <div class="form-content" style="margin: 0; position: relative">
            <span class="total-text">所需支付金额</span>
            <span class="total-value2">{{ toFixed2(orderInfo.payAmount) }}</span>
            <span class="total-text">CNY</span>
            <template v-if="payWay === PAY_TYPE['全币种支付']">
              <span>&nbsp;/&nbsp;</span>
              <span class="total-value2">{{ toFixed2(orderInfo.payAmountDollar) }}</span>
              <span class="total-text">USD</span>
              <span class="total-exchange-rate" v-if="orderInfo.currentExchangeRate">
                实时百度汇率：{{ orderInfo.currentExchangeRate }}
              </span>
            </template>
          </div>

          <el-button
            class="another-pay-btn"
            round
            :disabled="disabled || errorDisabled"
            @click="handleAnotherPay"
          >
            找人代付
            <span class="tip">*可以分享给财务或其他人帮你付款</span>
          </el-button>

          <el-button
            type="primary"
            round
            size="large"
            class="big-btn"
            :disabled="disabled || errorDisabled"
            @click="tryLock"
          >
            立即支付
          </el-button>
        </div>
      </template>
    </div>

    <TopUpTip v-if="!isAnotherPay" />

    <template v-if="payWay === PAY_TYPE['全币种支付']">
      <TransferMoneyPaySelect
        :detailId="orderInfo.accountId"
        :pay-amount="orderInfo.payAmountDollar"
        :cny-amount="orderInfo.payAmount"
        :orderNum="orderNum"
        ref="TransferMoneyPaySelectRef"
        @submit="onSubmit"
      />
    </template>

    <TransferMoneyPay
      v-if="underWay"
      ref="TransferMoneyPayRef"
      :title="dialogTitle"
      :pay-way="payWay"
      :pay-amount="payWay === PAY_TYPE['全币种支付'] ? orderInfo.payAmountDollar : orderInfo.payAmount"
      :cny-amount="orderInfo.payAmount"
      :orderNum="orderNum"
      @submit="onSubmit"
      @close="handleClose"
    />
    <QrCodeDialog
      v-if="underWay"
      ref="QrCodeDialogRef"
      width="450px"
      :title="dialogTitle"
      :http-code="getPayCode"
      :check-code="getPayCheck"
      :auto-check-code="false"
      :is-zfb-pay="payWay === PAY_TYPE['支付宝支付']"
      :loading="QrCodeDialogLoading"
      @close="handleClose"
    >
      <div class="qrcode-pay-box">
        <span class="flex-start gap-5 qrcode-tips">
          <img v-if="payWay === PAY_TYPE['微信支付']" src="@/assets/icon/icon_weixinzhifu.png" alt="" />
          <!-- <img v-if="payWay === PAY_TYPE['支付宝支付']" src="@/assets/icon/icon_zhifubaozhifu.png" alt="" /> -->
          <svg v-if="payWay === PAY_TYPE['支付宝支付']" class="icon" aria-hidden="true">
            <use xlink:href="#icon-zhifubaozhifu"></use>
          </svg>
          手机
          {{ payWay === PAY_TYPE['微信支付'] ? '微信' : '支付宝' }}
          扫码支付
        </span>
        <div class="pay-total-box">
          <!-- <div>支付金额</div> -->
          <div class="pay-total">
            <span v-if="payWay === PAY_TYPE['全币种支付']">{{ toFixed2(orderInfo.payAmountDollar) }}</span>
            <span v-else>{{ toFixed2(orderInfo.payAmount) }}</span>
            {{ payWay === PAY_TYPE['全币种支付'] ? 'USD' : 'CNY' }}
          </div>
        </div>
      </div>
    </QrCodeDialog>

    <AnotherPayLink ref="AnotherPayLinkRef" @submit="showAnotherPay" />
    <PayTipDialog />
  </div>
</template>

<script setup lang="ts">
import Title from '@/components/public/Title.vue'
import CountDown from '@/components/public/CountDown.vue'
import CopyButton from '@/components/public/button/CopyButton.vue'
import TopUpTip from '@/views/finance/wallet/components/TopUpTip.vue'
import TransferMoneyPay from '@/views/order/components/dialog/TransferMoneyPay.vue'
import TransferMoneyPaySelect from '@/views/order/components/dialog/TransferMoneyPaySelect.vue'
import PayTipDialog from '@/views/order/components/dialog/PayTipDialog.vue'
import QrCodeDialog from '@/components/public/dialog/QrCodeDialog.vue'
import AnotherPayLink from '@/views/anotherPay/dialog/Link.vue'
import AnotherPayInfo from '@/views/anotherPay/info.vue'
import { computed, ref } from 'vue'
import { PAY_TYPE, payType } from '@/utils/order'
import { ElMessage } from 'element-plus'
import { orderPayCode, orderPayCheck, walletTopUpPayLock } from '@/api/order'
import { walletTopUpOrderDetail, walletTopUpSubmitCredential } from '@/api/wallet'
import { useTopUpPay } from '@/hooks/usePay'

const { orderNum, underWay, isAnotherPay, isOpenPayDialog, checkPlatform, payTipDialogVisible, payTipType } =
  useTopUpPay(history.state.orderNum)

function toFixed2(val: any) {
  if (val) {
    return val.toFixed(2)
  }
  return '0'
}

const prepayId = ref('')
const orderInfo = ref<{
  currentExchangeRate: number
  amount: number
  payAmount: number
  payAmountDollar: number
  payType: PAY_TYPE | null
  containPresentedAmount: number
  accountId: number
  creatTime: string
}>({
  currentExchangeRate: 0,
  amount: 0,
  payAmount: 0,
  payAmountDollar: 0,
  payType: null,
  containPresentedAmount: 0,
  accountId: 0,
  creatTime: '',
})

const payWay = ref<PAY_TYPE>(PAY_TYPE['微信支付'])
const disabled = ref(true)
const loading = ref(true)
const errorDisabled = ref(false)
const currentExchangeRateError = ref(false)

const QrCodeDialogLoading = ref(false)

const TransferMoneyPayRef = ref<InstanceType<typeof TransferMoneyPay>>()
const QrCodeDialogRef = ref<InstanceType<typeof QrCodeDialog>>()
const TransferMoneyPaySelectRef = ref<InstanceType<typeof TransferMoneyPaySelect>>()
const AnotherPayLinkRef = ref<InstanceType<typeof AnotherPayLink>>()

const payDollarTips = ref(`<div style="text-align: center">
    跨境卖家的最优选择<br/>
    收款支持币种:  离岸人民币,美元,澳元,加币,欧元,英镑,港币,日元,新西兰元,新加坡元
  </div>`)
const minAmount = computed(() => {
  if (orderInfo.value.payAmountDollar < 50) {
    payDollarTips.value = '<div>当前交易金额低于 50 USD，不支持使用全币种支付。</div>'
    return true
  }
  payDollarTips.value = currentExchangeRateError.value
    ? `<div>汇率异常，不支持使用全币种支付。</div>`
    : `<div style="text-align: center">
    跨境卖家的最优选择<br/>
    收款支持币种:  离岸人民币,美元,澳元,加币,欧元,英镑,港币,日元,新西兰元,新加坡元
  </div>`
  return false
})

const dialogTitle = computed(() => {
  return payType(payWay.value) as string
})

// 倒计时结束
function handleCountDownEnd() {
  disabled.value = true
  ElMessage.error('订单已关闭')
}
function handleEndTime(time: string) {
  let date = time.split(' ')[0] + ' 00:00:00'
  return new Date(date).getTime() + 1000 * 60 * 60 * 24 * 31 - 1000 // 加30天 到最后一天 23:59:59
}

const platform = ref(0)
// 支付二维码
function getPayCode() {
  let params = {
    orderNum: orderNum.value,
    payType: payWay.value,
  }
  return orderPayCode(params)
}
// 二维码状态
function getPayCheck() {
  let params = {
    orderNum: orderNum.value,
    platform: platform.value,
  }
  return orderPayCheck(params)
}

// 支付方式选择
function payWayChange() {
  getPayInfo()
}

// 下单锁定
function tryLock() {
  if (!payWay.value) {
    ElMessage.warning('请选择支付方式')
    return
  }
  loading.value = true
  walletTopUpPayLock({
    orderNum: orderNum.value,
    payType: payWay.value,
    useBalance: 0,
  })
    .then(() => getPayInfo(true))
    .catch(() => {
      loading.value = false
    })
}

// 获取支付信息
function getPayInfo(first: boolean = false) {
  loading.value = true
  disabled.value = true
  walletTopUpOrderDetail(prepayId.value)
    .then(res => {
      if (res.code === 200 && res.data) {
        orderInfo.value = res.data

        // 汇率
        if (!orderInfo.value.currentExchangeRate) {
          currentExchangeRateError.value = true
          if (payWay.value === PAY_TYPE['全币种支付'] || orderInfo.value.payType === PAY_TYPE['全币种支付']) {
            ElMessage.warning('获取当前汇率失败，不支持使用全币种支付。')
            payWay.value = PAY_TYPE['对公转账']
            getPayInfo()
            return
          }
        } else {
          currentExchangeRateError.value = false
        }

        errorDisabled.value = false

        // 记录支付方式
        if (orderInfo.value.payType != null) {
          history.state.payType = orderInfo.value.payType
        }
        // 防止在锁定全币种支付时金额变更低于50 USD
        if (payWay.value === PAY_TYPE['全币种支付'] && orderInfo.value.payAmountDollar < 50) {
          ElMessage.warning('当前交易金额低于50 USD，不支持使用全币种支付。')
          payWay.value = PAY_TYPE['微信支付']
          getPayInfo()
          return
        }
        // 支付方式
        if (orderInfo.value.payType != null && first) {
          if (orderInfo.value.payType < PAY_TYPE['余额支付']) {
            payWay.value = orderInfo.value.payType
          } else if (orderInfo.value.payType === PAY_TYPE['余额支付']) {
            payWay.value = PAY_TYPE['余额支付']
          } else {
            payWay.value = orderInfo.value.payType - PAY_TYPE['余额支付']
          }

          openPayBox()
        }
      }
    })
    .catch(() => {
      errorDisabled.value = true
    })
    .finally(() => {
      loading.value = false
      disabled.value = false
    })
}

// 银行/对公 转账
function onSubmit(params: any, close: () => void) {
  params.payAmount = orderInfo.value.payAmount
  params.payAmountDollar = orderInfo.value.payAmountDollar
  params.resourceIds = params.objectKeys
  params.objectKeys = undefined
  walletTopUpSubmitCredential(params)
    .then(() => {
      ElMessage.success('提交成功！')
      underWay.value = false
      payTipDialogVisible.value = true
      payTipType.value = 1
    })
    .finally(() => close())
}

// 找人代付
function handleAnotherPay() {
  AnotherPayLinkRef.value?.open(orderNum.value)
}
function showAnotherPay() {
  if (orderInfo.value.payType == null) {
    payWay.value = PAY_TYPE['微信支付']
  }
  getPayInfo()
  isAnotherPay.value = true
}

// 打开支付窗口
function openPayBox() {
  checkPlatform.value = payWay.value
  if (payWay.value === PAY_TYPE['全币种支付']) {
    TransferMoneyPaySelectRef.value?.open(orderInfo.value)
  }
  if (payWay.value === PAY_TYPE['银行卡转账'] || payWay.value === PAY_TYPE['对公转账']) {
    TransferMoneyPayRef.value?.open({ detailId: orderInfo.value.accountId })
  } else if (payWay.value === PAY_TYPE['微信支付']) {
    platform.value = 1
    handleOpenQrcodeDelay()
  } else if (payWay.value === PAY_TYPE['支付宝支付']) {
    platform.value = 2
    handleOpenQrcodeDelay()
  }
}
let openTimer: any
// 打开 微信支付/支付宝支付 延迟check接口
function handleOpenQrcodeDelay() {
  // QrCodeDialogLoading.value = true
  QrCodeDialogRef.value?.open()
  openTimer = setTimeout(() => {
    isOpenPayDialog.value = true
    // QrCodeDialogLoading.value = false
  }, 5000)
}

function handleClose() {
  if (openTimer) clearTimeout(openTimer)
  isOpenPayDialog.value = false
}

async function init() {
  if (!history.state.prepayId) {
    window.location.href = '/finance/wallet'
    return
  }
  prepayId.value = history.state.prepayId

  await walletTopUpOrderDetail(prepayId.value)
    .then(res => {
      if (res.code === 200 && res.data) {
        if (res.data.payType != null) {
          history.state.payType = res.data.payType
          if (res.data.payType < PAY_TYPE['余额支付']) {
            payWay.value = res.data.payType
          } else if (res.data.payType === PAY_TYPE['余额支付']) {
            payWay.value = PAY_TYPE['余额支付']
          } else {
            payWay.value = res.data.payType - PAY_TYPE['余额支付']
          }
        }
        getPayInfo(true)
      }
    })
    .catch(() => {
      errorDisabled.value = true
    })
}
init()
</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';

.qrcode-pay-box {
  margin-left: 20px;
  line-height: 40px;

  .pay-total-box {
    color: var(--text-gray-color);
    font-size: 14px;
    margin-top: 10px;

    .pay-total {
      font-family: PingFangSC;
      color: var(--el-color-warning);

      span {
        font-size: 26px;
        font-weight: 600;
      }
    }
  }

  .qrcode-tips {
    font-size: 16px;
    font-weight: bold;
    color: #1f2122;

    img {
      width: 16px;
      height: 16px;
    }

    .icon {
      width: 16px;
      height: 16px;
    }
  }
}
.content-box {
  min-width: 700px;
  padding: 32px;
  background-color: #fff;
  border-radius: 14px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 16px;

  .head-box {
    margin-top: 24px;
    gap: 20px;

    .head-icon {
      flex-shrink: 0;

      img {
        width: 80px;
        height: 100%;
      }
    }

    .head-text {
      padding-bottom: 8px;

      &__title {
        color: var(--text-color);
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 8px;
      }
      &__time {
        font-size: 16px;
        color: #777;
        line-height: 24px;
      }
    }
  }

  .form-box {
    align-items: flex-start;
    padding-left: 100px;

    .form-label {
      width: 90px;
      font-size: 14px;
      line-height: 40px;
    }

    .form-content {
      :deep(.el-radio-group) {
        margin: 0px 20px 5px 0px;
        font-size: 16px;

        .el-radio-button__inner {
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }

      .total-value {
        font-size: 22px;
        color: var(--el-color-primary);
      }
      .total-value2 {
        font-size: 22px;
        color: var(--el-color-warning);
        // margin: 0 4px;
      }
      .total-text {
        font-size: 14px;
        margin: 0 4px;
      }
      .total-tip {
        font-size: 12px;
        color: #777;
      }
      .total-exchange-rate {
        position: absolute;
        right: 0;
        bottom: -10px;
        font-size: 12px;
        color: #aaa;
        font-weight: 500;
        transform: scale(0.9);
      }
    }

    .select-pay-box {
      margin: 0;

      :deep(.el-radio-group) {
        margin: 0;
        font-size: 16px;

        .el-radio-button__inner {
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }
      .wx-color {
        color: rgb(25, 213, 108);
      }
      .zfb-color {
        color: rgb(0, 159, 232);
      }

      .iconfont {
        // background: linear-gradient(to right, #fff, #fff) no-repeat right;
        // background-size: 18px 11px;
        font-size: 20px;
      }

      .btn-corner-mark {
        position: absolute;
        top: -1px;
        left: -1px;
        width: 32px;
        height: 32px;
        background-image: url('@/assets/image/recommend_corner_mark.png');
        background-size: 100%;

        // position: absolute;
        // top: -1px;
        // left: -1px;
        // width: 35px;
        // height: 35px;
        // overflow: hidden;

        // &::after {
        //   content: '推荐';
        //   display: block;
        //   position: absolute;
        //   top: -3px;
        //   left: -41px;
        //   width: 100px;
        //   height: 24px;
        //   line-height: 33px;
        //   font-size: 12px;
        //   text-align: center;
        //   background-color: rgb(255, 85, 82);
        //   color: #fff;
        //   transform: rotate(-45deg);
        // }
      }
    }

    .another-pay-btn {
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
      width: 180px;
      height: 40px;
      font-size: 15px;
      font-weight: bold;
      position: relative;

      .tip {
        width: 180px;
        position: absolute;
        bottom: -18px;
        left: -10px;
        font-size: 12px;
        color: #aaa;
        font-weight: 500;
        transform: scale(0.8);
        letter-spacing: 1px;
      }
    }
    .big-btn {
      width: 180px;
      font-size: 16px;
      margin-left: 10px;
    }
  }
}
</style>
