<template>
  <div class="order-item-box">
    <div class="flex-between head-box">
      <div class="flex-start title">
        <!-- <img src="@/assets/icon/icon_vip.png" /> -->
        <span>{{ data.prepayNum }}</span>
      </div>
      <div class="order-status" v-if="WALLET_TOPUP_ORDER_STATUS['待支付'] === data.status">
        <div class="flex-end gap-5">
          <img src="@/assets/icon/clock_icon.png" />
          <span>{{ WALLET_TOPUP_ORDER_STATUS[data.status] }}</span>
        </div>
        <div class="order-time" v-if="data.creatTime">
          订单将在
          <CountDown :endTime="handleOrderTime(data.creatTime)" showDays @end="handleTimeEnd" />
          后关闭
        </div>
      </div>
      <div class="order-status flex-end gap-5" v-else>
        <img v-if="data.status === WALLET_TOPUP_ORDER_STATUS['待审核']" src="@/assets/icon/clock_icon.png" />
        <el-icon v-else-if="data.status === WALLET_TOPUP_ORDER_STATUS['支付成功']" color="#2ea571" :size="16">
          <SuccessFilled />
        </el-icon>
        <el-icon v-else-if="data.status === WALLET_TOPUP_ORDER_STATUS['订单关闭']" color="#d54941" :size="16">
          <CircleCloseFilled />
        </el-icon>
        <span>{{ WALLET_TOPUP_ORDER_STATUS[data.status] }}</span>
      </div>
    </div>
    <el-form label-width="95px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="支付时间">
            {{ data.payTime || '-' }}
          </el-form-item>
          <el-form-item label="支付方式">
            <span v-if="WALLET_TOPUP_ORDER_STATUS['订单关闭'] === data.status">-</span>
            <span v-else>
              {{
                (data.payTime || WALLET_TOPUP_ORDER_STATUS['待审核'] == data.status) && data.payType
                  ? payType(data.payType)
                  : '-'
              }}
            </span>
          </el-form-item>
          <el-form-item label="充值用户">
            {{ data.createBy || '-' }}
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item
            label="百度汇率"
            v-if="
              WALLET_TOPUP_ORDER_STATUS['待支付'] != data.status &&
              WALLET_TOPUP_ORDER_STATUS['订单关闭'] != data.status &&
              PAY_TYPE['对公转账'] != data.payType
            "
          >
            {{
              (data.payTime || WALLET_TOPUP_ORDER_STATUS['待审核'] == data.status) && data.currentExchangeRate
                ? data.currentExchangeRate
                : '-'
            }}
          </el-form-item>
          <el-form-item label="充值金额">{{ data.amount.toFixed(2) }} CNY</el-form-item>
          <el-form-item label="赠送金额">{{ data.containPresentedAmount.toFixed(2) }} CNY</el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div class="pay-amount" v-if="WALLET_TOPUP_ORDER_STATUS['订单关闭'] != data.status">
      <span v-if="WALLET_TOPUP_ORDER_STATUS['待审核'] == data.status">待审核&nbsp;</span>
      <span v-else-if="!data.payTime">还需支付&nbsp;</span>
      <span v-else>已支付&nbsp;</span>
      <span class="amount">
        {{ data.payTime ? data.realPayAmount.toFixed(2) : data.payAmount.toFixed(2) }}&nbsp;CNY
      </span>
      <span class="amount" v-if="PAY_TYPE['全币种支付'] == data.payType">
        /&nbsp;{{ realPayAmountDollar }}&nbsp;USD
      </span>
    </div>

    <div class="flex-end btn" v-if="data.status === WALLET_TOPUP_ORDER_STATUS['待支付']">
      <el-button round plain :disabled="disabled" @click="handleAction('取消订单')">取消订单</el-button>
      <el-button v-if="!data.isDefaultExchangeRate" round type="primary" :disabled="disabled" @click="toBuy">
        去支付
      </el-button>
      <el-tooltip v-else :visible="true" placement="top" effect="custom-danger" append-to=".pages">
        <template #content>
          <div class="flex-start gap-5" style="color: red">
            <el-icon><WarnTriangleFilled /></el-icon>
            汇率获取失败，请联系客服
          </div>
        </template>
        <el-button type="primary" round :disabled="disabled" @click="toBuy">去支付</el-button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import CountDown from '@/components/public/CountDown.vue'
import { PAY_TYPE, payType, WALLET_TOPUP_ORDER_STATUS } from '@/utils/order'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { computed, ref } from 'vue'
import currency from 'currency.js'

const router = useRouter()
const store = useUserStore()

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
})

const realPayAmountDollar = computed(() => {
  if (props.data) {
    let amount = props.data.payTime ? props.data.realPayAmount : props.data.payAmount
    if (amount && props.data.currentExchangeRate) {
      let val = currency(amount, { precision: 4 }).divide(props.data.currentExchangeRate).multiply(100).value
      return currency(Math.floor(val)).divide(100).value.toFixed(2)
    }
  }
  return '-'
})

const disabled = ref(false)

const emits = defineEmits<{
  action: [btn: string, row: any]
  countDownEnd: []
}>()

function handleOrderTime(time: string) {
  let date = time.split(' ')[0] + ' 00:00:00'
  return new Date(date).getTime() + 1000 * 60 * 60 * 24 * 31 - 1000 // 加30天 到最后一天 23:59:59
}

function handleTimeEnd() {
  disabled.value = true
}

// 按钮操作
function handleAction(btn: string) {
  emits('action', btn, props.data)
}

// 去支付
function toBuy() {
  store.realTimeCheckVip().then(() => {
    router.push({
      name: 'finance-wallet-pay',
      state: { prepayId: props.data.id, orderNum: props.data.prepayNum, payType: props.data.payType },
    })
  })
}
</script>

<style scoped lang="scss">
.order-item-box {
  position: relative;
  border-radius: 10px;
  margin: 16px 0;
  padding: 0 0 10px;
  background-color: var(--bg);
  border: 1px solid rgba(106, 178, 227, 0.36);
  overflow: hidden;

  :deep(.el-form) {
    .el-form-item__label {
      color: #777;
    }
  }

  .head-box {
    height: 55px;
    padding: 0 30px;
    background-color: rgba(106, 178, 227, 0.36);
    margin-bottom: 10px;

    .title {
      gap: 10px;
    }

    img {
      width: 16px;
      height: 16px;
      flex-shrink: 0;
    }
    span {
      font-size: 15px;
      font-weight: 600;
    }
  }
  .order-status {
    text-align: right;
    font-size: 15px;
    line-height: 16px;

    .order-time {
      font-weight: 400;
      font-size: 12px;
      color: #a0816c;
      background: rgb(255, 245, 231);
      padding: 0 10px;
      border-radius: 10px;
      margin-top: 5px;

      span {
        font-weight: 400;
        font-size: 12px;
        color: #a0816c;
      }
    }
  }
  .pay-amount {
    position: absolute;
    top: 62px;
    right: 30px;
    font-size: 14px;
    color: var(--text-color);
    line-height: 24px;

    .amount {
      font-weight: bold;
      font-size: 16px;
      color: #ff5722;
    }
  }
  .btn {
    position: absolute;
    bottom: 15px;
    right: 30px;

    .el-button {
      width: 100px;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 5px;
  }
}
</style>
