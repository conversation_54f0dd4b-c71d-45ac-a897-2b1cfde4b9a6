<template>
  <div class="pages">
    <div class="content-box">
      <Title>钱包充值</Title>

      <div class="flex-start head-box">
        <span>当前账户余额：</span>
        <span>{{ WalletAmount }}</span>
        <span>CNY</span>
      </div>

      <div class="flex-start gap-10 form-box">
        <div class="form-label">充值面值：</div>
        <div class="form-content">
          <el-radio-group size="large" v-model="faceValue" @change="handleFaceValueChange">
            <el-radio-button
              class="radio-btn"
              v-for="item in faceValueList"
              :value="item.value"
              :label="item.value"
            >
<!--              <div class="btn-corner-mark" v-if="item.corner"></div>-->
              <span class="radio-value">{{ item.value }}</span>
              <span>CNY</span>
              <div class="radio-tip" v-if="item.giveNumber > 0">
                <span>送{{ item.giveNumber }}CNY</span>
              </div>
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <div class="flex-start gap-10 form-box">
        <div class="form-label"></div>
        <div class="form-content">
          <el-input
            v-model="faceNumber"
            class="face-number-input"
            size="large"
            placeholder="自定义金额"
            @input="handleFaceNumberChange"
          >
            <template #suffix>
              <span style="color: var(--text-color)">CNY</span>
            </template>
          </el-input>
        </div>
      </div>

      <div class="flex-start gap-10 form-box">
        <div class="form-label">充值金额：</div>
        <div class="form-content">
          <span class="total-value">{{ toFixed2(totalFaceValue.value + totalFaceValue.giveNumber) }}</span>
          <span class="total-text">CNY</span>
          <span class="total-tip" v-if="totalFaceValue.giveNumber">
            (含赠送金额{{ toFixed2(totalFaceValue.giveNumber) }}CNY)
          </span>
        </div>
      </div>

      <div class="flex-end gap-10 form-box">
        <div class="form-content" style="margin: 0">
          <span class="total-text">所需支付金额</span>
          <span class="total-value2">{{ toFixed2(totalFaceValue.value) }}</span>
          <span class="total-text">CNY</span>
        </div>

        <el-button
          type="primary"
          round
          size="large"
          class="big-btn"
          :disabled="!totalFaceValue.value"
          @click="handleSubmit"
        >
          提交订单
        </el-button>
      </div>
    </div>

    <TopUpTip />
  </div>
</template>

<script setup lang="ts">
import Title from '@/components/public/Title.vue'
import TopUpTip from '@/views/finance/wallet/components/TopUpTip.vue'
import { computed, ref } from 'vue'
import { getBusinessBalanceDetailVo } from '@/api/user'
import { createWalletTopUpOrder } from '@/api/wallet'
import { ElLoading } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

function toFixed2(val: any) {
  if (val) {
    return val.toFixed(2)
  }
  return '0'
}

const WalletAmount = ref(0)

const faceValueList = [
  { value: 5000, giveNumber: 0 },
  { value: 10000, giveNumber: 500, corner: true },
  { value: 20000, giveNumber: 1000 },
  { value: 50000, giveNumber: 3000 },
]
// 选择面值
const faceValue = ref(0)
// 输入面值
const faceNumber = ref<number>()

const totalFaceValue = computed(() => {
  if (faceValue.value > 0) {
    let val = faceValueList.find(item => item.value === faceValue.value)
    if (val) {
      return val
    }
  }
  if (faceNumber.value && faceNumber.value > 0) {
    let num = 0
    // 每1w送500
    if (faceNumber.value >= 10000) {
      num = Math.floor(faceNumber.value / 10000) * 500
    }
    // 每5w另加500
    if (faceNumber.value >= 50000) {
      num += Math.floor(faceNumber.value / 50000) * 500
    }
    return {
      value: faceNumber.value,
      giveNumber: num,
    }
  }
  return {
    value: 0,
    giveNumber: 0,
  }
})

function handleFaceValueChange(value: number) {
  if (value > 0) {
    faceNumber.value = undefined
  } else if (value < 0) {
    faceValue.value = 0
  }
}
function handleFaceNumberChange(value: string) {
  let num = parseFloat(value.replace(/[^\d]/g, ''))
  if (num < 0 || isNaN(num)) {
    faceNumber.value = undefined
    return
  }
  faceValue.value = 0
  if (num > 999999) {
    faceNumber.value = 999999
    return
  }
  faceNumber.value = num
}

function handleSubmit() {
  if (faceValue.value || faceNumber.value) {
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在提交订单...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    createWalletTopUpOrder({
      amount: faceValue.value || faceNumber.value,
    })
      .then(res => {
        if (res.data && res.data.id) {
          router.replace({
            name: 'finance-wallet-pay',
            state: { prepayId: res.data.id, orderNum: res.data.prepayNum },
          })
        }
      })
      .finally(() => {
        el_loading.close()
      })
  }
}

function init() {
  getBusinessBalanceDetailVo().then(res => {
    if (res.data) {
      WalletAmount.value = res.data.balance.toFixed(2) || 0
    }
  })
}

init()
</script>

<style scoped lang="scss">
@use '@/styles/customForm.scss';

.content-box {
  min-width: 700px;
  padding: 32px;
  background-color: #fff;
  border-radius: 14px;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 16px;

  .head-box {
    background: #f1f4fb;
    padding: 11px 14px;
    line-height: 24px;
    border-radius: 6px;
    margin: 20px 0;
    align-items: baseline;

    span {
      &:nth-child(1) {
        font-size: 16px;
      }
      &:nth-child(2) {
        font-size: 22px;
      }
      &:nth-child(3) {
        margin-left: 6px;
        font-size: 14px;
      }
    }
  }

  .form-box {
    align-items: flex-start;

    .form-label {
      width: 90px;
      font-size: 14px;
      line-height: 40px;
    }

    .form-content {
      margin-bottom: 14px;

      :deep(.el-radio-group) {
        margin: 0px 20px 5px 0px;
        font-size: 16px;

        .el-radio-button__inner {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 5px;
        }
      }

      .radio-btn {
        position: relative;
        width: 130px;

        &:not(.is-active) {
          .radio-value {
            color: var(--el-color-primary);
          }
        }

        .radio-tip {
          position: absolute;
          top: -10px;
          right: -1px;
          z-index: 2;
          background-color: #fb8b41;
          color: #fff;
          font-size: 12px;
          border-radius: 0px 6px 0px 6px;
          padding: 3px 6px;

          span {
            display: block;
            transform: scale(0.9);
          }
        }

        .btn-corner-mark {
          position: absolute;
          top: -1px;
          left: -1px;
          width: 35px;
          height: 35px;
          overflow: hidden;

          &::after {
            content: '推荐';
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 3px;
            left: -39px;
            width: 100px;
            height: 18px;
            line-height: 16px;
            font-size: 12px;
            text-align: center;
            background-color: rgb(255, 85, 82);
            color: #fff;
            transform: rotate(-45deg) scale(0.8);
            font-family: 'PingFang PC';
          }
        }
      }

      .face-number-input {
        width: 270px;
      }

      .total-value {
        font-size: 22px;
        color: var(--el-color-primary);
      }
      .total-value2 {
        font-size: 22px;
        color: var(--el-color-warning);
        margin: 0 4px;
      }
      .total-text {
        font-size: 14px;
        margin: 0 4px;
      }
      .total-tip {
        font-size: 12px;
        color: #777;
      }
    }
    .big-btn {
      width: 170px;
      font-size: 16px;
      margin-left: 10px;
    }
  }
}
</style>
