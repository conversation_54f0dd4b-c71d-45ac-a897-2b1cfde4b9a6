import request from '@/request'

/**
 * 初始化子账号
 * @param data
 * @returns
 */
export function initBusinessSon(data: any) {
  return request({
    url: '/biz/business/initBusinessSon',
    method: 'post',
    data,
  })
}

/**
 * 子账号重绑微信
 * @param data
 * @returns
 */
export function rebind(data: any) {
  return request({
    url: '/biz/account/bizUser/bizUserUpdateWeChat',
    // url: '/biz/business/rebind',
    method: 'post',
    data,
  })
}

/**
 * 重置密码
 * @param data
 * @returns
 */
export function resetPassword(data: any) {
  return request({
    url: '/biz/business/resetPassword',
    method: 'put',
    data,
  })
}

/**
 * 修改密码
 * @param data
 * @returns
 */
export function updatePassword(data: any) {
  return request({
    url: '/biz/business/updatePassword',
    method: 'put',
    data,
  })
}

/**
 * 修改姓名
 * @param data
 * @returns
 */
export function bizUserUpdateName(data: any) {
  return request({
    url: '/biz/account/bizUser/bizUserUpdateName',
    method: 'post',
    data,
  })
}

/**
 * 编辑商家数据
 * @param data
 * @returns
 */
export function initBusinessInfo(data: any) {
  return request({
    url: '/biz/business/initBusinessInfo',
    method: 'put',
    data,
  })
}

/**
 * 修改商家名称
 * @param data
 * @returns
 */
export function resetBusinessName(data: any) {
  return request({
    url: '/biz/business/resetBusinessName',
    method: 'put',
    data,
  })
}

/**
 * 修改商家规模
 * @param data
 * @returns
 */
export function resetBusinessScale(data: any) {
  return request({
    url: '/biz/business/resetBusinessScale',
    method: 'put',
    data,
  })
}

/**
 * 账号状态变更
 * @param data
 * @returns
 */
export function updateAccStatus(data: any) {
  return request({
    url: '/biz/business/updateBusinessAccountStatus',
    method: 'put',
    data,
  })
}

/**
 * 发票信息管理
 * @param data
 * @returns
 */
export function updateInvoice(data: any) {
  return request({
    url: '/biz/business/updateInvoice',
    method: 'put',
    data,
  })
}

/**
 * 账号申请表待审批数量
 * @param data
 * @returns
 */
export function getBusinessAccountApplyNum() {
  return request({
    url: '/biz/business/account/apply/businessAccountApplyNum',
    method: 'get',
  })
}

/**
 * 账号申请列表
 * @param data
 * @returns
 */
export function getAppltyList() {
  return request({
    url: '/biz/business/account/apply/list',
    method: 'get',
  })
}
/**
 * 账号申请审批
 * @param data
 * @returns
 */
export function accountAudit(data: any) {
  return request({
    url: '/biz/business/account/apply/audit',
    method: 'post',
    data,
  })
}
/**
 * 检查手机号（绑定使用）
 * @param data
 * @returns
 */
export function checkPhoneStatus(data: any) {
  return request({
    url: '/biz/business/checkPhoneStatus',
    method: 'post',
    data,
  })
}
/**
 * 检查手机号（变更使用）
 * @param data
 * @returns
 */
export function checkPhone(data: any) {
  return request({
    url: '/biz/account/bizUser/checkPhone',
    method: 'post',
    data,
  })
}
/**
 * 检查手机号（变更使用）
 * @param data
 * @returns
 */
export function checkBindingPhone(data: any) {
  return request({
    url: '/biz/account/bizUser/checkBindingPhone',
    method: 'post',
    data,
  })
}
/**
 * 变更手机号
 * @param data
 * @returns
 */
export function bizUserUpdatePhone(data: any) {
  return request({
    url: '/biz/account/bizUser/bizUserUpdatePhone',
    method: 'post',
    data,
  })
}

/**
 * 获取会员有效订单
 * @params params
 * @returns
 */
export function getValidOrderList(params: any) {
  return request({
    url: '/order/order/get-valid-order-list',
    method: 'get',
    params,
  })
}
