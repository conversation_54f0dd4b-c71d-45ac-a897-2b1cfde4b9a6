import request from '@/request'

/**
 * 创建或获取代付链接
 * @param data 
 * @returns 
 */
export function createOrGetPayLink(data: any) {
  return request({
    url: '/order/another-pay/create-link',
    method: 'post',
    data
  })
}

/**
 * 通过code获取有效代付链接
 * @param params 
 * @returns 
 */
export function getOrGetPayLinkByCode(params: any) {
  return request({
    url: '/order/another-pay/get-by-code',
    method: 'get',
    params
  })
}

/**
 * 通过订单号获取有效代付链接
 * @param params 
 * @returns 
 */
export function getOrGetPayLink(params: any) {
  return request({
    url: '/order/another-pay/get-by-orderNum-or-mergeId',
    method: 'get',
    params
  })
}

/**
 * 取消代付
 * @param params 
 * @returns 
 */
export function cancelAnotherPay(params: any) {
  return request({
    url: '/order/another-pay/cancel',
    method: 'get',
    params
  })
}

/**
 * 代付订单支付锁定
 * @param data 
 * @returns 
 */
export function payLockAnotherPay(data: any) {
  return request({
    url: '/order/another-pay/payLock',
    method: 'post',
    data
  })
}

/**
 * 检查订单状态
 * @param data 
 * @returns 
 */
export function checkAnotherPay(data: any) {
  return request({
    url: '/order/another-pay/check',
    method: 'post',
    data
  })
}

/**
 * 代付订单生成二维码
 * @param data 
 * @returns 
 */
export function anotherPayCode(data: any) {
  return request({
    url: '/order/another-pay/code',
    method: 'post',
    data
  })
}

/**
 * 订单支付页信息-会员
 * @param params 
 * @returns 
 */
export function anotherPayMemberInfo(params: any) {
  return request({
    url: '/order/another-pay/pay-Member-info',
    method: 'get',
    params
  })
}

/**
 * 订单支付页信息-视频
 * @param data 
 * @returns 
 */
export function anotherPayInfo(data: any) {
  return request({
    url: '/order/another-pay/pay-info',
    method: 'post',
    data
  })
}

/**
 * 订单支付页信息-钱包充值
 * @param params 
 * @returns 
 */
export function anotherPayPrepayInfo(params: any) {
  return request({
    url: '/order/another-pay/businessBalancePrepay/getOnlineeDetail',
    method: 'get',
    params
  })
}

/**
 * 获取收款人账号配置表
 * @param params 
 * @returns 
 */
export function getAnotherPayValidConfig(params: any) {
  if (params.detailId) {
    return request({
      url: `/order/another-pay/payee/info/detail/${params.detailId}`,
      method: 'get',
    })
  }
  if (params.payeeId) {
    return request({
      url: `/order/another-pay/payee/info/${params.payeeId}`,
      method: 'get',
    })
  }
  return request({
    url: `/order/another-pay/payee/type/${params.type}`,
    method: 'get',
  })
}

/**
 * 提交凭证信息
 * @param data 
 * @returns 
 */
export function submitAnotherPayCredentials(data: any) {
  return request({
    url: '/order/another-pay/submit-credential',
    method: 'post',
    data
  })
}

/**
 * 提交凭证信息-钱包充值
 * @param data 
 * @returns 
 */
export function submitAnotherPayPrepayCredentials(data: any) {
  return request({
    url: '/order/another-pay/businessBalancePrepay/submit-credential',
    method: 'post',
    data
  })
}
