import request from '@/request'

/**
 * 会员配置
 * @returns
 */
export function getMemberConfig() {
  return request({
    url: '/biz/business/getMemberConfig',
    method: 'get',
  })
}
/**
 * 创建会员订单
 * @param data
 * @returns
 */
export function createVipOrder(data: any) {
  return request({
    url: '/order/order/member/create-order',
    method: 'post',
    data,
  })
}

/**
 * 取消会员订单
 * @param orderNum
 * @returns
 */
export function cancelMemberOrder(orderNum: any) {
  return request({
    url: `/order/order/cancelMemberOrder/${orderNum}`,
    method: 'put',
  })
}

/**
 * 子账号取消订单
 * @param orderNum
 * @returns
 */
export function cancelSonMemberOrder(params: any) {
  return request({
    url: `/order/order/cancelMemberOrder`,
    method: 'put',
    params,
  })
}

/**
 * 关闭过往会员订单
 * @param orderNum
 * @returns
 */
export function latestUnPayMemberOrder() {
  return request({
    url: `/order/order/member/latestUnPayOrder`,
    method: 'delete',
  })
}

/**
 * 查询会员未支付数据
 * @returns
 */
export function getMemberUnPay() {
  return request({
    url: `/order/order/member/getMemberUnPay`,
    method: 'get',
  })
}

/**
 * 查询会员订单列表
 * @param params
 * @returns
 */
export function memberOrderList(params: any) {
  return request({
    url: `/order/order/member/list`,
    method: 'get',
    params,
  })
}

/**
 * 查看会员订单_详细信息
 * @param orderNum
 * @returns
 */
export function memberOrderDetail(orderNum: any) {
  return request({
    url: `/order/order/backend/member/detail/${orderNum}`,
    method: 'get',
  })
}

/**
 * 会员订单支付页信息
 * @param params
 * @returns
 */
export function memberOrderPayInfo(params: any) {
  return request({
    url: '/order/pay/pay-Member-info',
    method: 'get',
    params,
  })
}
/**
 * 查看发票
 * @param id
 * @returns
 */
export function viewInvoice(id: any) {
  return request({
    url: `/order/finance/view-invoice/${id}`,
    method: 'get',
  })
}

/**
 * 校验种草码接口
 * @param params
 * @returns
 */
export function checkSeedCode(params: any) {
  return request({
    url: `/order/pay/checkSeedCodeV1`,
    method: 'post',
    params,
  })
}

/**
 * 会员活动
 * @returns
 */
export function memberActivityList() {
  return request({
    url: `/biz/business/member/activity/list`,
    method: 'get',
  })
}
