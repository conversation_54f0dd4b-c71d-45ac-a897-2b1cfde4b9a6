import request from '@/request'

/**
 * 获取模特标签 指定级别标签
 * @param {number} rank 1、2、3
 * @param {number} categoryId 1009:模特标签,1008:类目标签
 * @returns
 */
export function modelCategorySelectRank(rank: number, categoryId: number) {
  return request({
    url: '/biz/tag/rank',
    method: 'get',
    params: {
      rank,
      categoryId,
    },
  })
}

/**
 * 获取模特擅长品类下拉框/模特标签下拉框
 * @param {any} params 1009:模特标签,1008:类目标签
 * @returns
 */
export function modelCategorySelect(params: any) {
  return request({
    url: '/biz/tag/list',
    method: 'get',
    params,
  })
}

// 获取模特擅长品类下拉框/模特标签下拉框1009:模特标签,1008:类目标签
export function modelCategoryTagSelect(params: any) {
  return request({
    url: '/biz/tag/tag-select',
    method: 'get',
    params,
  })
}
/**
 * 查询收藏模特列表
 * @param params
 * @returns
 */
export function modelMerchantCollectList(params: any) {
  return request({
    url: '/biz/business/account/collect-model-list',
    method: 'get',
    params,
  })
}
/**
 * 查询商家端模特信息静态列表
 * @param params
 * @returns
 */
export function staticModelList(params: any) {
  return request({
    url: '/biz/model/reference-list',
    method: 'get',
    params,
  })
}
/**
 * 查询商家端模特信息列表
 * @param params
 * @returns
 */
export function modelMerchantList(params: any) {
  return request({
    url: '/biz/business/account/model-list',
    method: 'get',
    params,
  })
}
/**
 * 获取模特信息详细信息
 * @param params: any
 * @param id: any
 * @returns
 */
export function modelMerchantDetails(id: any, params?: any) {
  return request({
    url: `/biz/business/account/model/${id}`,
    method: 'get',
    params,
  })
}
/**
 * 获取模特信息详细信息-未登录
 * @param id
 * @returns
 */
export function referenceModelInfo(id: any, params?: any) {
  return request({
    url: `/biz/model/referenceModelInfo/${id}`,
    method: 'get',
    params,
  })
}
/**
 * 收藏模特-取消收藏
 * @param params
 * @returns
 */
export function modelCollect(params: any) {
  return request({
    url: `/biz/business/account/collect-model`,
    method: 'post',
    params,
  })
}
/**
 * 拉黑模特
 * @param modelId
 * @returns
 */
export function blackModel(modelId: any) {
  return request({
    url: `/biz/business/blacklist/blackModel/${modelId}`,
    method: 'post',
  })
}
/**
 * 取消拉黑模特
 * @param modelId
 * @returns
 */
export function cancelBlackModel(modelId: any) {
  return request({
    url: `/biz/business/blacklist/cancelBlackModel/${modelId}`,
    method: 'post',
  })
}
/**
 * 黑名单列表
 * @param params
 * @returns
 */
export function blackModelList(params: any) {
  return request({
    url: `/biz/business/blacklist/list`,
    method: 'get',
    params,
  })
}
/**
 * 判断模特是否有档期
 * @param data
 * @returns
 */
export function modelIsSlotOrNo(data: any) {
  return request({
    url: `/biz/business/account/modelIsSlotOrNo`,
    method: 'post',
    data,
  })
}
