import request from '@/request'
import { fingerprint } from '@/utils/fingerprintjs2'
import { encrypt } from '@/utils/jsencrypt'

/**
 * 登录
 * @param data
 * @returns
 */
export function loginApi(data: any) {
  return request({
    url: '/auth/business/login',
    method: 'post',
    headers: {
      isToken: false,
    },
    data,
  })
}
/**
 * 手机号登录
 * @param data
 * @returns
 */
export function phoneLoginApi(data: any) {
  return request({
    url: '/auth/business/phoneLogin',
    method: 'post',
    headers: {
      isToken: false,
    },
    data,
  })
}
/**
 * 手机验证码
 * @param data
 * @returns
 */
export function sendSms(data: any) {
  if (data.phoneNum) {
    data.verifyToken = encrypt(data.phoneNum + '.' + fingerprint.value)
  }
  return request({
    url: '/auth/sms',
    method: 'post',
    headers: {
      isToken: false,
    },
    data,
  })
}
/**
 * 手机验证码(注册)
 * @param data
 * @returns
 */
export function registerSendSms(data: any) {
  if (data.phoneNum) {
    data.verifyToken = encrypt(data.phoneNum + '.' + fingerprint.value)
  }
  return request({
    url: '/auth/register/sms',
    method: 'post',
    headers: {
      isToken: false,
    },
    data,
  })
}
/**
 * 图形验证码
 * @returns
 */
export function getImgCode() {
  return request({
    url: '/code',
    method: 'get',
    headers: {
      isToken: false,
    },
  })
}
/**
 * 登出
 * @returns
 */
export function logoutApi() {
  return request({
    url: '/auth/business/logout',
    method: 'delete',
  })
}
/**
 * 获取用户信息
 * @returns
 */
export function getUserInfoApi() {
  return request({
    url: '/biz/account/info',
    method: 'get',
  })
}
/**
 * 获取商家余额信息
 * @param data
 * @returns
 */
export function getBusinessBalanceDetailVo() {
  return request({
    url: '/biz/business/getBusinessBalanceDetailVo',
    method: 'get',
  })
}
/**
 * 获取商家余额锁定信息
 * @param data
 * @returns
 */
export function getBalanceLockRecord() {
  return request({
    url: '/order/order/getBalanceLockRecord',
    method: 'get',
  })
}

/**
 * 绑定账号
 * @param data
 * @returns
 */
export function bindingLoginUser(data: any) {
  return request({
    url: '/biz/business/bindingLoginUser',
    method: 'post',
    data,
  })
}
/**
 * 解绑账号
 * @param data
 * @returns
 */
export function unbindLoginUser(data: any) {
  return request({
    url: '/biz/business/unbindLoginUser',
    method: 'post',
    data,
  })
}
/**
 * 用户协议
 * @returns
 */
export function userAgreement() {
  return request({
    url: '/biz/text/user-agreement',
    method: 'get',
  })
}
/**
 * 查询隐私协议
 * @returns
 */
export function privacyAgreement() {
  return request({
    url: '/biz/text/privacy-agreement',
    method: 'get',
  })
}
/**
 * 通过专属链接code获取专属企微二维码
 * @param dedicatedLinkCode
 * @returns
 */
export function getChannelQrcode(dedicatedLinkCode: string) {
  return request({
    url: `/biz/marketing-channel/qrcode/${dedicatedLinkCode}`,
    method: 'get',
  })
}

///api/biz/marketing-channel/qrcode/vip
export function getVipQrcode() {
  return request({
    url: `/biz/marketing-channel/qrcode/vip`,
    method: 'get',
  })
}

///biz/account/phone/check
// 检查手机号是否已注册
export function checkPhone(params: any) {
  return request({
    url: '/biz/account/phone/check',
    method: 'get',
    params,
  })
}