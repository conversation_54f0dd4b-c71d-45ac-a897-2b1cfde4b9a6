import request from '@/request'
import { ElMessage } from 'element-plus'

/**
 * 上传
 * @param data
 * @returns
 */
export function uploadFile(data: any) {
  return request({
    url: '/file/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
      repeatSubmit: false,
    },
    data,
  })
}
export type Bucket = 'credit' | 'model'
/**
 * 上传到云端
 * @param {*} data FormData对象
 * @returns
 */
export async function uploadCloudFile(data: any, bucket?: Bucket) {
  return new Promise(async (resolve, reject) => {
    try {
      let name = data.get('file').name || '.jpg'
      const fileSuffix = name.substring(name.lastIndexOf('.') + 1)
      let res = await request({
        url: '/file/sign?fileName=1.' + fileSuffix + (bucket ? '&bucket=' + bucket : ''),
        method: 'get',
        headers: {
          repeatSubmit: false,
        },
      })
      let resParams = {
        code: res.code,
        data: {
          id: res.data.objectKey,
          name,
          picUrl: res.data.objectKey,
        },
      }
      let res2 = await fetch(res.data.uploadUrl, {
        method: 'PUT',
        body: data.get('file'),
      })
      if (res2.headers.get('etag')) {
        resolve(resParams)
      } else {
        ElMessage.error('etag Error!')
        reject('not etag')
      }
    } catch (error) {
      reject(error)
    }
  })
}
/**
 * 抓取amazon产品链接图片上传至文件存储系统
 * @param params
 * @returns
 */
export function uploadAmazonUrl(params: any) {
  return request({
    url: '/file/upload-amazon-url',
    method: 'get',
    params,
  })
}
/**
 * 获取文件下载链接
 * @param params
 * @returns
 */
export function fileDownloadUrl(params: any) {
  return request({
    url: '/file/download',
    method: 'get',
    params,
  })
}
/**
 * 渠道埋点
 * @param params
 * @returns
 */
export function marketingChannelVisit(params: any) {
  return request({
    url: '/biz/marketing-channel/visit',
    method: 'get',
    params,
  })
}
/**
 * console log error
 * @param ev
 * @returns
 */
export const recordConsoleLogError = (() => {
  if (import.meta.env.VITE_APP_ENV === 'development') {
    return (message: any, url: any, line: any, column: any, error: any) =>
      console.log(
        `global exception error ===> 【message: ${message}】,【url: ${url}】,【line: ${line}, column: ${column}】,【error: ${error}】`
      )
    // return (message: any, url: any, line: any, column: any, error: any) => console.table({message, url, line, column, error})
  }
  return () => {}
  // return (message: any, url: any, line: any, column: any, error: any) =>
  //   request({
  //     url: '/log/error',
  //     method: 'post',
  //     data: {
  //       msg: `global exception error ===> 【message: ${message}】,【url: ${url}】,【line: ${line}, column: ${column}】,【error: ${error}】`,
  //     },
  //   })
})()
