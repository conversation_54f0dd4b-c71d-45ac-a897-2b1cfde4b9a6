import request from '@/request'

/**
 * 二维码
 * @param params 
 * @returns 
 */
export function wechatQrcode(params: any) {
  return request({
    url: '/auth/business/wechat/qrcode',
    method: 'get',
    params
  })
}

/**
 * 检查二维码状态
 * @param params 
 * @returns 
 */
export function checkQrcode(params: any) {
  return request({
    url: '/biz/auth/wechat/check',
    method: 'get',
    params
  })
}

/**
 * 检查手机端二维码状态
 * @param params
 * @returns
 */
export function checkMobileQrcode(params: any) {
  return request({
    url: '/biz/auth/mobile/wechat/check',
    method: 'get',
    params
  })
}

/**
 * 检查扫码用户是否正确
 * @param params 
 * @returns 
 */
export function checkAccount(params: any) {
  return request({
    url: '/biz/business/checkAccount',
    method: 'get',
    params
  })
}

/**
 * 微信授权
 * @param data 
 * @returns 
 */
export function wechatAuth(data: any) {
  return request({
    url: '/biz/auth/wechat/oauth2',
    method: 'post',
    data
  })
}

/**
 * 检查申请人是否可用
 * @param data 
 * @returns 
 */
export function checkWechat(data: any) {
  return request({
    url: '/biz/auth/wechat/checkWechat',
    method: 'post',
    data
  })
}

/**
 * 检查手机号是否可用
 * @param data 
 * @returns 
 */
export function checkPhone(data: any) {
  return request({
    url: '/biz/auth/wechat/checkPhone',
    method: 'post',
    data
  })
}

/**
 * 加入商家
 * @param data 
 * @returns 
 */
export function joinBusiness(data: any) {
  return request({
    url: '/biz/auth/wechat/joinBusiness',
    method: 'post',
    data
  })
}

/**
 * 邀请子账号轮询检测
 * @param params 
 * @returns 
 */
export function checkBusiness(params: any) {
  return request({
    url: '/biz/auth/wechat/business/check',
    method: 'get',
    params
  })
}

/**
 * 获取阿里授权code
 * @returns 
 */
export function getAuthToken() {
  return request({
    url: '/biz/aliyun/getAuthToken',
    method: 'get',
  })
}

/**
 * 一键登录取号
 * @param params 
 * @returns 
 */
export function getPhoneWithToken(params: any) {
  return request({
    url: '/biz/aliyun/getPhoneWithToken',
    method: 'get',
    params
  })
}

/**
 * 手机号登录
 * @param data 
 * @returns 
 */
export function authPhoneLogin(data: any) {
  return request({
    url: '/biz/auth/phoneLogin',
    method: 'post',
    data
  })
}

/**
 * 取消订单（子账号入驻页）
 * @param params 
 * @returns 
 */
export function cancelMemberOrder(params: any) {
  return request({
    url: '/order/order/cancel-member-order-by-sub-account',
    method: 'put',
    params
  })
}

/**
 * 通过TICKET获取登录用户信息
 * @param params 
 * @returns 
 */
export function getLoginUserByTicket(params: any) {
  return request({
    url: '/biz/auth/wechat/get-login-user-by-ticket',
    method: 'get',
    params
  })
}