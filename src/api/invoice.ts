import request from '@/request'

/**
 * 发票管理-数量统计
 * @returns 
 */
export function invoiceStatistics() {
  return request({
    url: '/order/invoice/company-invoice-statistics',
    method: 'get',
  })
}

/**
 * 发票管理-申请开票
 * @param data 
 * @returns 
 */
export function applyForBilling(data: any) {
  return request({
    url: '/order/invoice/apply-for-billing',
    method: 'post',
    data
  })
}

/**
 * 发票管理-申请重开
 * @param data 
 * @returns 
 */
export function applyForReopening(data: any) {
  return request({
    url: '/order/invoice/apply-for-reopening',
    method: 'post',
    data
  })
}

/**
 * 发票管理-取消开票
 * @param params 
 * @returns 
 */
export function cancelInvoice(params: any) {
  return request({
    url: '/order/invoice/cancel-invoice',
    method: 'post',
    params
  })
}

/**
 * 发票管理-去除发票新标记
 * @param params 
 * @returns 
 */
export function removeInvoiceNewFlag(params: any) {
  return request({
    url: '/order/invoice/remove-invoice-new-flag',
    method: 'get',
    params
  })
}

/**
 * 发票管理-未开票列表
 * @param params 
 * @returns 
 */
export function companyNotInvoicedList(params: any) {
  return request({
    url: '/order/invoice/company-not-invoiced-list',
    method: 'get',
    params
  })
}

/**
 * 发票管理-待开票列表
 * @param params 
 * @returns 
 */
export function toBeInvoicedList(params: any) {
  return request({
    url: '/order/invoice/to-be-invoiced-list',
    method: 'get',
    params
  })
}

/**
 * 发票管理-已开票列表
 * @param params 
 * @returns 
 */
export function invoicedFinishList(params: any) {
  return request({
    url: '/order/invoice/invoice-finish-list',
    method: 'get',
    params
  })
}

/**
 * 发票管理-发票详情
 * @param params 
 * @returns 
 */
export function getInvoiceDetail(params: any) {
  return request({
    url: '/order/invoice/company-invoice-detail',
    method: 'get',
    params
  })
}

/**
 * 发票管理-订单详情
 * @param params 
 * @returns 
 */
export function getInvoiceOrderDetail(params: any) {
  return request({
    url: '/order/invoice/company-not-invoiced-order-detail',
    method: 'get',
    params
  })
}