import request from '@/request'

/**
 * 获取商家余额流水列表
 * @param params
 * @returns
 */
export function businessBalanceFlowList(params: any) {
  return request({
    url: '/biz/business/businessBalanceFlowList',
    method: 'get',
    params,
  })
}

/**
 * 创建充值订单
 * @param data
 * @returns
 */
export function createWalletTopUpOrder(data: any) {
  return request({
    url: '/biz/business/businessBalancePrepay/online/save',
    method: 'post',
    data,
  })
}

/**
 * 钱包充值订单提交凭证信息
 * @param data
 * @returns
 */
export function walletTopUpSubmitCredential(data: any) {
  return request({
    url: '/biz/business/businessBalancePrepay/online/submit-credential',
    method: 'post',
    data,
  })
}

/**
 * 钱包充值订单详情
 * @param id
 * @returns
 */
export function walletTopUpOrderDetail(id: any) {
  return request({
    url: `/biz/business/businessBalancePrepay/online/${id}`,
    method: 'get',
  })
}

/**
 * 钱包充值订单记录
 * @param params
 * @returns
 */
export function walletTopUpOrderRecord(params: any) {
  return request({
    url: `/biz/business/businessBalancePrepay/online/list`,
    method: 'get',
    params,
  })
}

/**
 * 取消钱包充值订单
 * @param params
 * @returns
 */
export function cancelWalletTopUpOrder(params: any) {
  return request({
    url: `/biz/business/online/cancel`,
    method: 'post',
    params,
  })
}

/**
 * 查询线上钱包充值未支付数据
 * @param
 * @returns
 */
export function getOnlineUnPay() {
  return request({
    url: `/biz/business/online/getOnlineUnPay`,
    method: 'get',
  })
}
