import request from '@/request'

/**
 * 裂变入口配置
 * @returns 
 */
export function fissionActivityConfig() {
  return request({
    url: '/system/config/fissionActivity',
    method: 'get',
  })
}

/**
 * 获取裂变会员折扣
 * @returns 
 */
export function getFissionMemberDiscount() {
  return request({
    url: '/system/config/getFissionMemberDiscount',
    method: 'get',
  })
}

/**
 * 获取裂变折扣
 * @returns 
 */
export function getFissionDiscountV1() {
  return request({
    url: '/biz/fission-channel/getFissionDiscountV1',
    method: 'get',
  })
}

/**
 * 获取裂变活动规则
 * @returns 
 */
export function getFissionAgreement() {
  return request({
    url: '/biz/text/fission-agreement',
    method: 'get',
  })
}
