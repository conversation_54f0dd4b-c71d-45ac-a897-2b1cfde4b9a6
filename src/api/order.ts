import request from '@/request'

/**
 * 创建订单
 * @param data
 * @returns
 */
export function createOrder(data: any) {
  return request({
    url: '/order/order/create-order',
    method: 'post',
    data,
  })
}
/**
 * 抓取产品图信息
 * @param data
 * @returns
 */
export function getProductInfo(data: any) {
  return request({
    url: '/biz/amazon/product/info',
    method: 'post',
    data,
  })
}
/**
 * 创建订单数量
 * @returns
 */
export function createOrderCount() {
  return request({
    url: '/order/order/get-user-account-create-order-count',
    method: 'get',
  })
}
/**
 * 创建订单-加入购物车
 * @param data
 * @returns
 */
export function createOrderAddCart(data: any) {
  return request({
    url: '/order/order/add-cart',
    method: 'post',
    data,
  })
}

/**
 * 重新加入购物车
 * @param params
 * @returns
 */
export function rejoinCart(params: any) {
  return request({
    url: '/order/order/rejoin-cart',
    method: 'post',
    params,
  })
}

/**
 * 购物车删除模特
 * @param data
 * @returns
 */
export function updateCartIntentionModel(data: any) {
  return request({
    url: '/order/order/update-cart-intention-model',
    method: 'post',
    data,
  })
}

/**
 * 编辑购物车
 * @param data
 * @returns
 */
export function orderEditCart(data: any) {
  return request({
    url: '/order/order/edit-cart',
    method: 'post',
    data,
  })
}
/**
 * 购物车数量统计
 * @returns
 */
export function orderCartCount() {
  return request({
    url: '/order/order/cart-count',
    method: 'get',
  })
}
/**
 * 修改订单详情
 * @param data
 * @returns
 */
export function updateOrder(data: any) {
  return request({
    url: `/order/order`,
    method: 'put',
    data,
  })
}
/**
 * 获取修改订单详情
 * @param id
 * @returns
 */
export function updateOrderDetails(id: any) {
  return request({
    url: `/order/order/${id}`,
    method: 'get',
  })
}
/**
 * 获取购物车订单详情
 * @param id
 * @returns
 */
export function updateCartOrderDetails(id: any) {
  return request({
    url: `/order/order/query-cart/${id}`,
    method: 'get',
  })
}
/**
 * 订单总数
 * @returns
 */
export function orderListCount() {
  return request({
    url: '/order/order/get-order-count',
    method: 'get',
  })
}
/**
 * 工作台-订单列表
 * @returns
 */
export function accountOrderList() {
  return request({
    url: '/order/order/accountOrderList',
    method: 'get',
  })
}
/**
 * 订单列表
 * @param params
 * @returns
 */
export function orderList(params: any) {
  return request({
    url: '/order/order/list',
    method: 'get',
    params,
  })
}
/**
 * 需支付-订单列表
 * @param params
 * @returns
 */
export function needPayOrderList(params: any) {
  return request({
    url: '/order/merge/need-pay-order',
    method: 'get',
    params,
  })
}
/**
 * 订单列表-下单运营
 * @param params
 * @returns
 */
export function orderUserList(params: any) {
  return request({
    url: '/order/order/user/nickname/list',
    method: 'get',
    params,
  })
}
/**
 * 订单详情
 * @param id
 * @returns
 */
export function orderDetails(id: any) {
  return request({
    url: `/order/order/detail/company/${id}`,
    method: 'get',
  })
}
/**
 * 合并订单
 * @param data
 * @returns
 */
export function mergeOrder(data: any) {
  return request({
    url: `/order/merge/merge-order`,
    method: 'post',
    data,
  })
}
/**
 * 取消合并订单-校验会到期的订单
 * @param params
 * @returns
 */
export function checkCancelMergeOrder(params: any) {
  return request({
    url: `/order/merge/check-cancel-merge`,
    method: 'get',
    params,
  })
}
/**
 * 取消合并订单
 * @param params
 * @returns
 */
export function cancelMergeOrder(params: any) {
  return request({
    url: `/order/merge/cancel-merge`,
    method: 'post',
    params,
  })
}
/**
 * 开启订单
 * @param params
 * @returns
 */
export function reopenOrder(params: any) {
  return request({
    url: `/order/order/reopenOrder`,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
/**
 * 查询视频订单历史变更记录
 * @param videoId
 * @returns
 */
export function getVideoHistoryChangeRecord(videoId: any) {
  return request({
    url: `/order/order/get-video-history-change-record/${videoId}`,
    method: 'get',
  })
}
/**
 * 视频订单发货信息
 * @param videoId
 * @returns
 */
export function orderShippingInfo(videoId: any) {
  return request({
    url: `/order/order/shipping-info/${videoId}`,
    method: 'get',
  })
}
/**
 * 订单购物车列表
 * @param params
 * @returns
 */
export function orderCartList(params: any) {
  return request({
    url: '/order/order/cart-list',
    method: 'get',
    params,
  })
}
/**
 * 删除购物车订单
 * @param data
 * @returns
 */
export function deleteOrderCart(data: any) {
  return request({
    url: '/order/order/delete-cart',
    method: 'delete',
    data,
  })
}

/**
 * 复制购物车订单
 * @param params
 * @returns
 */
export function copyOrderCart(params: any) {
  return request({
    url: '/order/order/copy-cart',
    method: 'post',
    params,
  })
}

/**
 * 购物车结算
 * @param data
 * @returns
 */
export function cartSettleAccounts(data: any) {
  return request({
    url: '/order/order/cart-settle-accounts',
    method: 'post',
    data,
  })
}

/**
 * 下单运营下拉框
 */
export function cartCreateUserSelect() {
  return request({
    url: '/order/order/cart-create-user-select',
    method: 'get',
  })
}

/**
 * 取消订单
 * @param data
 * @returns
 */
export function cancelOrder(params: any) {
  return request({
    url: '/order/order/cancel',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
/**
 * 订单各个状态统计
 * @returns
 */
export function orderStatusCount() {
  return request({
    url: '/order/order/merchant-status-count',
    method: 'get',
  })
}
/**
 * 工作台-订单各个状态统计
 * @returns
 */
export function workbenchOrderStatusCount() {
  return request({
    url: '/order/order/workbenchOrderStatusCount',
    method: 'get',
  })
}
/**
 * 查询商家信息列表
 * @param params
 * @returns
 */
export function bizMerchantList(params: any) {
  return request({
    // url: '/biz/merchant/list',
    url: '/biz/business/businessAccountList',
    method: 'get',
    params,
  })
}

/**
 * 查询下单运营
 * @param params
 * @returns
 */
export function orderMerchantList(params: any) {
  return request({
    url: '/order/order/company-order-user-select',
    method: 'get',
    params,
  })
}

// 查询视频订单历史拍摄要求和限制条件
// export function replyCaseInfoList(id: any) {
//   return request({
//     url: `/order/order/case/info/${id}`,
//     method: 'get',
//   })
// }
/**
 * 查询视频订单匹配情况反馈
 * @param id
 * @returns
 */
export function replyCaseList(id: any) {
  return request({
    url: `/order/order/case/${id}`,
    method: 'get',
  })
}
/**
 * 商家回复匹配情况反馈
 * @param data
 * @returns
 */
export function replyCase(data: any) {
  return request({
    url: '/order/order/reply-video-case',
    method: 'post',
    data,
  })
}
/**
 * 根据订单id查询商家反馈素材
 * @param videoId
 * @returns
 */
export function feedbackList(videoId: any) {
  return request({
    url: `/order/feedback/list?videoId=${videoId}`,
    method: 'get',
  })
}

/**
 * 校验发货地址
 * @param params
 * @returns
 */
export function checkShippingAddress(params: any) {
  return request({
    url: '/order/order/check-shipping-address',
    method: 'post',
    params,
  })
}
/**
 * 发货 or 补发
 * @param data
 * @returns
 */
export function orderShipping(data: any) {
  return request({
    url: '/order/order/shipping',
    method: 'post',
    data,
  })
}
/**
 * 标记发货
 * @param data
 * @returns
 */
export function orderShippingFlag(data: any) {
  return request({
    url: '/order/order/shipping-flag',
    method: 'post',
    data,
  })
}
/**
 * 更换模特
 * @param data
 * @returns
 */
export function orderChangeModel(data: any) {
  return request({
    url: '/order/order/change-model',
    method: 'post',
    data,
  })
}
/**
 * 确认模特
 * @param params
 * @returns
 */
export function orderConfirmModel(params: any) {
  return request({
    url: '/order/order/confirm-model',
    method: 'post',
    params,
  })
}

/**
 * 商家允许 or 取消 运营上传素材至平台
 * @param params
 * @returns
 */
export function allowUpload(params: any) {
  return request({
    url: '/order/order/allow-upload',
    method: 'post',
    params,
  })
}

/**
 * 确认成品 / 确认素材
 * @param params
 * @returns
 */
export function orderAffirm(data: any) {
  return request({
    url: '/order/order/affirm',
    method: 'post',
    data,
  })
}

/**
 * 余额支付
 * @param data
 * @returns
 */
export function orderBalancePay(data: any) {
  return request({
    url: '/order/order/balancePay',
    method: 'post',
    data,
  })
}

/**
 * 订单支付页信息
 * @param data
 * @returns
 */
export function orderPayInfo(data: any) {
  return request({
    url: '/order/pay/pay-info',
    method: 'post',
    data,
  })
}
/**
 * 视频订单支付锁定
 * @param data
 * @returns
 */
export function orderPayLock(data: any) {
  return request({
    url: '/order/order/payLock',
    method: 'post',
    data,
  })
}
/**
 * 会员订单支付锁定
 * @param data
 * @returns
 */
export function orderMemberPayLock(data: any) {
  return request({
    url: '/order/order/member/payLock',
    method: 'post',
    data,
  })
}
/**
 * 钱包充值订单支付锁定
 * @param data
 * @returns
 */
export function walletTopUpPayLock(data: any) {
  return request({
    url: '/order/order/online-recharge/payLock',
    method: 'post',
    data,
  })
}

/**
 * 订单支付二维码
 * @param data
 * @returns
 */
export function orderPayCode(data: any) {
  return request({
    url: '/order/pay/code',
    method: 'post',
    data,
  })
}

/**
 * 检查订单支付状态
 * @param data
 * @returns
 */
export function orderPayCheck(data: any) {
  return request({
    url: '/order/pay/check',
    method: 'post',
    data,
  })
}

/**
 * 提交凭证信息
 * @param data
 * @returns
 */
export function orderUploadCredentials(data: any) {
  return request({
    url: '/order/order/submit-credential',
    method: 'post',
    data,
  })
}

/**
 * 获取凭证单信息
 * @param params
 * @returns
 */
export function getDocumentInfo(params: any) {
  return request({
    url: '/order/order/get-document-info',
    method: 'post',
    // headers: {
    //   'Content-Type': 'application/x-www-form-urlencoded'
    // },
    params,
  })
}

/**
 * 申请退款
 * @param data
 * @returns
 */
export function applyRefund(data: any) {
  return request({
    url: '/order/refund/apply-refund',
    method: 'post',
    data,
  })
}

/**
 * 申请退款-获取退款信息
 * @param data
 * @returns
 */
export function getRefundInfo(data: any) {
  return request({
    url: '/order/refund/get-refund-info',
    method: 'post',
    data,
  })
}

/**
 * 商家取消退款
 * @param data
 * @returns
 */
export function cancelRefund(data: any) {
  return request({
    url: '/order/refund/cancel-refund',
    method: 'post',
    data,
  })
}

/**
 * 退款订单列表
 * @param params
 * @returns
 */
export function orderRefundList(params: any) {
  return request({
    url: '/order/refund/list',
    method: 'get',
    params,
  })
}

/**
 * 催一催
 * @param params
 * @returns
 */
export function doRemind(params: any) {
  return request({
    url: '/order/reminder',
    method: 'post',
    params,
  })
}

/**
 * 获取上传素材详情
 * @param videoId
 * @returns
 */
export function getUploadMaterial(videoId: any) {
  return request({
    url: `/order/order/get-upload-material/${videoId}`,
    method: 'get',
  })
}

/**
 * 吐槽订单
 * @param data
 * @returns
 */
export function doRoast(data: any) {
  return request({
    url: '/order/roast',
    method: 'post',
    data,
  })
}
/**
 * 系统吐槽
 * @param data
 * @returns
 */
export function systemRoast(data: any) {
  return request({
    url: '/order/roast/system',
    method: 'post',
    data,
  })
}
/**
 * 获取收款人账号配置表
 * @param params
 * @returns
 */
export function getValidConfig(params: any) {
  if (params.detailId) {
    return request({
      url: `/order/payee/info/detail/${params.detailId}`,
      method: 'get',
    })
  }
  if (params.payeeId) {
    return request({
      url: `/order/payee/info/${params.payeeId}`,
      method: 'get',
    })
  }
  return request({
    url: `/order/payee/type/${params.type}`,
    method: 'get',
  })
}

/**
 * 获取当前商家可参与的活动以及是否弹窗
 * @returns
 */
export function getBusinessParticipatoryActivity() {
  return request({
    url: '/order/promotion/get-business-participatory-activity',
    method: 'get',
  })
}

/**
 * 获取当前商家可参与的活动以及是否弹窗
 * @returns
 */
export function getRemindBusinessMemberHalfPriceRenewal() {
  return request({
    url: '/order/promotion/get-remind-business-member-half-price-renewal',
    method: 'get',
  })
}

/**
 * 获取当前服务费配置
 * @returns
 */
export function getServiceFeeConfig() {
  return request({
    url: '/order/price/config/service/active',
    method: 'get',
  })
}
///order/promotion/get-valid-promotion-activity-list
export function getValidPromotionActivityList() {
  return request({
    url: '/order/promotion/get-valid-promotion-activity-list',
    method: 'get',
  })
}
