import request from '@/request'

/**
 * 查询字典数据列表
 * @param params 
 * @returns 
 */
export function listData(params: any) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params
  })
}

/**
 * 查询字典数据详细
 * @param dictCode 
 * @returns 
 */
export function getData(dictCode: string) {
  return request({
    url: '/system/dict/data/' + dictCode,
    method: 'get'
  })
}

/**
 * 根据字典类型查询字典数据信息
 * @param dictType 
 * @returns 
 */
export function getDicts(dictType: string) {
  return request({
    url: '/system/dict/data/type/' + dictType,
    method: 'get'
  })
}

/**
 * 新增字典数据
 * @param data 
 * @returns 
 */
export function addData(data: any) {
  return request({
    url: '/system/dict/data',
    method: 'post',
    data
  })
}

/**
 * 修改字典数据
 * @param data 
 * @returns 
 */
export function updateData(data: any) {
  return request({
    url: '/system/dict/data',
    method: 'put',
    data
  })
}

/**
 * 删除字典数据
 * @param dictCode 
 * @returns 
 */
export function delData(dictCode: string) {
  return request({
    url: '/system/dict/data/' + dictCode,
    method: 'delete'
  })
}
