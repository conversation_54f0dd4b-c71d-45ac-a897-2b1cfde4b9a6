import { ref } from 'vue'
import { defineStore } from 'pinia'
// import router from '../../router'
import { removeToken } from '@/utils/auth'
import { logoutApi, getUserInfoApi } from '@/api/user'
import { recursionMenuTree } from '../../utils/util'
import type { routerListData } from '../../router/type'
import { menuData, routerData, type AuthType } from '../../router/routerData'
import type { RouteRecordRaw } from 'vue-router'
import { orderCartCount, orderCartList } from '@/api/order'
import type { OrderUserInfo, ModelInfo, LimitingCondition, UrlItem } from '@/views/order/type'
import { ElLoading } from 'element-plus'
import { showWelcomeDialog } from '@/hooks/useWelcome'
import { openLogin } from '@/hooks/useLogin'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { handelRenewDialog } = useShowRenewDialog()

export type userInfo = {
  id: number
  bizUserId: number | null
  lastLoginTime: string
  account: number
  businessId: number
  seedId: string
  name: string
  nickName: string
  pic: string
  status: number
  userStatus: number
  isOwnerAccount: number
  businessVO?: businessVO | null
  businessAccountVOS?: businessAccountVOS
  [x: string]: any
}
export type businessAccountVOS = userInfo[] | null | undefined
export type businessVO = {
  balance: number
  isBalanceLock: number
  customerType: number
  id: number
  invoiceContent: string
  invoiceDutyParagraph: string
  invoiceTitle: string
  invoiceTitleType: number
  isProxy: number
  memberCode: string
  memberFirstTime: string
  memberLastTime: string
  memberPackageType: number
  memberStatus: number
  memberType: number
  memberValidity: string
  name: string
  ownerAccount: number
  scale: number | null
  status: number
  waiterId: number
}
export type shoppingCartList = {
  amount: number
  createOrderBusiness: OrderUserInfo
  createOrderUser: OrderUserInfo
  createTime: string
  id: number
  intentionModel: ModelInfo
  modelType: number
  picCount: number
  platform: number
  productChinese: string
  productEnglish: string
  productLink: string
  referencePic: UrlItem[]
  referenceVideoLink: string
  shootRequired: LimitingCondition[]
  shootingCountry: number
  videoFormat: number
}[]

// const importComponent = (component: string) => import(component)

// 退出登录后不刷新页面的路由
export const notRefreshRouter = ['/activity/fission']
const noShowRenewRouter = ['/center/pay', '/order/pay', '/center/info', , '/center/acc', '/model/list']

export const useUserStore = defineStore(
  'user',
  () => {
    let lastLoginTime = ref(0)
    let userInfo = ref<userInfo>({
      lastLoginTime: '',
      account: 0,
      businessId: 0,
      seedId: '',
      name: '',
      nickName: '',
      pic: '',
      status: 0,
      userStatus: 0,
      isOwnerAccount: 0,
      id: 0,
      bizUserId: null,
    })
    let shoppingCartCount = ref(0)
    let shoppingCartList = ref<shoppingCartList>([])
    let menuList = ref<routerListData>(recursionMenuTree(menuData))
    let routerList = ref<RouteRecordRaw[]>([])

    /**
     * 获取用户信息
     * @returns
     */
    function getUserInfo() {
      return new Promise<any>((resolve, reject) => {
        getUserInfoApi()
          .then(res => {
            if (res.data.userStatus === 0) {
              setUserInfo(res.data)
              let pathname = window.location.pathname
              if (!noShowRenewRouter.includes(pathname)) {
                handelRenewDialog()
              }
              resolve(res.data)
            } else {
              removeInfo()
              reject(res.data)
            }
          })
          .catch(e => {
            if (e.message && (e.message.includes('Network Error') || e.message.includes('timeout'))) {
              reject('network')
            } else {
              removeInfo()
              reject(e)
            }
          })
      })
    }

    function setUserInfo<T extends userInfo>(uInfo: T) {
      userInfo.value = uInfo
      lastLoginTime.value = uInfo.lastLoginTime
        ? new Date(uInfo.lastLoginTime).getTime()
        : new Date().getTime()
      setUserRouterList()
      if (isOwnerAcc() && isVip() && userInfo.value.businessVO?.scale == null) {
        showWelcomeDialog({
          businessName: userInfo.value.businessVO?.name,
          name: userInfo.value.name,
        })
      }
    }
    function setUserRouterList() {
      menuList.value = recursionMenuTree(menuData)
      routerList.value = recursionMenuTree(routerData)
    }
    async function logout() {
      const el_loading = ElLoading.service({
        lock: true,
        text: '',
        background: 'rgba(255, 255, 255, 0.7)',
      })
      try {
        await logoutApi()
        removeToken()
        removeInfo()
        el_loading.close()
        let pathname = window.location.pathname
        if (notRefreshRouter.includes(pathname)) {
          openLogin()
          return
        }
        window.location.href = '/'
      } catch (error) {
        removeToken()
        removeInfo()
        el_loading?.close()
        window.location.href = '/'
      }
    }
    function removeInfo() {
      lastLoginTime.value = 0
      userInfo.value = {
        lastLoginTime: '',
        account: 0,
        businessId: 0,
        seedId: '',
        name: '',
        nickName: '',
        pic: '',
        status: 0,
        userStatus: 0,
        isOwnerAccount: 0,
        id: 0,
        bizUserId: null,
      }
      shoppingCartCount.value = 0
      // menuList.value = []
      routerList.value = []
      sessionStorage.removeItem('wnkj-user')
    }
    /**
     * 是否是主账号
     * @returns
     */
    function isOwnerAcc() {
      return userInfo.value.isOwnerAccount === 1
    }
    /**
     * 是否是代理
     * @returns
     */
    function isProxy() {
      return userInfo.value.businessVO?.isProxy === 1
    }
    /**
     * 是否是会员
     * @returns
     */
    function isVip() {
      return (
        userInfo.value.account &&
        userInfo.value.businessVO?.memberCode &&
        (userInfo.value.businessVO?.memberStatus === 1 || userInfo.value.businessVO?.memberStatus === 2)
      )
    }
    /**
     * 是否是开通过会员
     * @returns
     */
    function isViped() {
      return userInfo.value.account && userInfo.value.businessVO?.memberStatus !== 0
    }
    /**
     * 实时检测vip状态 获取最新用户信息 避免页面过长时间未更新
     * @returns reslove状态表示已更新最新用户信息成功
     */
    function realTimeCheckVip() {
      return new Promise<any>(async (resolve, reject) => {
        let check = false
        if (isVip()) {
          let time = userInfo.value.businessVO?.memberValidity || ''
          let now = new Date(time + '23:59:59').getTime()
          check = now - new Date().getTime() < 1000 * 60 * 60 * 24
        } else {
          check = true
        }
        if (check) {
          const el_loading = ElLoading.service({
            lock: true,
            text: '',
            background: 'rgba(0, 0, 0, 0.7)',
          })
          try {
            let res = await getUserInfo()
            el_loading.close()
            if (res) {
              resolve(res)
            }
          } catch (error) {
            el_loading.close()
            reject(error)
          }
        } else {
          resolve(true)
        }
      })
    }
    /**
     * 权限检查
     * @returns
     */
    function checkAuth(auth?: boolean | Array<AuthType>) {
      if (Array.isArray(auth) && auth.length) {
        if (auth.includes('ownerAcc')) {
          if (!isOwnerAcc()) return false
        }
        if (auth.includes('vip')) {
          if (!isVip()) return false
        }
        if (auth.includes('viped')) {
          if (!isViped()) return false
        }

        return true
      }

      if (auth === true) {
        return isOwnerAcc() && isVip()
      }
      return false
    }
    /**
     * 获取购物车数量
     */
    function getShoppingCartCount() {
      orderCartCount().then(res => {
        shoppingCartCount.value = res.data || 0
      })
    }
    /**
     * 获取购物车前11位数据
     */
    function getShoppingCartList() {
      orderCartList({
        pageNum: 1,
        pageSize: 11,
      }).then(res => {
        if (res.data) {
          shoppingCartList.value = res.data.rows || []
        }
      })
    }

    return {
      lastLoginTime,
      userInfo,
      shoppingCartCount,
      shoppingCartList,
      menuList,
      routerList,
      getUserInfo,
      setUserInfo,
      setUserRouterList,
      getShoppingCartCount,
      getShoppingCartList,
      logout,
      removeInfo,
      isOwnerAcc,
      isProxy,
      isVip,
      isViped,
      realTimeCheckVip,
      checkAuth,
    }
  },
  {
    persist: {
      key: 'wnkj-user',
      storage: sessionStorage,
      beforeRestore: () => {
        const user = sessionStorage.getItem('wnkj-user')
        if (user) {
          const lastLoginTime = JSON.parse(user).userLastLoginTime
          if (new Date().getTime() - lastLoginTime > 1000 * 60 * 60 * 2) {
            console.warn('store-user: sessionStorage-2h or 0')
            sessionStorage.removeItem('wnkj-user')
          }
        }
      }
    }
  }
)
