import type { App } from 'vue'
import loginModal from './modal/login-modal'
import drag from './drag'
import ellipsisTooltips from './text/ellipsisTooltips'
import buttonBlur from './button/blur'

export default function directive(app: App<Element>){
  app.directive('loginModal', loginModal)
  app.directive('drag', drag)
  app.directive('ellipsisTooltips', ellipsisTooltips)
  app.directive('btnBlur', buttonBlur)
}