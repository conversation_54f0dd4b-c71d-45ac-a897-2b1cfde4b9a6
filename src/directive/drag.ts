/**
 * v-drag 拖拽
 */
export default {
  mounted(el: Element, binding: any) {
    // console.log(el, binding);
    
    // 可拖拽元素 默认自身
    const dragBox = binding.value?.drag ? document.querySelector(binding.value.drag) : el
    // 可拖拽区域 默认body
    const rangeBox = binding.value?.range ? document.querySelector(binding.value.range) : document.body

    // console.dir(dragBox);
    // console.dir(rangeBox);
    if (binding.value?.range && rangeBox.style.position == '') {
      rangeBox.style.position = 'relative'
    }
    // 获取当前元素与父级元素的距离
    const parentBox = (elem: any, range: any, type: string) => {
      let offset: string = ''
      switch (type) {
        case 'top':
          offset = 'offsetTop'
          break
        case 'left':
          offset = 'offsetLeft'
          break
      }
      let elemDis = elem[offset] //获得elem元素距相对定位的父元素的top left
      let elems = elem.offsetParent //获取相对定位的父元素
      // debugger
      if(elems !== null) {
        if (range) {
          while (elems.className !== range && elems.nodeName !== 'BODY') {
            elemDis += elems[offset]
            elems = elems.offsetParent
          }
        } else {
          while (elems.nodeName !== 'BODY') {
            elemDis += elems[offset]
            elems = elems.offsetParent
          }
        }
      }
      // console.log('while',elems.className,elems.nodeName,binding.value.value.range.slice(1),elemDis);
      return elemDis
    }

    let isDown = false
    let mX: number,
      mY: number,
      mOffsetX,
      mOffsetY,
      currentX: number,
      currentY = 0
    let moveTop: number,
      moveLeft: number,
      moveRight: number,
      moveBottom = 0
    // 鼠标mousedown事件(按下)
    el.addEventListener('mousedown', (e) => {
      isDown = true
      // 元素距离可视区域距离
      mX = e.pageX
      mY = e.pageY
      // 鼠标点击位置与盒子的距离
      // mOffsetX = e.offsetX
      // mOffsetY = e.offsetY
      // console.log(mX,mY,'---',mOffsetX,mOffsetY,'---',e);
      // 是否有滚动条滚动的距离
      let scrollT = rangeBox.scrollTop
      let scrollL = rangeBox.scrollLeft
      // let scrollT = binding.value.value.scroll ? document.querySelector(binding.value.value.sroll).scrollTop : rangeBox.scrollTop
      // let scrollL = binding.value.value.scroll ? document.querySelector(binding.value.value.sroll).scrollLeft : rangeBox.scrollLeft

      let name = binding.value?.range ? binding.value.range.slice(1) : undefined
      // 可移动距离
      moveTop = parentBox(dragBox, name, 'top') + scrollT + (currentY ? currentY : 0)
      moveLeft = parentBox(dragBox, name, 'left') + scrollL + (currentX ? currentX : 0)
      moveRight =
        rangeBox.offsetWidth -
        (dragBox.offsetWidth + parentBox(dragBox, name, 'left')) -
        (currentX ? currentX : 0)
      moveBottom =
        rangeBox.offsetHeight -
        (dragBox.offsetHeight + parentBox(dragBox, name, 'top')) -
        (currentY ? currentY : 0)

      // console.log('top:', moveTop, 'left:', moveLeft, 'right:', moveRight, 'bottom:', moveBottom)
      // 鼠标mousemove事件(移动)
      document.addEventListener('mousemove', move)
      // 鼠标mouseup事件(抬起)
      document.addEventListener('mouseup', up)
    })
    // 鼠标移动
    const move = (e: { pageX: number; pageY: number }) => {
      if (!isDown) return
      el.classList.add('move-modal')
      // 鼠标移动后距离 - 移动前距离
      let x = e.pageX - mX
      let y = e.pageY - mY

      // console.log('top:',moveTop,'left:',moveLeft,'right:',moveRight,'bottom:',moveBottom);
      // x、y 超出可拖动范围时停止增加x、y的值
      if (y <= -moveTop) {
        y = -moveTop
      }
      if (y >= moveBottom) {
        y = moveBottom
      }
      if (x <= -moveLeft) {
        x = -moveLeft
      }
      if (x >= moveRight) {
        x = moveRight
      }
      // console.log();
      // 移动时加上上次移动的距离
      if (currentX) {
        x += currentX
      }
      if (currentY) {
        y += currentY
      }

      // console.log("y:",y,'x:',x,);
      // 改变元素位置
      dragBox.style.cssText = `transform: translate3d(${x}px, ${y}px, 1px);`
    }

    const up = () => {
      isDown = false
      // 获取移动后的位置
      let formatValue = window.getComputedStyle(dragBox, null).transform.replace(/[^0-9\-\,\.]/g, '')
      let formatArr = formatValue.split(',')
      currentX = Number(formatArr[formatArr.length - 4])
      currentY = Number(formatArr[formatArr.length - 3])
      // console.log('currentXY:', currentX, currentY)
      // 删除移动事件
      document.removeEventListener('mousemove', move)
      document.removeEventListener('mouseup', up)
      el.classList.remove('move-modal')
    }
  },
}
