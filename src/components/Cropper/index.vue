<template>
  <div class="cropper-dialog-box">
    <div class="cropper-content">
      <div class="cropper" style="text-align: center; width: 70%" v-loading="loading">
        <VueCropper
          ref="cropperRef"
          :img="option.img"
          :outputSize="option.outputSize"
          :outputType="option.outputType"
          :info="option.info"
          :canScale="option.canScale"
          :autoCrop="option.autoCrop"
          :autoCropWidth="option.autoCropWidth"
          :autoCropHeight="option.autoCropHeight"
          :fixed="option.fixed"
          :fixedNumber="option.fixedNumber"
          :full="option.full"
          :fixedBox="option.fixedBox"
          :canMove="option.canMove"
          :canMoveBox="option.canMoveBox"
          :original="option.original"
          :centerBox="option.centerBox"
          :height="option.height"
          :infoTrue="option.infoTrue"
          :maxImgSize="option.maxImgSize"
          :enlarge="option.enlarge"
          :mode="option.mode"
          @realTime="realTime"
          @imgLoad="imgLoad"
        ></VueCropper>
      </div>

      <div class="action-bar">
        <div class="form-title">裁剪图片预览</div>
        <div
          class="previewsImgBox"
          :style="{
            width: previewsWidth + 'px',
            height: previewsHeight + 'px',
          }"
        >
          <img :style="{ ...previewStyle, zoom: previewZoom }" :src="previewsImg" />
        </div>

        <template v-if="isShowAction && !loading">
          <div class="form-title">图片缩放/旋转</div>
          <div class="form-content">
            <el-button size="small" type="primary" plain icon="ZoomIn" @click="changeScale(1)">
              放大
            </el-button>
            <el-button size="small" type="primary" plain icon="ZoomOut" @click="changeScale(-1)">
              缩小
            </el-button>
          </div>
          <div class="form-content">
            <el-button size="small" type="primary" plain @click="rotateLeft">↺ 左旋转</el-button>
            <el-button size="small" type="primary" plain @click="rotateRight">↻ 右旋转</el-button>
          </div>
          <div class="form-title">裁剪生成图片的质量</div>
          <div class="form-content" style="padding-right: 10px">
            <el-slider
              v-model="form.outputSize"
              :format-tooltip="formatTooltip"
              :min="1"
              @input="outputSizeChange()"
            ></el-slider>
          </div>
          <div class="form-title">裁剪生成图片的格式</div>
          <div class="form-content">
            <el-radio-group v-model="form.outputType" @change="outputTypeChange()" style="line-height: 22px">
              <el-radio :label="'png'">png</el-radio>
              <el-radio :label="'jpeg'">jpeg</el-radio>
              <el-radio :label="'webp'">webp</el-radio>
            </el-radio-group>
          </div>
          <div class="form-title between">
            <span>截图框宽高固定比例</span>
            <el-switch v-model="form.fixed" size="small" @change="fixedChange()"></el-switch>
          </div>
          <div class="form-content" v-if="form.fixed">
            <div style="margin-top: 8px">
              <el-radio-group
                v-model="form.fixedNumber"
                @change="fixedNumberChange()"
                style="line-height: 22px"
              >
                <el-radio v-for="(item, index) in fixedNumberArr" :key="index" :label="index">
                  {{ item.join(':') }}
                </el-radio>
                <el-radio :label="-1">自定义</el-radio>
              </el-radio-group>
            </div>
            <div class="fixedNumberInput" v-if="form.fixedNumber === -1">
              <el-input-number
                size="small"
                v-model="fixedNumberInput.a"
                :min="1"
                :max="20"
                @change="fixedNumberCustom(0)"
              ></el-input-number>
              <span style="width: 60px; text-align: center">:</span>
              <el-input-number
                size="small"
                v-model="fixedNumberInput.b"
                :min="1"
                :max="20"
                @change="fixedNumberCustom(0)"
              ></el-input-number>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import 'vue-cropper/dist/index.css'
import { VueCropper } from 'vue-cropper'
import { ref, watch, nextTick } from 'vue'
//   import { throttle } from '@/utils/public'
import aegis from '@/utils/aegis'

const props = defineProps({
  // 图片文件
  img: {
    type: Object,
    default: () => {},
  },
  // 截图框比例
  fixedNumberArr: {
    type: Array,
    default: () => [
      [16, 9],
      [9, 16],
    ],
  },
  // 预览图宽度
  previewsWidth: {
    type: Number,
    default: 230,
  },
  // 是否可编辑
  isShowAction: {
    type: Boolean,
    default: true,
  },
})

defineExpose({
  finish, // 裁剪
})

const emits = defineEmits(['loadImg', 'setImg', 'closeCropper', 'error'])

const loading = ref(false)

watch(
  () => props.img,
  newVal => {
    loading.value = true
    nextTick(() => {
      try {
        let fr = new FileReader()
        fr.readAsDataURL(newVal.raw)
        fr.onloadend = (f) => {
          if(option.value.img == f.target.result) emits('loadImg', 'success')
          option.value.img = f.target.result
          loading.value = false
        }
        // if (newVal?.raw) {
        //   if (window.createObjcectURL != undefined) {
        //     option.value.img = window.createObjectURL(newVal.raw)
        //   } else if (window.URL != undefined) {
        //     option.value.img = window.URL.createObjectURL(newVal.raw)
        //   } else if (window.webkitURL != undefined) {
        //     option.value.img = window.webkitURL.createObjectURL(newVal.raw)
        //   } else {
        //     option.value.img = newVal?.url || ''
        //   }
        // }
      } catch (error) {
        option.value.img = ''
        emits('error', error)
      }
    })
  },
  {
    immediate: true,
    deep: true,
  }
)

const cropperRef = ref()

const dialogVisible = ref(false)
const option = ref({
  img: '', // 裁剪图片的地址
  outputSize: 1, // 裁剪生成图片的质量
  outputType: 'png', // 裁剪生成图片的格式（jpeg || png || webp）
  full: true, // 是否输出原图比例的截图
  info: true, // 图片大小信息
  canScale: true, // 图片是否允许滚轮缩放
  autoCrop: true, // 是否默认生成截图框
  autoCropWidth: 300, // 默认生成截图框宽度
  autoCropHeight: 300, // 默认生成截图框高度
  canMove: true, // 上传图片是否可以移动
  fixedBox: false, // 固定截图框大小 不允许改变
  fixed: true, // 是否开启截图框宽高固定比例
  fixedNumber: [3, 3], // 截图框的宽高比例
  canMoveBox: true, // 截图框能否拖动
  original: false, // 上传图片按照原始比例渲染
  centerBox: true, // 截图框是否被限制在图片里面
  height: true, // 是否按照设备的dpr 输出等比例图片
  infoTrue: true, // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
  enlarge: 1, // 图片根据截图框输出比例倍数
  mode: 'contain', // 图片默认渲染方式(fill, contain, cover, auto, 100px, 100%)
  maxImgSize: 1000, // 限制图片最大宽度和高度
})
const picsList = ref([]) //页面显示的数组
const previewsImg = ref('') // 实时预览图片
const previewZoom = ref(1)
const previewStyle = ref({})
const previewsHeight = ref(200)

// 操作栏表单
const form = ref({
  outputSize: 100, // 裁剪生成图片的质量
  outputType: 'png', // 裁剪生成图片的格式（jpeg || png || webp）
  fixed: true, // 是否开启截图框宽高固定比例
  fixedNumber: 0, // 截图框的宽高比例
})
// 截图框自定义比例
const fixedNumberInput = ref({
  a: 1,
  b: 1,
})

// 初始化函数
function imgLoad(msg) {
  console.log('cropper-init=====' + msg)
  aegis.info('vue-cropper-init=====' + msg)
  if(msg === 'success') {
    fixedChange()
  }
  emits('loadImg', msg)
}
// 图片缩放
function changeScale(num) {
  num = num || 1
  cropperRef.value.changeScale(num)
}
// 向左旋转
function rotateLeft() {
  cropperRef.value.rotateLeft()
}
// 向右旋转
function rotateRight() {
  cropperRef.value.rotateRight()
}
// 实时预览函数
function realTime(data) {
  // console.log('===data===',data);
  previewsImg.value = data.url
  previewStyle.value = data.img
  previewZoom.value = props.previewsWidth / data.w
  // throttleFn()
}
function blobToDataURL(blob, callback) {
  let reader = new FileReader()
  reader.readAsDataURL(blob)
  reader.onload = function (e) {
    callback(e.target.result)
  }
}
// 节流
//   const throttleFn = throttle(function() {
//     cropperRef.value.getCropBlob(blob => {
//       blobToDataURL(blob, res => {
//         console.log('----realTime----')
//         previewsImg.value = res
//       })
//     })
//   }, 33)
// 将base64转换为文件
function dataURLtoFile(dataurl, filename) {
  let arr = dataurl.split(',')
  let mime = arr[0].match(/:(.*?);/)[1]
  let bstr = atob(arr[1])
  let len = bstr.length
  let u8arr = new Uint8Array(len)
  while (len--) {
    u8arr[len] = bstr.charCodeAt(len)
  }
  let name = filename.substring(0, filename.lastIndexOf('.') + 1) + form.value.outputType
  return new File([u8arr], name, { type: mime })
}
// 点击裁剪，这一步是可以拿到处理后的地址
function finish() {
  cropperRef.value.getCropData(data => {
    let file = dataURLtoFile(data, props.img.name)
    let newImg = {
      raw: file,
      size: file.size,
      name: file.name,
      url: data,
      status: 'ready',
      uid: new Date().getTime(),
    }
    // console.log('=======ccccc======',newImg);
    emits('setImg', newImg)
    emits('closeCropper')
  })
}

// 滑块 格式化 Tooltip
function formatTooltip(val) {
  return val / 100
}
// 裁剪生成图片的质量改变
function outputSizeChange() {
  // console.log(form.value.outputSize);
  option.value.outputSize = formatTooltip(form.value.outputSize)
}
// 裁剪生成图片的格式改变
function outputTypeChange() {
  // console.log(form.value.outputType);
  option.value.outputType = form.value.outputType
}
// 是否开启截图框宽高固定比例
function fixedChange() {
  // console.log(form.value.fixed);
  option.value.fixed = form.value.fixed
  if (option.value.fixed) {
    // if (form.value.fixedNumber === -1) {
    //   // 用于解决开启固定比例时截图框不更新问题
    //   option.value.autoCropWidth = 330
    //   option.value.autoCropHeight = 330
    //   return
    // }
    form.value.fixedNumber = props.fixedNumberArr.length ? 0 : -1
    fixedNumberChange()
  }
}
// 截图框的宽高比例
function fixedNumberChange() {
  // console.log(form.value.fixedNumber);
  form.value.fixedNumber !== -1 ? fixedNumberCustom(1) : fixedNumberCustom(0)
}
// 自定义比例 val = 1 固定比例   val != 1 自定义比例
function fixedNumberCustom(val) {
  if (val === 1) {
    option.value.fixedNumber = props.fixedNumberArr[form.value.fixedNumber]
    let num = props.fixedNumberArr[form.value.fixedNumber][0] < 5 ? 100 : 30
    option.value.autoCropWidth = props.fixedNumberArr[form.value.fixedNumber][0] * num
    option.value.autoCropHeight = props.fixedNumberArr[form.value.fixedNumber][1] * num
    // console.log(option.value.fixedNumber,option.value.autoCropWidth,option.value.autoCropHeight);
  } else {
    option.value.fixedNumber = [fixedNumberInput.value.a, fixedNumberInput.value.b]
    option.value.autoCropWidth = fixedNumberInput.value.a * (fixedNumberInput.value.a < 5 ? 100 : 30)
    option.value.autoCropHeight = fixedNumberInput.value.b * (fixedNumberInput.value.b < 5 ? 100 : 30)
  }
  previewsHeight.value = (
    (props.previewsWidth / option.value.fixedNumber[0]) *
    option.value.fixedNumber[1]
  ).toFixed(2)
}
</script>

<style lang="scss" scoped>
.between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cropper-dialog-box {
  .cropper-content {
    display: flex;
    justify-content: flex-start;

    .cropper {
      width: auto;
      height: 580px;
      margin-bottom: 15px;
    }

    .action-bar {
      margin-left: 10px;
      width: 30%;

      .form-title {
        padding-left: 15px;
        border-left: 4px solid #3b99fc;
        margin: 0 0 15px 20px;
      }
      .form-content {
        width: 100%;
        margin: 0 0 15px 0;
        padding: 0 0 0 20px;
        box-sizing: border-box;
      }
      .fixedNumberInput {
        margin-top: 8px;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        align-items: center;
      }
      .previewsImgBox {
        // display: flex;
        width: 230px;
        height: 230px;
        overflow: hidden;
        text-align: center;
        margin: 0 0 0 20px;
        border: 1px solid #3b99fc;
        // padding: 0 2px;
        box-sizing: border-box;
        margin-bottom: 10px;

        img {
          display: block;
          object-fit: contain;
          width: 100%;
          height: 200px;
        }
      }
    }
  }
}
</style>
