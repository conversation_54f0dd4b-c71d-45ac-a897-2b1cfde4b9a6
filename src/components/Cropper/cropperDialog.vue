<template>
  <el-dialog
    v-model="dialogVisible"
    width="70%"
    draggable
    v-bind="$attrs"
    :close-on-click-modal="false"
    append-to-body
    align-center
    @close="closeChange"
    :style="dialogStyle"
    style="min-width: 800px"
  >
    <Cropper
      ref="cropperRef"
      :img="img"
      :fixed-number-arr="fixedNumberArr"
      :previews-width="previewsWidth"
      :is-show-action="isShowAction"
      @set-img="handleSetImg"
      @load-img="handleLoadImg"
      @error="handleError"
    />
    <template v-if="showFooterButton" #footer>
      <div class="dialog-footer">
        <slot name="footerButton">
          <el-button @click="close">{{ cancelButtonText }}</el-button>
          <el-button type="primary" :disabled="!confirmBtn" @click="sub">
            {{ confirmButtonText }}
          </el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import Cropper from '@/components/Cropper/index'
import { ElMessage } from 'element-plus'
import { ref } from 'vue'

const props = defineProps({
  // 图片文件
  img: {
    type: Object,
    default: () => {},
  },
  // 截图框比例
  fixedNumberArr: {
    type: Array,
    default: () => [
      [16, 9],
      [9, 16],
    ],
  },
  // 预览图宽度
  previewsWidth: {
    type: Number,
    default: 230,
  },
  isShowAction: {
    type: Boolean,
    default: true,
  },
  showFooterButton: {
    type: Boolean,
    default: true,
  },
  beforeClose: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  cancelButtonText: {
    type: String,
    default: '取消',
  },
  confirmButtonText: {
    type: String,
    default: '确定',
  },
  dialogStyle: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits(['confirm', 'close', 'before-close'])

defineExpose({
  open,
  close,
})

const dialogVisible = ref(false)
const confirmBtn = ref(false)
const cropperRef = ref()

// 打开
function open() {
  dialogVisible.value = true
  confirmBtn.value = false
}
// 关闭
function close() {
  // 关闭前回调
  if (props.beforeClose) {
    emits('before-close', () => (dialogVisible.value = false))
    return
  }
  dialogVisible.value = false
}
// 确定
function sub() {
  cropperRef.value.finish()
  // emits('confirm')
}
// 关闭时回调
function closeChange() {
  // 关闭前回调
  if (props.beforeClose) {
    emits('before-close', () => (dialogVisible.value = false))
    return
  }
  emits('close')
}

function handleSetImg(newImg) {
  // console.log(newImg);
  emits('confirm', newImg)
  dialogVisible.value = false
}

function handleLoadImg(msg) {
  if (msg === 'success') {
    confirmBtn.value = true
  } else {
    dialogVisible.value = false
    ElMessage.error('cropper init error')
  }
}
function handleError() {
  dialogVisible.value = false
}
</script>

<style scoped lang="scss"></style>
