<template>
  <dialog
    class="dialog-comp"
    ref="dialogRef"
    :style="{ width: width, 'font-size': fontSize, ...dialogStyle }"
    data-dialog-comp="1"
    @click.stop="dialogClick"
    @close="closeChange"
  >
    <div class="dialog-box">
      <div v-if="showTitle" class="header" :class="{ center: center }">
        <slot name="header">
          <span class="title">{{ title }}</span>
          <div class="close-btn" @click="close"></div>
        </slot>
      </div>
      <template v-else>
        <slot name="close">
          <!-- <i style="font-size: 12px;">
            <svg style="width: 22px;height: 22px;" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" data-v-6fbb019e="">
              <path fill="currentColor" d="m466.752 512-90.496-90.496a32 32 0 0 1 45.248-45.248L512 466.752l90.496-90.496a32 32 0 1 1 45.248 45.248L557.248 512l90.496 90.496a32 32 0 1 1-45.248 45.248L512 557.248l-90.496 90.496a32 32 0 0 1-45.248-45.248z"></path>
              <path fill="currentColor" d="M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"></path>
            </svg>
          </i> -->
          <div class="close-round-btn" @click="close"></div>
        </slot>
      </template>
      <div class="main" :style="{padding}">
        <slot></slot>
      </div>
      <div v-if="showButton" class="footer" :class="{ center: center }">
        <slot name="footer">
          <div class="btn cancal-btn" @click="close">取消</div>
          <div class="btn confirm-btn" @click="sub">确定</div>
        </slot>
      </div>
    </div>
  </dialog>
  <dialog
    class="dialog-comp"
    ref="confirmDialog"
    style="width: 30%; max-width: 500px"
    :style="{ 'font-size': fontSize }"
  >
    <div class="dialog-box">
      <div class="header" :class="{ center: center }">
        <span class="title">{{ confirmTitle }}</span>
        <div class="close-btn" @click="confirmClose"></div>
      </div>
      <div class="main-confirm">
        {{ confirmMessage }}
      </div>
      <div class="footer">
        <div class="btn cancal-btn" @click="confirmClose">取消</div>
        <div class="btn confirm-btn" @click="confirm">确定</div>
      </div>
    </div>
  </dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const dialogRef = ref()
const confirmDialog = ref()

type CompProps = {
  title?: string
  width?: string
  fontSize?: string
  dialogStyle?: {[x: string]: any}
  padding?: string
  center?: boolean
  modal?: boolean
  modalClickClose?: boolean
  showTitle?: boolean
  showButton?: boolean
  beforeClose?: boolean
  confirmTitle?: string
  confirmMessage?: string
}

const props = withDefaults(defineProps<CompProps>(), {
  title: '提示',
  width: '35%',
  fontSize: '14px',
  dialogStyle: () => ({}),
  padding: '20px',
  center: false, // 标题 按钮 居中
  showTitle: true, // 显示隐藏标题
  showButton: true, // 显示隐藏按钮
  modal: true, // 遮罩层
  modalClickClose: true, // 点击遮罩层关闭
  beforeClose: false, // 关闭前回调
  confirmTitle: '关闭提示',
  confirmMessage: '确认关闭？',

})

const emits = defineEmits(['confirm', 'close'])

defineExpose({
  open,
  close,
})

// 打开
function open() {
  props.modal ? dialogRef.value.showModal() : dialogRef.value.show()
}
// 关闭
function close() {
  // 关闭前回调
  if (props.beforeClose) {
    confirmDialog.value.showModal()
    return
  }
  dialogRef.value.close()
}

function dialogClick(e: any) {
  // 点击遮罩层关闭弹窗
  if (props.modalClickClose && e.target?.className === 'dialog-comp' && e.target?.dataset.dialogComp === '1') {
    close()
  }
}

// 确定
function sub() {
  emits('confirm')
}

// 关闭时回调
function closeChange() {
  emits('close')
}

// 确认弹窗-取消
function confirmClose() {
  confirmDialog.value.close()
}
// 确认弹窗-确认
function confirm() {
  confirmDialog.value.close()
  dialogRef.value.close()
}
</script>

<style lang="scss" scoped>
dialog:focus-visible {
  outline: 0 !important;
}
.dialog-comp {
  box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
  border-radius: 4px;
  border: 0;
  box-sizing: border-box;
  padding: 0;
  position: fixed;
  top: 0px;
  bottom: 0px;
  left: 0px;
  right: 0px;
  margin: auto;

  &::backdrop {
    background: #7070708c;
    backdrop-filter: blur(2px);
  }

  .dialog-box {
    width: 100%;
    // height: 100%;
    .header {
      position: relative;
      height: 44px;
      // border-bottom: 1px solid #ccc;
      padding: 0 16px;
      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      align-content: center;

      .title {
        font-size: 16px;
      }

      .close-btn {
        position: absolute;
        right: 16px;
        width: 16px;
        height: 14px;
        cursor: pointer;
      }
      .close-btn::before,
      .close-btn::after {
        position: absolute;
        content: '';
        background: #555;
        left: 8px;
        width: 1px;
        height: 12px;
      }
      .close-btn::before {
        transform: rotate(45deg);
      }
      .close-btn::after {
        transform: rotate(-45deg);
      }
    }
    .close-round-btn {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 10px;
      height: 10px;
      padding: 4px;
      border: 1px solid var(--border-color);
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      align-content: center;
      z-index: 99999;

      &::before,
      &::after {
        // position: absolute;
        content: '';
        background: #555;
        left: 9px;
        width: 2px;
        height: 10px;
      }
      &::before {
        transform: rotate(45deg);
        margin-right: -1px;
      }
      &::after {
        transform: rotate(-45deg);
        margin-left: -1px;
      }
      &:hover {
        box-shadow: 0 0 4px var(--modal-bg);
      }
    }

    .main {
      padding: 20px;
    }
    .main-confirm {
      padding: 10px 20px;
    }

    .footer {
      height: 50px;
      // border-top: 1px solid #ccc;
      padding: 0 16px;
      box-sizing: border-box;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }

    .center {
      justify-content: center;
    }

    .start {
      justify-content: flex-start;
    }

    .end {
      justify-content: flex-end;
    }

    .btn {
      display: inline-block;
      line-height: 1;
      white-space: nowrap;
      cursor: pointer;
      background: #fff;
      border: 1px solid #dcdfe6;
      color: #606266;
      -webkit-appearance: none;
      text-align: center;
      box-sizing: border-box;
      outline: none;
      margin: 0;
      transition: 0.1s;
      font-weight: 500;
      -moz-user-select: none;
      -webkit-user-select: none;
      -ms-user-select: none;
      padding: 9px 15px;
      font-size: 12px;
      border-radius: 3px;
    }
    .confirm-btn {
      color: #fff;
      background-color: #409eff;
      border-color: #409eff;
      margin-left: 10px;

      &:focus,
      &:hover {
        color: #fff;
        border-color: #79bbff;
        background-color: #79bbff;
        outline: 0;
      }
      &:active {
        color: #fff;
        border-color: #337ecc;
        background-color: #337ecc;
        outline: 0;
      }
    }
    .cancal-btn {
      &:focus,
      &:hover {
        color: #409eff;
        border-color: #c6e2ff;
        background-color: #ecf5ff;
        outline: 0;
      }
      &:active {
        color: #409eff;
        border-color: #409eff;
        background-color: #ecf5ff;
        outline: 0;
      }
    }
  }
}
</style>
