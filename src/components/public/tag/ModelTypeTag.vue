<template>
  <template v-for="item in modelTypeOptions" :key="item.value">
    <el-text
      class="flex-start model-type-color"
      :style="{ color, 'font-size': size }"
      v-if="type == item.value || type == '3'"
    >
      <el-icon v-if="isIcon" style="width: 0.87em">
        <PriceTag style="transform: rotateZ(-45deg) rotateY(20deg);" />
      </el-icon>
      <svg v-else class="svg-icon" aria-hidden="true">
        <use v-if="item.value == '0'" xlink:href="#icon-yingxinagzhe"></use>
        <use v-else xlink:href="#icon-suren"></use>
      </svg>
      {{ item.label }}
    </el-text>
  </template>
</template>

<script setup lang="ts">
import { modelTypeOptions } from '@/utils/data'
defineProps({
  type: {
    type: [Number, String],
  },
  isIcon: {
    type: Boolean,
    default: false,
  },
  color: {
    type: String,
    default: '#6a7484',
  },
  size: {
    type: String,
  },
})
</script>

<style scoped lang="scss">
.model-type-color {
  gap: 3px;

  img {
    width: 16px;
    height: 16px;
  }

  .el-icon svg {
    min-width: 13px;
    min-height: 13px;
  }
}
</style>
