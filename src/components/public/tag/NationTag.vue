<template>
  <template v-for="(item, i) in nationOptions" :key="item.value">
    <el-text
      class="flex-start"
      :class="{ 'color-write': colorWrite, 'nation-color': !colorWrite }"
      :style="{ color, 'font-size': size }"
      v-if="type == item.value"
    >
      <!-- <img src="@/assets/image/icon/address.png" > -->
      <!-- <svg class="svg-icon" aria-hidden="true">
        <use xlink:href="#icon-dingwei"></use>
      </svg> -->
      <!-- <i class="iconfont icon-dingwei"></i> -->
      <el-icon v-if="isIcon"><Location style="margin: 1px 2px 0 0;" /></el-icon>
      <svg v-else class="icon" aria-hidden="true">
        <use :xlink:href="nationalFlag[i]"></use>
      </svg>
      <span>{{ item.label }}</span>
    </el-text>
  </template>
</template>

<script setup lang="ts">
import { nationOptions } from '@/utils/data'
import { ref } from 'vue'
defineProps({
  type: {
    type: [Number, String],
  },
  colorWrite: {
    type: Boolean,
    default: false,
  },
  isIcon: {
    type: Boolean,
    default: false,
  },
  color: {
    type: String,
    default: '#6a7484',
  },
  size: {
    type: String,
  },
})

const nationalFlag = ref([
  '#icon-meiguoguoqi',
  '#icon-jianada',
  '#icon-DE',
  '#icon-faguoguoqi',
  '#icon-yidali',
  '#icon-xibanya',
  '#icon-yingguoguoqi',
])
</script>

<style scoped lang="scss">
@import '@/assets/iconfont/iconfont.css';
.icon {
  width: 21px;
  height: 21px;
}

.color-write {
  color: #fff;
  gap: 2px;
  font-size: 0.8vw;
}

.nation-color {
  color: #6c9eff;
  gap: 2px;
}

.el-icon svg {
  min-width: 13.5px;
  min-height: 13.5px;
}
</style>
