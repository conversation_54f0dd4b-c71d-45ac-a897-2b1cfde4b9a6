<template>
  <span v-if="store.userInfo.account && store.isOwnerAcc()" class="owner-tag owner-1">主账号</span>
  <span v-if="store.userInfo.account && !store.isOwnerAcc()" class="owner-tag owner-0">子账号</span>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/stores/modules/user'

const store = useUserStore()
</script>

<style lang="scss" scoped>
.owner-tag {
  font-size: 12px;
  font-weight: 300;
  line-height: 18px;
  text-align: center;
  padding: 0px 7px;
  border-radius: 4px;

  &.owner-1 {
    color: #1D3EBE;
    background-color: #DCE1FF;
  }
  &.owner-0 {
    color: #1D7FBF;
    background-color: #D5EEFF;
  }
}
</style>