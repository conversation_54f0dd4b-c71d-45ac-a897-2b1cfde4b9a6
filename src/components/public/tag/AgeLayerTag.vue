<template>
  <template v-for="item in ageLayerOptions" :key="item.value">
    <el-text class="flex-start age-layer-color" v-if="type == item.value">
      <!-- <img :src="getImageUrl(item.icon)" > -->
      <svg class="svg-icon" aria-hidden="true">
        <use v-if="item.value == '1'" xlink:href="#icon-yingerpiaozhichi"></use>
        <use v-else-if="item.value == '2'" xlink:href="#icon-ertong-06"></use>
        <use v-else-if="item.value == '3'" xlink:href="#icon-chengnianren"></use>
        <use v-else xlink:href="#icon-laonianren"></use>
      </svg>
      {{ item.label }}
    </el-text>
  </template>
</template>

<script setup lang="ts">
import { ageLayerOptions } from '@/utils/data'
import img1 from '@/assets/image/icon/age1.png'

const imgs = [
  img1,
]

defineProps({
  type: {
    type: [Number, String]
  }
})

function getImageUrl(icon: string) {
  let url = imgs.find(item => item.indexOf(icon) != -1)
  return url || ''
}
</script>

<style scoped lang="scss">
.age-layer-color {
  color: rgb(61, 61, 61);
  gap: 3px;

  img {
    width: 18px;
    height: 18px;
  }
}
</style>