<template>
  <template v-for="item in modelPlatformOptions" :key="item.value">
    <el-text class="flex-start model-platform-color" v-if="platform(item.value)">
      <img src="@/assets/image/icon/platform.png" >
      {{ item.label }}
    </el-text>
  </template>
</template>

<script setup lang="ts">
import { modelPlatformOptions } from '@/utils/data'

const props = defineProps({
  type: {
    type: [Number, String]
  }
})

const platform = (val: any) => {
  if(typeof props.type === 'string') {
    let arr = props.type.split(',') || ''
    return arr.includes(val)
  }
  if(typeof props.type === 'number') {
    return val == props.type
  }
  return false
}
</script>

<style scoped lang="scss">
.model-platform-color {
  color: #13227A;
  gap: 3px;

  img {
    width: 18px;
    height: 18px;
  }
}
</style>