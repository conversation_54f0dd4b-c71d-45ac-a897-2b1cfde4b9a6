<template>
  <el-text class="flex-start sex-color-1" v-if="type == 1">
    <!-- <img src="@/assets/image/icon/sex1.png" > -->
    <svg class="svg-icon" aria-hidden="true">
      <use xlink:href="#icon-nanxing"></use>
    </svg>
    男性
  </el-text>
  <el-text class="flex-start sex-color-0" v-else>
    <!-- <img src="@/assets/image/icon/sex0.png" > -->
    <svg class="svg-icon" aria-hidden="true">
      <use xlink:href="#icon-nvxing"></use>
    </svg>
    女性
  </el-text>
</template>

<script setup lang="ts">
defineProps({
  type: {
    type: [String, Number],
    default: 1
  }
})
</script>

<style scoped lang="scss">
.sex-color-0 {
  color: rgb(246, 150, 97);
  gap: 3px;
}
.sex-color-1 {
  color: rgb(18, 150, 219);
  gap: 3px;
}
</style>