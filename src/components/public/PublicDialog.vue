<template>
  <el-dialog
    :modal-class="dialogPadding ? 'public-dialog' : ''"
    v-model="dialogVisible"
    :title="title"
    align-center
    v-bind="$attrs"
    :show-close="!customClose"
    :before-close="beforeClose"
    @close="closeChange"
    style="border-radius: var(--dialog-radius); padding: 10px 0 20px"
    :style="dialogStyle"
  >
    <template v-if="customClose" #header="{ close, titleId, titleClass }">
      <slot name="header">
        <div
          v-if="titleCenter"
          class="flex-center dialog-header"
          :style="{ borderBottom: titleLine ? '1px solid #e4e8eb' : 'none' }"
          :class="{ 'header-border': title }"
        >
          <h4 v-if="title" :id="titleId" :class="titleClass">{{ title }}</h4>
          <div class="close-round-btn" @click="close"></div>
        </div>
        <div v-else class="header-left dialog-header" style="height: 50px">
          <h4 v-if="title" :id="titleId" :class="titleClass">{{ title }}</h4>
          <div class="close-round-btn" @click="close"></div>
        </div>
      </slot>
    </template>
    <slot></slot>
    <template v-if="showFooterButton" #footer>
      <div class="dialog-footer">
        <slot name="footerButton">
          <el-button @click="close">{{ cancelButtonText }}</el-button>
          <el-button type="primary" @click="sub">
            {{ confirmButtonText }}
          </el-button>
        </slot>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  customClose?: boolean
  showFooterButton?: boolean
  beforeClose?: Function
  title?: string
  cancelButtonText?: string
  confirmButtonText?: string
  dialogStyle?: object
  titleCenter?: boolean
  dialogPadding?: boolean
  titleLine?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  customClose: false,
  showFooterButton: true,
  title: '',
  cancelButtonText: '取消',
  confirmButtonText: '确定',
  dialogStyle: () => ({}),
  titleCenter: true,
  dialogPadding: true,
  titleLine: true,
})

const emits = defineEmits(['confirm', 'close'])

const dialogVisible = ref(false)

defineExpose({
  open,
  close,
})

// 打开
function open() {
  dialogVisible.value = true
}
// 关闭
function close() {
  dialogVisible.value = false
}
// 确定
function sub() {
  emits('confirm')
}

// 关闭时回调
function closeChange() {
  emits('close')
}
</script>

<style scoped lang="scss">
.header-left {
  display: flex;
  align-items: center;
  margin-left: 20px;
}
.dialog-header {
  height: 10px;

  &.header-border {
    border-bottom: 1px solid #e4e8eb;
    height: 50px;
  }

  h4 {
    margin: 0 0 5px;
  }
}
</style>

<style lang="scss">
.public-dialog {
  .el-dialog {
    .el-dialog__body {
      padding: 0 20px;
    }
  }
}
</style>
