<template>
  <div class="title">
    <div class="mark"></div>
    <slot></slot>
  </div>
</template>

<script setup lang="ts">

</script>

<style scoped lang="scss">
.title {
  position: relative;
  color: var(--text-color);
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  line-height: 16px;
  padding: 0 0 0 8px;

  .mark {
    position: absolute;
    left: 0;
    top: 0;
    border-radius: 5px;
    width: 4px;
    height: 100%;
    background: #5695C0;
  }
}
</style>