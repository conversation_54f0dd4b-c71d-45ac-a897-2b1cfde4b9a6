<template>
  <el-upload
    list-type="picture-card"
    v-bind="$attrs"
    ref="uploadRef"
    :key="modelValue.length"
    :file-list="modelValue"
    action=""
    :accept="acceptType"
    :capture="isMobileDevice() ? 'user' : ''"
    :auto-upload="false"
    :on-change="handleUploadChange"
    :disabled="disabled"
    class="picture-card-upload"
    :class="{
      hidden: disabled || (limit && modelValue.length >= limit),
    }"
  >
    <slot>
      <el-icon><Plus /></el-icon>
    </slot>
    <template #file="{ file }">
      <div style="width: 100%; height: 100%">
        <img class="el-upload-list__item-thumbnail" :src="$picUrl + file.url" alt="" />
        <span class="el-upload-list__item-actions">
          <span v-if="preview" class="el-upload-list__item-preview" @click="handlePreview(file)">
            <el-icon><View /></el-icon>
          </span>
          <span
            v-if="!disabled && download"
            class="el-upload-list__item-delete"
            @click="handleDownload(file)"
          >
            <el-icon><Download /></el-icon>
          </span>
          <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
            <el-icon><Delete /></el-icon>
          </span>
        </span>
      </div>
    </template>
  </el-upload>
</template>

<script setup lang="ts">
import { uploadCloudFile, type Bucket } from '@/api/index'
import { beforeUpload, isMobileDevice } from '@/utils/public'
import { ElLoading, ElMessage } from 'element-plus'
import { useViewer } from '@/hooks/useViewer'
import { nextTick, getCurrentInstance, ref, computed, type PropType } from 'vue'

const { showViewer } = useViewer()

const { proxy } = getCurrentInstance() as any

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  // 自动上传
  autoUpload: {
    type: Boolean,
    default: true,
  },
  // 预览
  preview: {
    type: Boolean,
    default: true,
  },
  // 下载
  download: {
    type: Boolean,
    default: false,
  },
  // 上传数量限制 0-不限制
  limit: {
    type: Number,
    default: 0,
  },
  // 文件大小 0-不限制
  fileSize: {
    type: Number,
    default: 0,
  },
  //可上传的文件类型
  fileType: {
    type: Array as PropType<string[]>,
    default: () => ['jpg', 'jpeg', 'png'],
  },
  accept: {
    type: String,
    default: '',
  },
  //上传类型
  bucketType: {
    type: String as PropType<Bucket>,
    default: '',
  },
})

const acceptType = computed(() => {
  if (props.accept) {
    return props.accept
  }
  if (props.fileType.length > 0) {
    return props.fileType.map(item => `.${item}`).join(',')
  }
  if (isMobileDevice()) {
    return 'image/*'
  }
  return ''
})

const emits = defineEmits(['update:modelValue', 'onPreview', 'confirm', 'change'])

const uploadRef = ref()

function handlePreview(file: any) {
  if (props.preview) {
    showViewer([file.url])
  }
  emits('onPreview', file)
}

function handleUploadChange(file: any) {
  if (props.limit && props.modelValue.length > props.limit) {
    ElMessage.warning(`最多可上传${props.limit}个文件`)
    return
  }
  if (!beforeUpload([file.raw], { size: props.fileSize, fileType: props.fileType })) {
    uploadRef.value?.handleRemove(file.raw)
    return
  }
  upload(file)
}

function handleRemove(file: any) {
  let i = props.modelValue.findIndex((item: any) => item.uid == file.uid || item.id == file.id)
  if (i > -1) {
    props.modelValue.splice(i, 1)
    validate()
  }
}

function handleDownload(file: any) {
  window.open(file.url)
}

function upload(file: any) {
  if (props.autoUpload) {
    const el_loading = ElLoading.service({
      lock: true,
      text: '上传中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const formData = new FormData()
    formData.append('file', file.raw)
    uploadCloudFile(formData, props.bucketType)
      .then((res: any) => {
        if (res.code == 200) {
          props.modelValue.push({
            ...res.data,
            url: res.data.picUrl.slice(-3) === 'gif' ? res.data.picUrl : res.data.picUrl + '!fullSize',
            uid: file.uid,
          })
          emits('change', file)
          validate()
        }
      })

      .finally(() => el_loading.close())
  }
  emits('confirm', file)
}

function validate() {
  nextTick(() => {
    proxy.$parent?.validate()
  })
}
</script>

<style scoped lang="scss">
.picture-card-upload {
  --picture-card-width: 80px;
  --picture-card-height: 80px;

  :deep(.el-upload-list__item) {
    width: var(--picture-card-width);
    height: var(--picture-card-height);

    img {
      object-fit: fill;
    }
  }
  :deep(.el-upload--picture-card) {
    width: var(--picture-card-width);
    height: var(--picture-card-height);
  }
  .el-upload-list--picture-card {
    .el-upload-list__item-actions {
      span + span {
        margin-left: 8px;
      }
    }
  }
}
.hidden {
  :deep(.el-upload--picture-card) {
    display: none;
  }
}
</style>
