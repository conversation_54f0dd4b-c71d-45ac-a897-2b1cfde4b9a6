<template>
  <el-upload
    class="upload-proof"
    ref="UploadProofRef"
    drag
    :multiple="multiple"
    action=""
    :accept="isMobileDevice() ? 'image/*' : ''"
    :capture="isMobileDevice() ? 'user' : ''"
    :auto-upload="false"
    :show-file-list="false"
    :disabled="(limit && limit <= modelValue.length) || disabled"
    :class="{ 'upload-disabled': (limit && limit <= modelValue.length) || disabled }"
    @change="handleChange"
  >
    <slot>
      <!-- <el-icon class="el-icon--upload" size="50"><upload-filled /></el-icon> -->
      <!-- <div class="el-upload__text flex-column">
        <span class="up-text">选择要上传的文件</span>
        <span>或将其拖放、粘贴到此处</span>
      </div> -->
      <div class="flex-center upload-container">
        <img class="icon-upload" src="@/assets/icon/icon_upload.png" alt="" />
        <div>
          <div class="up-text primary">点击选择要上传的文件</div>
          <div class="up-text">或将其拖放、复制到此处</div>
        </div>
      </div>
    </slot>
  </el-upload>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, type PropType, ref } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { uploadCloudFile, type Bucket } from '@/api/index'
import { handlePasteFile, beforeUpload, isMobileDevice } from '@/utils/public'

const UploadProofRef = ref()

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  title: {
    type: String,
    default: '文件上传',
  },
  // MB
  size: {
    type: Number,
    default: 10,
  },
  // 0不限制上传个数
  limit: {
    type: Number,
    default: 0,
  },
  fileType: {
    type: Array as PropType<string[]>,
    default: () => ['jpg', 'jpeg', 'png'],
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  bucket: {
    type: String as PropType<Bucket>,
    default: '',
  },
})

const emits = defineEmits(['update:modelValue', 'change'])

let uploadingNum = 0

function handlePaste(event: ClipboardEvent) {
  if ((props.limit && props.limit <= props.modelValue.length) || props.disabled) return
  handlePasteFile(event, {
    fileType: props.fileType,
    size: props.size,
  }).then(files => {
    if (files.length) {
      emits('change', files)
      upload(
        files.map(file => ({
          raw: file,
          name: file.name,
          size: file.size,
          status: 'ready',
          uid: file.uid,
        }))
      )
    }
  })
}

function handleChange(file: any, files: any) {
  emits('change', files)
  upload(files)
}

function upload(files: any[]) {
  if (props.limit && props.modelValue.length + files.length + uploadingNum > props.limit) {
    ElMessage.warning(`最多只能上传${props.limit}个文件`)
    return
  }
  if (
    beforeUpload(files, {
      size: props.size,
      fileType: props.fileType,
    })
  ) {
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在上传中，请稍后',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    let requests: any[] = []
    files.forEach(f => {
      const fromData = new FormData()
      fromData.append('file', f.raw)
      requests.push(uploadCloudFile(fromData, props.bucket))
    })
    uploadingNum += requests.length
    UploadProofRef.value?.clearFiles()
    Promise.all(requests)
      .then(res => {
        ElMessage.success('上传成功')
        res.forEach((item: any) => {
          props.modelValue.push(item.data)
        })
      })
      .finally(() => {
        uploadingNum -= requests.length
        UploadProofRef.value?.clearFiles()
        el_loading.close()
      })
  } else {
    UploadProofRef.value?.clearFiles()
  }
}

onMounted(() => {
  document.addEventListener('paste', handlePaste)
})
onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>

<style scoped lang="scss">
.upload-proof {
  position: relative;
  width: 100%;
  // height: 180px;

  :deep(.el-upload-dragger) {
    padding: 10px;
  }
  :deep(.el-upload-list) {
    max-height: 100px;
    overflow-y: auto;
  }
  :deep(.el-upload-list__item) {
    width: 100%;
    height: auto;
  }

  &.upload-disabled {
    &::after {
      content: '';
      display: block;
      cursor: not-allowed;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.4);
    }
  }
}

.el-icon--upload {
  color: var(--el-color-primary);
  margin-bottom: 0;
}
.el-upload__text {
  font-size: 14px;
  line-height: 20px;
}
.upload-container {
  gap: 20px;
  padding: 15px 10px;

  .icon-upload {
    width: 50px;
    height: 50px;
  }
  .up-text {
    text-align: left;
    color: var(--text-color);

    &.primary {
      font-size: 16px;
      color: var(--el-color-primary);
    }
  }
}
</style>
