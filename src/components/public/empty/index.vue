<template>
  <el-empty class="public-empty" :image="emptyImg" :image-size="imageSize">
    <template #description>
      <div class="empty-desc">
        <slot name="description">
          {{ description }}
        </slot>
      </div>
    </template>
    <template #default>
      <slot name="default"></slot>
    </template>
  </el-empty>
</template>

<script setup>
import NotContent from '@/assets/image/empty/not-content.png'
import NotContentBlue from '@/assets/image/empty/not-content-blue.png'
import Network from '@/assets/image/empty/network.png'
import Order from '@/assets/image/empty/order.png'
import Search from '@/assets/image/empty/search.png'
import Wallet from '@/assets/image/empty/wallet.png'
import Collect from '@/assets/image/empty/collect.png'
import OrderContent from '@/assets/image/empty/order-content.png'
import PayOrder from '@/assets/image/empty/pay-order.png'
import Video from '@/assets/image/empty/video.png'
import { computed } from 'vue'

const props = defineProps({
  description: {
    type: String,
    default: '暂无数据',
  },
  imageSize: {
    type: Number,
    default: 180,
  },
  imageType: {
    type: String,
    default: ''
  }
})

const emptyImg = computed(() => {
  if (props.imageType === 'not-content-blue') {
    return NotContentBlue
  }
  if (props.imageType === 'network') {
    return Network
  }
  if (props.imageType === 'order') {
    return Order
  }
  if (props.imageType === 'search') {
    return Search
  }
  if (props.imageType === 'wallet') {
    return Wallet
  }
  if (props.imageType === 'collect') {
    return Collect
  }
  if (props.imageType === 'order-content') {
    return OrderContent
  }
  if (props.imageType === 'pay-order') {
    return PayOrder
  }
  if (props.imageType === 'video') {
    return Video
  }
  return NotContent
})
</script>

<style lang="scss" scoped>
.public-empty {
  --empty-desc-color: var(--el-text-color-secondary);

  .empty-desc {
    font-size: 14px;
    color: var(--empty-desc-color);
    margin: 0;
  }
}
</style>
