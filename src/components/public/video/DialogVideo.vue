<template>
  <DialogComp
    ref="dialogCompRef"
    :show-title="false"
    :show-button="false"
    width="70%"
    padding="0"
    :dialog-style="{
      background: 'transparent',
      boxShadow: 'none',
      minWidth: '600px',
    }"
    @close="closeChange"
  >
    <template #close>
      <el-icon class="close-icon-1" :size="34" color="#ffffff99" @click="close()"><CircleClose /></el-icon>
      <el-icon class="close-icon-2" :size="18" color="#ffffff" @click="close()"><CloseBold /></el-icon>
    </template>
    <div class="player-box" v-if="isShow">
      <div class="flex-column loading-box">
        <el-icon v-show="loading" class="is-loading" size="25" color="var(--el-color-primary)">
          <Loading />
        </el-icon>
      </div>
      <VideoPlayer v-if="videoSrc" :src="videoSrc" @load="loading = true" />
    </div>
    <div class="problem">
      <span @click="openWin">播放遇到问题？点我查看</span>
    </div>
  </DialogComp>
</template>

<script setup lang="ts">
import VideoPlayer from './VideoPlayer.vue'
import DialogComp from '../DialogComp.vue'
import { ref } from 'vue'
import { isSafari } from '@/utils/public'

type CompProps = {
  videoSrc?: string
}

const props = withDefaults(defineProps<CompProps>(), {
  videoSrc: '',
})

const dialogCompRef = ref()

const loading = ref(false)

const isShow = ref(false)

const emits = defineEmits(['close', 'update:videoSrc'])

// 打开
const open = (() => {
  if (isSafari()) {
    isShow.value = false
    return () => {
      if (props.videoSrc) window.open(props.videoSrc)
    }
  } else {
    isShow.value = true
    return () => {
      loading.value = true
      dialogCompRef.value.open()
    }
  }
})()

function openWin() {
  if (props.videoSrc) window.open(props.videoSrc)
}

defineExpose({
  open,
})

// 关闭
function close() {
  dialogCompRef.value.close()
}
// 关闭时回调
function closeChange() {
  emits('update:videoSrc', '')
  emits('close')
}
</script>

<style scoped lang="scss">
.close-icon-1 {
  position: fixed;
  top: 30px;
  right: 30px;
  cursor: pointer;
}
.close-icon-2 {
  display: none;
}
.player-box {
  position: relative;

  .loading-box {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffffcc;
    z-index: -1;
    justify-content: center;
  }
}
.problem {
  text-align: right;
  color: #fff;
  font-size: 13px;

  span {
    cursor: pointer;
  }
}
@media screen and (max-width: 768px) {
  .close-icon-1 {
    display: none;
  }
  .close-icon-2 {
    display: block;
    position: fixed;
    top: 10px;
    right: 10px;
    cursor: pointer;
  }
  .problem {
    text-align: left;
    padding-left: 20px;
  }
}
</style>
