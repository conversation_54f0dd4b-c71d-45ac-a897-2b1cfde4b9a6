<template>
  <el-dialog
    v-model="videoVisible"
    modal-class="video-player-dialog"
    width="auto"
    style="--el-dialog-bg-color: #0000;--el-dialog-box-shadow: none;"
    :show-close="false"
    :close-on-click-modal="true"
    align-center
    destroy-on-close
    append-to-body
  >
    <div class="flex-center video-container">
      <el-icon class="video-close-icon" :size="34" color="#ffffff99" @click="close()">
        <CircleClose />
      </el-icon>
      <video controls autoplay width="auto" controlsList="nodownload" disablePictureInPicture>
        <source :src="videoUrl" type="video/mp4">
        您的浏览器不支持 video 标签。
      </video>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { CircleClose } from '@element-plus/icons-vue'
import { ref } from 'vue'

defineExpose({
  open,
  close
})

const videoVisible = ref(false)
const videoUrl = ref('')

function open(url: string) {
  videoUrl.value = url
  videoVisible.value = true
}

function close() {
  videoVisible.value = false
}

</script>

<style scoped lang="scss">
.video-close-icon {
  position: absolute;
  top: -35px;
  right: -35px;
  cursor: pointer;
  @include mediaTo('phone') {
    top: -35px;
    right: -23px;
  }
}
.video-container {
  position: relative;
  width: min-content;
  min-width: 260px;
  height: 60vh;
  min-height: 560px;
  margin: auto;
  background-color: #000;
  border-radius: 20px;

  @include mediaTo('phone') {
    height: min-content;
    min-height: 200px;
  }

  video {
    border: 5px solid #000;
    border-radius: 20px;
    max-height: 100%;

    @include mediaTo('phone') {
      max-width: 80vmin;
      max-height: 70vh;
    }
  }
}
</style>
