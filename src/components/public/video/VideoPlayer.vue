<template>
  <iframe
    ref="iframeRef"
    style="width: 100%; height: 90vh; min-height: 500px; border: 0"
    :src="src"
    allowfullscreen>
  </iframe>
  <!-- <div class="video-box">
    <video
      ref="videoPlayer"
      :src="src"
      controls
      autoplay
      @error="playError"
    >
    </video>
    <div class="text" v-if="err">
      视频播放错误
    </div>
  </div> -->
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

const iframeRef = ref()

const props = defineProps({
  src: {
    type: String,
    required: true,
  },
})

const emits = defineEmits(['load'])

onMounted(() => {
  iframeRef.value.onload = () => {
    emits('load')
  }
})

// const err = ref(false)

// function playError(e: Event) {
//   console.error(e)
//   err.value = true
// }
</script>

<style scoped lang="scss">
.video-box {
  position: relative;

  video {
    width: 100%;
    height: 100%;
  }

  .text {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
