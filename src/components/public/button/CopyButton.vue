<template>
  <el-button class="copy-button" :class="[props.type]" round plain size="small" v-bind="$attrs" @click="copy(props.copyContent)">
    <slot>复制</slot>
  </el-button>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'
import copy from '@/utils/copy'

type Type = 'primary' | 'success' | 'warning' | 'danger' | 'info'
const props = defineProps({
  type: {
    type: String as PropType<Type>,
    default: 'primary',
  },
  copyContent: {
    type: String,
    default: '',
  },
})
</script>

<style scoped lang="scss">
.copy-button {
  &.primary {
    color: var(--el-color-primary);
    &:not(.is-link) {
      border-color: var(--el-color-primary);
    }
  }
  &.warning {
    color: var(--el-color-warning);
    &:not(.is-link) {
      border-color: var(--el-color-warning);
    }
  }
  &.danger {
    color: var(--el-color-danger);
    &:not(.is-link) {
      border-color: var(--el-color-danger);
    }
  }
  &.info {
    color: var(--el-color-info);
    &:not(.is-link) {
      border-color: var(--el-color-info);
    }
  }
}
</style>
