<template>
  <el-button v-bind="$attrs" :loading="exportLoading" @click="handleExport">{{ exportBtnText }}</el-button>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { download } from '@/request'
import { ElMessageBox } from 'element-plus'

const props = defineProps({
  text: {
    type: String,
    default: '导出',
  },
  loadingText: {
    type: String,
    default: '导出中',
  },
  url: {
    type: String,
    required: true,
  },
  params: {
    type: [Object, Array, Function],
    default: () => ({}),
  },
  fileName: String,
  beforeConfirm: {
    type: Boolean,
    default: true,
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  message: {
    type: String,
    default: '确认导出！',
  },
  title: {
    type: String,
    default: '提示',
  },
  options: {
    type: Object,
    default: () => ({
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      // type: 'warning',
      // icon: 'Warning'
    }),
  },
})

const exportLoading = ref(false)
const exportBtnText = ref(props.text)

function handleExport() {
  if (!props.url) return
  if (props.beforeConfirm) {
    ElMessageBox.confirm(props.message, props.title, props.options)
      .then(() => {
        exportLoading.value = true
        exportBtnText.value = props.loadingText
        download(props.url, handleParams(), props.fileName, props.config).finally(() => {
          exportLoading.value = false
          exportBtnText.value = props.text
        })
      })
      .catch(() => {})
  } else {
    exportLoading.value = true
    exportBtnText.value = props.loadingText
    download(props.url, handleParams(), props.fileName, props.config).finally(() => {
      exportLoading.value = false
      exportBtnText.value = props.text
    })
  }
}

function handleParams() {
  if (typeof props.params === 'function') {
    return props.params()
  }
  return props.params
}
</script>
