<template>
    <div class="container" ref="containerRef">
        <slot></slot>
        <Teleport to="body">
            <Transition
                @beforeEnter="handleBeforeEnter"
                @enter="handleEnter"
                @afterEnter="handleAfterEnter"
            >
                <div
                    v-if="showMenu"
                    class="context-menu"
                    :style="{
                        left: x + 'px',
                        top: y + 'px'
                    }"
                >
                    <div class="menu-list">
                        <div
                            class="menu-item"
                            v-for="(item) in menu"
                            :key="item.label"
                            @click="handleClick(item)"
                        >
                         {{ item.label }}
                        </div>
                    </div>
                </div>
            </Transition>
        </Teleport>
    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useContextMenu } from '@/hooks/useContextMenu'

const props = defineProps({
    menu: {
        type: Array<{label: string, value: number}>,
        default: () => []
    }
})

const containerRef = ref<HTMLElement | null>(null)
const emit = defineEmits(['select'])
const { x, y, showMenu } = useContextMenu(containerRef)

function handleClick(item: any) {
    showMenu.value = false
    emit('select', item)
}

function handleBeforeEnter(e: Element) {
  if(e instanceof HTMLElement)
    e.style.height = '0'
}
function handleEnter(e: Element) {
  if(e instanceof HTMLElement){
    e.style.height = 'auto'
    const h = e.clientHeight
    e.style.height = '0'
    requestAnimationFrame(() => {
        requestAnimationFrame(() => {
            e.style.height = `${h}px`
            e.style.transition = `0.5s`
        })
    })
  }
}
function handleAfterEnter(e: Element) {
  if(e instanceof HTMLElement)
    e.style.transition = 'none'
}
</script>

<style lang="scss" scoped>
.context-menu {
    position: fixed;
    overflow: hidden;
    padding: 2px;
    z-index: 999;

    .menu-list {
        background-color: #fff;
        border-radius: 6px;
        box-shadow: 0 0 4px 0px #4f748680;

        .menu-item {
            padding: 6px 10px;
            font-size: 14px;
            color: #585858;
            min-width: 70px;

            &:hover {
                cursor: pointer;
                background-color: #b1c6dc26;
            }
        }
    }
}
</style>