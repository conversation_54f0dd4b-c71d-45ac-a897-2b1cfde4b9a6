<template>
  <el-select-v2
    class="select-v2"
    v-model="value"
    :options="list"
    :props="{
      label: keyLabel,
      value: keyValue,
    }"
    @change="change"
    @visible-change="visibleChange"
    filterable
    :multiple="multiple"
    :disabled="disabled"
    :remote="remote"
    :placeholder="placeholder"
    :remote-method="handleRemoteMethod"
    :loading="loading"
    clearable
    style="width: 240px"
    no-data-text="无数据"
    no-match-text="无匹配数据"
    v-bind="$attrs"
    ref="elSelectDropdownRef"
  >
  </el-select-v2>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
  },
  // 开启远程搜索
  remote: {
    type: Boolean,
    default: false,
  },
  // 搜索字段名
  keyword: {
    type: String,
    default: 'key',
  },
  keyValue: {
    type: String,
    default: 'value',
  },
  keyLabel: {
    type: String,
    default: 'label',
  },
  // 请求
  request: {
    type: Function,
    required: true,
  },
  requestParams: {
    type: Object,
    default: () => ({}),
  },
  // 请求赋值处理
  requestCallback: {
    type: Function,
    default: (data: any) => data,
  },
  placeholder: {
    type: String,
    default: '请选择',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

const elSelectDropdownRef = ref()

const emits = defineEmits(['update:modelValue', 'change'])

const list = ref<any[]>([])
const loading = ref<boolean>(false)
const value = ref<string | number | Array<string | number>>()

watch(
  () => props.modelValue,
  (val: any) => {
    value.value = val
  },
  {
    immediate: true,
  }
)

function visibleChange(visible: boolean) {
  if (!visible && props.remote) {
    list.value.length = 0
  }
}

let timer: any

const handleRemoteMethod = (query: string) => {
  if (timer) {
    clearTimeout(timer)
  }
  timer = setTimeout(() => {
    loading.value = true
    getList(query)
  }, 200)
}

function change(value: any) {
  emits('update:modelValue', value)
  let data = list.value.find(item => item[props.keyValue] == value)
  emits('change', value, data)
}

function getList(query?: string) {
  props
    .request({
      ...props.requestParams,
      [props.keyword]: query,
    })
    .then((res: any) => {
      list.value = props.requestCallback(res)
    })
    .finally(() => {
      loading.value = false
    })
}

if (!props.remote) {
  getList()
}
</script>

<style lang="scss">
.select-v2 {

  .el-select__wrapper.is-filterable.el-tooltip__trigger.el-tooltip__trigger {

    .el-select__selection.is-near {
      
      .el-select__selected-item {
        max-width: 40%;

        .el-tag {
          max-width: 100% !important;
        }
      }
    }
  }
}
</style>