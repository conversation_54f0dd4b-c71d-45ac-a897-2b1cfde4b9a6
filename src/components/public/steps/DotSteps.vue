<template>
  <div class="steps-box">
    <el-steps :active="active" :direction="direction" align-center>
      <el-step
        v-for="(step, index) in steps"
        :title="step.title"
        :description="step.description"
        :key="index"
        :style="{ width }"
      >
        <template #icon>
          <div class="step-icon" :class="{ 'is-border': isBorder, 'is-active': index <= active }"></div>
        </template>
        <template #title>
          <slot name="title" :step="step" :index="index"></slot>
        </template>
        <template #description>
          <slot name="description" :step="step" :index="index"></slot>
        </template>
      </el-step>
    </el-steps>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue'

type Steps = {
  title: string
  description?: string
  [key: string]: any
}[]

const props = defineProps({
  active: {
    type: Number,
    default: 1,
  },
  direction: {
    type: String as PropType<'horizontal' | 'vertical'>,
    default: 'horizontal',
  },
  steps: {
    type: Array as PropType<Steps>,
    default: () => [
      { title: 'Step 1', description: 'First step description' },
      { title: 'Step 2', description: 'Second step description' },
      { title: 'Step 3', description: 'Third step description' },
    ],
  },
  isBorder: {
    type: Boolean,
    default: false,
  },
  width: {
    type: String,
    default: '120px',
  },
})
</script>

<style scoped lang="scss">
.step-icon {
  width: 12px;
  height: 12px;
  margin-top: 2px;
  border: 2px solid #fff;
  border-radius: 50%;
  background-color: var(--el-text-color-placeholder);

  &.is-active {
    background-color: var(--el-color-primary);
  }

  &.is-border {
    margin-top: 2px;
    border-color: var(--el-text-color-placeholder);
    background-color: #fff;

    &.is-active {
      border-color: var(--el-color-primary);
      background-color: #fff;
    }
  }
}

:deep(.el-steps) {
  .el-step {
    &.is-horizontal {
      .el-step__head {

        .el-step__line {
          margin-top: 1px;
        }

        &.is-process {
          .el-step__line {
            background: linear-gradient(
              to right,
              var(--el-color-primary) 50%,
              var(--el-text-color-placeholder) 50%
            );
          }
        }
      }
    }
    &.is-vertical {
      min-height: 45px;
      
      .el-step__head {

        .el-step__line {
          top: 11px;
          bottom: -11px;
        }

        &.is-process {
          .el-step__line {
            background: linear-gradient(
              to bottom,
              var(--el-color-primary) 55%,
              var(--el-text-color-placeholder) 45%
            );
          }
        }
      }
      
    }
  }

  .el-step__head {
    .el-step__icon.is-icon {
      background: transparent;
    }
    &.is-finish {
      .el-step__line {
        background-color: var(--el-color-primary);
      }
    }
  }
  .el-step__main {
    margin-bottom: 10px;
    .is-process {
      &.el-step__title,
      &.el-step__description {
        color: var(--el-color-primary);
        font-weight: 400;
      }
    }
  }
}
</style>
