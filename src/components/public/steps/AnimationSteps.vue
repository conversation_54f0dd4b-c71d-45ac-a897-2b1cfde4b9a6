<template>
  <div class="a-steps-container">
    <el-steps style="width: 100%" :active="step" align-center process-status="finish" finish-status="success">
      <el-step
        ref="stepRef"
        v-for="(s, index) in steps"
        :class="{ 'is-active': index === step }"
        :title="s.title"
        :description="s.description"
        :key="index"
        :style="{ width }"
      />
    </el-steps>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, type PropType } from 'vue'

type Steps = {
  title: string
  description?: string
  [key: string]: any
}[]

const props = defineProps({
  active: {
    type: Number,
    default: 0,
  },
  steps: {
    type: Array as PropType<Steps>,
    default: () => [
      { title: 'Step 1', description: 'First step description' },
      { title: 'Step 2', description: 'Second step description' },
      { title: 'Step 3', description: 'Third step description' },
    ],
  },
  width: {
    type: String,
    default: '100%',
  },
})

const step = ref(0)
const stepRef = ref<any[] | null>(null)
let timer: any

watch(() => props.active, (newValue, oldValue) => {
  if (newValue > 0 && newValue > oldValue && newValue <= props.steps.length) {
    step.value = newValue
    handleAnimation(newValue)
  } else {
    clearClass()
    step.value = 0
  }
})

function handleAnimation(index: number) {
  if (stepRef.value && stepRef.value[index]) {
    let head = stepRef.value[index].$el?.children[0]
    let icon = stepRef.value[index].$el?.children[0]?.children[1]
    let main = stepRef.value[index].$el?.children[1]
    if (icon && main) {
      timer = setTimeout(() => {
        head?.classList.remove('is-wait')
        head?.classList.add('is-finish')
        icon.classList.add('step-active-animation-box')
        icon.addEventListener('animationend', function () {
          icon.classList.remove('step-active-animation-box')
        })
        for (const element of main.children) {
          element.classList.remove('is-wait')
          element.classList.add('is-finish')
        }
        main.classList.add('step-active-animation-text')
        main.addEventListener('animationend', function () {
          main.classList.remove('step-active-animation-text')
        })
      }, 1000)
    }
  }
}

function clearClass() {
  if (timer) {
    clearTimeout(timer)
  }
  if (stepRef.value) {
    stepRef.value.forEach((item, index) => {
      if (index && item.$el.children?.length) {
        let head = item.$el?.children[0]
        let main = item.$el?.children[1]
        if (head) {
          head.classList.remove('is-finish')
          head.classList.add('is-wait')
        }
        if (main) {
          for (const element of main.children) {
            element.classList.remove('is-finish')
            element.classList.add('is-wait')
          }
        }
      }
    })
  }
}
</script>

<style scoped lang="scss">
.a-steps-container {
  width: 100%;

  :deep(.el-steps) {

    .is-active {
      .is-finish {
        .el-step__icon {
          background-color: var(--el-color-primary);
          color: #fff;
        }
      }
    }

    .el-step:first-child {

      .el-step__head {
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
      }
      .el-step__title {
        color: var(--el-color-primary);
      }
      .el-step__description {
        color: var(--el-color-primary);
      }
    }

    .el-step__head {
      .el-step__icon {
        transition: 0s;
      }
      // .el-step__main {
      //   transition: .15s ease-out;
      //   transition-delay: 2s;
      // }
      &.is-success {
        border-color: var(--el-color-primary);
        color: var(--el-color-primary);
  
        .el-step__line {
          .el-step__line-inner {
            transition: 1s !important;
            border-width: 1px !important;
            width: 100% !important;
          }
        }
      }
    }
    .el-step__main {

      .is-success {
        &.el-step__title {
          color: var(--el-color-primary);
        }
        &.el-step__description {
          color: var(--el-color-primary);
        }
      }
    }
  }
}
</style>

<style>
/* 图标动画 */
.step-active-animation-box {
  box-shadow: 0px 0px 1px #5695c0;
  animation: step-animate-box 1s linear;
}

@keyframes step-animate-box {
  0% {
    box-shadow: 0px 0px 1px #5695c0;
  }
  25% {
    box-shadow: 0px 0px 12px #5695c0;
  }
  50% {
    box-shadow: 0px 0px 5px #5695c0;
  }
  75% {
    box-shadow: 0px 0px 10px #5695c0;
  }
  100% {
    box-shadow: 0px 0px 1px #5695c0;
  }
}

/* 文字动画 */
.step-active-animation-text {
  /* text-shadow: 0px 0px 1px #5695c0; */
  /* animation: step-animate-text 1s linear; */
}

@keyframes step-animate-text {
  0% {
    text-shadow: 0px 0px 1px #5695c0;
  }
  25% {
    text-shadow: 0px 0px 6px #5695c0;
  }
  50% {
    text-shadow: 0px 0px 3px #5695c0;
  }
  75% {
    text-shadow: 0px 0px 6px #5695c0;
  }
  100% {
    text-shadow: 0px 0px 1px #5695c0;
  }
}
</style>
