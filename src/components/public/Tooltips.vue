<template>
  <el-tooltip
    v-model:visible="tooltipVisible"
    :content="tooltipContent"
    raw-content
    v-bind="tooltipsOptions"
    virtual-triggering
    :virtual-ref="tooltipsTriggerRef"
    popper-class="public-tooltips"
    :key="tooltipKey"
  />
</template>

<script setup lang="ts">
import { getTooltipFields } from '@/hooks/useTooltips'

const { tooltipsOptions, tooltipKey, tooltipVisible, tooltipContent, tooltipsTriggerRef } = getTooltipFields()
</script>

<style lang="scss">
.public-tooltips {
  background: rgb(48 49 51 / 70%) !important;
  border: 1px solid transparent !important;

  .el-popper__arrow {
    &::before {
      display: block;
      border: 4px solid rgb(48 49 51 / 70%) !important;
      width: 0;
      height: 0;
      border-left-color: transparent !important;
      border-top-color: transparent !important;
      background: transparent !important;
      transform: translate(-2px, 2px) rotateZ(45deg) !important;
    }
  }
}
.public-white-tooltips {
  background: #fff !important;
  border: 1px solid transparent !important;
  box-shadow: var(--el-box-shadow-light);
  .el-popper__arrow {
    &::before {
      display: block;
      border: 4px solid #fff !important;
      width: 0;
      height: 0;
      border-left-color: transparent !important;
      border-top-color: transparent !important;
      background: transparent !important;
      transform: translate(-2px, 2px) rotateZ(45deg) !important;
    }
  }
}
</style>
