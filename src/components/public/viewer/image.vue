<template>
  <div class="el-image-viewer__wrapper" tabindex="-1" @click.stop>
    <div class="el-image-viewer__mask" @click.stop="handleClose"></div>

    <div class="el-image-viewer__canvas">
      <img :src="url" alt="" class="el-image-viewer__img" />

      <div class="el-image-viewer__close" @click.stop="$emit('close')">
        <el-icon :size="24" color="#0006"><Close /></el-icon>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance } from 'vue'
const { proxy } = getCurrentInstance() as any
// 单图片预览查看器
const props = defineProps({
  src: String,
  suffix: {
    type: String,
    default: '',
  },
  hideOnClickModal: {
    type: Boolean,
    default: true,
  },
})

const emits = defineEmits(['close'])

const url = computed(() => {
  if (props.src) {
    return proxy.$picUrl + props.src + props.suffix
  }
  return ''
})

function handleClose() {
  if (props.hideOnClickModal) {
    emits('close')
  }
}
</script>

<style scoped lang="scss">
.el-image-viewer__wrapper {
  z-index: 9999;

  .el-image-viewer__canvas {
    flex-direction: column;
    gap: 20px;

    .el-image-viewer__img {
      z-index: 2;
      max-width: 58vmin !important;
      min-width: 58px !important;
      max-height: 58vmin !important;
      min-height: 58px !important;
    }

    .el-image-viewer__close {
      z-index: 2;
      width: 44px;
      height: 44px;
      border-radius: 50%;
      background: rgb(231, 231, 231);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
    }
  }
}
</style>
