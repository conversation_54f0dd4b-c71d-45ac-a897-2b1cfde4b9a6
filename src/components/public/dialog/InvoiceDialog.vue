<template>
  <PublicDialog
    ref="DialogRef"
    width="70%"
    :showFooterButton="showFooterButton"
    custom-close
    align-center
    destroy-on-close
    :fullscreen="fullscreen"
    :close-on-click-modal="true"
    @close="closeChange"
    :style="dialogStyle"
  >
    <template v-if="url">
      <template v-if="urlType === 'pdf'">
        <IFrame :src="url" :subtractHeight="100" style="padding-top: 20px;"></IFrame>
      </template>
      <template v-else-if="urlType === 'ofd'">
        <div v-html="ofdDiv"></div>
      </template>
      <template v-else-if="urlType === 'xml'"></template>
    </template>
  </PublicDialog>
</template>

<script setup>
import IFrame from '@/components/public/IFrame.vue'
// import { parseOfdDocument, renderOfd } from 'ofd.js'
import { ref, computed } from 'vue'

const props = defineProps({
  url: {
    type: String,
    default: '',
  },
  showFooterButton: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  dialogStyle: {
    type: Object,
    default: () => ({}),
  },
})

const urlType = computed(() => {
  if (props.url) {
    const fileSuffix = props.url.substring(props.url.lastIndexOf('.') + 1).toLowerCase()
    // if (fileSuffix === 'ofd') {
    //   getOFD()
    // }
    return fileSuffix
  }
  return ''
})

const fullscreen = ref(false)
const ofdDiv = ref('')

const emits = defineEmits(['confirm', 'close', 'before-close'])

defineExpose({
  open,
  close,
})

const DialogRef = ref()

// 打开
function open() {
  DialogRef.value.open()
}
// 关闭
function close() {
  DialogRef.value.close()
}
// 关闭时回调
function closeChange() {
  emits('close')
}

async function getOFD() {
  // parseOfdDocument({
  //   ofd: props.url,
  //   // signaturesCallback(signatures) {
  //   //   console.log(signatures)
  //   //   let screenWidth = 1050 //设置内容的显示框大小
  //   //   //将流数据渲染给接受的div盒子
  //   //   renderOfd(0, screenWidth).then(res => {
  //   //     console.log(res)
  //   //     ofdDiv.value = res
  //   //     // const divs = res
  //   //     // let contentDiv = document.getElementById('divId') // 获取盒子元素
  //   //     // contentDiv.innerHTML = ''
  //   //     // for (const div of divs) {
  //   //     //   contentDiv.appendChild(div)
  //   //     // }
  //   //   })
  //   // },
  //   success(core) {
  //     console.log(core)
  //   },
  //   fail(error) {
  //     console.log(error)
  //   },
  // })
}
</script>

<style scoped lang="scss"></style>
