<template>
  <PublicDialog
    ref="DialogRef"
    :width="width"
    :title="title"
    :showFooterButton="false"
    custom-close
    align-center
    :titleCenter="title ? true : false"
    :close-on-click-modal="true"
    v-bind="$attrs"
  >
    <div
      class="flex-between confirm-box"
      :style="{
        height
      }"
    >
      <slot>
        <h2>{{ content }}</h2>
      </slot>
      <span v-if="warningText" class="tip">{{ warningText }}</span>
      <slot name="button">
        <div class="flex-center btn">
          <el-button class="btn-big" plain round @click="onCancel">{{ cancelText }}</el-button>
          <el-button class="btn-big" type="primary" round @click="onConfirm">{{ confirmText }}</el-button>
        </div>
      </slot>
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const DialogRef = ref()

const props = defineProps({
  title: {
    type: String,
    default: '温馨提示'
  },
  content: {
    type: String,
    default: '确认操作？'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  confirmText: {
    type: String,
    default: '确定'
  },
  beforeCancel: {
    type: Boolean,
    default: false
  },
  beforeConfirm: {
    type: Boolean,
    default: false
  },
  warningText: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '450px'
  },
  height: {
    type: String,
    default: 'auto'
  },
})

defineExpose({
  open,
  close
})

const emits = defineEmits<{
  cancel: [close: () => void],
  confirm: [close: () => void],
}>()

function open() {
  DialogRef.value.open()
}
function close() {
  DialogRef.value.close()
}

function onCancel() {
  if(props.beforeCancel){
    emits('cancel', close)
    return
  }
  close()
}

function onConfirm() {
  emits('confirm', close)
}

</script>

<style scoped lang="scss">
.confirm-box {
  flex-direction: column;
  gap: 20px;
  min-height: 180px;

  .tip {
    font-size: 16px;
    color: var(--el-color-warning);
    text-align: center;
  }
  .btn {
    margin: 24px 0 15px;
    gap: 20px;

    .el-button.is-round {
      padding: 8px 30px;
    }
  }
}
</style>