<template>
  <el-dialog
    v-model="welcomeDialog"
    title="欢迎加入蜗牛会员大家庭！"
    width="600"
    center
    align-center
    :show-close="false"
    destroy-on-close
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :z-index="99999"
  >
    <template #header="{ titleId, titleClass }">
      <span :id="titleId" :class="titleClass" style="position: relative; top: 16px">
        欢迎加入蜗牛会员大家庭！
      </span>
    </template>
    <div class="content-box">
      <p>
        &emsp;&emsp;尊敬的会员，恭喜您成功开通蜗牛会员服务！我们团队将全力以赴，为您提供卓越的服务体验。如果您有任何疑问或需要帮助，请随时联系我们。
      </p>
      <p>为了进一步提升我们的服务质量，更好的满足您的需求，请补充下面信息~</p>
      <div style="width: 425px">
        <el-form ref="formRef" :model="form" label-width="100px" :rules="rules">
          <template v-if="!store.userInfo.phone">
            <div style="margin: 0 0 20px 20px; color: #444">基本信息</div>
            <el-form-item label="手机号" prop="phone">
              <el-input
                type="tel"
                v-model="form.phone"
                maxlength="11"
                placeholder="请填写手机号"
                @input="handleTel"
              />
            </el-form-item>
            <el-form-item label="验证码" prop="code">
              <div class="code-box">
                <el-input v-model="form.code" maxlength="4" placeholder="请填写验证码" />
                <el-button
                  class="code-btn"
                  link
                  :disabled="!checkPhoneBtn"
                  :loading="codeBtnLoading"
                  @click.stop="getPhoneCode"
                >
                  <template v-if="codeTime">
                    {{ codeTime + '秒后可再次获取' }}
                  </template>
                  <template v-else>获取验证码</template>
                </el-button>
              </div>
            </el-form-item>
            <div style="margin: 0 0 20px 20px; color: #444">企业信息</div>
          </template>
          <el-form-item label="公司名称：" prop="businessName">
            <el-input
              class="input"
              v-model="form.businessName"
              placeholder="例如：深圳赛维科技有限公司"
              maxlength="50"
              clearable
            />
          </el-form-item>
          <el-form-item label="姓名：" prop="name">
            <el-input
              class="input"
              v-model="form.name"
              placeholder="请输入个人姓名或公司内花名"
              maxlength="8"
              clearable
            />
          </el-form-item>
          <el-form-item prop="scale">
            <template #label>
              <div class="flex-column scale-label">
                <span>公司规模：</span>
                <div class="tip">（员工人数）</div>
              </div>
            </template>
            <ul class="scale-list">
              <li
                v-for="(item, i) in businessScale"
                :key="i"
                :class="{ active: item.value == form.scale }"
                @click="form.scale = item.value"
              >
                {{ item.label }}
                <el-icon v-if="item.value == form.scale" class="active-icon" color="var(--el-color-primary)">
                  <Check />
                </el-icon>
              </li>
            </ul>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <ImageCodeDialog />

    <template #footer>
      <el-button style="width: 328px" type="primary" :loading="loading" round @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ImageCodeDialog from '@/views/login/ImageCodeDialog.vue'
import { useWelcome, businessScale } from '@/hooks/useWelcome'
import { initBusinessInfo } from '@/api/center'
import { ElLoading } from 'element-plus'
import { useUserStore } from '@/stores/modules/user'
import { wechatAuth, checkPhone, getAuthToken, getPhoneWithToken, authPhoneLogin } from '@/api/wechat'
import { phone_reg } from '@/utils/RegExp'
import useImgOrPhoneCode from '@/hooks/useImgOrPhoneCode'

const {
  codeTime,
  codeBtnLoading,
  phoneForm,
  sendPhoneCode, // 发送手机验证码
} = useImgOrPhoneCode()

const store = useUserStore()
const { welcomeDialog, form } = useWelcome()

const checkPhoneBtn = ref(false)

const formRef = ref()
const loading = ref(false)
const rules = {
  businessName: [
    { required: true, message: '请输入公司名称', trigger: 'blur' },
    { min: 1, max: 50, message: '公司名称长度限制 50 个字符', trigger: 'change' },
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { min: 2, max: 8, message: '姓名长度在 2 到 8 个字符', trigger: 'change' },
  ],
  scale: [{ required: true, message: '请选择规模', trigger: 'change' }],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { validator: validatePhone, trigger: 'change' },
  ],
  code: [{ required: true, message: '请输入验证码', trigger: 'manul' }],
}

function handleSubmit() {
  formRef.value.validate((valid: any) => {
    if (valid) {
      const el_loading = ElLoading.service({
        lock: true,
        text: '保存中...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      initBusinessInfo({
        phone: form.value.phone && form.value.phone != '' ? form.value.phone : null,
        phoneCaptcha: form.value.code && form.value.code != '' ? form.value.code : null,
        accountName: form.value.name,
        name: form.value.businessName,
        scale: form.value.scale,
      })
        .then(() => {
          store.getUserInfo()
          welcomeDialog.value = false
        })
        .finally(() => {
          el_loading.close()
        })
    }
  })
}

const checkPhoneLoading = ref(false)

function cancelTask(asyncTask: any) {
  let cancel = () => {}

  return (...args: any[]) => {
    return new Promise((resolve, reject) => {
      cancel()
      cancel = () => {
        resolve = reject = () => {}
      }
      asyncTask(...args).then(
        (res: any) => resolve(res),
        (err: any) => reject(err)
      )
    })
  }
}
function validatePhone(rule: any, value: any, callback: any) {
  checkPhoneBtn.value = false
  if (!phone_reg.test(value)) {
    return callback(new Error('请输入正确的手机号'))
  }
  checkPhone({ phone: value })
    .then((res: any) => {
      if (res.code !== 200) {
        callback(new Error(res.msg))
      } else if (res.data) {
        callback(new Error(res.data))
      } else {
        checkPhoneBtn.value = true
        callback()
      }
    })
    .catch(err => {
      console.log(err)
      callback(new Error(err.data?.msg || '网络错误！请稍后再试'))
    })
    .finally(() => {})
}

function handleTel(val: any) {
  form.value.phone = val.replace(/\D/g, '')
}

function getPhoneCode() {
  if (codeTime.value) return
  formRef.value.validateField('phone', (valid: any) => {
    if (valid) {
      sendPhoneCode({ phoneNum: form.value.phone })
    }
  })
}
</script>

<style scoped lang="scss">
.content-box {
  padding: 20px 20px 0;

  .input {
    font-size: 13px;

    :deep(.el-input__inner) {
      // --el-input-inner-height: 32px;
      font-family: 'wn-font';
    }
  }
  :deep(.el-form-item) {
    align-items: baseline;
  }

  .scale-label {
    width: fit-content;
    text-align: right;
    align-items: flex-end;
    position: relative;

    .tip {
      position: absolute;
      top: 22px;
      right: -1px;
      width: 88px;
    }
  }

  .scale-list {
    margin: 0;
    padding: 0;
    list-style-type: none;

    li {
      position: relative;
      width: 320px;
      text-align: center;
      line-height: 24px;
      border: 1px solid #e4e4e4;
      color: var(--el-text-color);
      font-size: 13px;
      border-radius: 20px;
      padding: 3px;
      margin: 5px 0 10px;
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }
      &.active {
        color: var(--el-color-primary);
        border-color: var(--el-color-primary);
      }

      .active-icon {
        position: absolute;
        top: 50%;
        right: 10px;
        transform: translateY(-50%);
      }
    }
  }
}
.code-box {
  width: 100%;
  position: relative;
  .code-btn {
    &:active,
    &:focus,
    &:hover {
      color: #169bd5 !important; // 保持原有颜色
      background-color: transparent !important;
      border-color: transparent !important;
    }
    position: absolute;
    top: 1px;
    right: 15px;
    z-index: 3;
    height: 100%;
    width: fit-content;
    margin: 0;
    padding: 0;
    font-weight: 500;
    color: #169bd5;
  }
}
</style>
