<template>
  <div class="model-details">
    <el-dialog
      width="500px"
      v-model="showRenewDialog"
      align-center
      :show-close="false"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialog-box">
        <div class="model-details__close flex-end">
          <el-icon style="cursor: pointer" size="16" @click="close"><Close /></el-icon>
        </div>
        <!-- <div class="model-head"></div> -->
        <div class="model-content">
          <div class="content-one">
            您的会员有效期倒计时：
            <span class="content-one__num">
              <CountDown
                :endTime="handleOrderTime(store.userInfo.businessVO?.memberValidity)"
                showDays
                h="时"
                m="分"
                @end="handleTimeEnd"
                :showSeconds="false"
              />
            </span>
          </div>
          <div class="content-two">
            每月仅需
            <span class="content-two__num">9.95</span>
            USD
          </div>
          <div class="content-three">会员期内半价续费,过期恢复原价</div>
          <div class="content-five">
            <!-- <div class="five-tip" v-if="!store.isOwnerAcc()">可提醒主账号进行续费</div> -->
            <el-button
              type="primary"
              round
              class="five-btn"
              style="
                background: linear-gradient(90deg, #50c4ff 0%, #5d84ff 100%);
                width: 220px;
                border: none;
                box-shadow: 4px 3px 14px 0px rgba(0,112,222,0.3), inset 0px 0px 7px 0px #A6C2FF;
              "
              @click="handleClose"
            >
              去续费
            </el-button>
          </div>
          <div class="content-six flex-end">
            <el-tooltip
              placement="top"
              raw-content
              :content="handleShowTooltips()"
              :hide-after="100"
              :offset="5"
              popper-class="public-white-tooltips"
            >
              <span class="flex-start content-six__tip" style="">
                活动说明
                <img style="width: 12px; height: 12px;" src='@/assets/icon/help-circle__icon.png'></img>
                <!-- <el-icon>
                  <QuestionFilled />
                </el-icon> -->
              </span>
            </el-tooltip>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import CountDown from '@/components/public/CountDown.vue'
import { useUserStore } from '@/stores/modules/user'
import { useRouter } from 'vue-router'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const router = useRouter()
const store = useUserStore()
const { showRenewDialog, isFiveRenew } = useShowRenewDialog()

function handleShowTooltips() {
  return `<div style="max-width: 310px;color:#6f8cb2">活动说明：<br />1.此半价续费活动针对老会员专享，在截止至会员到期前30天内，进行续费均可享受半价优惠;<br />2.活动有效期内在会员/工作台/个人资料/用户导航栏等界面均有入口可进入。</div>`
}

function close() {
  showRenewDialog.value = false
}

function open() {
  showRenewDialog.value = true
}

function handleTimeEnd() {
  isFiveRenew.value = false
}

function handleClose() {
  // if (!store.isOwnerAcc()) {
  //   showRenewDialog.value = false
  // } else {
    showRenewDialog.value = false
    router.push('/vip')
  // }
}

function handleOrderTime(time?: string) {
  if (!time) return 0
  let date = time.split(' ')[0] + ' 23:59:59'
  return new Date(date).getTime()
}

defineExpose({
  open,
  close,
})
</script>

<style lang="scss" scoped>
.model-details {
  :deep(.el-dialog__header) {
    display: none;

  }
  :deep(.el-dialog) {
    background: url('@/assets/image/dialog_bg.png') center/cover no-repeat;
    border-radius: 15px;
  }
  .model-content {
    font-family: 'Source-Han-Sans-bold';
    padding: 15px 0;
    color: #11265b;
    text-align: center;
  }
  .content-one {
    font-size: 20px;
    font-weight: 500;
    &__num {
      color: #008fff;
      font-weight: bold;
    }
  }
  .content-two {
    font-size: 30px;
    font-weight: 500;
    &__num {
      font-family: 'D-DIN' , 'D-DIN';
      font-size: 50px;
      color: #008fff;
      font-weight: bold;
    }
  }
  .content-three {
    font-weight: 500;
    font-size: 16px;
    color: #11265BA6;
  }
  .content-five {
    margin-top: 30px;
    .five-tip {
      font-size: 12px;
      color: #6f8cb2;
      margin-bottom: 5px;
    }
  }
  .content-six {
    padding-right: 8px;
    &__tip {
      font-family: 'Source-Han-Sans';
      gap: 2px; 
      color: #6F8CB2;
      font-size: 12px;
    }
  }
}
</style>
