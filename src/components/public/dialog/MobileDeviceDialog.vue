<template>
  <el-dialog
    v-model="mobileVisible"
    title=""
    width="330"
    center
    align-center
    destroy-on-close
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    style="
      border-radius: var(--dialog-radius);
      background: linear-gradient(180deg, #e0effc, #fff);
      padding: 30px;
    "
    :z-index="99999"
    @close="handleClose"
  >
    <div class="flex-column mobile-device-dialog">
      <img src="@/assets/image/bell.png" />
      <div class="mobile-device-title">温馨提示</div>
      <div class="mobile-device-tip">为保证您更好的使用体验，建议复制网页链接后前往电脑进行后续的操作</div>
    </div>
    <template #footer>
      <el-button class="mobile-device-btn" @click="handleCancel">继续操作</el-button>
      <el-button class="mobile-device-btn" type="primary" @click="handleSubmit">复制链接</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useMobileDeviceDialog } from '@/hooks/useMobileDeviceAction'
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import copy from '@/utils/copy'

const route = useRoute()

const { mobileVisible, handleClose, handleCancel, handleConfirm } = useMobileDeviceDialog()

function handleSubmit() {
  copy(window.location.origin + route.fullPath)
  handleConfirm()
}
</script>

<style scoped lang="scss">
.mobile-device-dialog {
  img {
    width: 116px;
    position: absolute;
    top: -55px;
  }

  .mobile-device-title {
    width: 100%;
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    margin: 15px 0px;
    color: var(--text-color);
  }
  .mobile-device-tip {
    width: 100%;
    text-align: center;
    font-size: 16px;
    line-height: 24px;
    margin: 0px 0px 10px;
    color: var(--text-color);
    padding: 0 2px;
    box-sizing: border-box;
  }
}
.mobile-device-btn {
  width: 128px;
  height: 40px;
  font-size: 14px;
  border-radius: 8px;

  & + .mobile-device-btn {
    margin-left: 14px;
  }

  &:nth-child(1) {
    border-color: transparent;
    color: #2a68cc;
    background: #ecf2ff;
  }
  &:nth-child(2) {
    background: #6da5ff;
    border-color: #6da5ff;
  }
}
</style>
