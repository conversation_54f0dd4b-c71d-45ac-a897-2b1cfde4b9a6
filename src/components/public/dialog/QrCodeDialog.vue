<template>
  <PublicDialog
    ref="DialogRef"
    :title="title"
    :width="width"
    :showFooterButton="false"
    custom-close
    align-center
    destroy-on-close
    :close-on-click-modal="false"
    v-bind="$attrs"
  >
    <slot name="header"></slot>
    <div class="flex-center content-box" :class="{'is-loading': loading}" v-loading="loading">
      <QrCode
        ref="QrCodeRef"
        size="large"
        :http-code="httpCode"
        :check-code="checkCode"
        :auto-check-code="autoCheckCode"
        :code-type="codeType"
        :is-zfb-pay="isZfbPay"
        @change="chageCheckQrcode"
        @success="checkSuccess"
      />
      <div>
        <slot>
          <div class="tips template-pre">
            {{ qrCodeTips }}
          </div>
        </slot>
      </div>
    </div>
    <slot name="footer"></slot>
  </PublicDialog>
</template>

<script setup lang="ts">
import QrCode from '@/components/public/qrcode/QrCode.vue'
import { nextTick, ref, type PropType } from 'vue'
import type { CODE_TYPE, QRCODE_STATUS } from '@/components/public/qrcode/type'

const DialogRef = ref()
const QrCodeRef = ref()

const props = defineProps({
  title: {
    type: String,
    default: '扫码提示',
  },
  // 获取code
  httpCode: {
    type: Function,
  },
  // 检测code状态
  checkCode: {
    type: Function,
  },
  // 自动状态检测
  autoCheckCode: {
    type: Boolean,
    default: true,
  },
  // 0:登录 1:绑定 2:验证
  codeType: {
    type: Number as PropType<CODE_TYPE>,
    default: 0,
  },
  delay: {
    type: Number,
    default: 1500,
  },
  qrCodeTips: {
    type: String,
    default: '打开微信扫一扫',
  },
  width: {
    type: String,
    default: '380px',
  },
  // 支付宝支付特殊处理
  isZfbPay: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  }
})

defineExpose({
  open,
  close,
  load
})

const emits = defineEmits<{
  change: [
    {
      res: any
      ticket: string
      type: CODE_TYPE
      update: (status: QRCODE_STATUS | string, data: any) => void
      next: () => void
      stop: (err?: any) => void
    },
  ]
  error: [error: any]
  success: [data: any]
}>()

function open() {
  DialogRef.value.open()
}
function close() {
  DialogRef.value.close()
}
function load() {
  nextTick(() => {
    QrCodeRef.value?.load()
  })
}

function chageCheckQrcode(data: {
  res: any
  ticket: string
  type: CODE_TYPE
  update: (status: string, data: any) => void
  next: () => void
  stop: (err?: any) => void
}) {
  emits('change', data)
}
function checkSuccess(data: any) {
  emits('success', data)
}
</script>

<style scoped lang="scss">
.content-box {
  position: relative;
  padding: 0 10px 10px;

  &.is-loading {
    &::before {
      content: '';
      display: block;
      width: 50%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      background-color: #fff;
    }
  }

  h2 {
    margin: 0 0 5px;
  }

  .tips {
    text-align: center;
    font-size: 22px;
    font-weight: bold;
    color: var(--el-color-primary);
  }
}
</style>
