<template>
  <PublicDialog
    ref="DialogRef"
    width="500px"
    :title="title"
    :showFooterButton="false"
    custom-close
    align-center
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="confirm-box">
      <el-upload
        class="upload-proof"
        ref="uploadProofRef"
        :file-list="fileList"
        drag
        :multiple="multiple"
        action=""
        :accept="isMobileDevice() ? 'image/*' : ''"
        :capture="isMobileDevice() ? 'user' : ''"
        :auto-upload="false"
        @change="handleChange"
        @remove="handleRemove"
      >
        <slot>
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text flex-column">
            <span class="up-text">选择要上传的文件</span>
            <span>或将其拖放、粘贴到此处</span>
          </div>
        </slot>
        <template #tip>
          <slot name="tip"></slot>
        </template>
      </el-upload>
      <div class="flex-center btn">
        <slot name="button">
          <el-button class="btn-big" plain round @click="DialogRef.close()">取消</el-button>
          <el-button class="btn-big" type="primary" round @click="onConfirm">确认</el-button>
        </slot>
      </div>
      <!-- <label for="paste-file" />
      <input type="file" id="paste-file" style="display: none;" /> -->
    </div>
  </PublicDialog>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { uploadCloudFile } from '@/api/index'
import { handlePasteFile, beforeUpload, isMobileDevice } from '@/utils/public'

const DialogRef = ref()
const uploadProofRef = ref()
const fileList = ref<any[]>([])

const props = defineProps({
  modelValue: {
    type: Array,
  },
  title: {
    type: String,
    default: '文件上传',
  },
  // MB
  size: {
    type: Number,
    default: 5,
  },
  // 0不限制上传个数
  limit: {
    type: Number,
    default: 0,
  },
  fileType: {
    type: Array<string>,
    default: () => ['jpg', 'jpeg', 'png'],
  },
  multiple: {
    type: Boolean,
    default: true,
  },
})

const isShow = ref(false)

defineExpose({
  open,
  close,
})

const emits = defineEmits(['update:modelValue', 'change', 'success'])

function open() {
  DialogRef.value.open()
  isShow.value = true
}
function close() {
  DialogRef.value.close()
  isShow.value = false
}
function handleClose() {
  fileList.value = []
  uploadProofRef.value?.clearFiles()
}

function handlePaste(event: ClipboardEvent) {
  if (!isShow.value) return
  handlePasteFile(event, {
    fileType: props.fileType,
    size: props.size,
  }).then(files => {
    if (files.length) {
      files.forEach(file => {
        fileList.value.push({
          raw: file,
          name: file.name,
          size: file.size,
          status: 'ready',
          uid: file.uid,
        })
      })
      emits('change', fileList.value)
    }
  })
}

function handleChange(file: any, files: any) {
  fileList.value = files
  emits('change', fileList.value)
}
function handleRemove(file: any, files: any) {
  fileList.value = files
  emits('change', fileList.value)
}

function onConfirm() {
  // console.log(fileList.value);
  if (!fileList.value.length) {
    ElMessage.warning('请选择要上传的文件')
    return
  }
  if (props.limit && fileList.value.length > props.limit) {
    ElMessage.warning(`最多只能上传${props.limit}个文件`)
    return
  }
  if (
    beforeUpload(fileList.value, {
      size: props.size,
      fileType: props.fileType,
    })
  ) {
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在上传中，请稍后',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    let requests: any[] = []
    fileList.value.forEach(f => {
      const fromData = new FormData()
      fromData.append('file', f.raw)
      requests.push(uploadCloudFile(fromData))
    })
    Promise.all(requests)
      .then(res => {
        ElMessage.success('上传成功')
        let data: any[] = []
        res.forEach(item => {
          data.push(item.data)
        })
        emits('update:modelValue', data)
        emits('success', data)
        DialogRef.value.close()
      })
      .finally(() => el_loading.close())
  }
}

onMounted(() => {
  document.addEventListener('paste', handlePaste)
})
onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>

<style scoped lang="scss">
.confirm-box {
  gap: 20px;
  // max-height: 300px;
  padding-bottom: 60px;
  position: relative;

  .upload-proof {
    width: 100%;
    // height: 180px;

    :deep(.el-upload-dragger) {
      padding: 10px;
    }
    :deep(.el-upload-list) {
      max-height: 100px;
      overflow-y: auto;
    }
    :deep(.el-upload-list__item) {
      width: 100%;
      height: auto;
    }
  }

  .el-icon--upload {
    color: var(--el-color-primary);
  }
  .up-text {
    font-size: 16px;
    color: var(--el-color-primary);
  }
  .btn {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    margin: 10px 0;
  }
}
</style>
