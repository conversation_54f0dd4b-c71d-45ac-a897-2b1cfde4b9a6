<template>
  <div class="vc-box" :style="boxStyle">
    <div class="image-box" :style="{
      width: width,
      height: height
    }">
      <div v-if="playType" class="play-icon">
        <img src="@/assets/icon/icon_play_2.png" alt="">
      </div>
      <div v-else class="play-icon-2">
        <img src="@/assets/icon/icon_play.png" alt="">
      </div>
      <div class="platform-title" v-if="platform == '0'">Amazon</div>
      <div class="platform-title" v-if="platform == '1'">TikTok</div>
      <!-- <PercentageImg :src="src" :fit="fit" direction="horizontal" /> -->
      <el-image
        :src="$picUrl +src + suffix"
        :fit="fit"
        class="cover-img"
        preview-teleported
      >
        <template #error>
          <div class="image-error">
            <el-icon :size="25"><Picture /></el-icon>
          </div>
        </template>
      </el-image>
    </div>
    <div class="vc-title" :style="{ width }">
      {{ title }}
    </div>
  </div>
</template>

<script setup lang="ts">
// import PercentageImg from '@/components/public/image/PercentageImg.vue'

interface Props {
  title?: string
  width?: string
  height?: string
  platform?: string
  color?: string
  size?: string | number
  fit?: string
  suffix?: string
  src: string
  boxStyle?: {[x: string]: any}
  playType?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '',// 视频标题
  width: '140px',
  height: '140px',
  color: '#ffffffc4',
  size: 38,
  fit: 'contain',
  suffix: '',
  src: '',
  boxStyle: () => ({}),
  playType: true
})
</script>

<style scoped lang="scss">
.vc-box {
  position: relative;

  .platform-title {
    position: absolute;
    top: 2px;
    right: 0;
    font-size: 12px;
    font-weight: 400;
    color: rgb(123 123 123 / 70%);
    background-color: rgb(255 255 255 / 50%);
    padding: 1px 3px;
    border-radius: 11px;
    height: 16px;
    line-height: 16px;
    transform: scale(0.8);
    // text-shadow: 1px 1px 1px #000c;
    z-index: 8;
  }

  .image-box {
    width: 100px;
    height: 100px;
    flex-shrink: 0;
    position: relative;
    cursor: pointer;
    background: var(--bottom-bg-color);
    border-radius: 8px;
    border: 1px solid var(--border-gray-color);
    overflow: hidden;
  
    .el-image {
      width: 100%;
      height: 100%;
    }
    
    .play-icon {
      position: absolute;
      top: 0px;
      right: 0px;
      z-index: 9;
      width: 100%;
      height: 100%;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
      }
    }
    .play-icon-2 {
      position: absolute;
      top: 5px;
      right: 8px;
      z-index: 9;

      img {
        width: 15px;
        height: 15px;
      }
    }

    .cover-img {
      width: 100%;
      height: 100%;
    }
    .image-error {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .vc-title {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
  }
}
</style>