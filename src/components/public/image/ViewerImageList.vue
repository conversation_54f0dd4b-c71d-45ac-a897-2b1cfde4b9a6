<template>
  <div
    class="viewer-list"
    :class="{
      'viewer-list-start': layout === 'start',
      'viewer-list-center': layout === 'center',
      'viewer-list-column': layout === 'column',
    }"
    :style="{'minHeight': data?.length && data?.length > 0 ? '94px': '0px'}"
  >
    <template v-if="urlName">
      <div
        class="viewer-list-img-item"
        :style="imgItemStyle"
        v-for="(item, i) in data"
        :key="item[urlName] + i"
      >
        <el-image :src="$picUrl + item[urlName] + _suffix" :fit="fit" />
        <span class="item-actions">
          <el-icon @click="handlePreview(item[urlName], i)"><View /></el-icon>
          <el-icon v-if="showDownloadBtn" @click="handleDownload(item[urlName], item[fileName])"><Download /></el-icon>
          <el-icon v-if="showDeleteBtn" @click="handleDelete(item, i)"><Delete /></el-icon>
        </span>
      </div>
    </template>
    <template v-else>
      <div
        class="viewer-list-img-item"
        :style="imgItemStyle"
        v-for="(item, i) in data"
        :key="item + i"
      >
        <el-image :src="$picUrl + item + _suffix" :fit="fit" />
        <span class="item-actions">
          <el-icon @click="handlePreview(item, i)"><View /></el-icon>
          <el-icon v-if="showDownloadBtn" @click="handleDownload(item)"><Download /></el-icon>
          <el-icon v-if="showDeleteBtn" @click="handleDelete(item, i)"><Delete /></el-icon>
        </span>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed, type PropType, ref } from "vue"
import { useViewer, type ViewerScale } from '@/hooks/useViewer'
import { downUrlImgFile } from '@/utils/download'

const { showViewer } = useViewer()

type FlexLayout = "start" | "center" | "column"

const props = defineProps({
  data: {
    type: Array<any>,
    default: () => []
  },
  urlName: {
    type: String,
    default: ''
  },
  fileName: {
    type: String,
    default: 'name'
  },
  suffix: {
    type: String as PropType<ViewerScale>,
    default: ''
  },
  isPreviewAll: {
    type: Boolean,
    default: false
  },
  showDeleteBtn: {
    type: Boolean,
    default: true
  },
  showDownloadBtn: {
    type: Boolean,
    default: false
  },
  layout: {
    type: String as PropType<FlexLayout>,
    default: "start"
  },
  fit: {
    type: String,
    default: 'fill'
  },
  imgItemStyle: {
    type: Object,
    default: () => ({})
  }
})

const emits = defineEmits(['delete'])

const _suffix = computed(() => props.suffix ? '!' + props.suffix : '')

const previewUrl = ref<any[]>([])

function handlePreview(url: string, i: number) {
  if(props.isPreviewAll) {
    previewUrl.value = props.data.map(item => props.urlName ? item[props.urlName] : item)
    showViewer(previewUrl.value, { index: i, scale: props.suffix })
  } else {
    previewUrl.value = [url]
    showViewer(previewUrl.value, { scale: props.suffix })
  }
}
function handleDelete(item: any, i: any) {
  emits('delete', item, i)
}
function handleDownload(url: any, name?: string) {
  if(!name) {
    name = new Date().getTime() + ''
  }
  downUrlImgFile(url, name)
}

</script>

<style scoped lang="scss">
.viewer-list {
  --image-width: 80px;
  --image-height: 80px;
  min-height: calc(var(--image-height) + 14px);
  max-height: calc(var(--image-height) * 2 + 15px);
  overflow-y: auto;
}
.viewer-list-start {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}
.viewer-list-center {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.viewer-list-column {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 10px;
}
.viewer-list-img-item {
  position: relative;
  width: var(--image-width);
  height: var(--image-height);
  flex-shrink: 0;

  .el-image {
    width: 100%;
    height: 100%;
  }
  .item-actions {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #fff;
    font-size: 20px;
    opacity: 0;
    background-color: #00000080;
    transition: opacity .3s;

    &:hover {
      opacity: 1;
    }

    .el-icon {
      cursor: pointer;
    }
  }
}
</style>