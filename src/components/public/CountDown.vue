<template>
  <span class="countdown-box" :class="{ 'is-background': background }" v-html="remainingTime"></span>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  /**
   * 结束时间，可以是时间戳或者字符串
   * 字符串格式：'yyyy-MM-dd HH:mm:ss'
   */
  endTime: {
    type: [Number, String],
    required: true,
  },
  showDays: {
    type: Boolean,
    default: false,
  },
  background: {
    type: Boolean,
    default: false,
  },
  d: {
    type: String,
    default: '天',
  },
  h: {
    type: String,
    default: ':',
  },
  m: {
    type: String,
    default: ':',
  },
  s: {
    type: String,
    default: '',
  },
  showHours: {
    type: Boolean,
    default: true,
  },
  showMinutes: {
    type: Boolean,
    default: true,
  },
  showSeconds: {
    type: Boolean,
    default: true,
  },
  padStart: {
    type: Number,
    default: 2,
  },
})

const emit = defineEmits(['end'])

const remainingTime = ref('')

const updateTime = () => {
  const now = new Date().getTime()
  let distance = 0
  if (typeof props.endTime === 'string') {
    distance = new Date(props.endTime).getTime() - now
  } else {
    distance = props.endTime - now
  }

  if (distance < 0) {
    remainingTime.value = `00${props.h}00${props.m}00${props.s}`
    clearInterval(intervalId.value)
    emit('end')
  } else {
    let days = 0
    let hours = Math.floor(distance / (1000 * 60 * 60))
    if (props.showDays) {
      days = Math.floor(distance / (1000 * 60 * 60 * 24))
      hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    }
    const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((distance % (1000 * 60)) / 1000)

    let elStart = ''
    let elEnd = ''
    let elStart2 = ''
    let elEnd2 = ''
    if (props.background) {
      elStart = '<span class="countdown-bg">'
      elEnd = '</span>'
      elStart2 = '<span class="countdown-unit">'
      elEnd2 = '</span>'
    }

    remainingTime.value = ''
    if (props.showDays && days >= 0) {
      remainingTime.value += elStart + days + elEnd + (props.d ? elStart2 + props.d + elEnd2 : '')
    }
    if (props.showHours) {
      remainingTime.value +=
        elStart +
        hours.toString().padStart(props.padStart, '0') +
        (props.h ? elEnd + elStart2 + props.h + elEnd2 : '')
    }
    if (props.showMinutes) {
      remainingTime.value +=
        elStart +
        minutes.toString().padStart(props.padStart, '0') +
        elEnd +
        (props.m ? elStart2 + props.m + elEnd2 : '')
    }
    if (props.showSeconds) {
      remainingTime.value +=
        elStart +
        seconds.toString().padStart(props.padStart, '0') +
        elEnd +
        (props.s ? elStart2 + props.s + elEnd2 : '')
    }
  }
}

const intervalId = ref()

onMounted(() => {
  updateTime()
  intervalId.value = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (intervalId.value) {
    clearInterval(intervalId.value)
  }
})
</script>

<style lang="scss">
.countdown-box {
  --countdown-bg-color: var(--el-color-primary);
  --countdown-radius: 4px;
  --countdown-font-size: inherit;
  --countdown-padding: 3px;
  --countdown-gap: 4px;
  font-size: var(--countdown-font-size);

  &.is-background {
    --countdown-font-size: 14px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    gap: var(--countdown-gap);
  }

  .countdown-bg {
    display: inline-block;
    text-align: center;
    background: var(--countdown-bg-color);
    border-radius: var(--countdown-radius);
    width: calc(var(--countdown-padding) + var(--countdown-font-size));
    line-height: calc(var(--countdown-padding) + var(--countdown-font-size));
    padding: var(--countdown-padding);
  }
  .countdown-unit {
    text-align: center;
    min-width: calc(var(--countdown-font-size) / 2);
  }
}
</style>
