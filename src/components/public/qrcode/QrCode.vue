<template>
  <div class="flex-center qrcode-box">
    <div
      class="code-img"
      :class="{
        large: size === 'large',
        small: size === 'small',
      }"
      v-loading="qrcodeLoading"
    >
      <div class="flex-column code-tips-modal cur" v-if="qrCodeError" @click="load">
        <el-icon :size="iconSize" color="#fa5151"><WarningFilled /></el-icon>
        <div class="tips-1">错误提示</div>
        <div class="tips-2">{{ qrCodeError }}</div>
      </div>
      <!-- 支付宝支付 -->
      <!-- <div class="flex-column code-tips-modal zfb-pay-box" v-else-if="isZfbPay">
        <el-icon v-if="!qrcodeLoading" class="is-loading" :size="iconSize" color="var(--el-color-primary)"><Loading /></el-icon>
        <div class="tips-1">支付确认中</div>
        <div class="tips-2">请在新窗口完成支付</div>
      </div> -->

      <div class="flex-column code-tips-modal" v-else-if="qrCodeStatus === 'LOGIN_SUCCESS'">
        <el-icon :size="iconSize" color="#07c160"><SuccessFilled /></el-icon>
        <div class="tips-1">{{ successText }}</div>
      </div>
      <div class="flex-column code-tips-modal" v-else-if="qrCodeStatus === 'LOGINING'">
        <el-icon :size="iconSize" color="#07c160"><SuccessFilled /></el-icon>
        <div class="tips-1">已扫码</div>
        <div class="tips-2" v-if="isZfbPay">请根据支付宝提示操作</div>
        <div class="tips-2" v-else>请在手机上完成客服添加</div>
      </div>
      <div class="flex-column code-tips-modal cur" v-else-if="qrCodeStatus === 'DISABLE'" @click="load">
        <el-icon :size="iconSize" color="#fa5151"><CircleCloseFilled /></el-icon>
        <div class="tips-1">账号被禁用</div>
        <!-- <div class="tips-2">请刷新重试</div> -->
      </div>
      <div class="flex-column code-tips-modal cur" v-else-if="qrCodeStatus === 'BINDING'" @click="load">
        <el-icon :size="iconSize" color="#fa5151"><WarningFilled /></el-icon>
        <div class="tips-1">{{ codeType == 5 ? '此账号已绑定' : '该微信已被绑定' }}</div>
        <div class="tips-2 refresh">点此刷新</div>
      </div>
      <div class="flex-column code-tips-modal cur" v-else-if="qrCodeStatus === 'UN_REGISTER'" @click="load">
        <el-icon :size="iconSize" color="#fa5151"><WarningFilled /></el-icon>
        <div class="tips-1">您不是平台用户</div>
        <div class="tips-2 refresh">点此刷新</div>
      </div>
      <div
        class="flex-column code-tips-modal cur"
        v-else-if="qrCodeStatus === 'LOGIN_NO_PHONE'"
        @click="load"
      >
        <el-icon :size="iconSize" color="#fa5151"><WarningFilled /></el-icon>
        <div class="tips-1">未绑定手机号</div>
        <div class="tips-2 refresh">点此刷新</div>
      </div>
      <div
        class="flex-column code-tips-modal cur"
        v-else-if="qrCodeStatus === 'EXPIRE' || qrCodeStatus === 'UNKNOWN'"
        @click="load"
      >
        <el-icon :size="iconSize" color="#fa5151"><WarningFilled /></el-icon>
        <div class="tips-1">{{ qrCodeStatus === 'EXPIRE' ? '二维码已过期' : '未知错误' }}</div>
        <div class="tips-2 refresh">点此刷新</div>
      </div>

      <img v-if="qrCodeDataUrl" :src="qrCodeDataUrl" alt="" />
      <!-- 支付宝支付 -->
      <iframe
        v-if="isZfbPay && zfbPayForm"
        :srcdoc="zfbPayForm"
        frameborder="no"
        border="0"
        marginwidth="0"
        marginheight="0"
        scrolling="no"
        width="180"
        height="180"
        class="zfb-pay-iframe"
      ></iframe>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onUnmounted, ref, type PropType } from 'vue'
import QRCode from 'qrcode'
import type { CODE_TYPE, QRCODE_SIZE, QRCODE_STATUS } from './type'
import { ElMessage, ElMessageBox } from 'element-plus'
import { wechatQrcode, checkQrcode } from '@/api/wechat'

const props = defineProps({
  // 获取code
  httpCode: {
    type: Function,
    default: wechatQrcode,
  },
  // 检测code状态
  checkCode: {
    type: Function,
    default: checkQrcode,
  },
  // 自动状态检测
  autoCheckCode: {
    type: Boolean,
    default: true,
  },
  // 支付宝支付特殊处理
  isZfbPay: {
    type: Boolean,
    default: false,
  },
  // 0:登录 1:绑定 2:验证 3:一键登录
  codeType: {
    type: Number as PropType<CODE_TYPE>,
    default: 0,
  },
  // 自定义加载方法
  load: {
    type: Function,
  },
  delay: {
    type: Number,
    default: 1300,
  },
  size: {
    type: String as PropType<QRCODE_SIZE>,
    default: '',
  },
  successText: {
    type: String,
    default: '扫码成功',
  },
})

defineExpose({
  load,
  clear,
})

const emits = defineEmits<{
  change: [
    {
      res: any
      ticket: string
      type: CODE_TYPE
      update: (status: QRCODE_STATUS | string, data: any) => void
      next: () => void
      stop: (err?: any) => void
    },
  ]
  error: [error: any]
  success: [data: any]
  init: []
}>()

const zfbPayForm = ref('')
const qrcodeLoading = ref(false)
const qrCodeDataUrl = ref('')
const qrCodeError = ref('')
const qrCodeStatus = ref<QRCODE_STATUS>('WAITING')
let timer: number | undefined
let checkErrorIndex = 0

const iconSize = computed(() => {
  if (props.size === 'large') {
    return 55
  }
  if (props.size === 'small') {
    return 30
  }
  return 42
})

function load() {
  if (props.load) {
    props.load()
  } else {
    getCode()
  }
}
function clear() {
  checkErrorIndex = 0
  if (timer) clearInterval(timer)
}

function getCode() {
  qrcodeLoading.value = true
  qrCodeStatus.value = 'WAITING'
  zfbPayForm.value = ''
  props
    .httpCode({ type: props.codeType })
    .then(async (res: any) => {
      if (res.data.message) {
        qrCodeError.value = res.data.message
        emits('error', res.data.message)
        return
      }
      try {
        if (props.isZfbPay) {
          // 方案1-支付宝支付二维码
          zfbPayForm.value = res.data.pageRedirectionData
          // 方案2-打开新窗口支付宝支付
          // const divForm = document.getElementsByClassName("zfb-pay-win");
          // if (divForm.length) {
          //   for (let i = 0; i < divForm.length; i++) {
          //     document.body.removeChild(divForm[i]);
          //   }
          // }
          // let newWindow = window.open('', '_blank')
          // console.log("newWindow", newWindow);
          // // 检查新窗口是否被阻止弹出（用户可能禁用了弹出窗口）
          // if (newWindow && !newWindow.closed) {
          //   // 将form表单写入新窗口
          //   newWindow.document.write(res.data.pageRedirectionData);
          //   console.dir('newWindow.document', newWindow.document);
          //   newWindow.document.forms[0].submit();
          // } else {
          //   const div = document.createElement("div");
          //   div.setAttribute("class", "zfb-pay-win");
          //   // 新开窗口跳转 接口返回的form 表单字符串
          //   div.innerHTML = res.data.pageRedirectionData
          //   document.body.appendChild(div);
          //   ElMessageBox.alert('确认打开新窗口', '支付提示', {
          //     confirmButtonText: '打开',
          //   }).then(() => {
          //     document.forms[0].setAttribute("target", "_blank")
          //     document.forms[0].submit();
          //   })
          // }

          qrCodeError.value = ''
          checkErrorIndex = 3
          checkCode('')
        } else {
          const dataUrl = await QRCode.toDataURL(res.data.qrcode, {
            errorCorrectionLevel: 'H',
            width: '100%',
          })
          qrCodeDataUrl.value = dataUrl
          qrCodeError.value = ''
          checkErrorIndex = 3
          checkCode(res.data.ticket)
        }
        emits('init')
      } catch (error: any) {
        qrCodeError.value = error?.msg || 'Failed to generate QR Code'
        emits('error', error)
        console.error('Failed to generate QR Code', error)
      }
    })
    .catch((err: any) => {
      qrCodeError.value = err?.data?.msg || 'Failed to obtain QR code'
      emits('error', err)
      console.error('Failed to obtain QR code', err)
    })
    .finally(() => (qrcodeLoading.value = false))
}
// 方案1
// function checkCode(ticket: string) {
//   timer = setInterval(() => {
//     props
//       .checkCode({ ticket })
//       .then((res: any) => {
//         if(props.autoCheckCode) {
//           updateCodeStatus(res.data.loginStatus, { ticket, ...res.data })
//         }
//         emits('change', res, ticket, updateCodeStatus, clear)
//       })
//       .catch((error: any) => {
//         emits('error', error)
//         checkErrorIndex--
//         if (checkErrorIndex === 0) {
//           clear()
//           qrCodeStatus.value = 'UNKNOWN'
//         }
//       })
//   }, props.delay)
// }
// 方案2
async function checkCode(ticket: string) {
  while (checkErrorIndex) {
    await new Promise((reslove, reject) => {
      setTimeout(() => {
        props
          .checkCode({ ticket })
          .then((res: any) => {
            let next = () => reslove(true)
            let stop = (err?: any) => reject(err)
            if (props.autoCheckCode) {
              updateCodeStatus(res.data.loginStatus, { ticket, ...res.data })
              reslove(true)
              next = () => {}
              stop = () => {}
            }
            emits('change', {
              res,
              ticket,
              type: props.codeType,
              update: updateCodeStatus,
              next,
              stop,
            })
          })
          .catch((error: any) => {
            emits('error', error)
            checkErrorIndex--
            if (checkErrorIndex === 0) {
              clear()
              qrCodeStatus.value = 'UNKNOWN'
              reject(false)
              return
            }
            reslove(true)
          })
      }, props.delay)
    })
  }
}
function updateCodeStatus(status: QRCODE_STATUS | string, data: any) {
  switch (status) {
    case 'WAITING':
      qrCodeStatus.value = 'WAITING'
      break
    case 'LOGINING':
      qrCodeStatus.value = 'LOGINING'
      break
    case 'DISABLE':
    case 'ACCOUNT_DISABLE':
      ElMessage.error('您的账户已被禁用！')
      clear()
      qrCodeStatus.value = 'DISABLE'
      break
    case 'BINDING':
      if (props.codeType === 5) {
        ElMessage.error('此账号已绑定')
      } else {
        ElMessage.error('该账户已被绑定！')
      }
      clear()
      qrCodeStatus.value = 'BINDING'
      break
    case 'EXPIRE':
      clear()
      qrCodeStatus.value = 'EXPIRE'
      ElMessage.error('此二维码已过期！请刷新')
      // if (isAlert) {
      //   isAlert = false
      //   ElMessageBox.alert('此二维码已过期！请刷新', '扫码提示', {
      //     confirmButtonText: '刷新',
      //     callback: () => {
      //       isAlert = true
      //       load()
      //     },
      //   })
      // }
      break
    case 'LOGIN_SUCCESS':
      qrCodeStatus.value = 'LOGIN_SUCCESS'
      clear()
      emits('success', data)
      break
    case 'UN_REGISTER':
      ElMessage.error('您还不是平台用户！可直接扫码登录')
      qrCodeStatus.value = 'UN_REGISTER'
      clear()
      break
    case 'LOGIN_NO_PHONE':
      ElMessage.error('未绑定手机号')
      qrCodeStatus.value = 'LOGIN_NO_PHONE'
      clear()
      break
    case 'ERROR':
      qrCodeError.value = data
      clear()
      break
    default:
      ElMessage.error('未知错误！请刷新重试')
      clear()
      qrCodeStatus.value = 'UNKNOWN'
      break
  }
}

onUnmounted(() => {
  clear()
})

getCode()
</script>

<style scoped lang="scss">
.qrcode-box {
  .code-img {
    position: relative;
    width: 140px;
    height: 140px;

    img {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
    }

    .code-tips-modal {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;
      color: var(--text-color);
      gap: 10px;
      background-color: rgba(255, 255, 255, 0.95);
      justify-content: center;

      .tips-1 {
        text-align: center;
        font-size: 18px;
      }
      .tips-2 {
        text-align: center;
        font-size: 13px;

        &.refresh {
          cursor: pointer;

          &:hover {
            color: var(--el-color-primary);
          }
        }
      }
    }

    .cur {
      cursor: pointer;
    }

    &.large {
      width: 180px;
      height: 180px;

      .code-tips-modal {
        .tips-1 {
          font-size: 20px;
        }
        .tips-2 {
          font-size: 14px;
        }
      }
    }
    &.small {
      width: 100px;
      height: 100px;

      .code-tips-modal {
        gap: 0px;

        .tips-1 {
          font-size: 16px;
        }
        .tips-2 {
          width: 90%;
          font-size: 12px;
        }
      }
    }
  }

  // .zfb-pay-iframe {
  //   position: fixed;
  //   left: 50%;
  //   top: 50%;
  //   transform: translate(-50%, -50%);
  //   width: 70vw;
  //   height: 70vh;
  //   min-width: 900px;
  //   min-height: 600px;
  //   overflow: hidden;
  //   z-index: 9999;
  // }
}
</style>
