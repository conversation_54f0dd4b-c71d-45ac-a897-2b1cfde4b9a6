<template>
  <!-- 虚拟触发 -->
  <el-popover
    :placement="dynamicPlacement"
    :width="modelInfo.isShow === 1 ? 480 : 300"
    :show-after="100"
    :hide-after="0"
    :offset="0"
    @before-leave="resetModelInfo()"
    ref="popoverRef"
    :virtual-ref="virtualRef"
    :visible="popoverVisible"
    virtual-triggering
    style="border-radius: 10px; transition: transform 0.15s ease-out"
    @after-enter="checkViewportSpace()"
  >
    <div
      v-if="modelInfo.isShow === 1"
      class="model-info-box"
      v-loading="loading"
      @mouseenter="handleMouseEnter()"
      @mouseleave="handleMouseLeave()"
    >
      <div class="head-info flex-start gap" style="align-items: start">
        <div class="head-img" v-if="modelInfo.modelPic">
          <el-image class="img" :src="$picUrl + modelInfo.modelPic + '!3x4'" fit="fill" />
        </div>
        <!-- <el-image v-if="modelInfo.modelPic" class="head-img" :src="$picUrl + modelInfo.modelPic + '!3x4'" fit="fill" /> -->
        <!-- <div class="head-img img-name" v-else-if="modelInfo.name">
          {{ modelInfo.name.substring(0,1) }}
        </div> -->
        <div class="flex-center head-img gray" v-else>
          <el-icon :size="20"><Picture /></el-icon>
        </div>
        <div>
          <div class="name">{{ modelInfo.name }}</div>
          <div class="flex-start gap" style="flex-wrap: wrap">
            <!-- 国家 -->
            <NationTag :type="modelInfo.nation" isIcon />
            <!-- 平台 -->
            <!-- <PlatformTag :type="modelInfo.platform" /> -->
            <el-tag v-if="modelInfo.platform == '2'" type="warning" size="small" round>其他</el-tag>
            <!-- 模特类型 -->
            <ModelTypeTag :type="modelInfo.type" isIcon />
            <!-- 性别 -->
            <!-- <SexTag :type="modelInfo.sex" /> -->
          </div>
        </div>
      </div>

      <el-divider style="margin: 10px 0" />

      <div class="flex-start tag-box gap" v-show="modelInfo.specialtyCategory.length">
        <div style="color: #7b4d12">擅长品类</div>
        <template v-for="item in modelInfo.specialtyCategory" :key="item.dictId">
          <el-tag type="warning" round>{{ item.name }}</el-tag>
        </template>
      </div>

      <div class="flex-start videoList">
        <template v-for="(item, index) in videoList" :key="index">
          <VideoCover
            v-if="index < 4"
            :src="item.picUri"
            width="90px"
            height="90px"
            fit="cover"
            suffix="!1x1"
            :playType="false"
            @click="playVideo(item.videoUrl)"
          />
        </template>
      </div>

      <div class="flex-center">
        <el-button type="warning" link @click="toModelPage">查看模特更多详情＞</el-button>
      </div>
    </div>
    <div class="flex-column videoListEmpty" v-else-if="!loading">
      <img src="@/assets/image/home1.png" alt="" />
      <div class="text">哎呀，模特下线啦</div>
    </div>
    <div
      v-else
      v-loading="loading"
      class="flex-column"
      style="width: 100%; min-width: 150px; height: 120px"
    ></div>
  </el-popover>
  <DialogVideo ref="dialogCompRef" v-model:videoSrc="videoSrc" />
</template>

<script setup lang="ts">
import { computed, ref, nextTick, onMounted, onUnmounted } from 'vue'
import VideoCover from '@/components/public/image/VideoCover.vue'
import DialogVideo from '@/components/public/video/DialogVideo.vue'
import SexTag from '@/components/public/tag/SexTag.vue'
import NationTag from '@/components/public/tag/NationTag.vue'
import ModelTypeTag from '@/components/public/tag/ModelTypeTag.vue'
import PlatformTag from '@/components/public/tag/PlatformTag.vue'
import { useModelMap } from '@/hooks/modelDetailsInfo'
import { useRouter } from 'vue-router'
import { http_reg } from '@/utils/RegExp'
import { ElMessage } from 'element-plus'

const router = useRouter()

const { modelInfo, loading, getModelInfo, resetModelInfo } = useModelMap()

defineExpose({
  open,
  close,
})

const emits = defineEmits(['details'])

let key = 0
const popoverRef = ref()
const virtualRef = ref()
const popoverVisible = ref(false)
const popoverFocus = ref(false)
const modelId = ref<number | string>('')
const videoList = computed(() => {
  if (modelInfo.value.amazonVideo.length || modelInfo.value.tiktokVideo.length) {
    return [...modelInfo.value.amazonVideo, ...modelInfo.value.tiktokVideo]
  }
  return []
})
const dialogCompRef = ref()
const videoSrc = ref('')

function playVideo(url: string | null) {
  if (!url || !http_reg.test(url)) {
    ElMessage.warning('视频链接有误！')
    return
  }
  dialogCompRef.value.open()
  videoSrc.value = url
  // videoSrc.value = 'https://uqcf789uvlq.feishu.cn/file/QlnVbKaUrooTn3xUvNjcdQGynrg'
}

const dynamicPlacement = ref('top')

const checkViewportSpace = () => {
  nextTick(() => {
    const popoverContent = document.querySelector('.model-info-box') as HTMLElement
    if (!popoverContent) return
    const contentHeight = popoverContent.offsetHeight
    const triggerRect = virtualRef.value?.getBoundingClientRect()
    dynamicPlacement.value = contentHeight > triggerRect.top ? 'bottom' : 'top'
  })
}

function open(el: EventTarget, id: string | number) {
  key++
  getModelInfo(id)
  virtualRef.value = el
  modelId.value = id
  popoverFocus.value = false
  popoverVisible.value = true
}
function close() {
  let oldKey = key
  // 延迟关闭，不然鼠标移入不了
  setTimeout(() => {
    // console.log('close', popoverFocus.value, key, oldKey);
    if (!popoverFocus.value && key == oldKey) {
      popoverVisible.value = false
    }
  }, 50)
}

function handleMouseEnter() {
  popoverFocus.value = true
}
function handleMouseLeave() {
  popoverFocus.value = false
  close()
}

function toModelPage() {
  emits('details', modelId.value)
  // router.push({
  //   path: '/model/list',
  //   query: {
  //     id: modelId.value,
  //     name: JSON.stringify(modelInfo.value.name),
  //   },
  // })
}
</script>

<style scoped lang="scss">
.model-info-box {
  padding: 10px;

  .gap {
    gap: 10px;
  }

  .head-info {
    gap: 10px;
    margin: 10px 0;

    .head-img {
      width: 60px;
      height: 60px;
      flex-shrink: 0;
      border-radius: 50%;
      overflow: hidden;
      border: 1px solid var(--border-gray-color);
      .img {
        width: 100%;
        height: auto;
      }

      &.img-name {
        font-size: 20px;
        color: #fff;
        background-color: #3b99fc;
        text-align: center;
        line-height: 57px;
        font-weight: 600;
      }

      &.gray {
        background-color: var(--bg);
      }
    }
    .name {
      margin-bottom: 10px;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color);
    }
    .text {
      font-size: 14px;
      color: var(--text-color);
    }
  }

  .tag-box {
    margin: 20px 0;
    flex-wrap: wrap;
    max-height: 100px;
    overflow-y: auto;

    :deep(.el-tag) {
      max-width: 100%;

      .el-tag__content {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .videoList {
    flex-wrap: wrap;
    margin: 20px 0;
    gap: 20px;
  }
}
.videoListEmpty {
  img {
    width: 150px;
  }
  .text {
    font-size: 14px;
    color: #333;
  }
}
</style>
