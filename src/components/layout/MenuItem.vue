<template>
  <div
    class="menu-item"
    :class="{ 'm-t-10': item.meta.icon, 'ban-modal': itemDisabled }"
    @click.stop="handleClick"
  >
    <!--目录-->
    <el-sub-menu
      :key="item.id"
      :index="item.path"
      :disabled="itemDisabled"
      v-if="item.menu === '0' && item.children && item.children.length > 0"
      :class="{ 'hide-icon-arrow': item.meta.menuOpeneds }"
      expand-close-icon="ArrowRightBold"
      expand-open-icon="ArrowRightBold"
    >
      <template #title>
        <div class="flex-start gap-10 sub-menu-title">
          <img v-if="item.meta.icon" :src="iconUrl(item.meta.icon)" alt="" />
          <div class="no-select menu-title-text">{{ item.meta.title }}</div>
        </div>
      </template>
      <MenuItem
        v-for="child in item.children"
        :key="child.path"
        :parent="item.path"
        :item="child"
        :disabled="disabled"
        :updateMenu="updateMenu"
      />
    </el-sub-menu>
    <!--菜单-->
    <el-menu-item v-else-if="item.win" @click="routerNewWindow(item.name)">
      <span v-if="item.meta.icon" class="iconfont" :class="[`icon-${item.meta.icon}`]"></span>
      <span>{{ item.meta.title }}</span>
    </el-menu-item>
    <!--路由跳转path-->
    <template v-else>
      <el-menu-item
        v-if="!item.meta.auth || stroe.checkAuth(item.meta.auth)"
        :index="item.meta.redirect || item.path"
        :disabled="itemDisabled"
        :class="{ 'is-active': item.path === router.currentRoute.value.meta.activeMenu }"
        @click="handleRouter"
      >
        <div v-if="item.meta.icon" class="flex-start gap-10 sub-menu-title">
          <img
            v-if="(item.meta.redirect || item.path) === activeMenu"
            :src="iconUrl(item.meta.icon + '_sel')"
            alt=""
          />
          <img v-else :src="iconUrl(item.meta.icon)" alt="" />
          <div class="no-select menu-title-text">{{ item.meta.title }}</div>
        </div>
        <div v-else class="flex-start gap-10 menu-title">
          <div class="no-select menu-title-text">{{ item.meta.title }}</div>
        </div>
      </el-menu-item>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import useIcon from './useIcon'
import { computed } from 'vue'
import { routerWhiteList } from '@/router/routerData'
import { openLogin } from '@/hooks/useLogin'

import { useMobileDeviceAction } from '@/hooks/useMobileDeviceAction'
const { handleMDA } = useMobileDeviceAction()

import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { handelRenewDialog } = useShowRenewDialog()
const { iconUrl } = useIcon()
const stroe = useUserStore()
const router = useRouter()
const route = useRoute()
const props = defineProps({
  parent: {
    type: String,
    default: '',
  },
  item: {
    type: Object,
    default: () => {},
  },
  updateMenu: {
    type: Function,
  },
  activeMenu: {
    type: String,
    default: '',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
})

function handleMenuItemDisabled(path: any) {
  if (path === '/model/list' || routerWhiteList.includes(path)) {
    return true
  }
  return false
}

const itemDisabled = computed(() => {
  // 未登录时，白名单菜单可点击
  if (props.disabled && handleMenuItemDisabled(props.item.meta.redirect || props.item.path)) {
    return false
  }
  return props.disabled
})

const noShowRenewRouter = ['/center/acc', '/center/info', '/workbench/info', '/model/list']
// 未登录时，点击打开登录弹窗
function handleClick() {
  if (itemDisabled.value) {
    openLogin()
  } else {
    if (!stroe.isVip()) return
    if (!noShowRenewRouter.includes(route.fullPath)) {
      handelRenewDialog()
    }
  }
}

// 路由跳转
async function handleRouter(item: any) {
  if (item.index) {
    if (item.index != router.currentRoute.value.path) {
      if (
        (item.index != '/model/list' && !routerWhiteList.includes(item.index)) ||
        item.index === '/workbench/info'
      ) {
        let mda = await handleMDA()
        if (mda) {
          if (props.updateMenu) props.updateMenu()
          return
        }
      }
      router.push(item.index)
    }
  }
}

async function routerNewWindow(name: string) {
  let mda = await handleMDA()
  if (mda) {
    return
  }
  const { href } = router.resolve({ name })
  window.open(href, '_blank')
}
</script>

<style scoped lang="scss">
.m-t-10 {
  margin-top: 10px;
}
.sub-menu-title {
  width: 100%;
  font-size: 16px;
  padding-left: 8px;

  img {
    width: 24px;
    height: 24px;
  }

  .menu-title-text {
    width: 100%;
    padding: 0;
  }
}
.menu-title {
  width: 100%;
  font-size: 14px;
  --color: #9ba6ba;

  &::before {
    content: '';
    display: inline-block;
    flex-shrink: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--color);
  }

  .menu-title-text {
    width: 100%;
    padding: 0 0 0 10px;
  }
}
:deep(.el-menu-item) {
  padding: 0 10px 0 var(--el-menu-base-level-padding);

  &.is-active {
    .menu-title {
      --color: #09172f;

      .menu-title-text {
        border-radius: 10px 0 0 10px;
        background: linear-gradient(90deg, #d9dee8 0%, #f1f4fb 100%);
      }
    }
    .sub-menu-title {
      border-radius: 10px 0 0 10px;
      background: linear-gradient(90deg, #d9dee8 0%, #f1f4fb 100%);
    }
  }

  &.is-disabled {
    opacity: 1;
  }
}
:deep(.el-sub-menu) {
  &.is-disabled {
    .el-sub-menu__title {
      opacity: 1;
    }
  }
}

.el-menu--collapse {
  span {
    display: none;
  }
}
.iconfont {
  margin-right: 6px;
}
// :deep(.el-submenu) {
//     .el-submenu__title:hover {
//         background-color: rgba(14, 221, 240, 0.32) !important;
//     }
// }

// .el-menu-item:hover {
//     background-color: rgba(14, 221, 240, 0.32) !important;
// }
.el-menu--vertical.el-menu--popup-container {
  .el-menu-item {
    color: var(--text-color);
    // &:hover {
    //     background-color: rgba(87, 87, 87, 0.32) !important;
    // }
  }
}
</style>
