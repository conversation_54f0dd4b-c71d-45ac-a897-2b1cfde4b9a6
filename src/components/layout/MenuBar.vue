<template>
  <div id="menu">
    <div class="header">
      <!-- <img src="@/assets/wnkx_logo.png" alt=""> -->
      <img :src="$picUrl + 'static/assets/img_logo_wnkx.webp'" alt="" @click.stop="toPage('/')" />
    </div>
    <!-- <div class="bubble-box" v-if="oneOrderInfo.type && oneOrderInfo.type === 4 && isShowBubble">
      <img style="width: 114px; height: 38px" src="@/assets/image/bubble_icon.png" alt="" />
      <div class="bubble-box-btn">
        会员用户每月首单
        <br />
        返还会员费 ${{ store.userInfo.account ? oneOrderInfo.amount || 0 : twoOrderInfo.amount || 0 }}
      </div>
    </div> -->
    <div
      class="flex-end"
      style="margin-top: 8px"
      v-if="(oneOrderInfo.type && oneOrderInfo.type === 4) || showRenew"
      @click="handleToOrderCreate"
    >
      <div class="create-order-btn-2">
        <div class="flex-start create-order-btn-left-1">
          <span>
            <span>立减</span>
            <br />
            <span>$19.9</span>
          </span>
        </div>
        <div class="flex-start create-order-btn-left-2">
          <span style="transform: scale(0.9)">
            <span>会员每月首单</span>
            <br />
            <span>返会员费$19.9</span>
          </span>
        </div>
        <div class="flex-center create-order-btn-right">
          <span>
            <span>创建</span>
            <br />
            <span>订单</span>
          </span>
        </div>
        <img class="icon-shandian" src="@/assets/image/shandian.png" alt="" />
      </div>
    </div>
    <div class="flex-end" style="margin-top: 8px" v-else>
      <el-button class="create-order-btn" type="primary" plain @click="handleToOrderCreate">
        <template #icon>
          <!-- <img v-if="activeMenu == '/order/create'" src="@/assets/icon/icon-edit-2.png" alt=""> -->
          <img src="@/assets/icon/icon-edit.png" alt="" />
        </template>
        创建订单
      </el-button>
    </div>
    <div class="scroll-box">
      <el-menu
        ref="menuRef"
        class="nav-el-menu-vertical"
        :default-active="activeMenu"
        :default-openeds="defaultOpeneds"
        :collapse="collapse"
        :key="menuRefKey"
        @click.stop
        @close="handleClose"
      >
        <MenuItem
          v-for="item in navList"
          :key="item.id"
          :item="item"
          :activeMenu="activeMenu"
          :disabled="disabledMenuItem"
          :updateMenu="handleUpdateMenu"
        />
      </el-menu>

      <div v-if="isShowInviteNewItem" class="invite-new-item" @click="handleFissionActivity">
        <img src="@/assets/image/activity/fission/entrance.webp" alt="" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import MenuItem from './MenuItem.vue'
import { ref, reactive, computed, getCurrentInstance, watch, onMounted, onUnmounted } from 'vue'
import { fissionActivityConfig, getFissionDiscountV1 } from '@/api/activity/fission'
import type { routerListData, routerListObject } from '@/router/type'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { loginSuccessAction, openLogin } from '@/hooks/useLogin'
import { getChannelCode } from '@/utils/auth'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { twoOrderInfo, oneOrderInfo, handelRenewDialogV2 } = useShowRenewDialog()

import { useMobileDeviceAction } from '@/hooks/useMobileDeviceAction'
const { handleMDA } = useMobileDeviceAction()

const router = useRouter()
const store = useUserStore()

const { proxy } = getCurrentInstance() as any

const menuRef = ref()
const menuRefKey = ref(0)
// const channel = new BroadcastChannel('bubble_channel')

// const isShowBubble = ref(sessionStorage.getItem('isShowBubble') === 'false' ? false : true)
// sessionStorage.removeItem('isShowBubble')
const indexNav: routerListObject = {
  name: 'page',
  path: '/page',
  menuOrder: 0,
  menu: '1',
  meta: {
    title: '首页',
    icon: 'house',
  },
}
// console.log(store.menuList);

// 菜单列表
const navList = reactive<routerListData>(store.menuList)

const activeMenu = computed(() => router.currentRoute.value.path)

const disabledMenuItem = computed(() => {
  if (store.userInfo.account) {
    return false
  }
  return true
})

const defaultOpeneds = computed(() => {
  let i: string[] = []
  store.menuList.forEach(item => {
    if (item.path && item.meta.menuOpeneds) {
      i.push(item.path)
    }
  })
  return i
})

const props = defineProps({
  isCollapse: {
    type: Boolean,
    default: false,
  },
})

const collapse = computed(() => {
  // if(!props.isCollapse) {
  //   try {
  //     defaultOpeneds.value.forEach(item => {
  //       // console.log(item)
  //       if(item) {
  //         menuRef.value?.open(item)
  //       }
  //     })
  //   } catch (error) {
  //     console.log(error);
  //   }
  // }
  return props.isCollapse
})

const isShowInviteNewItem = ref(false)

async function getConfig() {
  let config = await fissionActivityConfig()

  if (config && config.data) {
    let discount = await getFissionDiscountV1()
    // 活动时间
    if (discount && discount.data.startTime && discount.data.endTime) {
      let now = new Date().getTime()
      let startTime = new Date(discount.data.startTime).getTime()
      let endTime = new Date(discount.data.endTime).getTime()
      if (now >= startTime && now <= endTime) {
        isShowInviteNewItem.value = true
      }
    }
  }
}
getConfig()

function handleFissionActivity() {
  if (!store.userInfo.account) {
    loginSuccessAction.value.fun = () => {
      toPage('/activity/fission')
    }
    loginSuccessAction.value.intercept = true
    openLogin()
    return
  }
  toPage('/activity/fission')
}

function handleClose(key: any, keyPath: any) {
  // console.log(key, keyPath, store.menuList);
  let item = store.menuList.find(menu => menu.path === key)
  if (item && item.meta.redirect) {
    router.push(item.meta.redirect)
  }
  if (!props.isCollapse && item?.meta.menuOpeneds) {
    menuRef.value.open(keyPath)
  }
}

function handleUpdateMenu() {
  menuRefKey.value++
}

async function handleToOrderCreate() {
  if (!store.userInfo.account) {
    openLogin()
    return
  }
  let mda = await handleMDA()
  if (mda) {
    return
  }

  // if (isShowBubble.value) {
  //   isShowBubble.value = false
  //   sessionStorage.setItem('isShowBubble', 'false')
  //   // 创建广播通道
  //   const channel = new BroadcastChannel('bubble_channel')
  //   channel.postMessage({ type: 'hideBubble' })
  //   setTimeout(() => {
  //     sessionStorage.removeItem('isShowBubble')
  //   }, 300)
  // }

  if (activeMenu.value != '/order/create') {
    toPage('/order/create')
  }
}

async function handleToFission() {
  let mda = await handleMDA()
  if (mda) {
    return
  }
  toPage('/activity/fission')
}

function toPage(path: string) {
  if (path === '/') {
    let search = getChannelCode()
    window.location.href = proxy.$officialWebsiteUrl + (search ? `?c=${search}` : '')
  } else {
    const { href } = router.resolve({ path })
    window.open(href, '_blank')
  }
}
const showRenew = computed(() => {
  if (
    twoOrderInfo.value &&
    twoOrderInfo.value.type &&
    twoOrderInfo.value.startTime &&
    twoOrderInfo.value.endTime
  ) {
    let now = new Date().getTime()
    let startTime = new Date(twoOrderInfo.value.startTime).getTime()
    let endTime = new Date(twoOrderInfo.value.endTime).getTime()
    if (twoOrderInfo.value.type === 4 && now >= startTime && now < endTime) {
      return true
    }
  }
  return false
})
onMounted(() => {
  // channel.onmessage = e => {
  //   if (e.data.type == 'hideBubble') {
  //     isShowBubble.value = false
  //   }
  // }
  if (!store.userInfo.account) {
    handelRenewDialogV2()
  }
})

onUnmounted(() => {
  // channel.close()
})
</script>

<style lang="scss" scoped>
#menu {
  // width: 250px;

  .scroll-box {
    width: 170px;
    margin-top: 10px;
    max-height: calc(100vh - 110px);
    overflow: hidden;
    // scrollbar-width: none;
    // -ms-overflow-style: none;

    &:hover {
      overflow-y: auto;
    }
    &::-webkit-scrollbar {
      /*滚动条整体样式*/
      width: 4px;
      // display: none;
    }
    // &::-webkit-scrollbar-thumb {
    //   /*滚动条里面小方块*/
    //   border-radius: 15px;
    //   box-shadow: inset 0 0 5px #669fc64d;
    //   background: #669fc64d;
    // }
    // &::-webkit-scrollbar-track {
    //   /*滚动条里面轨道*/
    //   box-shadow: inset 0 0 5px rgba(0, 220, 255, 0.2);
    //   background: #fff0;
    // }
  }

  .create-order-btn {
    width: 140px;
    height: 36px;
    font-size: 16px;
    gap: 8px;
    padding: 8px 15px 8px 11px;
    border-radius: 10px;
    justify-content: flex-start;
    font-family: wn-font;

    img {
      width: 25px;
      height: 25px;
    }

    &:hover {
      background-color: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary);
      color: var(--el-color-primary);
    }
    &.is-disabled:hover {
      background-color: var(--el-color-primary-light-9);
      border-color: var(--el-color-primary-light-8);
      color: var(--el-color-primary-light-5);
    }
  }
  .create-order-btn-2 {
    position: relative;
    width: 140px;
    height: 36px;
    font-size: 16px;
    border-radius: 10px;
    font-family: wn-font;
    overflow: hidden;
    cursor: pointer;

    .create-order-btn-left-1 {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100px;
      height: 100%;
      font-size: 12px;
      opacity: 1;
      color: #0594f3;
      background-color: rgb(181, 226, 255);
      padding-left: 10px;
      line-height: 14px;
      font-weight: bold;
      transition: 0.1s;
    }
    .create-order-btn-left-2 {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      width: 100px;
      height: 100%;
      font-size: 12px;
      padding-left: 5px;
      line-height: 14px;
      font-weight: bold;
      opacity: 0;
      color: #0594f3;
      background-color: rgb(181, 226, 255);
      transition: 0.1s;
    }
    .create-order-btn-right {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 2;
      padding-left: 11px;
      width: 82px;
      height: 100%;
      font-size: 14px;
      line-height: 14px;
      color: #fff;
      background-color: rgb(5, 148, 243);
      clip-path: polygon(8% 0, 100% 0, 100% 100%, 0 100%);
      transition: 0.1s;

      br {
        display: none;
      }
    }
    .icon-shandian {
      position: absolute;
      top: 0;
      right: 75px;
      z-index: 3;
      width: 22px;
      height: 100%;
      transform: translateX(0px);
      transition: 0.1s;
    }

    &:hover {
      .create-order-btn-left-1 {
        opacity: 0;
      }
      .create-order-btn-left-2 {
        opacity: 1;
      }
      .create-order-btn-right {
        width: 43px;
        font-size: 13px;
        padding-left: 11px;

        br {
          display: block;
        }
      }
      .icon-shandian {
        transform: translateX(38px);
      }
    }
  }

  :deep(.el-menu) {
    border-right: 0;
    background-color: transparent;
    font-family: wn-font;
    --el-menu-sub-item-height: 42px;
    --el-menu-item-height: 42px;
    --el-menu-text-color: #6a7484;
    --el-menu-active-color: #09172f;

    .el-sub-menu {
      .el-sub-menu__icon-arrow {
        right: calc(var(--el-menu-base-level-padding) + 5px);
      }
      &.hide-icon-arrow {
        .el-sub-menu__icon-arrow {
          display: none;
        }
      }
      &.is-opened {
        .el-sub-menu__icon-arrow {
          transform: rotateZ(90deg) !important;
        }
      }
    }
  }

  .header {
    width: 100%;
    text-align: center;
    font-family: wn-font;
    font-size: 24px;
    font-weight: bold;
    line-height: 37px;
    padding: 16px 0 0;

    span {
      color: var(--el-color-primary);
    }

    img {
      width: 90px;
      cursor: pointer;
    }
  }

  .invite-new-item {
    box-sizing: border-box;
    // width: 100%;
    padding: 0 8px 0 21px;
    margin-top: 20px;
    cursor: pointer;

    img {
      width: 140px;
    }
  }
}
:deep(.nav-el-menu-vertical) {
  &:not(.el-menu--collapse) {
    width: 180px;
    // min-height: 400px;
    padding-bottom: 4px;
  }

  &.ban-modal::after {
    top: 92px;
  }
}
.bubble-box {
  position: absolute;
  top: 39px;
  z-index: 33;
  left: 120px;
  &-btn {
    text-align: center;
    line-height: 12px;
    position: absolute;
    font-size: 0.75em;
    top: 5px;
    left: 11px;
    color: #5f4320;
  }
}
</style>
