<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <TransitionGroup name="breadcrumb">

      <!-- <el-breadcrumb-item v-if="currentRoute != '/page'">首页</el-breadcrumb-item> -->

      <template v-for="(item, index) in currentNavList" :key="item.name">
        <el-breadcrumb-item v-if="index < currentNavList.length" :to="{ path: item.path }">
          {{ item.meta.title }}
        </el-breadcrumb-item>
        <el-breadcrumb-item v-else>
          <span class="no-redirect">{{ item.meta.title }}</span>
        </el-breadcrumb-item>
      </template>
      
    </TransitionGroup>
  </el-breadcrumb>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
const router = useRouter()

const currentNavList = ref(router.currentRoute.value.matched.filter((item) => item.meta && item.meta.title))
const currentRoute = ref(router.currentRoute.value.path)
// console.log(router.currentRoute.value)

watch(
  () => router.currentRoute.value,
  (newX) => {
    currentNavList.value = newX.matched.filter((item) => item.meta && item.meta.title)
    currentRoute.value = router.currentRoute.value.path
    // console.log(currentNavList.value);
    // console.log(router.currentRoute.value.path)
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
@use '@/styles/transition.scss';
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 50px;
  margin-left: 24px;

  .el-breadcrumb__item {
    white-space: nowrap;
  }

  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
