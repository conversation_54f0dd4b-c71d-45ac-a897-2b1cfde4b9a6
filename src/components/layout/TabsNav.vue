<template>
  <div class="tabsNav-box">
    <!-- <ContextMenu :menu="contextMenus" @select="handleSelect($event, indexNavItem)">
      <div
        class="navItem index"
        :class="{ active: curNav === '/page' }"
        @click="toPath(indexNavItem)"
      >
        首页
      </div>
    </ContextMenu> -->
    <ContextMenu v-for="item in navList" :key="item.path" :menu="contextMenus" @select="handleSelect($event, item)">
      <div
        class="navItem"
        :class="{ active: curNav === item.path, index: item.path === '/page' }"
        @click="toPath(item)"
      >
        {{ item.title }}
        <el-icon v-if="item.path !== '/page'" class="delBtn" @click="delNavList(item.path, $event)"><Close /></el-icon>
      </div>
    </ContextMenu>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { notSaveNavRouter } from '@/router/routerData'
import ContextMenu from '@/components/public/ContextMenu.vue'
import { useUserStore } from '@/stores/modules/user'
import { routerData } from '@/router/routerData'

const router = useRouter();
// const route = useRoute();
const store = useUserStore()

type nav = {
    path: string;
    name: string;
    title: string;
};

type indexNav = {
    path: string;
    title: string;
}

const indexNavItem: indexNav = { path: '/page', title: '首页' }
const contextMenus = ref([
  {label: '刷新页面', value: 0},
  {label: '关闭当前', value: 1},
  {label: '关闭其他', value: 2},
  {label: '关闭全部', value: 3},
])

const navList = ref<nav[]>([]);

const curNav = ref<string>(router.currentRoute.value.path);

watch(
    () => router.currentRoute.value,
    () => {
      navList.value = navList.value.filter(item => !notSaveNavRouter.includes(item.path))
      curNav.value = router.currentRoute.value.path;
      let index = navList.value.findIndex(item => item.path == curNav.value);
      if (index == -1 && curNav.value !== indexNavItem.path && !router.currentRoute.value.meta.notShowNav) {
        navList.value.push({
          path: curNav.value,
          name: router.currentRoute.value.name as string,
          title: router.currentRoute.value.meta.title as string
        });
      } else if(router.currentRoute.value.meta.notShowNav) {
          let n = (router.currentRoute.value.name as string).lastIndexOf('-')
          let pName = (router.currentRoute.value.name as string).substring(0, n)
          let i = navList.value.findIndex(item => item.name == pName);
          if(i == -1){
            let obj = getParentPath(pName)
            if(obj) {
              curNav.value = obj.path
              navList.value.push(obj)
            }
          } else {
            curNav.value = navList.value[i].path
          }
      }
      // console.log(navList.value);
    },
    { immediate: true }
);
function getParentPath(name: string): nav {
  let obj: nav = {
    path: '',
    name: '',
    title: ''
  }
  for (let i = 0; i < routerData.length; i++) {
    if(routerData[i].name === name){
      obj = {
        name: routerData[i].name as string,
        path: routerData[i].path,
        title: routerData[i].meta?.title as string
      }
      break
    }
  }
  return obj
}
// 右键菜单
function handleSelect(r: any, nav: indexNav) {
  switch (r.value) {
    case 0:
      refreshCurrentRoute()
      break;
    case 1:
      closeCurrentRoute(nav.path)
      break;
    case 2:
      closeOtherRoute(nav.path)
      break;
    default:
      closeAllRoute()
      break;
  }
}
function refreshCurrentRoute() {
  // router.replace(router.currentRoute.value);
  router.go(0)
}
function closeCurrentRoute(path: string) {
  navList.value = navList.value.filter(item => item.path !== path)
  if(path === curNav.value && curNav.value !== indexNavItem.path){
    router.push(indexNavItem.path)
  }
}
function closeOtherRoute(path: string) {
  navList.value = navList.value.filter(item => item.path === path && item.path === curNav.value)
}
function closeAllRoute() {
  navList.value = []
  router.replace(indexNavItem.path)
}

function toPath(nav: indexNav) {
  if(nav.path === curNav.value) return;
  router.push(nav.path)
}
function delNavList(path: string, e: Event) {
  e.stopPropagation();
  navList.value = navList.value.filter(item => item.path !== path)
  if(path === curNav.value && curNav.value !== indexNavItem.path){
    let back = window.history.state.back
    let n = navList.value.findIndex(item => item.path === back)
    if(n != -1) {
      router.go(-1)
    } else {
      router.push(indexNavItem.path)
    }
  }
}
</script>

<style lang="scss" scoped>
.tabsNav-box {
    width: 100%;
    height: 54px;
    background-color: var(--bg);
    padding: 10px;
    margin-bottom: 8px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    gap: 10px;
    overflow: hidden;

    .navItem {
        font-size: 14px;
        height: 34px;
        line-height: 34px;
        padding: 0 14px;
        cursor: pointer;
        border-radius: 4px;
        background-color: var(--nav-bg);
        flex-shrink: 0;
        position: relative;

        .delBtn {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            background-color: #afafaf;
            font-size: 10px;
            color: #fff;
            position: absolute;
            top: 0;
            right: 0;
            transform: translate(50%, -50%);
        }
    }
    .active {
        background-color: var(--nav-active-bg);
        color: #fff;
    }

    &:hover {
        overflow-x: overlay;
    }
    &::-webkit-scrollbar {
        height: 6px;
    }
}
</style>
