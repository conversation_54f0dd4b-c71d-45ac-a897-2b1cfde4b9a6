<template>
  <div
    class="main-container"
    :class="{
      'min-w': activeMenu === '/model/list',
    }"
    @click="checkLogin"
  >
    <div class="content-head-box">
      <div class="left-box">
        <img
          v-if="isLogo"
          class="logo"
          :src="$picUrl + 'static/assets/img_logo_wnkx.webp'"
          alt=""
          @click.stop="toPage('/')"
        />
        <!-- <el-icon v-show="isCollapse" @click="collapseFn()"><Expand /></el-icon>
        <el-icon v-show="!isCollapse" @click="collapseFn()"><Fold /></el-icon>
        <Breadcrumb /> -->
        <div class="flex-start head-menu">
          <div class="item" @click.stop="toPage('/')">首页</div>
          <div
            class="item"
            :class="{ active: activeMenu === '/model/list' }"
            @click.stop="toPage('/model/list')"
          >
            模特库
          </div>
          <div class="flex-center item vip" @click.stop="toPage('/vip')">
            <img src="@/assets/icon/member_icon_2.svg" alt="" />
            <span>会员</span>
          </div>
        </div>
      </div>

      <!-- <div class="right-box" v-loginModal> -->
      <!-- <el-switch
          v-model="switchBtn"
          inline-prompt
          active-icon="Moon"
          inactive-icon="Sunny"
          style="
            --el-switch-on-color: #2c2c2c;
            --el-switch-off-color: #cecece;
          "
          @change="switchChange" /> -->

      <UserBox />
      <!-- </div> -->
    </div>

    <div
      class="page-box"
      :class="{
        'max-page-box': activeMenu === '/order/create',
      }"
    >
      <slot></slot>
    </div>

    <div class="tip-number" style="display: flex" v-show="!isMobileDevice() && activeMenu != '/order/create'">
      备案号：
      <el-link type="info" target="_blank" :underline="false" href="https://beian.miit.gov.cn/">
        闽ICP备2024057555号
      </el-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import UserBox from '@/components/layout/header/UserBox.vue'
// import Breadcrumb from '@/components/layout/Breadcrumb.vue'
// import TabsNav from '@/components/layout/TabsNav.vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { openLogin } from '@/hooks/useLogin'
import { computed, getCurrentInstance } from 'vue'
import { getChannelCode } from '@/utils/auth'
import { isMobileDevice } from '@/utils/public'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
const { handelRenewDialogV2 } = useShowRenewDialog()

const props = defineProps({
  isLogo: {
    type: Boolean,
    default: false,
  },
})

const { proxy } = getCurrentInstance() as any

const store = useUserStore()
const router = useRouter()

const activeMenu = computed(() => router.currentRoute.value.path)

function toPage(path: string) {
  if (path) {
    let search = getChannelCode()
    if (path === '/') {
      window.location.href = proxy.$officialWebsiteUrl + (search ? `?c=${search}` : '')
    } else if (path === '/vip') {
      router.push('/vip' + (search ? `?c=${search}` : ''))
    } else {
      router.push(path + (search ? `?c=${search}` : ''))
    }
  }
}

function checkLogin() {
  if (!store.userInfo.account) {
    openLogin()
  }
}
handelRenewDialogV2()
</script>

<style scoped lang="scss">
.main-container {
  flex-grow: 1;
  height: 100%;
  min-width: 1200px;

  &.min-w {
    min-width: 375px;
  }

  .page-box {
    background-color: transparent;
    padding: 0 0 0 8px;
    height: calc(100% - 90px);
    overflow: hidden;

    @include mediaTo('phone') {
      padding: 0;
    }

    &.max-page-box {
      height: calc(100% - 60px);
    }
  }

  .content-head-box {
    height: 60px;
    padding: 10px 20px 10px 10px;
    background-color: var(--bg);
    // border-bottom: 1px solid var(--border-gray-color);
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-box {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      .logo {
        width: 90px;
        margin-right: 30px;
        margin-left: 15px;
        cursor: pointer;
      }

      i {
        font-size: 22px;
        color: var(--head-icon-color);
        cursor: pointer;

        &:hover {
          transition: 0s;
          // background: #0d4184;
          filter: drop-shadow(0 0 20px var(--head-icon-color));
        }
      }

      .head-menu {
        gap: 15px;

        .item {
          cursor: pointer;
          width: 80px;
          text-align: center;
          color: #1e2937;
          font-size: 15px;
          height: 30px;
          line-height: 30px;
          border-radius: 15px;
          font-family:
            PingFangSC,
            PingFang SC;

          @include mediaTo('phone') {
            width: 66px;
            color: #9ba6ba;
          }

          img {
            width: 18px;
            height: 14px;
            padding: 0 3px;
            transform: translate(-2px, 1px);
            -moz-transform: translate(-2px, 0px);
          }

          &.vip {
            height: 30px;
            line-height: 30px;
          }

          &:hover {
            color: var(--el-color-primary);
            background-color: #ffffff99;
          }

          &.active {
            // color: var(--el-color-primary);
            cursor: auto;
            font-weight: 600;
            background-color: #ffffff99;

            @include mediaTo('phone') {
              color: #1e2937;
            }

            &:hover {
              color: #1e2937;
            }
          }
        }
        .item-n {
          cursor: context-menu;
          text-align: center;
          color: #6a7484;
          font-size: 15px;
          width: 70px;
          height: 24px;
          line-height: 26px;
          font-family:
            PingFangSC,
            PingFang SC;

          &.active {
            color: var(--el-color-primary);
          }
        }
      }
    }
  }

  .tip-number {
    height: 28px;
    line-height: 28px;
    font-size: 12px;
    color: var(--el-color-info);
    padding-left: 35px;
    background-color: transparent;

    .el-link {
      font-size: 12px;
    }
  }
}
</style>
