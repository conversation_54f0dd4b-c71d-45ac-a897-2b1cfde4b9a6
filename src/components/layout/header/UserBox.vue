<template>
  <div class="header-user-box">
    <div
      class="work-item"
      :class="{ active: router.currentRoute.value.path === '/workbench/info' }"
      @click.stop="toPage('/workbench/info')"
    >
      工作台
    </div>
    <div class="flex-end gap-5" v-if="!store.userInfo.account" @click="handleClickLogin">
      <div class="user-info">
        <div class="login-btn">登录</div>
      </div>
    </div>
    <div class="flex-end gap-5" v-else>
      <!-- <div class="user-info">
        <div class="name">{{ store.userInfo.nickName }}</div>
        <div>{{ store.userInfo.businessVO?.name }}</div>
      </div> -->
      <!-- <el-dropdown trigger="click" v-if="store.userInfo.phone" @command="handleCommand"> -->
      <el-tooltip placement="bottom" effect="light" popper-class="el-tooltip-vip" :offset="12" ref="tooltipRef">
        <template #content>
          <div class="user-business top-radius text-n-all" v-if="store.userInfo.businessVO?.name">
            {{ store.userInfo.businessVO.name }}
          </div>
          <div
            class="flex-between pd-8 vip-tooltip"
            :class="{
              'top-radius': !store.userInfo.businessVO?.name,
            }"
            v-if="store.isVip()"
          >
            <div class="flex-column gap-5 vip-tooltip-box">
              <span>蜗牛会员</span>
              <div style="line-height: 15px">
                {{
                  store.userInfo.businessVO?.memberStatus === 2
                    ? `会员将于${endTimeToDay(store.userInfo.businessVO?.memberValidity)}天后到期，请尽快续费，以免影响正常使用哦`
                    : `会员有效期至${store.userInfo.businessVO?.memberValidity}`
                }}
              </div>
              <div class="acc-tag">
                <OwnerTag />
              </div>
            </div>
            <el-button round type="primary" size="small" @click="toVipPage">
              <span v-if="isFiveRenew">半价</span>
              <span v-else>去</span>
              续费
            </el-button>
          </div>
          <div class="flex-column gap-5 pd-8" v-else>
            {{
              store.userInfo.businessVO?.memberStatus === 3
                ? `会员已于${store.userInfo.businessVO?.memberValidity}到期`
                : '立即开通，享受会员权益'
            }}
            <el-button round type="warning" size="small" @click="toVipPage">
              {{ store.userInfo.businessVO?.memberStatus === 3 ? '去续费' : '去开通' }}
            </el-button>
          </div>
          <ul class="tooltip-menu">
            <!-- <li class="flex-start gap-10" @click="routerNewWindow('/order/shoppingCart')">
              <img src="@/assets/icon/icon_shopping_cart.png" style="transform: scale(1.3)" alt="" />
              &lt;!&ndash;
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                <g fill="none" stroke="black">
                  <path
                    fill="currentColor"
                    stroke="#acb1c5b3"
                    stroke-width="80"
                    d="M432 928a48 48 0 1 1 0-96 48 48 0 0 1 0 96m320 0a48 48 0 1 1 0-96 48 48 0 0 1 0 96M96 128a32 32 0 0 1 0-64h160a32 32 0 0 1 31.36 25.728L320.64 256H928a32 32 0 0 1 31.296 38.72l-96 448A32 32 0 0 1 832 768H384a32 32 0 0 1-31.36-25.728L229.76 128zm314.24 576h395.904l82.304-384H333.44l76.8 384z"
                  ></path>
                </g>
              </svg>
              &ndash;&gt; 购物车
            </li> -->
            <li class="flex-start gap-10" @click="toPage('/order/list')">
              <img src="@/assets/icon/icon_order.png" alt="" />
              我的订单
            </li>
            <li class="flex-start gap-10" @click="toPage('/finance/wallet')">
              <img src="@/assets/icon/icon_finance.png" alt="" />
              财务管理
            </li>
            <li class="flex-start gap-10" @click="toPage('/center/info')">
              <img src="@/assets/icon/icon_personal.png" alt="" />
              个人中心
            </li>
            <li class="flex-start gap-10" @click="toPage('/help')">
              <img style="width: 17px; height: 17px" src="@/assets/icon/icon_help.png" alt="" />
              帮助中心
            </li>
            <li class="flex-start gap-10" v-if="store.userInfo.seedId" @click="openFissionEntryDialog">
              <img style="width: 17px; height: 17px" src="@/assets/icon/icon_gift.png" alt="" />
              种草官后台
            </li>
            <li class="flex-start gap-10" @click="logout()">
              <img src="@/assets/icon/icon_exit.png" alt="" />
              退出登录
            </li>
          </ul>
        </template>
        <div class="flex-center gap-5" style="position: relative">
          <el-avatar
            v-if="store.userInfo.pic"
            class="user-avatar"
            icon="UserFilled"
            :src="store.userInfo.pic"
          />
          <div v-else class="flex-center user-avatar-box">
            {{ userAvatarName }}
          </div>

          <img v-if="store.isVip()" class="vip-icon-img" src="@/assets/icon/icon_vip_y.png" alt="" />
          <img
            v-else
            class="vip-icon-img n"
            src="@/assets/icon/icon_vip_n.png"
            style="left: 9px; bottom: -1px"
            alt=""
          />
          <!-- <el-icon color="#9ba6ba"><CaretBottom /></el-icon> -->
          <div class="user-info is-login">
            <div class="name">{{ store.userInfo.nickName }}</div>
            <!-- <div>{{ store.userInfo.businessVO?.name }}</div> -->
            <template v-if="store.isVip() || store.isViped()">
              <OwnerTag />
            </template>
          </div>
        </div>
      </el-tooltip>
      <!-- <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item icon="SwitchButton" command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown> -->
    </div>

  </div>
</template>

<script setup lang="ts">
import OwnerTag from '@/components/public/tag/OwnerTag.vue'
import { useUserStore } from '@/stores/modules/user'
import { endTimeToDay } from '@/utils/time'
import { ElMessageBox } from 'element-plus'
import { computed, ref } from 'vue'
import { useRouter } from 'vue-router'
import { loginSuccessAction, openLogin } from '@/hooks/useLogin'
import { getChannelCode } from '@/utils/auth'
import { useShowRenewDialog } from '@/hooks/useShowRenewDialog'
import { openFissionEntryDialog } from '@/hooks/useSeed'

import { useMobileDeviceAction } from '@/hooks/useMobileDeviceAction'
const { handleMDA } = useMobileDeviceAction()

const { isFiveRenew } = useShowRenewDialog()
const store = useUserStore()
const router = useRouter()

const tooltipRef = ref()

const userAvatarName = computed(() => {
  if (store.userInfo.account) {
    if (store.userInfo.nickName) {
      return store.userInfo.nickName.substring(0, 1)
    }
    if (store.userInfo.name) {
      return store.userInfo.name.substring(0, 1)
    }
  }
  return ''
})

// 登录停留当前页面
const stayPages = ['/activity/fission', '/vip']
function handleClickLogin() {
  if (stayPages.includes(router.currentRoute.value.path)) {
    loginSuccessAction.value.fun = () => {
      window.location.reload()
    }
    loginSuccessAction.value.intercept = true
  }
  openLogin()
}

async function toPage(path: string) {
  let mda = await handleMDA()
  if (mda) {
    return
  }
  if (path == '/workbench/info') {
    let search = getChannelCode()
    router.push(path + (search ? `?c=${search}` : ''))
    return
  }
  if (!store.userInfo.account) {
    openLogin()
    return
  }
  router.push(path)
}

function toVipPage() {
  tooltipRef.value?.onClose()
  router.push('/vip')
  // if (store.isOwnerAcc()) {
  //   router.push('/center/vip')
  //   return
  // }
  // let div = ''
  // if (store.isVip()) {
  //   div = `<p>仅主账号可续费会员</p><p style="font-size: 14px;color: var(--text-gray-color)">如果会员快要到期，请联系主账号进行续费~</p>`
  // } else {
  //   div = `<p>仅主账号可开通会员</p><p style="font-size: 14px;color: var(--text-gray-color)">如果要开通会员，请联系主账号进行开通~</p>`
  // }
  // ElMessageBox.alert(div, '温馨提醒', {
  //   confirmButtonText: '确定',
  //   type: 'warning',
  //   dangerouslyUseHTMLString: true,
  // })
}

function routerNewWindow(path: string) {
  const { href } = router.resolve({ path })
  window.open(href, '_blank')
}

// 注销
function logout() {
  ElMessageBox.confirm('确认退出登录?', '退出提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      console.log('注销')
      store.logout()
      // ElMessage.success('注销成功')
    })
    .catch(() => {})
}
</script>

<style scoped lang="scss">
.header-user-box {
  gap: 10px;
  display: flex;
  align-content: center;
  align-items: center;
  color: #000;
  font-size: 14px;
  padding-right: 10px;

  .work-item {
    position: relative;
    cursor: pointer;
    width: 90px;
    height: 28px;
    text-align: center;
    color: var(--text-gray-color);
    font-size: 15px;
    line-height: 28px;
    margin-right: 20px;
    border-radius: 15px;
    font-family:
      PingFangSC,
      PingFang SC;
    
    @include mediaTo('phone') {
      display: none;
    }

    &::after {
      content: '';
      display: inline-block;
      height: 18px;
      width: 1px;
      background-color: var(--border-color);
      position: absolute;
      top: 50%;
      right: -5px;
      transform: translateY(-50%);
    }

    &:hover {
      color: var(--el-color-primary);
      background-color: #ffffff99;
    }

    &.active {
      background-color: #ffffff99;
      cursor: auto;

      &:hover {
        color: var(--text-gray-color);
      }
    }
  }

  .user-info {
    text-align: left;
    margin-right: 10px;
    line-height: 21px;
    font-size: 13px;
    color: var(--text-gray-color);

    &.is-login {
      @include mediaTo('phone') {
        display: none;
      }
    }

    .login-btn {
      color: var(--text-gray-color);
      font-size: 15px;
      line-height: 18px;
      cursor: pointer;
      font-family:
        PingFangSC,
        PingFang SC;
      
      @include mediaTo('phone') {
        color: #9ba6ba;
      }

      &:hover {
        color: var(--el-color-primary);
      }
    }

    .name {
      font-weight: 500;
      font-size: 16px;
      color: var(--text-color);
    }
  }

  .user-avatar {
    --el-avatar-size: 40px;
    --el-avatar-icon-size: 22px;
    cursor: pointer;
  }
  .user-avatar-box {
    cursor: pointer;
    font-size: 18px;
    color: #ffffff;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--el-color-primary-light-1);
  }

  .vip-icon-img {
    position: absolute;
    bottom: 0;
    left: 8.5px;
    width: 23px;
    height: 11px;

    &.n {
      bottom: -1px;
      left: 9px;
    }
  }

  .el-dropdown {
    color: #000;

    .name-box {
      cursor: pointer;
    }
  }
}

.top-radius {
  border-radius: 8px 8px 0 0;
}
.user-business {
  width: 100%;
  max-width: 300px;
  box-sizing: border-box;
  padding: 10px 10px 0 18px;
  background: linear-gradient(90deg, #f0fdff 0%, #f4f9ff 100%);
}
.vip-tooltip {
  max-width: 300px;
  gap: 20px;
  padding: 10px 10px 10px 18px;
  background: linear-gradient(90deg, #f0fdff 0%, #f4f9ff 100%);

  .vip-tooltip-box {
    position: relative;
    align-items: flex-start;
    color: var(--text-gray-color);
    max-width: 160px;

    span {
      font-weight: bold;
      font-size: 14px;
      color: var(--text-color);
    }

    .acc-tag {
      position: absolute;
      top: 0;
      left: 60px;
    }
  }
}
.tooltip-menu {
  margin: 0;
  padding: 5px 8px;
  font-size: 13px;
  color: #64667b;

  li {
    padding: 0 10px;
    height: 36px;
    cursor: pointer;

    &:hover {
      background-color: #f4f9ff;
    }

    &:last-child {
      border-top: 1px solid #e4e4e4;
    }
  }

  img,
  svg {
    width: 18px;
    height: 18px;
  }
}
</style>

<style lang="scss">
.el-tooltip-vip {
  padding: 0;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  // --el-bg-color-overlay: linear-gradient(90deg, #f0fdff 0%, #f4f9ff 100%);
  --el-border-color-light: #ffffff;

  .el-popper__arrow {
    left: -8px !important;
  }
}
</style>
