<template>
  <div class="flex-center page-header-box">
    <div
      class="flex-between page-header main-width"
      :style="{ 'background-color': 'rgba(255, 255, 255, ' + opacity + ')' }"
    >
      <div class="flex-start head-menu">
        <img class="logo" :src="$picUrl + 'static/assets/img_logo_wnkx.webp'" alt="" @click="toPage('/')" />
        <div class="flex-start head-menu">
          <div class="item" @click="toPage('/')">首页</div>
          <div
            class="item"
            :class="{ active: router.currentRoute.value.path === '/model/list' }"
            @click="toPage('/model/list')"
          >
            模特库
          </div>
          <div
            class="flex-center item vip"
            :class="{ active: router.currentRoute.value.path === '/vip' }"
            @click="toPage('/vip')"
          >
            <img
              v-if="router.currentRoute.value.path === '/vip'"
              src="@/assets/icon/member_icon.svg"
              alt=""
            />
            <img v-else src="@/assets/icon/member_icon_2.svg" alt="" />
            <span>会员</span>
          </div>
        </div>
      </div>
      <UserBox />
    </div>
  </div>
</template>

<script setup lang="ts">
import UserBox from '@/components/layout/header/UserBox.vue'
import { getCurrentInstance, onMounted, onUnmounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { getChannelCode } from '@/utils/auth'

const { proxy } = getCurrentInstance() as any
const router = useRouter()

const opacity = ref(1)

function toPage(path?: string) {
  if (path) {
    let search = getChannelCode()
    if (path === '/') {
      window.location.href = proxy.$officialWebsiteUrl + (search ? `?c=${search}` : '')
    } else if (path === '/vip') {
      if (router.currentRoute.value.path === '/vip') return
      router.push('/vip' + (search ? `?c=${search}` : ''))
    } else {
      router.push(path + (search ? `?c=${search}` : ''))
    }
  }
}

function handleScroll() {
  let scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  if (scrollTop < 300) {
    opacity.value = scrollTop / 300
  } else {
    opacity.value = 1
  }
}

onMounted(() => {
  if (window.innerWidth <= 768) {
    handleScroll()
    window.addEventListener('scroll', handleScroll)
  }
})
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="scss">
.page-header-box {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  width: 100%;
  min-width: 800px;
  overflow-x: auto;
  font-family:
    PingFangSC,
    PingFang SC;

  @include mediaTo('phone') {
    min-width: 375px;
  }

  .page-header {
    width: 100%;
    // max-width: 1400px;
    min-width: 800px;
    height: 62px;
    box-sizing: border-box;
    padding: 0 20px;
    background-color: #fff;

    @include mediaTo('phone') {
      min-width: 375px;
      padding: 0 12px;
    }

    .logo {
      width: 90px;
      margin-right: 30px;
      margin-left: 15px;
      cursor: pointer;
      margin-top: -1px;
      flex-shrink: 0;

      @include mediaTo('phone') {
        display: none;
      }
    }

    .head-menu {
      gap: 15px;
      align-content: baseline;

      .item {
        cursor: pointer;
        width: 80px;
        height: 30px;
        line-height: 28px;
        text-align: center;
        color: #1e2937;
        font-size: 15px;
        // padding: 1px 0;
        // margin-right: 20px;

        @include mediaTo('phone') {
          width: 66px;
          color: #9ba6ba;
        }

        img {
          width: 18px;
          padding: 0 3px;
          transform: translate(-2px, 1px);
          -moz-transform: translate(-2px, 0px);

          @include mediaTo('phone') {
            display: none;
          }
        }

        &:hover {
          color: var(--el-color-primary);
        }

        &.active {
          color: var(--el-color-primary);
          cursor: auto;
        }

        &.vip.active {
          width: 80px;
          height: 28px;
          cursor: auto;
          background-color: #14110e;
          border-radius: 25px;

          @include mediaTo('phone') {
            width: 66px;
            background-color: #ffffff99;
            color: var(--text-color);
          }

          span {
            background: linear-gradient(180deg, #fff9f3 0%, #ffbb88 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 500;

            @include mediaTo('phone') {
              -webkit-text-fill-color: initial;
            }
          }
        }
      }
    }
  }
}
</style>
