import iconMode from '@/assets/icon/icon_mode.png'
import iconModeSel from '@/assets/icon/icon_mode_sel.png'
import iconOrder from '@/assets/icon/icon_order.png'
import iconOrderSel from '@/assets/icon/icon_order_sel.png'
import iconFinance from '@/assets/icon/icon_finance.png'
import iconFinanceSel from '@/assets/icon/icon_finance_sel.png'
import iconPersonal from '@/assets/icon/icon_personal.png'
import iconPersonalSel from '@/assets/icon/icon_personal_sel.png'
import iconCollect from '@/assets/icon/icon_collect.png'
import iconCollectOn from '@/assets/icon/icon_collect_on.png'
import iconWorkbench from '@/assets/icon/icon_workbench.png'
import iconWorkbenchSel from '@/assets/icon/icon_workbench_sel.png'

const iconUrl = (icon: string) => {
  switch (icon) {
    case 'icon_mode':
      return iconMode
    case 'icon_mode_sel':
      return iconModeSel
    case 'icon_order':
      return iconOrder
    case 'icon_order_sel':
      return iconOrderSel
    case 'icon_finance':
      return iconFinance
    case 'icon_finance_sel':
      return iconFinanceSel
    case 'icon_personal':
      return iconPersonal
    case 'icon_personal_sel':
      return iconPersonalSel
      case 'icon_collect':
      return iconCollect
    case 'icon_collect_on':
      return iconCollectOn
    case 'icon_workbench':
      return iconWorkbench
    case 'icon_workbench_sel':
      return iconWorkbenchSel
    default:
      return ''
  }
}

export default function useIcon() {
  return {
    iconUrl,
  }
}
