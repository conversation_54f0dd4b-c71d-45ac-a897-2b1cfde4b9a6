<template>
  <el-upload
    list-type="picture-card"
    v-bind="$attrs"
    ref="uploadRef"
    :key="modelValue.length"
    :file-list="modelValue"
    action=""
    :accept="isMobileDevice() ? 'image/*' : ''"
    :capture="isMobileDevice() ? 'user' : ''"
    :on-preview="handlePreview"
    :on-remove="removeLifePhoto"
    :auto-upload="false"
    :on-change="handleUploadChange"
    :disabled="disabled"
    :class="{
      hidden: disabled || (limit && modelValue.length >= limit),
    }"
  >
    <slot>
      <el-icon><Plus /></el-icon>
    </slot>
  </el-upload>
  <CropperDialog
    ref="CropperDialogRef"
    :img="imgFile"
    :fixed-number-arr="fixedNumberArr"
    :previews-width="180"
    :is-show-action="false"
    @confirm="onConfirmCropper"
  />
</template>

<script setup>
import { ref, nextTick, getCurrentInstance } from 'vue'
import CropperDialog from '@/components/Cropper/cropperDialog'
import { uploadCloudFile } from '@/api/index'
import { beforeUpload, isMobileDevice } from '@/utils/public'
import { ElLoading, ElMessage } from 'element-plus'
import { useViewer } from '@/hooks/useViewer'
const { proxy } = getCurrentInstance()

const { showViewer } = useViewer()

const props = defineProps({
  modelValue: {
    type: Array,
  },
  // 截图框比例
  fixedNumberArr: {
    type: Array,
    default: () => [[2, 2]],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  autoUpload: {
    type: Boolean,
    default: true,
  },
  preview: {
    type: Boolean,
    default: false,
  },
  // 上传数量限制 0-不限制
  limit: {
    type: Number,
    default: 0,
  },
  // 文件大小
  fileSize: {
    type: Number,
    default: 1,
  },
})

const emits = defineEmits(['update:modelValue', 'onPreview', 'confirm'])

const uploadRef = ref()
const CropperDialogRef = ref()
const imgFile = ref({})

function handlePreview(file) {
  if (props.preview) {
    showViewer([file.url])
  }
  emits('onPreview', file)
}

function handleUploadChange(file) {
  if (props.limit && props.modelValue.length > props.limit) {
    ElMessage.warning(`最多可上传${props.limit}个文件`)
    return
  }
  if (!beforeUpload([file.raw], { size: props.fileSize })) {
    uploadRef.value.clearFiles()
    return
  }
  imgFile.value = file
  CropperDialogRef.value.open()
  nextTick(() => {
    uploadRef.value?.clearFiles()
  })
}

function removeLifePhoto(file) {
  let i = props.modelValue.findIndex(item => item.uid == file.uid || item.id == file.id)
  if (i > -1) {
    props.modelValue.splice(i, 1)
  }
}

// function handleClose(close) {
//   props.modelValue.length = 0
//   uploadRef.value.clearFiles()
//   close()
// }

function onConfirmCropper(img) {
  if (props.autoUpload) {
    const el_loading = ElLoading.service({
      lock: true,
      text: '上传中...',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    const formData = new FormData()
    formData.append('file', img.raw)
    uploadCloudFile(formData)
      .then(res => {
        if (res.code == 200) {
          props.modelValue.push({
            ...res.data,
            url: proxy.$picUrl + res.data.picUrl,
            uid: img.uid,
          })
        }
      })
      .finally(() => el_loading.close())
  }
  emits('confirm', img)
}
</script>

<style scoped lang="scss">
.hidden {
  :deep(.el-upload--picture-card) {
    display: none;
  }
}
</style>
