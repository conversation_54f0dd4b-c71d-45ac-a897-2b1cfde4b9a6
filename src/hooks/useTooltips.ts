import { ref } from 'vue'
import { ORDER_STATUS } from '@/utils/order'
import { dayjs } from 'element-plus'

const tooltips: any = {
  // 模特库
  // 'model-title-platform': '模特支持拍摄的平台',
  'model-title-category': '根据产品类目快速查找擅长拍摄的模特',
  // 'model-title-type': '平台现有模特类型',
  'model-yxz':
    '▲仅支持美国亚马逊平台可选<br/>在亚马逊拥有个人店铺且有一定粉丝，<br/>其视频只发在自己的Amazon Shop并关联至卖家产品页面的Videos区域。',
  'model-sr':
    '即普通视频创作者。他们提供免费版权以及免费原片，<br />您可将原片用于任何渠道进行商用。<br/>若需要视频版权在其他平台投放，只能选择素人创作者。',
  // 订单列表 tab
  [`order-tab-${ORDER_STATUS['需支付']}`]: '需要您支付的订单',
  [`order-tab-${ORDER_STATUS['待审核']}`]: '订单审核中',
  [`order-tab-${ORDER_STATUS['待匹配']}`]: '视频创作者对接中',
  [`order-tab-${ORDER_STATUS['需发货']}`]: '模特匹配成功，可以发货的订单',
  [`order-tab-${ORDER_STATUS['待完成']}`]: '等待视频创作者完成视频',
  [`order-tab-${ORDER_STATUS['需确认']}`]: '拍摄已完成，可以确认拍摄内容',
  // 订单列表
  'order-list-huan': '您选择的模特最近没有空档啦~已为您重新匹配过合适的模特进行拍摄~',
  'order-list-intentionModel': '商家选择的视频创作者',
  'order-list-shootModel': '实际拍摄的视频创作者',
  // 创建订单
  'order-create-platform': '产品发布平台',
  'order-create-videoFormat': '视频展示比例',
  'order-create-nation': '拍摄地所属国家',
  'order-create-model-sr':
    '即普通视频创作者。他们提供免费版权以及免费原片，<br/>您可将原片用于任何渠道进行商用;<br/>若需要视频版权在其他平台投放，只能选择素人创作者。',
  'order-create-model-yxz':
    '▲仅支持美国亚马逊平台可选<br/>在亚马逊拥有个人店铺且有一定粉丝，<br/>其视频只发在自己的Amazon Shop并关联至卖家产品页面的Videos区域。',
  'order-create-model-yxz-disabled':
    '此类型模特仅支持<span style="color:#ffad00">美国亚马逊</span>平台的订单可选，<br/>且无法提供拍摄照片服务。',
  'order-create-link-wlj-disabled':
    '亚马逊影响者需要知道产品链接，拍摄的视频后续会发在自己的<br/>Amazon Shop并关联至卖家产品页面的Videos区域。',
  'order-create-productLink': '请填入您的产品链接（注意：仅可填写一个<br/>有效链接），若暂无链接，可上传主图图片。',
  'order-create-intentionModel':
    '我们会根据您选择的意向拍摄模特优先进行匹配，<br/>如因模特的档期安排等情况无法接单，我们将会为您匹配最合适的拍摄模特。',
  'order-create-referenceVideoLink': '提供参考视频的链接供模特拍摄参考',
  'order-create-picCount': '可额外加购图片拍摄服务',
  // 财务管理
  'finance-wallet-amount': '可支配资金数额',
  'finance-wallet-origins': '主要分为收入来源和支出方向两部分',
  // 支付页
  'order-pay-video-commission': '拍摄视频佣金费用',
  'order-pay-picCount': '拍摄照片佣金费用',
  'order-pay-service': '预服务费包含匹配/对接/剪辑/上传/售后等服务。',
  'order-pay-taxes': `▲佣金代缴税费=(视频佣金 + 照片佣金)* 0.06，服务费不额外收取税费<br/>▲您与我司合作支付的所有费用，我司均可为您提供正规发票，确保您的财务流程合规。<br/>《中华人民共和国增值税暂行条例》为中国增值税的基本法规，其中涉及到境外单位或个人在境内提供应税劳务时的代扣代缴规定。<br/>具体内容可以通过访问国家税务总局官网查看详细条例<a href="https://12366.chinatax.gov.cn/bzds/010/010-5-2.html" target="_blank" style="color: #fff;">https://12366.chinatax.gov.cn/bzds/010/010-5-2.html</a>`,
  'order-pay-PayPal':
    '美国PayPal官方支付佣金手续费：$0.3+4.40%<br/>PayPal商家费用链接：<a href="https://www.paypal.com/c2/webapps/mpp/merchant-fees#fixed-fees-commercialtrans" target="_blank" style="color: #fff;">https://www.paypal.com/c2/webapps/mpp/merchant-fees#fixed-fees-commercialtrans</a>',
  'order-pay-rate': '根据订单生成时间获取的实时百度汇率',
}

const tooltipKey = ref(0)
const tooltipVisible = ref(false)
const tooltipContent = ref('')
const tooltipsTriggerRef = ref(null)
const tooltipsOptions = ref(initOptions())

// const init = () => {
  // const target = dayjs('2025-06-01 00:00:00')
  // const isAfterOrEqual = dayjs().isSame(target, 'second') || dayjs().isAfter(target)
  // if (isAfterOrEqual) {
  //   tooltips['order-pay-service'] = ``
  // }
// }

function initOptions() {
  return {
    placement: 'top',
    effect: 'dark',
    offset: 5,
    // 延迟会导致移入其他元素时tooltip消失
    showAfter: 0,
    hideAfter: 0,
  }
}

/**
 *
 * @param event 元素
 * @param content key or value
 * @param isValue content is value 默认false
 * @returns
 */
function showTooltips(event: any, content: string, isValue?: boolean) {
  // init()
  tooltipsTriggerRef.value = event.target
  if (isValue && content) {
    tooltipContent.value = content
    tooltipVisible.value = true
    return
  }
  if (tooltips[content]) {
    tooltipContent.value = tooltips[content]
    tooltipVisible.value = true
  }
}

function getTipContent(type: string) {
  return tooltips[type]
}

export function getTooltipFields() {
  return {
    tooltipsOptions,
    tooltipKey,
    tooltipVisible,
    tooltipContent,
    tooltipsTriggerRef,
  }
}

export function useTooltips() {
  return {
    tooltipKey,
    tooltipVisible,
    tooltipContent,
    tooltipsTriggerRef,
    showTooltips,
    getTipContent,
  }
}
