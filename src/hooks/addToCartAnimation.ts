import { useUserStore } from '@/stores/modules/user'
/**
 * 加入购物车动画
 * @param className
 * @returns
 */
export function addToCartAnimation(className: string) {
  if(!className) return
  const cartFloat = document.querySelector('.shopping-cart-flaot')
  const container = document.querySelector(className)
  // console.log(cartFloat, container);
  if (!cartFloat || !container) return
  const carRect = cartFloat.getBoundingClientRect()
  const jumpTarget = {
    x: carRect.left + carRect.width / 2,
    y: carRect.top + carRect.height / 5,
  }
  const rect = container.getBoundingClientRect()
  const start = {
    x: rect.left,
    y: rect.top,
  }
  
  
  let div = document.createElement('div')
  div.className = 'add-to-car'
  let i = document.createElement('i')
  i.className = 'icon'
  // 设置初始位置
  div.style.transform = `translateX(${start.x}px)`
  i.style.transform = `translateY(${start.y}px)`
  div.appendChild(i)
  document.body.appendChild(div)
  // 强行渲染
  div.clientWidth

  // 设置结束位置
  div.style.transform = `translateX(${jumpTarget.x}px)`
  i.style.transform = `translateY(${jumpTarget.y}px)`

  cartFloat.addEventListener('animationend', function () {
    cartFloat.classList.remove('add-to-car-animate')
  });
  div.addEventListener(
    'transitionend',
    function () {
      div.remove()
      cartFloat.classList.add('add-to-car-animate')
      useUserStore().getShoppingCartCount()
      useUserStore().getShoppingCartList()
    },
    {
      once: true, // 事件仅触发一次
    }
  )
}
