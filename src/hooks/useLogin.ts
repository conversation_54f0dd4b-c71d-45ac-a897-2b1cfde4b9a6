import Cookies from 'js-cookie'
import { ref } from 'vue'
import { isMobileDevice } from '@/utils/public'

export const loginDialogVisible = ref(false)

// 登录成功后跳转前执行的函数
export const loginSuccessAction = ref<{
  fun: (() => void) | null // 执行的函数
  eval: string // 执行的函数的字符串
  intercept: boolean  // 是否拦截默认的跳转
}>({
  fun: null,
  eval: '',
  intercept: false
})

export function openLogin() {
  loginDialogVisible.value = true
  if (window.innerWidth <= 768 || isMobileDevice()) {
    Cookies.set('h5-login-tip', new Date().getTime() + '', { domain: '.woniu.video', expires: 0.01 })
  }
}

export function closeLogin() {
  loginSuccessAction.value = {
    fun: null,
    eval: '',
    intercept: false
  }
  loginDialogVisible.value = false
  if (window.innerWidth <= 768 || isMobileDevice()) {
    Cookies.remove('h5-login-tip', { domain: '.woniu.video', expires: 0.01 })
  }
}
