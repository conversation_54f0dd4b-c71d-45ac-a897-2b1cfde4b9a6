import { ref } from 'vue'

const welcomeDialog = ref(false)

const form = ref({
  phone: '',
  code: '',
  businessName: '',
  name: '',
  scale: 1,
})

function resetForm() {
  form.value = {
    phone: '',
    code: '',
    businessName: '',
    name: '',
    scale: 1,
  }
}

export const businessScale = [
  { value: 1, label: '1~20人' },
  { value: 2, label: '21~100人' },
  { value: 3, label: '101~200人' },
  { value: 4, label: '201~1000人' },
  { value: 5, label: '1000人以上' },
]

export function showWelcomeDialog(params?: any) {
  if (params) {
    form.value = {
      phone: '',
      code: '',
      businessName: params.businessName,
      name: params.name,
      scale: params.scale || undefined,
    }
  } else {
    resetForm()
  }
  welcomeDialog.value = true
}

export function hideWelcomeDialog() {
  welcomeDialog.value = false
}

export function useWelcome() {
  return {
    welcomeDialog,
    form,
  }
}
