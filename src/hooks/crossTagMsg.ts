import { BroadcastChannel } from 'broadcast-channel'
const channel = new BroadcastChannel('cross-tag-msg')

export enum CrossTagMsgType {
  // 购物车页相关
  ORDER_CART_DELETE = 'order_cart_delete',
  ORDER_CART_EDIT = 'order_cart_edit',
  ORDER_CART_ADD = 'order_cart_add',
  // 创建订单页相关
  ORDER_FORM_UPDATE = 'order_form_update',
  ORDER_FORM_SUBMIT = 'order_form_submit',
}

/**
 * 跨标签页通信
 * @param type
 * @param msg
 */
export function crossTagSendMsg(type: CrossTagMsgType, msg: any) {
  channel.postMessage({ type, msg })
}

/**
 * 接收跨标签页通信信息
 * @param callback
 */
export function crossTagListenMsg(callback: (data: any) => void) {
  channel.addEventListener('message', e => {
    e && callback && callback(e)
  })
}
