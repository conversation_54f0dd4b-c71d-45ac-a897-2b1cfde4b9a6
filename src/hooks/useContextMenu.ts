import { onMounted, onUnmounted, ref } from 'vue'
import type { Ref } from 'vue'
/**
 *
 * @param container
 * @returns
 */
export function useContextMenu(container: Ref) {
  const x = ref(0)
  const y = ref(0)
  const visible = ref(false)

  function showMenu(e: MouseEvent) {
    e.preventDefault()
    e.stopPropagation()
    x.value = e.clientX
    y.value = e.clientY
    visible.value = true
  }

  function closeMenu() {
    visible.value = false
  }

  onMounted(() => {
    container.value.addEventListener('contextmenu', showMenu)
    window.addEventListener('click', closeMenu, true)
    window.addEventListener('contextmenu', closeMenu, true)
  })

  onUnmounted(() => {
    if (container.value)
      container.value.removeEventListener('contextmenu', showMenu)
  })

  return {
    x,
    y,
    showMenu: visible,
  }
}
