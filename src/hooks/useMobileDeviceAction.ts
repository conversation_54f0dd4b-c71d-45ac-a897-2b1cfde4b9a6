import { ref } from 'vue'
import { isMobileDevice } from '@/utils/public'

const isMobile = ref(false)
const mobileResolve = ref<null | ((value: any) => void)>(null)
const mobileVisible = ref(false)

function handleClose() {
  mobileVisible.value = false
  mobileResolve.value = null
}
function handleCancel() {
  mobileVisible.value = false
  if (mobileResolve.value !== null) {
    mobileResolve.value(false)
    mobileResolve.value = null
  }
}
function handleConfirm() {
  mobileVisible.value = false
  if (mobileResolve.value !== null) {
    mobileResolve.value(true)
    mobileResolve.value = null
  }
}

export function useMobileDeviceDialog() {
  return {
    mobileResolve,
    mobileVisible,
    handleClose,
    handleCancel,
    handleConfirm,
  }
}

function handleMDA() {
  return new Promise(resolve => {
    if (mobileVisible.value) return
    if (!isMobile.value) {
      resolve(false)
      return
    }
    mobileVisible.value = true
    mobileResolve.value = resolve
  })
}

export function useMobileDeviceAction() {
  isMobile.value = isMobileDevice()

  return {
    handleMDA,
  }
}
