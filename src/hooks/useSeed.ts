import { ref } from 'vue'
import { useUserStore } from '@/stores/modules/user'
import html2canvas from 'html2canvas'
import QRCode from 'qrcode'
import { ElMessage } from 'element-plus'

const qrCodeDataUrl = ref('')
const qrcodeLoading = ref(true)
const qrcodeError = ref(false)
const qrcodeErrorMsg = ref('')
const saveLoading = ref(false)

const dialogVisible = ref(false)

export function useSeed() {
  return {
    qrCodeDataUrl,
    qrcodeLoading,
    qrcodeError,
    qrcodeErrorMsg,
    saveLoading,
    dialogVisible,
  }
}

export function openFissionEntryDialog() {
  getCode()
  dialogVisible.value = true
}

async function getCode() {
  qrcodeLoading.value = true
  let bizUserId = useUserStore().userInfo.bizUserId
  if (bizUserId) {
    qrcodeError.value = false
    const http = encodeURIComponent(import.meta.env.VITE_APP_FISSION_INVITE_API + '/channel')
    const url = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx552e48004afb1d3e&redirect_uri=${http}&response_type=code&scope=snsapi_userinfo&state=ChannelREG${bizUserId}`
    try {
      const dataUrl = await QRCode.toDataURL(url, {
        errorCorrectionLevel: 'H',
        width: '100%',
      })
      qrCodeDataUrl.value = dataUrl
      qrcodeLoading.value = false
    } catch (error: any) {
      console.error('Failed to generate QR Code', error)
      qrcodeLoading.value = false
      qrcodeError.value = true
      qrcodeErrorMsg.value = 'Failed to generate QR Code'
    }
  } else {
    qrcodeError.value = true
    qrcodeErrorMsg.value = '用户信息错误'
  }
}
// 保存二维码
async function saveQrCode() {
  saveLoading.value = true
  try {
    const dom = document.getElementById('wn-fission-qrcode-box') as HTMLElement
    if (!dom) {
      saveLoading.value = false
      ElMessage.error('保存出错了！')
      console.error('Element null')
      return
    }
    const canvas = await html2canvas(dom, {
      useCORS: true,
      scale: 2,
      allowTaint: true,
      backgroundColor: '#fff',
    })
    const img = canvas.toDataURL('image/png')
    const a = document.createElement('a')
    a.href = img
    if (useUserStore().userInfo.nickName) {
      a.download = `${useUserStore().userInfo.nickName}的活动入口码.png`
    } else {
      a.download = '活动入口码.png'
    }
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    saveLoading.value = false
  } catch (error) {
    saveLoading.value = false
    console.error('Error capturing element:', error)
    ElMessage.error('保存出错了！')
  }
}
