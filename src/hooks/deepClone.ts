type result = {
    [x: string]: any;
};
const cache = new WeakMap();
/**
 * 深克隆
 * @param value
 */
export function deepClone(value: any): any {
    // 原始类型直接返回
    if (typeof value !== 'object' || value === null) {
        return value;
    }
    // Date
    if (value instanceof Date) {
        return new Date(value);
    }
    // RegExp
    if (value instanceof RegExp) {
        return new RegExp(value);
    }
    // 解决环形引用(循环引用)
    const cached = cache.get(value);
    if (cached) {
        return cached;
    }
    // Array or Object
    const result: result = Array.isArray(value) ? [] : {};
    // 原型保持一致
    Object.setPrototypeOf(result, Object.getPrototypeOf(value));
    cache.set(value, result);
    for (const key in value) {
        // console.log(key, value[key]);
        // 不克隆原型
        if (value.hasOwnProperty(key)) {
            result[key] = deepClone(value[key]);
        }
    }
    return result;
}
