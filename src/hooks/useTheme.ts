import { ref, watchEffect } from 'vue'
import { useDark, useToggle } from '@vueuse/core'

export const LOCAL_THEME_KEY = '__theme__'

export type Theme = 'light' | 'dark';

const theme = ref<Theme>(localStorage.getItem(LOCAL_THEME_KEY) as Theme || 'light')

const isDark = useDark({
  storageKey: 'THEME_KEY',
  valueDark: 'dark',
  valueLight: 'light',
})

const toggleDark = useToggle(isDark)

watchEffect(() => {
  document.documentElement.dataset.theme = theme.value
  localStorage.setItem(LOCAL_THEME_KEY, theme.value)
  theme.value === 'dark' ? toggleDark(true) : toggleDark(false)
})

export function useTheme() {
  return {
    theme
  }
}

// document.getElementsByTagName('body')[0].style.setProperty('--login-box-bg', '#ffffff80')
// document.getElementsByTagName('body')[0].style.setProperty('--login-title', '#3b99fc')
