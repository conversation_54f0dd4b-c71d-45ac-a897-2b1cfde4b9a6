import { ref } from 'vue'
import {
  getBusinessParticipatoryActivity,
  getRemindBusinessMemberHalfPriceRenewal,
  getValidPromotionActivityList,
} from '@/api/order'

interface OneOrderInfo {
  activityName?: string
  amount?: number
  currency?: string
  discountType?: number
  id?: number
  type?: number
  startTime?: string
  endTime?: string
}

const showRenewDialog = ref(false)
const isFiveRenew = ref(false)
const oneOrderInfo = ref<OneOrderInfo>({})
const twoOrderInfo = ref<OneOrderInfo>({})
//弹窗
function handelRenewDialog() {
  getBusinessParticipatoryActivity().then(res => {
    isFiveRenew.value = false
    if (res.data && res.data.length > 0) {
      isFiveRenew.value = res.data.some((item: any) => item.type === 2)
      oneOrderInfo.value = res.data.find((item: any) => item.type === 4) || {}
    }
  })
  getValidPromotionActivityList().then(res => {
    if (res.data && res.data.length > 0) {
      twoOrderInfo.value = res.data.find((item: any) => item.type === 4) || {}
      // memberActivity.value = res.data.some((item: any) => item.type === 4)
    }
  })
  if (window.location.pathname != '/vip') {
    getRemindBusinessMemberHalfPriceRenewal().then(res => {
      showRenewDialog.value = res.data
    })
  }
}

function handelRenewDialogV2() {
  getValidPromotionActivityList().then(res => {
    if (res.data && res.data.length > 0) {
      twoOrderInfo.value = res.data.find((item: any) => item.type === 4) || {}
    }
  })
}

export function useShowRenewDialog() {
  return {
    isFiveRenew,
    showRenewDialog,
    handelRenewDialog,
    oneOrderInfo,
    twoOrderInfo,
    handelRenewDialogV2
  }
}
