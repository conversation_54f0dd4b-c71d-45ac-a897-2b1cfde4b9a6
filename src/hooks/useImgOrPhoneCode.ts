import { ref } from 'vue'
import { sendSms, registerSendSms, getImgCode } from '@/api/user'
import { ElMessage } from 'element-plus'

const codeTime = ref(0)
const codeBtnLoading = ref(false)

const imgCodeDialogVisible = ref(false)
const imgCodeUrl = ref('')
const imgCode = ref('')
const imgCodeUUID = ref('')
const imgCodeLoading = ref(false)

let sendApi: (data: any) => Promise<any> = () => Promise.resolve()

const phoneForm = ref({
  phone: '',
  code: '',
})

// 获取图片验证码
function getImageCode() {
  if (imgCodeLoading.value) return
  imgCodeLoading.value = true
  imgCode.value = ''
  imgCodeUUID.value = ''
  getImgCode()
    .then(res => {
      if (res.code === 200) {
        imgCodeUrl.value = 'data:image/gif;base64,' + res.img
        imgCodeUUID.value = res.uuid
      }
    })
    .finally(() => (imgCodeLoading.value = false))
}
// 检测图片验证码
function checkImgCode(close: () => void) {
  sendPhoneCode({ phoneNum: phoneForm.value.phone, code: imgCode.value, uuid: imgCodeUUID.value }, close)
}

// 发送手机验证码
function sendPhoneCode(data: any, fun?: () => void) {
  if (codeBtnLoading.value) return

  let { isRegister, ...params } = data
  sendApi = isRegister ? registerSendSms : sendSms

  if (!params.uuid) {
    imgCodeDialogVisible.value = false
  }

  codeBtnLoading.value = true
  sendApi(params)
    .then((res: any) => {
      if (res.msg !== '验证码错误') {
        imgCodeDialogVisible.value = false
        // ElMessage.warning(res.msg)
      }
      if (res.code === 200) {
        if (res.data) {
          codeTime.value = 60
          const timer = setInterval(() => {
            codeTime.value--
            if (codeTime.value === 0) {
              clearInterval(timer)
              codeTime.value = 0
            }
          }, 1000)
        } else if (res.msg.indexOf('图片验证') !== -1 || params.uuid) {
          getImageCode()
          imgCodeDialogVisible.value = true
        }
        // else if (res.msg.indexOf('锁定')) {
        //   ElMessage.error(res.msg || '验证码获取次数过多，已自动锁定，请10分钟后再试')
        // }
        if (res.msg) {
          // ElMessage.warning(res.msg)
          ElMessage({ message: res.msg, type: 'warning', customClass: 'wn-err-msg' })
          // res.msg.indexOf('锁定') ? ElMessage.error(res.msg) : ElMessage.warning(res.msg)
        }
      }
    })
    .catch(() => {
      if (params.uuid) {
        getImageCode()
      }
    })
    .finally(() => {
      codeBtnLoading.value = false
      if (fun) fun()
    })
}

export default function useImgOrPhoneCode() {
  return {
    codeTime,
    codeBtnLoading,
    imgCodeDialogVisible,
    imgCodeUrl,
    imgCode,
    imgCodeUUID,
    imgCodeLoading,
    phoneForm,
    getImageCode,
    checkImgCode,
    sendPhoneCode,
  }
}
