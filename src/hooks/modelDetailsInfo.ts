import { ref } from 'vue'
import { modelMerchantDetails } from '@/api/model'
import type { modelItem } from '@/views/model/type/model'

const modelInfoMap = new Map()
const modelInfo = ref<modelItem>({
  amazonVideo: [],
  name: '',
  platform: '',
  specialtyCategory: [],
  tags: [],
  tiktokVideo: [],
  type: 0,
  account: '',
  status: ''
})
const loading = ref(false)

const getModelInfo = (id: number | string) => {
  if (!id) {
    return
  }
  loading.value = true
  if (modelInfoMap.has(id)) {
    modelInfo.value = modelInfoMap.get(id)
    loading.value = false
  } else {
    modelMerchantDetails(id)
      .then(res => {
        modelInfo.value = res.data
        modelInfoMap.set(id, res.data)
      })
      .finally(() => (loading.value = false))
  }
}
const resetModelInfo = () => {
  modelInfo.value = {
    amazonVideo: [],
    name: '',
    platform: '',
    specialtyCategory: [],
    tags: [],
    tiktokVideo: [],
    type: 0,
    account: '',
    status: ''
  }
  loading.value = true
}

export function useModelMap() {
  return {
    modelInfo,
    loading,
    getModelInfo,
    resetModelInfo,
  }
}
