import { ElMessageBox, ElMessage, ElLoading } from 'element-plus'
import {
  cancelOrder,
  orderAffirm,
  orderConfirmModel,
  checkShippingAddress,
  applyRefund,
  cancelRefund,
  reopenOrder
} from '@/api/order'

export default function useOrderApi() {
  /**
   * 取消订单
   * @param orderNum 订单号
   * @param callback 成功回调
   * @param error 失败回调
   */
  function handleCancelOrder(orderNum: string, callback?: (res: any) => void, error?: (err: any) => void) {
    ElMessageBox.confirm('确认取消订单？', '温馨提示', {
      confirmButtonText: '确定',
      cancelButtonText: '再想想',
    })
      .then(() => {
        const el_loading = ElLoading.service({
          lock: true,
          text: '正在取消中，请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        })
        cancelOrder({orderNum})
          .then(res => {
            ElMessage.success('取消成功')
            if (callback) callback(res)
          })
          .catch((e) => {
            if (error) error(e)
          })
          .finally(() => el_loading.close())
      })
      .catch(() => {})
  }

  /**
   * 检测模特收货地址是否更新
   * @param id 订单id
   * @param callback 成功回调
   * @param error 失败回调
   */
  function checkModelAddress(id: any, callback?: (res: any) => void, error?: (err: any) => void) {
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在加载中',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    checkShippingAddress({ videoId: id })
      .then(res => {
        if (callback) callback(res)
      })
      .catch((e) => {
        if (error) error(e)
      })
      .finally(() => el_loading.close())
  }

  /**
   * 商家确认模特
   * @param id 订单id
   * @param callback 成功回调
   * @param error 失败回调
   */
  function handleConfirmModel(id: any, callback?: (res: any) => void, error?: (err: any) => void) {
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在确认中，请稍后',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    orderConfirmModel({ id })
      .then(res => {
        ElMessage.success('确认成功')
        if (callback) callback(res)
      })
      .catch((e) => {
        if (error) error(e)
      })
      .finally(() => el_loading.close())
  }

  /**
   * 商家确认成品/确认素材
   * @param id 订单id
   * @param callback 成功回调
   * @param error 失败回调
   */
  function handleConfirmProduct(data: any, callback?: (res: any) => void, error?: (err: any) => void) {
    const el_loading = ElLoading.service({
      lock: true,
      text: '正在确认中，请稍后',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    orderAffirm(data)
      .then(res => {
        ElMessage.success('操作成功')
        if (callback) callback(res)
      })
      .catch((e) => {
        if (error) error(e)
      })
      .finally(() => el_loading.close())
  }

  /**
   * 申请退款
   * @param data
   * @param callback 成功回调
   * @param error 失败回调
   */
  function handleApplyRefund(data: any, callback?: (res: any) => void, error?: (err: any) => void) {
    ElMessageBox.confirm('确认申请退款？', '温馨提示', {
      confirmButtonText: '确定',
      cancelButtonText: '再想想',
    }).then(() => {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在执行中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      applyRefund(data)
        .then(res => {
          ElMessage.success('操作成功')
          if (callback) callback(res)
        })
        .catch((e) => {
          if (error) error(e)
        })
        .finally(() => el_loading.close())
    }).catch(() => {})
  }

  /**
   * 商家取消退款
   * @param ids 订单id
   * @param callback 成功回调
   * @param error 失败回调
   */
  function handleCancelRefund(ids: number[], callback?: (res: any) => void, error?: (err: any) => void) {
    ElMessageBox.confirm('确认取消退款？', '温馨提示', {
      confirmButtonText: '确定',
      cancelButtonText: '再想想',
    }).then(() => {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在执行中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      cancelRefund(ids)
        .then(res => {
          ElMessage.success('操作成功')
          if (callback) callback(res)
        })
        .catch((e) => {
          if (error) error(e)
        })
        .finally(() => el_loading.close())
    }).catch(() => {})
  }

  /**
   * 开启订单
   * @param orderNum 订单号
   * @param callback 成功回调
   */
  function handleReopenOrder(orderNum: string, callback?: (res: any) => void) {
    ElMessageBox.confirm('确认开启订单？', '温馨提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    }).then(() => {
      const el_loading = ElLoading.service({
        lock: true,
        text: '正在执行中',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      reopenOrder({
        orderNum
      })
        .then(res => {
          ElMessage.success('操作成功')
          if (callback) callback(res)
        })
        .finally(() => el_loading.close())
    }).catch(() => {})
  }

  return {
    handleCancelOrder,
    checkModelAddress,
    handleConfirmModel,
    handleConfirmProduct,
    handleApplyRefund,
    handleCancelRefund,
    handleReopenOrder
  }
}