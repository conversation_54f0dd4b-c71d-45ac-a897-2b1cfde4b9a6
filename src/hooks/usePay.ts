import { ElMessageBox } from 'element-plus'
import { computed, onUnmounted, ref } from 'vue'
import { orderPayCheck } from '@/api/order'
import { checkAnotherPay } from '@/api/anotherPay'
import { PAY_TYPE } from '@/utils/order'
import { useRouter } from 'vue-router'

const underWay = ref(true)
const routerPathUrl = ref('/order/list')
const mergeOrderNum = ref('')
const orderNum = ref('')
const platform = ref<number>()
const isOpenPayDialog = ref(false)
const checkPlatform = computed({
  get: () => {
    if (isOpenPayDialog.value && platform.value === PAY_TYPE['微信支付']) {
      return PAY_TYPE['微信支付']
    }
    if (isOpenPayDialog.value && platform.value === PAY_TYPE['支付宝支付']) {
      return PAY_TYPE['支付宝支付']
    }
    return undefined
  },
  set: (val: number) => {
    platform.value = val
  },
})
// 是否代付中
const isAnotherPay = ref(false)
const isAnotherPayCheck = ref(false)

// 是否在当前页面 防止多次跳转
const hasCurPage = ref(false)

// 支付成功提示弹窗
const payTipDialogVisible = ref(false)
// 支付成功提示类型 0: 视频订单支付成功 1: 提交成功 2: 会员订单支付成功 3: 订单已合并
const payTipType = ref(0)

function jumpPage(content?: string, title?: string) {
  ElMessageBox.alert(content || '订单状态已变更', title || '支付提示', {
    showClose: false,
    confirmButtonText: '确定',
    callback: () => {
      window.location.href = routerPathUrl.value
    },
  })
}

const payStatus = [
  '交易已关闭',
  '订单已支付',
  '退款中',
  '未支付',
  '已撤销',
  '用户支付中',
  '支付失败',
  '未知状态',
  '交易结束',
  '支付提交成功',
]

/**
 * 代付页-支付状态改变停止轮询时触发
 */
let anotherPayStatusChange = (status: number) => {
  if (status === 12) {
    window.location.reload()
  }
}

/**
 * 钱包充值-支付状态改变停止轮询时触发
 */
let topUpPayStatusChange = (status: number, state: any) => {
  if (status == 1) {
    history.replaceState(state, '', '/finance/wallet/paySucceed')
    window.location.replace('/finance/wallet/paySucceed')
  }
}

const checkFunc = (reslove: any, reject: any) => {
  // if (!underWay.value) {
  //   reject('!underWay')
  //   return
  // }
  let func = isAnotherPayCheck.value ? checkAnotherPay : orderPayCheck
  func({
    orderNum: !mergeOrderNum.value ? orderNum.value : undefined,
    mergeId: mergeOrderNum.value || undefined,
    platform: checkPlatform.value,
  })
    .then((res: any) => {
      if (!hasCurPage.value) {
        reject('!hasCurPage')
        return
      }
      if (!underWay.value) {
        // window.location.href = routerPathUrl.value
        reject('!underWay')
        return
      }
      // 1: 订单已支付 9: 支付提交成功 13：订单已合并
      if (
        !isAnotherPayCheck.value &&
        (res.data.payStatus === 1 || res.data.payStatus === 9 || res.data.payStatus === 13)
      ) {
        underWay.value = false
        if (res.data.payStatus === 9) {
          if (res.data.orderNum) orderNum.value = res.data.orderNum
          payTipType.value = 1
        } else if (res.data.payStatus === 13) {
          payTipType.value = 3
        } else if (res.data.orderNum?.startsWith('YF')) {
          // 预充值-支付成功
          if (topUpPayStatusChange)
            topUpPayStatusChange(res.data.payStatus, {
              orderNum: res.data.orderNum,
              prepayId: history.state.prepayId,
            })
          reject(res)
          return
        } else {
          payTipType.value = res.data.orderNum?.startsWith('HYWN') ? 2 : 0
        }
        payTipDialogVisible.value = true
        reject(res)
        return
      }
      // 3: 未支付 5: 用户支付中 10: 汇率异常 11: 代付中 12: 代付失效
      if (
        res.data.payStatus !== 3 &&
        res.data.payStatus !== 5 &&
        res.data.payStatus !== 10 &&
        res.data.payStatus !== 11 &&
        res.data.payStatus !== 12
      ) {
        underWay.value = false
        if (isAnotherPayCheck.value) {
          if (anotherPayStatusChange) anotherPayStatusChange(res.data.payStatus)
        } else {
          jumpPage(payStatus[res.data.payStatus])
        }
        reject(res)
        return
      }
      isAnotherPay.value = res.data.payStatus === 11
      if (res.data.payStatus === 12) {
        underWay.value = false
        if (anotherPayStatusChange) anotherPayStatusChange(res.data.payStatus)
        reject(res)
        return
      }
      reslove(true)
    })
    .catch((error: any) => {
      underWay.value = false
      if (error.message === 'Network Error' || error.message?.includes('timeout')) {
        jumpPage('网络连接异常，请检查网络连接后重试', '温馨提示')
      } else {
        jumpPage('Unknown Error', 'Network Tips')
      }
      console.error(error)
      reject(error)
    })
}

// const checkPayStatus = async () => {
//   let count = 0
//   while (underWay.value) {
//     await new Promise((reslove, reject) => {
//       if (count) {
//         setTimeout(() => {
//           checkFunc(reslove, reject)
//         }, 1500)
//       } else {
//         checkFunc(reslove, reject)
//       }
//     })
//     count++
//   }
// }
const checkPayStatus = async () => {
  let count = 0
  while (underWay.value) {
    try {
      await new Promise((resolve, reject) => {
        if (count > 0) {
          setTimeout(() => {
            if (!underWay.value) {
              reject('!underWay')
              return
            }
            checkFunc(resolve, reject)
          }, 1500)
        } else {
          checkFunc(resolve, reject)
        }
      })
      count++
    } catch (error) {
      if (error === '!underWay' || error === '!hasCurPage') break // 已有业务错误处理
      // 其他错误继续循环（如网络错误）
      console.error('轮询异常:', error)
    }
  }
}

/**
 * 页面销毁时，清空状态
 * @returns
 */
export function unUseOrderPay() {
  hasCurPage.value = false
  underWay.value = false
  platform.value = void 0
  isAnotherPay.value = false
  isAnotherPayCheck.value = false
  payTipDialogVisible.value = false
  mergeOrderNum.value = ''
  anotherPayStatusChange = () => {}
}

function setTimeoutFunc(clearTimeoutFunc: () => void) {
  return setTimeout(
    () => {
      if (!hasCurPage.value || !underWay.value) {
        clearTimeoutFunc()
        return
      }
      hasCurPage.value = false
      underWay.value = false
      ElMessageBox.confirm('您在支付页面停留时间过长，请刷新或重新进入。', '温馨提示', {
        showClose: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        cancelButtonText: '返回',
        confirmButtonText: '刷新',
      })
        .then(() => {
          window.location.reload()
        })
        .catch(() => {
          window.location.href = routerPathUrl.value
        })
    },
    1000 * 60 * 10
  ) // 10分钟超时
}

/**
 * 正常支付流程
 * @param order_num 订单号
 * @returns
 */
export function usePay(order_num: string, merge_id?: string) {
  underWay.value = true
  isAnotherPayCheck.value = false
  if (!order_num && !merge_id) {
    underWay.value = false
  }
  orderNum.value = order_num
  mergeOrderNum.value = merge_id || ''
  hasCurPage.value = true
  checkPayStatus()

  let timer = setTimeoutFunc(clearTimeoutFunc)

  function clearTimeoutFunc() {
    if (timer) {
      clearTimeout(timer)
    }
  }

  onUnmounted(() => {
    unUseOrderPay()
    clearTimeoutFunc()
  })

  return {
    orderNum,
    mergeOrderNum,
    underWay,
    isOpenPayDialog,
    routerPathUrl,
    checkPlatform,
    isAnotherPay,
    payTipDialogVisible,
    payTipType,
    checkPayStatus,
    clearTimeoutFunc,
  }
}

/**
 * 预充值流程
 * @param order_num 订单号
 * @returns
 */
export function useTopUpPay(order_num: string) {
  underWay.value = true
  isAnotherPayCheck.value = false
  if (!order_num) {
    underWay.value = false
  }
  orderNum.value = order_num
  hasCurPage.value = true
  routerPathUrl.value = '/finance/wallet'

  checkPayStatus()

  let timer = setTimeoutFunc(clearTimeoutFunc)

  function clearTimeoutFunc() {
    if (timer) {
      clearTimeout(timer)
    }
  }

  const router = useRouter()

  topUpPayStatusChange = (status: number, state: any) => {
    if (status == 1) {
      router.replace({ name: 'finance-wallet-paySucceed', state })
    }
  }

  onUnmounted(() => {
    unUseOrderPay()
    clearTimeoutFunc()
  })

  return {
    orderNum,
    underWay,
    isAnotherPay,
    isOpenPayDialog,
    checkPlatform,
    payTipDialogVisible,
    payTipType,
    checkPayStatus,
    clearTimeoutFunc,
  }
}

/**
 * 代付流程
 * @param order_num 订单号
 * @returns
 */
export function useAnotherPay(order_num: string, merge_id: string, change: (status: number) => void) {
  underWay.value = true
  isAnotherPay.value = true
  isAnotherPayCheck.value = true
  if (!order_num && !merge_id) {
    underWay.value = false
  }
  orderNum.value = order_num
  mergeOrderNum.value = merge_id || ''
  hasCurPage.value = true

  anotherPayStatusChange = change

  checkPayStatus()

  return {
    underWay,
    isOpenPayDialog,
    routerPathUrl,
    checkPlatform,
    isAnotherPay,
    checkPayStatus,
  }
}

/**
 * 支付成功提示弹窗
 * @returns
 */
export function usePayTip() {
  return {
    payTipDialogVisible,
    payTipType,
    orderNum,
    mergeOrderNum,
    routerPathUrl,
  }
}
