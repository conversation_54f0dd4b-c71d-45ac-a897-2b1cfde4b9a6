import { ref } from 'vue'
import { http_reg } from '@/utils/RegExp'

/**
 * 图片比例
 * compress：压缩图、fullSize：全尺寸、raw：原图、square：正方形
 */
export type ViewerScale =
  | ''
  | '1x1'
  | '3x2'
  | '2x3'
  | '3x4'
  | '4x3'
  | '16x9'
  | '9x16'
  | 'compress'
  | 'fullSize'
  | 'raw'
  | 'square'
  | 'thumbnail200'

export type ViewerOptions = {
  index?: number
  scale?: ViewerScale
}

const scaleMap: ViewerScale[] = ['1x1', '3x2', '2x3', '3x4', '4x3', '16x9', '9x16', 'compress', 'fullSize', 'raw','square','thumbnail200']
const imgViewerVisible = ref(false)
const imgViewerUrl = ref<string[]>([])
const imgViewerIndex = ref(0)
const http = import.meta.env.VITE_APP_FILE_HTTP_PATH
/**
 * 打开图片预览
 * @param list 图片
 * @param options 配置
 */
const showViewer = (list: string[], options?: ViewerOptions) => {
  if(!list?.length) return
  let scale = options?.scale ? options.scale.substring(options.scale.indexOf("!") + 1) : 'fullSize'
  imgViewerUrl.value = list.map(url => {
    if(!http_reg.test(url)) {
      url = http + url
    }
    const urlType = url.substring(url.lastIndexOf(".") + 1)
    const suffix: ViewerScale = url.substring(url.lastIndexOf("!") + 1) as ViewerScale
    if(suffix && scaleMap.includes(suffix) || urlType === 'gif') {
      return url
    }
    return url + '!' + scale
  })
  if (options?.index && options.index > 0 && options.index < list.length) {
    imgViewerIndex.value = options.index
  } else {
    imgViewerIndex.value = 0
  }
  imgViewerVisible.value = true
}
/**
 * 关闭图片预览
 */
const closeViewer = () => {
  imgViewerVisible.value = false
  imgViewerUrl.value = []
}

export function useViewer() {
  return {
    imgViewerVisible,
    imgViewerUrl,
    imgViewerIndex,
    showViewer,
    closeViewer,
  }
}
