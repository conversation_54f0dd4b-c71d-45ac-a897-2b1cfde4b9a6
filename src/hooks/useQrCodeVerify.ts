import type { CODE_TYPE, QRCODE_STATUS } from '@/components/public/qrcode/type'
import { checkAccount } from '@/api/wechat'
import { useUserStore } from '@/stores/modules/user'

export default function useQrCodeVerify() {
  const store = useUserStore()
  /**
   * 处理账号身份验证
   * @param code
   */
  function handleQrCodeVerifyAcc(code: {
    res: any
    ticket: string
    type: CODE_TYPE
    update: (status: QRCODE_STATUS | string, data: any) => void
    next: () => void
    stop: (err?: any) => void
  }) {
    if(code.type === 2 && code.res.data.loginStatus === 'LOGIN_SUCCESS') {
      checkAccount({
        ticket: code.ticket,
        account: store.userInfo.account
      }).then(res => {
        if(res.data) {
          code.update('ERROR', res.data)
          code.stop(res.data)
        } else {
          code.update(code.res.data.loginStatus, { ticket: code.ticket, ...code.res.data })
          code.next()
        }
      }).catch(() => {
        code.update('UNKNOWN', '')
        code.stop()
      })
      return
    }
    code.update(code.res.data.loginStatus, { ticket: code.ticket, ...code.res.data })
    code.next()
  }

  /**
   * 处理支付状态
   * @param code
   */
  function handleQrCodePayStatus(code: {
    res: any
    ticket: string
    type: CODE_TYPE
    update: (status: QRCODE_STATUS | string, data: any) => void
    next: () => void
    stop: () => void
  }) {
    if (code.res.data.payStatus == 0) {
      code.update('ERROR', '交易关闭！')
      code.stop()
    } else if (code.res.data.payStatus == 1) {
      code.update('LOGIN_SUCCESS', { ticket: code.ticket, ...code.res.data })
      code.stop()
    } else {
      code.next()
    }
  }

  return {
    handleQrCodeVerifyAcc,
    handleQrCodePayStatus
  }
}