<template>
  <RouterView v-slot="{ Component, route }">
    <KeepAlive :include="['home']">
      <component :is="Component" :key="setKey(route)" />
    </KeepAlive>
  </RouterView>

  <LoginDialog :show-close="showLoginClose" />
  <WelcomeDialog v-if="store.userInfo.account" />
  <Viewer />
  <MobileDeviceDialog />
  <FissionEnt
    v-if="
      store.userInfo.seedId ||
      (router.currentRoute.value.path === '/activity/fission' && store.userInfo.account)
    "
  />
</template>

<script setup lang="ts">
import LoginDialog from '@/views/login/LoginDialog.vue'
import WelcomeDialog from '@/components/public/dialog/WelcomeDialog.vue'
import Viewer from '@/components/public/viewer/index.vue'
import MobileDeviceDialog from '@/components/public/dialog/MobileDeviceDialog.vue'
import FissionEnt from '@/views/activity/dialog/FissionEnt.vue'
import { RouterView, useRouter } from 'vue-router'
import { useUserStore } from '@/stores/modules/user'
import { computed } from 'vue'

const store = useUserStore()
const router = useRouter()

const showLoginCloseList: any[] = [
  // '/activity/fission'
]
const showLoginClose = computed(() => {
  // if (showLoginCloseList.includes(router.currentRoute.value.fullPath)) {
  //   return false
  // }
  return true
})

function setKey(r: any) {
  if (r.matched && r.matched.length) {
    return r.matched[0].name || r.matched[0].path
  }
  return r.name || r.path
}

if (import.meta.env.VITE_APP_ENV === 'production') {
  var _hmt: any = _hmt || []
  ;(function () {
    var hm = document.createElement('script')
    hm.src = 'https://hm.baidu.com/hm.js?6bfeadf2feec71a19a9e823d6f734b72'
    var s = document.getElementsByTagName('script')[0]
    s.parentNode?.insertBefore(hm, s)
  })()
}
</script>

<style scoped></style>
