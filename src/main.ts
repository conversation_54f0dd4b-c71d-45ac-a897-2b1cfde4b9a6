import { createApp, type ComponentPublicInstance } from 'vue'
import pinia from './stores'

import Cookies from 'js-cookie'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import locale from 'element-plus/es/locale/lang/zh-cn'

import 'element-plus/theme-chalk/dark/css-vars.css'
import './assets/css/theme.css'
import './assets/main.css'
import './assets/iconfont/iconfont.css'
import './assets/iconfont/iconfont.js'
import './styles/customPreviewBox.scss'

import PublicDialog from '@/components/public/PublicDialog.vue'
import MessageBoxDialog from '@/components/public/dialog/MessageBoxDialog.vue'
import Empty from '@/components/public/empty/index.vue'

import App from './App.vue'
import router from './router'
import directive from './directive'

import { useDict } from '@/utils/dict'
import aegis from '@/utils/aegis'
// import { recordConsoleLogError } from '@/api/index'

const app = createApp(App)

app.config.globalProperties.useDict = useDict
app.config.globalProperties.$picUrl = import.meta.env.VITE_APP_FILE_HTTP_PATH
app.config.globalProperties.$officialWebsiteUrl = import.meta.env.VITE_APP_OFFICIAL_WEBSITE_API
app.config.globalProperties.$connectMeUrl = import.meta.env.VITE_APP_CONNECT_ME_URL

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.component('PublicDialog', PublicDialog)
app.component('MessageBoxDialog', MessageBoxDialog)
app.component('Empty', Empty)

app.use(pinia)
app.use(router)

directive(app)

if (import.meta.env.VITE_APP_ENV != 'development') {
  app.config.errorHandler = function (err: unknown, vm: ComponentPublicInstance | null, info: string) {
    if (err instanceof Error) {
      aegis.error(`Error: ${err.toString()}\nStack: ${err.stack}\nInfo: ${info}`)
    } else {
      aegis.error(`Error: ${err}\nInfo: ${info}`)
    }
  }
}

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale,
  // 支持 large、default、small
  size: (Cookies.get('size') as any) || 'default',
})
app.mount('#app')

if (import.meta.env.VITE_APP_ENV !== 'development') {
  document.addEventListener('contextmenu', function (event) {
    event.preventDefault() // 禁用右键菜单
  })
}

// 全局异常信息记录
// window.onerror = recordConsoleLogError
