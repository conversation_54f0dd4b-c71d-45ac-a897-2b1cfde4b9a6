version: "3.7"
services:
  wnkx-front-customer-daily:
    container_name: wnkx-front-customer-daily
    image: $IMAGE_TAG/wnkx-front-customer:latest-daily
    restart: always
    ports:
      - "25781:80"
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-daily:
        ipv4_address: *************
networks:
  wnkx-network-daily:
    external: true
