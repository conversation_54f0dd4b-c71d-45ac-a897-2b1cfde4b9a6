version: "3.7"
services:
  wnkx-front-customer:
    container_name: wnkx-front-customer
    image: $IMAGE_TAG/wnkx-front-customer:latest
    restart: always
    ports:
      - "25381:80"
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network:
        ipv4_address: *************
networks:
  wnkx-network:
    external: true
