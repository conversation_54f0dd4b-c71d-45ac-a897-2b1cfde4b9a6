version: "3.7"
services:
  wnkx-front-customer-test:
    container_name: wnkx-front-customer-test
    image: $IMAGE_TAG/wnkx-front-customer:latest-test
    restart: always
    ports:
      - "25481:80"
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-uat:
        ipv4_address: *************
networks:
  wnkx-network-uat:
    external: true
