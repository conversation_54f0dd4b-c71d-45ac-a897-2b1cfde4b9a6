version: "3.7"
services:
  wnkx-front-customer-uat:
    container_name: wnkx-front-customer-uat
    image: $IMAGE_TAG/wnkx-front-customer:latest-uat
    restart: always
    ports:
      - "25581:80"
    logging:
      driver: loki
      options:
        loki-url: "http://**********:3100/loki/api/v1/push"
        max-size: 50m
        max-file: "10"
    networks:
      wnkx-network-test:
        ipv4_address: *************
networks:
  wnkx-network-test:
    external: true
