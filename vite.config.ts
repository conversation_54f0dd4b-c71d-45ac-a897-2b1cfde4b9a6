import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import createVitePlugins from './vite/plugins'

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd())
  return {
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.json', '.vue'],
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    server: {
      port: 8888,
      host: true,
      // 设置 https 代理
      proxy: {
        '/api': {
          // target: 'http://************:8080', // dwy
          // target: 'http://************:8080', // fzw
          // target: 'https://api.woniu.video/', // 生产 api 地址
          // target: 'https://api.dev.woniu.video/', // 开发 api 地址
          // target: 'https://api.lc3oaioj.daily.woniu.video', //daily api 地址
          // target: 'https://api.test965523.dev.woniu.video/', // 测试 api 地址
          target: 'http://localhost:8090/', // 测试 api 地址
          // target: 'https://api.fklwnkbum.uat.woniu.video/', // 预发布 api 地址
          changeOrigin: true,
          rewrite: p => p.replace(/^\/api/, ''),
        },
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "@/styles/media.scss" as *;
          `,
          api: 'modern-compiler', // or "modern", "legacy"
        },
      },
    },
    // 生产环境打包配置
    // 去除 console debugger
    build: {
      sourcemap: true,
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
    },
    define: {
      __VUE_PROD_DEVTOOLS__: env.VITE_APP_ENV === 'development',
    },
  }
})
